import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';

import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { ContextType } from '@lit/context';
import { live } from 'lit/directives/live.js';
import { api } from './api.js';
import { docLinkStyles, pageStyles } from './components/cssstyles.js';
import { ProjectContext } from './components/project-context.js';
import { UserSelectedEvent } from './components/search-users.js';
import { consume, contexts, FKProjectChanged } from './contexts.js';
import { githubMarkdownCSS } from './githubMarkdownCSS.js';
import { RouteConfig } from './router.js';

@customElement('page-project-collaborators')
export class PageProjectCollaborators extends LitElement {

  static url(options: { orgSlug: string, projectSlug: string, tab?: 'general'|'usage' }): string {
    return new URL(['', options.orgSlug, options.projectSlug, 'collaborators'].join('/'), window.location.href).href;
  }

  static routes(): RouteConfig[] {
    return [{
      path: [
        '/:org/:project/collaborators',
      ],
      render: (groups) => ProjectContext.render({
        orgSlug: groups?.org,
        projectSlug: groups?.project,
      }, html`
        <page-project-collaborators
          .orgSlug=${groups?.org}
          .projectSlug=${groups?.project}
        ></page-project-collaborators>
      `),
    }];
  }

  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;
  @consume({ context: contexts.projectCollaborators, subscribe: true }) private _projectCollaborators?: ContextType<typeof contexts.projectCollaborators>;

  @property({ attribute: false }) projectSlug?: string;
  @property({ attribute: false }) orgSlug?: string;

  render() {
    if (!this._project)
      return nothing;

    return html`
      <project-header
        .orgSlug=${this.orgSlug}
        .projectSlug=${this.projectSlug}
        .submenu=${"collaborators"}
      ></project-header>
      <app-body user-role=${this._project.access}>
        <h1>Project Collaborators</h1>

        <form-section label="Project collaborators">
          <v-box>
            <search-users is-editor
              placeholder="Add collaborator"
              @user-selected=${(event: UserSelectedEvent) => this._addCollaborator(event.user)}
            ></search-users>
            ${this._projectCollaborators === undefined ? html`<sl-spinner></sl-spinner>` :
              this._projectCollaborators.length === 0 ? html`<div>No collaborators</div>` :
              this._projectCollaborators.map(({ user, userRole }) => html`
                <project-collaborator
                  .project=${this._project}
                  .user=${user}
                  .projectRole=${live(userRole)}
                ></project-collaborator>
              `)
            }
          </v-box>
        </form-section>
      </app-body>
      <app-footer></app-footer>
    `
  }

  private async _addCollaborator(user: WireTypes.User) {
    const { orgSlug, projectSlug } = this;
    if (!orgSlug || !projectSlug)
      return;
    await api.project.shareProject.POST({
      orgSlug,
      projectSlug,
      userId: user.userId,
      access: 'viewer',
    });
    this.dispatchEvent(new FKProjectChanged({ orgSlug, projectSlug }));
  }

  static styles = [githubMarkdownCSS, docLinkStyles, pageStyles, css`
    [user-role=viewer] {
      *:is([is-editor]) {
        display: none;
      }
    }
  `];
}
