import { consume, ContextType } from '@lit/context';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, state } from 'lit/decorators.js';
import { linkStyles, pageStyles } from './components/cssstyles.js';
import { contexts } from './contexts.js';
import { RouteConfig } from './router.js';

declare global {
  // This is embedded via esbuild define option.
  const COMMIT_SHA: string;
  const BUILD_TIMESTAMP: number;
  const PACKAGE_JSON_VERSION: string;
}

@customElement('page-administration-billing')
export class PageAdministrationBilling extends LitElement {
  static routes(): RouteConfig[] {
    return [{
      path: [
        '/administration/billing',
      ],
      render: () => html`<page-administration-billing></page-administration-billing>`,
    }];
  }

  static url(): string {
    return new URL('/administration/billing', window.location.href).href;
  }

  @consume({ context: contexts.serverInfo, subscribe: true }) @state() private _serverInfo?: ContextType<typeof contexts.serverInfo>;

  override render() {
    return html`
      <administration-header submenu=billing></administration-header>
      <app-body>
        <v-box>
          <div>
            Flakiness.io version <b><a-ext href=${`https://github.com/flakiness/flakiness/commits/${COMMIT_SHA}`}>v${PACKAGE_JSON_VERSION}</a-ext></b> built <b><sl-relative-time date=${new Date(BUILD_TIMESTAMP).toISOString()}></sl-relative-time></b>
          </div>
          <div>
            License valid until: <sl-format-date month="long" day="numeric" year="numeric" hour="numeric" minute="numeric" hour-format="12" date=${new Date(this._serverInfo?.deploymentLicense.validUntilMs!)}></sl-format-date>
          </div>
          <div>
            Billing is: <strong>${this._serverInfo?.enabledBilling ? 'enabled' : 'disabled'}</strong>
          </div>
          <div>
            Server hostname: <strong>${this._serverInfo?.hostname}</strong>
          </div>
        </v-box>
        ${this._serverInfo?.enabledBilling ? html`
          <product-plans show-plan-id></product-plans>
        ` : nothing}
      </app-body>
      <app-footer></app-footer>
    `
  }

  static styles = [pageStyles, linkStyles, css`
    :host {
      display: flex;
      flex-direction: column;
      gap: var(--sl-spacing-medium);
    }

    #plans {
      display: grid;
      grid-template-columns: auto auto auto auto auto;

      .header {
        border-bottom: 1px solid var(--fk-color-border);
        font-weight: var(--sl-font-weight-bold);
        padding: var(--sl-spacing-2x-small) 0;
      }

      .cell {
        padding: var(--sl-spacing-2x-small) 0;
      }
    }
  `];
}
