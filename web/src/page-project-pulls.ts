
import { ContextType } from '@lit/context';
import { Task } from '@lit/task';
import { SlChangeEvent, SlRadioGroup } from '@shoelace-style/shoelace';
import { LitElement, css, html } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { linkStyles, pageStyles } from './components/cssstyles.js';
import { ProjectAlerts } from './components/project-alerts.js';
import { ProjectContext } from './components/project-context.js';
import { consume, contexts } from './contexts.js';
import { createPullsDataProvider } from './data-table-providers/pullsProvider.js';
import { markdownStyles } from './markdown.js';
import { RouteConfig, URLState } from './router.js';
import { assert } from './utils.js';

const pageState = new URLState({
  prState: URLState.option<string>({
    name: 'pr_state',
    default: 'open',
    encode: filter => filter,
    decode: filter => filter ?? 'all',
  }),
  testState: URLState.option<string>({
    name: 'test_state',
    default: 'all',
    encode: filter => filter,
    decode: filter => filter ?? 'all',
  }),
});

@customElement('page-project-pulls')
export class PageProjectPulls extends LitElement {
  static url(options: { orgSlug: string, projectSlug: string, }): string {
    return new URL(['', options.orgSlug, options.projectSlug, 'pulls'].join('/'), window.location.href).href;
  }

  static routes(): RouteConfig[] {
    return [{
      path: [
        '/:org/:project/pulls',
        '/:org/:project/pulls/'
      ],
      render: (groups) => ProjectContext.render({
        orgSlug: groups?.org,
        projectSlug: groups?.project,
      }, html`
        <page-project-pulls
          .orgSlug=${groups?.org}
          .projectSlug=${groups?.project}
        ></page-project-pulls>
      `),
    }];
  }

  @consume({ context: contexts.user, subscribe: true }) private _user?: ContextType<typeof contexts.user>;
  @consume({ context: contexts.router, subscribe: true }) private _router?: ContextType<typeof contexts.router>;
  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;

  @property({ attribute: false }) projectSlug?: string;
  @property({ attribute: false }) orgSlug?: string;

  private _urlState = pageState.bind(this);

  private _dataTableProviderTask = new Task(this, {
    args: () => [
      this._project, this._urlState.prState, this._urlState.testState,
    ] as const,
    task: async ([project, prState, testState], { signal }) => {
      assert(project);
      if (!prState || !['all', 'open', 'closed', 'merged'].includes(prState))
        prState = 'all';
      if (!testState || !['all', 'tested', 'untested'].includes(testState))
        testState = 'all';

      return createPullsDataProvider({
        project,
        prState: prState as any,
        testState: testState as any,
      });
    }
  });

  override render() {
    return html`
      <project-header
        .orgSlug=${this.orgSlug}
        .projectSlug=${this.projectSlug}
        .submenu=${"pulls"}
      ></project-header>
      <app-body wide>
        ${ProjectAlerts.maybeRender(this._project)}
        <h-box>
          <sl-radio-group size=small value=${this._urlState.prState} @sl-change=${(e: SlChangeEvent) => {
            const prState = (e.target as SlRadioGroup).value;
            this._urlState.prState = prState;
          }}>
            <sl-radio-button value="all">All</sl-radio-button>
            <sl-radio-button value="open">Opened</sl-radio-button>
            <sl-radio-button value="closed">Closed</sl-radio-button>
            <sl-radio-button value="merged">Merged</sl-radio-button>
          </sl-radio-group>
          <sl-radio-group size=small value=${this._urlState.testState} @sl-change=${(e: SlChangeEvent) => {
            this._urlState.testState = (e.target as SlRadioGroup).value;
          }}>
            <sl-radio-button value="all">All</sl-radio-button>
            <sl-radio-button value="tested">Tested</sl-radio-button>
            <sl-radio-button value="untested">Untested</sl-radio-button>
          </sl-radio-group>
        </h-box>
        <data-table
          .pageNumber=${0}
          .pageSize=${20}
          .provider=${this._dataTableProviderTask.value}
        ></data-table>
      </app-body>
      <app-footer></app-footer>
    `;
  }

  static styles = [...markdownStyles, pageStyles, linkStyles, css`
    .markdown-body {
      margin-top: var(--sl-spacing-medium);
    }

    .commit-history {
      fk-commitstats + fk-commitstats {
        border-top: 1px solid var(--fk-color-border);
        border-radius: 0px;
      }
    }
  `]
}
