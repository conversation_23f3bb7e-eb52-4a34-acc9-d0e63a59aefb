import { css, html, LitElement, nothing } from 'lit';
import { customElement, property, query } from 'lit/decorators.js';

import { ContextType } from '@lit/context';
import { Task } from '@lit/task';
import { SlInput } from '@shoelace-style/shoelace';
import { live } from 'lit/directives/live.js';
import { api } from './api.js';
import { docLinkStyles, pageStyles } from './components/cssstyles.js';
import { ProjectContext } from './components/project-context.js';
import { consume, contexts, FKProjectChanged } from './contexts.js';
import { githubMarkdownCSS } from './githubMarkdownCSS.js';
import { RouteConfig } from './router.js';
import { assert } from './utils.js';

@customElement('page-project-usage')
export class PageProjectUsage extends LitElement {

  static url(options: { orgSlug: string, projectSlug: string, }): string {
    return new URL(['', options.orgSlug, options.projectSlug, 'usage'].join('/'), window.location.href).href;
  }

  static routes(): RouteConfig[] {
    return [{
      path: [
        '/:org/:project/usage',
      ],
      render: (groups) => ProjectContext.render({
        orgSlug: groups?.org,
        projectSlug: groups?.project,
      }, html`
        <page-project-usage
          .orgSlug=${groups?.org}
          .projectSlug=${groups?.project}
        ></page-project-usage>
      `),
    }];
  }

  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;
  @consume({ context: contexts.user }) private _user?: ContextType<typeof contexts.user>;

  @property({ attribute: false }) projectSlug?: string;
  @property({ attribute: false }) orgSlug?: string;
  
  @query('#data-retention') private _dataRetention?: SlInput;

  private _maxDataRetentionTask = new Task(this, {
    args: () => [this.orgSlug, this.projectSlug] as const,
    task: async ([orgSlug, projectSlug], { signal }) => {
      assert(orgSlug && projectSlug);
      const result = await api.project.maxDataRetention.GET({ orgSlug, projectSlug }, { signal });
      return result ?? Infinity;
    }
  });

  private _dataRetentionTask = new Task(this, {
    args: () => [this._project, this._maxDataRetentionTask.value] as const,
    task: async ([project, maxDataRetentionDays], { signal }) => {
      assert(project);
      return Math.min(project.preferredDataRetentionDays ?? Infinity, maxDataRetentionDays ?? Infinity);
    }
  });

  render() {
    if (!this._project)
      return nothing;
    const maxDataRetentionDays = this._maxDataRetentionTask.value ?? Infinity;
    const dataRetentionDays = this._dataRetentionTask.value ?? Infinity;

    return html`
      <project-header
        .orgSlug=${this.orgSlug}
        .projectSlug=${this.projectSlug}
        .submenu=${"usage"}
      ></project-header>
      <app-body user-role=${this._project.access}>
        <h1>Project Usage</h1>

        <form-section label="Data Retention" is-editor>
          <div slot=description>
            Maximum data retention for <strong>"${this._project.org.orgName}"</strong> organization is
            <strong>${maxDataRetentionDays === Infinity ? 'unlimited' : maxDataRetentionDays + ' days'}</strong>.
          </div>
          <h-box style="align-items: start;">
            <sl-input style="width: 12em;" placeholder="unlimited" id=data-retention type=number max=${9999} min=${1} .value=${live(dataRetentionDays === Infinity ? undefined : dataRetentionDays)}>
              <div slot=suffix>days</div>
            </sl-input>
            <sl-button @click=${async () => {
              if (!this.orgSlug || !this.projectSlug || !this._dataRetention)
                return;
              await api.project.setProjectPreferredDataRetention.POST({
                orgSlug: this.orgSlug,
                projectSlug: this.projectSlug,
                preferredDataRetentionDays: this._dataRetention.value ? parseInt(this._dataRetention.value, 10) : undefined,
              });
              this.dispatchEvent(new FKProjectChanged({ orgSlug: this.orgSlug, projectSlug: this.projectSlug }));
            }}>Save</sl-button>
          </h-box>
        </form-section>

        <usage-metrics
          .orgSlug=${this.orgSlug}
          .projectSlug=${this.projectSlug}
          .days=${Math.min(dataRetentionDays, 365)}
        ></usage-metrics>
      </app-body>
      <app-footer></app-footer>
    `
  }

  static styles = [githubMarkdownCSS, docLinkStyles, pageStyles, css`
    [user-role=viewer] {
      *:is([is-editor]) {
        display: none;
      }
    }
  `];
}
