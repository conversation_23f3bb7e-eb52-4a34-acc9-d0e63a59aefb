import { css, html, LitElement, nothing } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';

import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { ContextType } from '@lit/context';
import { live } from 'lit/directives/live.js';
import { api } from './api.js';
import { docLinkStyles, pageStyles } from './components/cssstyles.js';
import { OrgAlerts } from './components/org-alerts.js';
import { ProjectContext } from './components/project-context.js';
import { UserSelectedEvent } from './components/search-users.js';
import { consume, contexts, FKOrgChanged } from './contexts.js';
import { githubMarkdownCSS } from './githubMarkdownCSS.js';
import { RouteConfig } from './router.js';

@customElement('page-organization-members')
export class PageOrganizationMembers extends LitElement {

  static url(options: { orgSlug: string, tab?: 'billing'|'general'|'members' }): string {
    return new URL(['', options.orgSlug, 'members'].join('/'), window.location.href).href;
  }

  static routes(): RouteConfig[] {
    return [{
      path: [
        '/:org/members',
      ],
      render: (groups) => ProjectContext.render({
        orgSlug: groups?.org,
      }, html`
        <page-organization-members
          .orgSlug=${groups?.org}
        ></page-organization-members>
      `),
    }];
  }

  @consume({ context: contexts.user, subscribe: true }) private _user?: ContextType<typeof contexts.user>;
  @consume({ context: contexts.org, subscribe: true }) private _org?: ContextType<typeof contexts.org>;
  @consume({ context: contexts.orgMembers, subscribe: true }) @state() private _orgMembers?: ContextType<typeof contexts.orgMembers>;

  @property({ attribute: false }) orgSlug?: string;

  private async _addMember(user: WireTypes.User) {
    const { orgSlug } = this;
    if (!orgSlug)
      return;
    // If current user is the same as picked user, and current relationship is "owner",
    // then we cannot add the user as collaborator.
    if (user.userId === this._user?.userId && this._org?.access === 'owner')
      return;

    await api.organization.setMembership.POST({
      orgSlug,
      userId: user.userId,
      role: 'member',
    });
    this.dispatchEvent(new FKOrgChanged({ orgSlug }));
  }

  render() {
    if (this._org && !this._org.access)
      return html`<page-http-error code=403></page-http-error>`;

    return html`
      <org-header .orgSlug=${this.orgSlug} submenu=members></org-header>
      <app-body user-role=${this._org?.access}>
        ${OrgAlerts.maybeRender(this._org)}
        <h1>Organization Members</h1>
        <v-box>
          <search-users is-admin
            placeholder="Add member"
            @user-selected=${(event: UserSelectedEvent) => this._addMember(event.user)}
          ></search-users>
          <org-member
            .org=${this._org}
            .user=${this._orgMembers?.owner}
            .orgRole=${'owner'}
          ></org-member>
          ${this._orgMembers?.members?.length ? this._orgMembers.members.map(({ user, accessRole }) => html`
            <org-member
              .org=${this._org}
              .user=${user}
              .orgRole=${live(accessRole)}
            ></org-member>`) : nothing
          }
        </v-box>
      </app-body>
      <app-footer></app-footer>
    `
  }

  static styles = [githubMarkdownCSS, docLinkStyles, pageStyles, css`

    [user-role=member] *:is([is-admin], [is-owner]) {
      display: none;
    }

    [user-role=admin] *:is([is-owner]) {
      display: none;
    }
  `];
}
