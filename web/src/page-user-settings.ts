import { Task } from '@lit/task';
import { css, html, LitElement, nothing } from 'lit';
import { customElement } from 'lit/decorators.js';
import { api } from './api.js';

import { clientName, KNOWN_CLIENT_IDS } from '@flakiness/server/common/knownClientIds.js';
import { ContextType } from '@lit/context';
import { linkStyles, pageStyles } from './components/cssstyles.js';
import { consume, contexts } from './contexts.js';
import { RouteConfig } from './router.js';

@customElement('page-user-settings')
export class PageUserSettings extends LitElement {
  static routes(): RouteConfig[] {
    return [{
      path: [ '/settings' ],
      render: (groups) => html`<page-user-settings></page-user-settings>`,
    }];
  }

  static url(): string {
    return new URL('/settings', window.location.href).href;
  }

  @consume({ context: contexts.user, subscribe: true })
  private _user?: ContextType<typeof contexts.user>;;

  @consume({ context: contexts.serverInfo, subscribe: true })
  private _serverInfo?: ContextType<typeof contexts.serverInfo>;;

  private _sessionsTask = new Task(this, {
    args: () => [this._user],
    task: async ([user], { signal }) => {
      const [currentSession, allSessions] = await Promise.all([
        api.user.currentSession.GET(),
        api.user.sessions.GET()
      ]);
      return { currentSession, allSessions };
    }
  });

  render() {
    const { currentSession, allSessions } = this._sessionsTask.value ?? { currentSession: undefined, allSessions: [] };
    return html`
      <app-header .customTitle=${"Settings"}></app-header>
      <app-body>
        <h1>Settings</h1>

        <form-section label="User Sessions">
          <section class=sessions>
            <div class="session header">
              <span></span>
              <span></span>
              <span>Last Access</span>
              <span></span>
              <span></span>
            </div>
          
            ${allSessions.map(session => html`
              <div class=session>
                <span>${session.sessionPublicId === currentSession?.sessionPublicId ? 'current' : nothing}</span>
                <sl-tooltip>
                  <span slot=content>${clientName(session.clientId)}</span>
                  <sl-icon name=${
                    session.clientId === KNOWN_CLIENT_IDS.OFFICIAL_CLI ? 'terminal' : 
                    session.clientId === KNOWN_CLIENT_IDS.OFFICIAL_WEB ? 'browser-chrome' :
                    ''
                  }></sl-icon>
                </sl-tooltip>

                <sl-format-date month="long" day="numeric" year="numeric" hour="numeric" minute="numeric" date=${new Date(session.lastAccessTimestampSeconds * 1000).toISOString()}></sl-format-date>
                <div>${session.name ?? nothing}</div>
                <sl-button size=small @click=${async () => {
                  await api.user.logoutSession.POST({
                    sessionId: session.sessionPublicId,
                  });
                  this._sessionsTask.run();
                }}>Logout</sl-button>
              </div>
            `)}
          </section>
        </form-section>
      </app-body>
      <app-footer></app-footer>
    `;
  }

  static styles = [linkStyles, pageStyles, css`
    .sessions {
      display: grid;
      grid-template-columns: auto auto 1fr auto auto;
      border: 1px solid var(--fk-color-border);
      border-radius: var(--sl-border-radius-large);
    }

    .session {
      display: grid;
      grid-column: 1/-1;
      grid-template-columns: subgrid;
      padding: var(--sl-spacing-small);
      gap: var(--sl-spacing-small);
      align-items: center;

      &.header {
        font-weight: bold;
        background: var(--sl-color-neutral-50);
      }

      &:not(.header):hover {
        background: var(--fk-color-tablerow-hover);
      }
    }
  `];
}
