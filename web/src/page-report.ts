import { FlakinessReport } from '@flakiness/report';
import { Query } from '@flakiness/server/common/fql/query.js';

import { Stats } from '@flakiness/server/common/stats/stats.js';
import { Timeline } from '@flakiness/server/common/timeline/timeline.js';
import { TimelineSplit } from '@flakiness/server/common/timeline/timelineSplit.js';
import { type WireTypes } from '@flakiness/server/common/wireTypes.js';
import { ContextType } from '@lit/context';
import { Task } from '@lit/task';
import { timeDay } from 'd3';
import { LitElement, css, html, nothing } from 'lit';
import { customElement, property, query } from 'lit/decorators.js';
import { styleMap } from 'lit/directives/style-map.js';
import { Temporal } from 'temporal-polyfill';
import { api } from './api.js';
import { linkStyles, pageStyles } from './components/cssstyles.js';
import { DataTableLinker } from './components/data-table.js';
import { ExecutionCalendarLinker } from './components/execution-calendar.js';
import { ExecutionHistory } from './components/execution-history.js';
import { FqlChangedEvent } from './components/fql-search.js';
import { FKHeadSelectEvent } from './components/head-selector.js';
import { ProjectAlerts } from './components/project-alerts.js';
import { ProjectContext } from './components/project-context.js';
import { ContextTask, consume, contexts } from './contexts.js';
import { createAnnotationStatsDataProvider, createAnnotationStatsLoaderRemote } from './data-table-providers/annotationsProvider.js';
import { createErrorStatsDataProvider, createErrorStatsLoaderRemote } from './data-table-providers/errorsProvider.js';
import { createDayDurationChartsColumn } from './data-table-providers/genericColumns.js';
import { createTagStatsDataProvider, createTagStatsLoaderRemote } from './data-table-providers/tagsProvider.js';
import { createTestStatsDataProvider, createTestStatsLoaderRemote } from './data-table-providers/testStatsProvider.js';
import { createTimelineStatsDataProvider, createTimelineStatsLoaderRemote } from './data-table-providers/timelineStatsProvider.js';
import { welcome_new_project } from './docs/welcome_new_project.js';
import { markdownStyles } from './markdown.js';
import { PageRun } from './page-run.js';
import { RouteConfig, URLState } from './router.js';
import { tasks } from './tasks.js';
import { assert, renderDatesRange } from './utils.js';


type PlaneName = 'tests'|'timelines'|'errors'|'tags'|'annotations';

const dashboardState = new URLState({
  query: URLState.option<string>({
    name: 'q',
    default: '',
    encode: filter => filter,
    decode: filter => filter,
  }),
  /* report time interval options */
  head: URLState.option<string|undefined>({
    name: 'head',
    default: undefined,
    encode: head => head,
    decode: head => head,
  }),
  commitsCount: URLState.option<number|undefined>({
    name: 'commits_count',
    default: undefined,
    encode: count => count !== undefined ? String(count) : undefined, 
    decode: countText => countText ? parseInt(countText, 10) : undefined,
  }),
  since: URLState.dateOption({
    name: 'since',
    default: undefined,
  }),
  until: URLState.dateOption({
    name: 'until',
    default: undefined,
  }),
  timelineSplit: URLState.option<TimelineSplit>({
    name: 'split',
    default: TimelineSplit.DEFAULT,
    encode: split => JSON.stringify(split.serialize()),
    decode: text => TimelineSplit.deserialize(JSON.parse(text) as WireTypes.JSONTimelineSplit),
  }),
  testId: URLState.option<Stats.TestId|undefined>({
    name: 'test_id',
    default: undefined,
    encode: testId => testId,
    decode: testId => testId ? testId as Stats.TestId : undefined,
  }),
  testTimeline: URLState.option<Timeline|undefined>({
    name: 'test_timeline',
    default: undefined,
    encode: timeline => JSON.stringify(timeline?.serialize()),
    decode: txt => txt ? Timeline.deserialize(JSON.parse(txt)) : undefined,
  }),

  /* ---- */
  plane: URLState.option<PlaneName|undefined>({
    name: 'tab',
    default: undefined,
    encode: plane => plane,
    decode: plane => ['tests', 'timelines', 'errors', 'commits', 'tags', 'annotations'].includes(plane) ? plane as PlaneName : undefined,
  }),
  page: URLState.option<number>({
    name: 'page',
    default: 0,
    // keep pages one-based in url for user's pleasure.
    encode: page => String(page + 1),
    decode: page => isNaN(parseInt(page, 10)) ? 1 : parseInt(page, 10) - 1,
  }),
  pageSize: URLState.option<number>({
    name: 'page_size',
    default: 20,
    encode: pageSize => String(pageSize),
    decode: pageSize => isNaN(parseInt(pageSize, 10)) ? 1 : parseInt(pageSize, 10),
  }),
  sortName: URLState.option<string|undefined>({
    name: 'sort_by',
    default: undefined,
    encode: sort => sort,
    decode: sort => sort,
  }),
  sortDirection: URLState.option<WireTypes.SortDirection|undefined>({
    name: 'sort_dir',
    default: undefined,
    encode: sort => sort,
    decode: sort => sort === 'asc' || sort === 'desc' ? sort as WireTypes.SortDirection : undefined,
  }),
});

@customElement('page-report')
export class PageReport extends LitElement {
  static routes(): RouteConfig[] {
    return [{
      path: [
        '/:org/:project/',
        '/:org/:project'
      ],
      render: (groups) => ProjectContext.render({
        orgSlug: groups?.org,
        projectSlug: groups?.project,
      }, html`
        <page-report
          .orgSlug=${groups?.org}
          .projectSlug=${groups?.project}
        ></page-report>
      `),
    }, {
      path: [
        '/:org/:project/pull/:pull',
        '/:org/:project/pull/:pull/'
      ],
      render: (groups) => ProjectContext.render({
        orgSlug: groups?.org,
        projectSlug: groups?.project,
      }, html`
        <page-report
          .orgSlug=${groups?.org}
          .projectSlug=${groups?.project}
          .pullRequestNumber=${groups?.pull}
        ></page-report>
      `),
    }];
  }

  static url(options: {
    orgSlug: string,
    projectSlug: string,
    plane?: PlaneName,
    fql?: string,
    testId?: Stats.TestId,
    testTimeline?: Timeline,
    pullRequest?: number,
    head?: string,
    since?: Date,
    until?: Date,
    commitsCount?: number,
    timelineSplit?: TimelineSplit,
  }) {
    const linker = dashboardState.createLinkRenderer({
      pathname: options.pullRequest ? 
        ['', options.orgSlug, options.projectSlug, 'pull', options.pullRequest].join('/') :
        ['', options.orgSlug, options.projectSlug].join('/'),
    });
    return linker({
      query: options.fql?.trim() ? options.fql : undefined,
      plane: options.plane,
      head: options.head,
      since: options.since,
      until: options.until,
      timelineSplit: options.timelineSplit,
      commitsCount: options.commitsCount,
      testTimeline: options.testTimeline,
      testId: options.testId,
    });
  }

  private _urlState = dashboardState.bind(this, () => {
    this._executionHistory?.hide();
  });

  @consume({ context: contexts.user, subscribe: true }) private _user?: ContextType<typeof contexts.user>;
  @consume({ context: contexts.router, subscribe: true }) private _router?: ContextType<typeof contexts.router>;
  @consume({ context: contexts.projectReferences, subscribe: true }) private _branches?: ContextType<typeof contexts.projectReferences>;
  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;

  @property({ attribute: false }) projectSlug?: string;
  @property({ attribute: false }) orgSlug?: string;
  @property({ attribute: false }) pullRequestNumber?: string;

  @query('execution-history') private _executionHistory?: ExecutionHistory;

  private _dataTableLinker = new Task(this, {
    args: () => [window.location.href],
    task: async ([], { signal }) => {
      // All self-links depend on self-href to be up-to-date.
      const linkRenderer = dashboardState.createLinkRenderer({ base: this._urlState });
      return ((state) => linkRenderer(state)) satisfies DataTableLinker;
    }
  });

  private _fqlTask = new ContextTask(this, contexts.fql, {
    args: () => [this._urlState.query] as const,
    task: async ([query], { signal }) => {
      return Query.parse(query ?? '');
    }
  });

  private _fqlLinker = new ContextTask(this, contexts.linkFQL, {
    args: () => [window.location.href],
    task: async ([], { signal }) => {
      // All self-links depend on self-href to be up-to-date.
      const linkRenderer = dashboardState.createLinkRenderer({ base: this._urlState });
      return {
        render: (query) => linkRenderer({ query, page: 0, }),
      }
    }
  });

  private _timelineLinker = new ContextTask(this, contexts.linkTimeline, {
    args: () => [window.location.href],
    task: async ([], { signal }) => {
      // All self-links depend on self-href to be up-to-date.
      const linkRenderer = dashboardState.createLinkRenderer({ base: this._urlState });
      return {
        render: (timeline) => linkRenderer({ timelineSplit: TimelineSplit.fromTimeline(timeline), page: 0 }),
      }
    }
  });

  private _reportOutcomes = new Task(this, {
    args: () => [
      this._reportOptionsTask.value
    ] as const,
    task: async ([reportOptions], { signal }) => {
      assert(reportOptions);
      const result = await api.report.counters.POST({
        ...reportOptions,
        fql: Query.parse(reportOptions.fql).clearStatusFilters().serialize(),
      });
      return result.testOutcomes;
    }
  });

  private _testLinkRenderer = new ContextTask(this, contexts.linkTest, {
    args: () => [this.orgSlug, this.projectSlug, this._branches, this._urlState.timelineSplit] as const,
    task: async ([orgSlug, projectSlug, branches, timelineSplit], { signal }) => {
      assert(orgSlug && projectSlug && branches && timelineSplit);
      const pageLinker = dashboardState.createLinkRenderer({
        base: this._urlState,
      });
      return {
        render: (testStats) => pageLinker({
          testId: testStats.test.testId,
          testTimeline: Timeline.deserialize(testStats.timeline),
        }),
      }
    }
  });

  private _headRefTask = tasks.headRef(this, {
    project: () => this._project,
    defaultBranch: () => this._branches?.defaultBranch,
    head: () => this.pullRequestNumber ? this._pullRequestTask.value?.lastTestedCommit : this._urlState.head,
  });

  private _reportTimeInterval = new Task(this, {
    args: () => [
      this._headRefTask.value, this._urlState.since?.getTime(), this._urlState.until?.getTime(), this._urlState.commitsCount
    ] as const,
    task: async([headRef, sinceTimestamp, untilTimestamp, maxCount]) => {
      assert(headRef);
      // If there's a maxCount (which is usually 1), then select a single day that belongs to the commit.
      if (maxCount) {
        const since = timeDay.floor(new Date(headRef.commit.timestamp));
        return {
          since,
          until: timeDay.offset(since, 1),
        }
      }
      // If either since or until is not set, then default to 7 last days from the commit.
      if (!sinceTimestamp || !untilTimestamp) {
        // Default to 7 days report.
        const day = timeDay.floor(new Date(headRef.commit.timestamp));
        return {
          since: timeDay.offset(day, -6),
          until: timeDay.offset(day, 1),
        }
      }
      // Otherwise, just the since and until that we're given.
      return {
        since: new Date(sinceTimestamp),
        until: new Date(untilTimestamp),
      }
    }
  });

  private _reportOptionsTask = new Task(this, {
    args: () => [
      this._project,
      this._fqlTask.value,
      this._urlState.timelineSplit,
      this._headRefTask.value,
      this._reportTimeInterval.value,
      this._urlState.commitsCount,
      this._pullRequestTask.value,
    ] as const,
    task: async ([project, fql, split, headRef, reportTimeInterval, maxCount, pullRequest], { signal }) => {
      assert(project && fql && split && headRef && reportTimeInterval);
      if (this.pullRequestNumber) {
        assert(pullRequest);
        return {
          orgSlug: project.org.orgSlug,
          projectSlug: project.projectSlug,
          fql: fql.serialize(),
          timelineSplit: split.serialize(),
          commitOptions: {
            head: headRef.commit.commitId,
            maxCount: 1,
          },
          customHistoryHead: pullRequest.baseSha,
          historyBuckets: 7,
          dailyReportBreakdown: false,
          timeZoneId: Temporal.Now.timeZoneId(),
          regressionWindowDays: project.regressionWindowDays,
          acceptableFlakinessRate: project.acceptableFlakinessRatio,
        } satisfies WireTypes.ReportOptions;
      }
      // For pull requests, the report is a special beast.
      return {
        orgSlug: project.org.orgSlug,
        projectSlug: project.projectSlug,
        fql: fql.serialize(),
        timelineSplit: split.serialize(),
        commitOptions: {
          head: headRef.commit.commitId,
          maxCount,
          sinceTimestamp: +reportTimeInterval.since as FlakinessReport.UnixTimestampMS|undefined,
          untilTimestamp: +reportTimeInterval.until as FlakinessReport.UnixTimestampMS|undefined,
        },
        historyBuckets: 0,
        dailyReportBreakdown: true,
        timeZoneId: Temporal.Now.timeZoneId(),
        regressionWindowDays: project.regressionWindowDays,
        acceptableFlakinessRate: project.acceptableFlakinessRatio,
      } satisfies WireTypes.ReportOptions;
    }
  });

  private _testStatsTask = new Task(this, {
    args: () => [
      this._reportOptionsTask.value, this._urlState.testId, this._urlState.testTimeline,
    ] as const,
    task: async ([reportOptions, testId, testTimeline], { signal }) => {
      assert(reportOptions && testId && testTimeline);
      const result = await api.report.getReportTestStats.GET({
        ...reportOptions,
        timelineSplit: TimelineSplit.fromTimeline(testTimeline).serialize(),
        testId,
        timeline: testTimeline.serialize(),
      });
      return result;
    }
  });


  private _pullRequestTask = new Task(this, {
    args: () => [
      this._project, this.pullRequestNumber,
    ] as const,
    task: async ([project, prNumber], { signal }) => {
      assert(project && prNumber);
      const number = parseInt(prNumber, 10);
      assert(!isNaN(number));
      const result = await api.project.pull.GET({
        number,
        orgSlug: project.org.orgSlug,
        projectSlug: project.projectSlug,
      });
      assert(result);
      return result;
    }
  });

  private _runLinker = new Task(this, {
    args: () => [this._project, this._urlState.testId, this._testStatsTask.value] as const,
    task: async ([project, testId, testStats], { signal }) => {
      assert(project && testId && testStats);
      return {
        render: (runStats: WireTypes.RunStats) => PageRun.url({
          orgSlug: project.org.orgSlug,
          projectSlug: project.projectSlug,
          reportId: runStats.run.runId,
          timelineSplit: TimelineSplit.fromTimeline(Timeline.deserialize(testStats.timeline)),
          testId,
          testTimeline: Timeline.deserialize(testStats.timeline),
        }),
      }
    }
  });

  private _tabCountsTask = new Task(this, {
    args: () => [
      this._reportOptionsTask.value
    ] as const,
    task: async ([reportOptions], { signal }) => {
      assert(reportOptions);
      const result = await api.report.counters.POST(reportOptions, { signal });
      return result;
    }
  });

  private _allEnvironments = tasks.allEnvironments(this, {
    commitOptions: () => this._reportOptionsTask.value?.commitOptions,
    project: () => this._project,
    testId: () => this._urlState.testId,
  });

  private _executionCalendarLinker = new Task(this, {
    args: () => [
      window.location.href,
    ] as const,
    task: async ([], { signal }) => {
      const pageLinker = dashboardState.createLinkRenderer({
        base: this._urlState,
      });

      const calendarLinker: ExecutionCalendarLinker = (options) => pageLinker({
        since: options.since,
        until: options.until,
        page: 0,
      });
      return calendarLinker;
    },
  });

  private _dataTableProviderTask = new Task(this, {
    args: () => [
      this._project, this._reportOptionsTask.value, this._urlState.plane, this._headRefTask.value,
    ] as const,
    task: async ([project, reportOptions, plane, head], { signal }) => {
      assert(project && reportOptions && head);

      plane ??= 'tests';
      let ts = Temporal.Instant.fromEpochMilliseconds(head.commit.timestamp).toZonedDateTimeISO(Temporal.Now.timeZoneId()).startOfDay();
      const historyDays = Array(30).fill(0).map((_, idx) => new Date(ts.subtract({ days: idx }).epochMilliseconds));
      if (plane === 'tests') {
        const historyColumn = this.pullRequestNumber === undefined ? undefined : createDayDurationChartsColumn<WireTypes.TestStats>({
          dayStats: testStats => testStats.dailyHistory,
          days: historyDays,
          name: `Base History`,
          onHistoryDaySelected: (testStats, selectedDay) => {
          },
        });
        const breakdownColumn = this.pullRequestNumber === undefined ? createDayDurationChartsColumn<WireTypes.TestStats>({
          dayStats: testStats => testStats.reportBreakdown,
          days: historyDays,
          name: 'Daily Breakdown',
          onHistoryDaySelected: (testStats, selectedDay) => {
          },
        }) : undefined;
        return createTestStatsDataProvider({
          historyColumn,
          breakdownColumn,
          statusColumnName: this.pullRequestNumber !== undefined ? html`PR S` : undefined,
          loader: createTestStatsLoaderRemote(reportOptions),
          sort: 'outcome',
          runLinker: (testStats) => PageRun.url({
            orgSlug: project.org.orgSlug,
            projectSlug: project.projectSlug,
            reportId: testStats.runId,
            testTimeline: Timeline.deserialize(testStats.timeline),
            testId: testStats.test.testId,
            attemptIdx: testStats.attemptIdx,
            timelineSplit: TimelineSplit.fromTimeline(Timeline.deserialize(testStats.timeline)),
          }),
        });
      }
      if (plane === 'timelines') {
        const durationsChartColumn = createDayDurationChartsColumn<WireTypes.TimelineStats>({
          dayStats: stats => stats.daily,
          name: 'Daily Breakdown',
          days: historyDays,
          onHistoryDaySelected: (timelineStats, selectedDay) => this._executionHistory?.show({
            since: timeDay.floor(selectedDay),
            until: timeDay.offset(timeDay.floor(selectedDay), 1),
            timeline: Timeline.deserialize(timelineStats.timeline),
            head,
          }),
        });
        return createTimelineStatsDataProvider({
          project,
          loader: createTimelineStatsLoaderRemote(reportOptions),
          durationsChartColumn,
        });
      }
      if (plane === 'errors')
        return createErrorStatsDataProvider({ loader: createErrorStatsLoaderRemote(reportOptions)});
      if (plane === 'tags')
        return createTagStatsDataProvider({ loader: createTagStatsLoaderRemote(reportOptions)});
      if (plane === 'annotations')
        return createAnnotationStatsDataProvider({ loader: createAnnotationStatsLoaderRemote(reportOptions)});
    }
  });

  override render() {
    return html`
      <execution-history></execution-history>
      <project-header
        .orgSlug=${this.orgSlug}
        .projectSlug=${this.projectSlug}
        .submenu=${this.pullRequestNumber ? "pulls" : "tests"}
      ></project-header>
      ${this._project && this._project.runsCount === 0 ? this._renderNoReportsScreen(this._project) :
        this._project ? this._renderContent() : nothing}
      <app-footer></app-footer>
    `;
  }

  private _renderPullRequestHeader() {
    const pr = this._pullRequestTask.value;
    if (!pr)
      return nothing;
    const testId = this._urlState.testId;
    return html`
      <h-box style="font-size: var(--sl-font-size-2x-large);">
        <fk-pr-icon .pr=${pr}></fk-pr-icon>
        <v-box>
          <div>
            <span>${pr.title}</span>
            <span style=${styleMap({
              color: 'var(--sl-color-neutral-400)'
            })}>#${pr.number}</span>
          </div>
          <div style=${styleMap({
            'font-size': 'var(--sl-font-size-small)',
            'color': 'var(--sl-color-neutral-600)',
          })}>
            #${pr.number} opened <sl-relative-time date=${new Date(pr.createdTimestamp).toISOString()}></sl-relative-time>
            by 
            <sl-avatar image=${pr.avatar_url} label=${pr.author} loading=lazy style=${styleMap({
              '--size': '16px',
            })}></sl-avatar>
            ${pr.author}
          </div>

        </v-box>
      </h-box>

      <timeline-selector ?multiple=${!this._urlState.testId}
        .envs=${this._allEnvironments.value}
        .split=${this._urlState.testTimeline ? TimelineSplit.fromTimeline(this._urlState.testTimeline) : this._urlState.timelineSplit}
        .commitOptions=${this._reportOptionsTask.value}
        @fk-select=${(event: CustomEvent<TimelineSplit>) => {
          if (testId) {
            this._urlState.testTimeline = event.detail.timelines(this._allEnvironments.value ?? [])[0];
          } else {
            this._urlState.timelineSplit = event.detail;
            this._urlState.page = 0;
          }
        }}
      ></timeline-selector>
    `
  }

  private _renderReportHeader() {
    const { orgSlug, projectSlug } = this;
    const reportOptions = this._reportOptionsTask.value;
    const { since, until } = this._reportTimeInterval.value ?? {};
    if (!orgSlug || !projectSlug || !reportOptions || !since || !until)
      return nothing;
    const link = dashboardState.createLinkRenderer({
      base: this._urlState,
    });

    const testId = this._urlState.testId;
    return html`
      <h-box style="align-items: start;">
        <v-box>
          <h-box>
            ${testId ? html`
              <v-box>
                <a href=${link({
                  testTimeline: undefined,
                  testId: undefined,
                })} style=${styleMap({
                  'display': 'inline-flex',
                  'align-items': 'center',
                  'gap': 'var(--sl-spacing-2x-small)',
                  'color': 'var(--sl-color-neutral-500)',
                  'max-width': 'fit-content',
                })}> 
                  <sl-icon name=arrow-left></sl-icon>
                  <span>back</span>
                </a>
                <teststats-title .project=${this._project} .testStats=${this._testStatsTask.value}></teststats-title>
              </v-box>
            ` : html`
              <div style="font-size: var(--sl-font-size-x-large);">
                Tests from ${renderDatesRange(since, until)}
              </div>
            `}
          </h-box>
          <h-box>
            <head-selector
              .head=${this._headRefTask.value}
              @fk-select=${(event: FKHeadSelectEvent) => {
                this._urlState.head = event.head.name;
                this._urlState.commitsCount = undefined;
              }}
            ></head-selector>
            <timeline-selector ?multiple=${!this._urlState.testId}
              .envs=${this._allEnvironments.value}
              .split=${this._urlState.testTimeline ? TimelineSplit.fromTimeline(this._urlState.testTimeline) : this._urlState.timelineSplit}
              .commitOptions=${this._reportOptionsTask.value}
              @fk-select=${(event: CustomEvent<TimelineSplit>) => {
                if (testId) {
                  this._urlState.testTimeline = event.detail.timelines(this._allEnvironments.value ?? [])[0];
                } else {
                  this._urlState.timelineSplit = event.detail;
                  this._urlState.page = 0;
                }
              }}
            ></timeline-selector>
          </h-box>
        </v-box>
        <x-filler></x-filler>
        <execution-calendar
          .linker=${this._executionCalendarLinker.value}
          .split=${this._urlState.testTimeline ? TimelineSplit.fromTimeline(this._urlState.testTimeline) : this._urlState.timelineSplit}
          .head=${this._headRefTask.value}
          .since=${since}
          .until=${until}
          .testId=${this._urlState.testId}
          @rangeselected=${(event: CustomEvent<{ since: Date, until: Date }>) => {
            this._urlState.since = event.detail.since;
            this._urlState.until = event.detail.until;
          }}
        ></execution-calendar>
      </h-box>
    `
  }

  private _renderContent() {
    const { orgSlug, projectSlug } = this;
    const reportOptions = this._reportOptionsTask.value;
    const { since, until } = this._reportTimeInterval.value ?? {};
    if (!orgSlug || !projectSlug || !reportOptions || !since || !until)
      return nothing;
    const link = dashboardState.createLinkRenderer({
      base: this._urlState,
    });

    const testId = this._urlState.testId;
    return html`
      <app-body wide>
        ${ProjectAlerts.maybeRender(this._project)}

        ${this.pullRequestNumber ? this._renderPullRequestHeader() : this._renderReportHeader()}

        ${testId ? html`
          <!-- Rendering single test overview -->
          <commit-history
            .project=${this._project}
            .head=${this._headRefTask.value}
            .testId=${this._urlState.testId}
            .since=${this._reportTimeInterval.value?.since}
            .until=${this._reportTimeInterval.value?.until}
            .split=${this._urlState.testTimeline ? TimelineSplit.fromTimeline(this._urlState.testTimeline) : undefined}
            .runlinker=${this._runLinker.value}
          ></commit-history>
        ` : html`
          <!-- Rendering generic tests report -->
          <h-box style="align-items: flex-start;">
            <fql-search q=${this._urlState.query} @fql-changed=${(e: FqlChangedEvent) => {
              this._urlState.query = e.query;
              this._urlState.page = 0;
            }}></fql-search>
            <status-selector></status-selector>
          </h-box>

          <report-outcomes .outcomes=${this._reportOutcomes.value}></report-outcomes>

          <fk-tabbar selected=${this._urlState.plane ?? 'tests'}>
            <fk-tab
              name=tests
              data-testid=plane-tests
              href=${link({ plane: 'tests', page: 0, sortDirection: undefined, sortName: undefined, })}
            >
              Tests (${this._tabCountsTask.value?.tests ?? 0})
            </fk-tab>
            <fk-tab
              name=timelines
              data-testid=plane-timelines
              href=${link({ plane: 'timelines', page: 0, sortDirection: undefined, sortName: undefined, })}
            >
              Environments (${this._tabCountsTask.value?.timelines ?? 0})
            </fk-tab>
            <fk-tab
              name=errors
              data-testid=plane-errors
              href=${link({ plane: 'errors', page: 0, sortDirection: undefined, sortName: undefined, })}
            >
              Errors (${this._tabCountsTask.value?.errors ?? 0})
            </fk-tab>
            <fk-tab
              name=tags
              data-testid=plane-tags
              href=${link({ plane: 'tags', page: 0, sortDirection: undefined, sortName: undefined, })}
            >
              Tags (${this._tabCountsTask.value?.tags ?? 0})
            </fk-tab>
            <fk-tab
              name=annotations
              data-testid=plane-annotations
              href=${link({ plane: 'annotations', page: 0, sortDirection: undefined, sortName: undefined, })}
            >
              Annotations (${this._tabCountsTask.value?.annotations ?? 0})
            </fk-tab>

            <fk-tabpane>
              <data-table
                .pageNumber=${this._urlState.page}
                .pageSize=${this._urlState.pageSize}
                .sortAxis=${this._urlState.sortName}
                .sortDirection=${this._urlState.sortDirection}
                .provider=${this._dataTableProviderTask.value}
                .linker=${this._dataTableLinker.value}
              ></data-table>
            </fk-tabpane>
          </fk-tabbar>
        `}
      </app-body>
    `
  }

  private _isPullRequest() {
    return !!this.pullRequestNumber;
  }

  private _renderNoReportsScreen(project: WireTypes.Project) {
    return html`
      <app-body>
        ${ProjectAlerts.maybeRender(project)}
        <div class=markdown-body>
          ${welcome_new_project(project)}
        </div>
      </app-body>
    `;
  }

  static styles = [...markdownStyles, pageStyles, linkStyles, css`
    .markdown-body {
      margin-top: var(--sl-spacing-medium);
    }

    .commit-history {
      fk-commitstats + fk-commitstats {
        border-top: 1px solid var(--fk-color-border);
        border-radius: 0px;
      }
    }
  `]
}
