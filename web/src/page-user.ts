import { Task } from '@lit/task';
import { LitElement, css, html, nothing } from 'lit';
import { customElement } from 'lit/decorators.js';
import { api } from './api.js';

import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { ContextType } from '@lit/context';
import { linkStyles, pageStyles } from './components/cssstyles.js';
import { consume, contexts } from './contexts.js';
import { PageNewOrganization } from './page-new-organization.js';
import { RouteConfig } from './router.js';

@customElement('page-user')
export class PageUser extends LitElement {
  static routes(): RouteConfig[] {
    return [{
      path: [ '/' ],
      render: (groups) => html`<page-user></page-user>`,
    }];
  }

  static url(): string {
    return new URL('/', window.location.href).href;
  }

  @consume({ context: contexts.user, subscribe: true })
  private _user?: ContextType<typeof contexts.user>;;

  @consume({ context: contexts.serverInfo, subscribe: true })
  private _serverInfo?: ContextType<typeof contexts.serverInfo>;;

  private _organizationsTask = new Task(this, {
    args: () => [this._user],
    task: async ([user], { signal }) => {
      const result = await api.user.dashboard.GET(undefined, { signal });
      const orgSlugToOrg = new Map<string, WireTypes.Organization>();
      for (const org of result.ownedOrgs)
        orgSlugToOrg.set(org.orgSlug, org);
      for (const org of result.sharedOrgs)
        orgSlugToOrg.set(org.orgSlug, org);
      return {
        orgs: [...orgSlugToOrg.values()],
        projects: result.externalProjects,
      }
    }
  });

  render() {
    const orgCreationDisabled = !this._user || (this._serverInfo?.onlySuperusersCanCreateOrganizations && !this._user.isSuperUser);
    return html`
      <app-header .customTitle=${"Dashboard"}></app-header>
      <app-body>
        ${this._serverInfo?.onlySuperusersCanCreateOrganizations ? html`
          <fk-callout variant="tip">
            <sl-icon slot="icon" name="info-circle"></sl-icon>
            <strong>Beta Testing Limitations</strong><br />
            <p>
              <div>Organization creation is disabled during beta testing period to limit demand.</div>
              <div>Please reach out to us at <fk-contact-us></fk-contact-us> to get an organization for yourself.</div>
            </p>
          </fl-callout>
        ` : nothing}

        <h-box>
          <div class=title>Organizations</div>
          <x-filler></x-filler>
          <sl-button
            ?disabled=${orgCreationDisabled}
            href=${PageNewOrganization.url()}
            type=submit
            class=new-org-button
            variant=success
            size="small">
              <sl-icon slot="prefix" name="folder-plus"></sl-icon>New organization
          </sl-button>
        </h-box>
        ${
          this._organizationsTask.render({
            complete: value => html`<list-organizations .orgs=${value.orgs}></list-organizations>`,
            pending: () => html`<sl-spinner></sl-spinner>`,
          })
        }
        ${
          this._organizationsTask.render({
            complete: value => value.projects && value.projects.length ? html`
              <h3>External projects</h3>
              <list-projects .projects=${this._organizationsTask.value?.projects}></list-projects>    
            ` : nothing
          })
        }
      </app-body>
      <app-footer></app-footer>
    `;
  }

  static styles = [linkStyles, pageStyles, css`
    .new-org-button {
      width: fit-content;
      grid-column: 2;
    }

    fk-contact-us {
      color: blue;
    }

    .title {
      font-size: var(--sl-font-size-2x-large);
    }

    a {
      color: var(--sl-color-neutral-900);
      text-decoration: none;
    }

    demo-chart {
      height: 200px;
    }
  `];
}
