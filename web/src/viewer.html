<!DOCTYPE HTML>
<script src="/index.js" type="module"></script>
<link rel="stylesheet" href="/style.css">
<title>Flakiness.io Report Viewer</title>
<script type="text/javascript">
  const favicon = document.createElement('link');
  favicon.rel = 'icon';
  favicon.href = window.location.hostname.includes('.test') ? '/logodev.svg' : '/logo.svg';
  favicon.sizes = 'any';
  favicon.type = 'image/svg+xml';
  document.head.append(favicon);

  document.addEventListener('DOMContentLoaded', () => {
    const element = document.createElement('the-app');
    element.setAttribute('report-viewer', true);
    document.body.append(element);
  }, false);
</script>