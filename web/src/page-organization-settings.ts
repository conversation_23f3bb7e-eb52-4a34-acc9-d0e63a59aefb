import { css, html, LitElement } from 'lit';
import { customElement, property, query, state } from 'lit/decorators.js';

import { ContextType } from '@lit/context';
import { live } from 'lit/directives/live.js';
import slugify from 'slugify';
import { api } from './api.js';
import { docLinkStyles, pageStyles } from './components/cssstyles.js';
import { OrgAlerts } from './components/org-alerts.js';
import { OrgNamePicked, PickOrgName } from './components/pick-org-name.js';
import { ProjectContext } from './components/project-context.js';
import { consume, contexts, TaskEvent } from './contexts.js';
import { githubMarkdownCSS } from './githubMarkdownCSS.js';
import { PageUser } from './page-user.js';
import { RouteConfig } from './router.js';

@customElement('page-organization-settings')
export class PageOrganizationSettings extends LitElement {

  static url(options: { orgSlug: string }): string {
    return new URL(['', options.orgSlug, 'settings'].join('/'), window.location.href).href;
  }

  static routes(): RouteConfig[] {
    return [{
      path: [
        '/:org/settings',
      ],
      render: (groups) => ProjectContext.render({
        orgSlug: groups?.org,
      }, html`
        <page-organization-settings
          .orgSlug=${groups?.org}
        ></page-organization-settings>
      `),
    }];
  }

  @consume({ context: contexts.router, subscribe: true }) private _router?: ContextType<typeof contexts.router>;
  @consume({ context: contexts.user, subscribe: true }) private _user?: ContextType<typeof contexts.user>;
  @consume({ context: contexts.org, subscribe: true }) private _org?: ContextType<typeof contexts.org>;
  @consume({ context: contexts.serverInfo, subscribe: true }) private _serverInfo?: ContextType<typeof contexts.serverInfo>;

  @property({ attribute: false }) orgSlug?: string;
  
  @query('pick-org-name') private _orgName?: PickOrgName;

  @state() private _orgRename?: OrgNamePicked;

  private async _renameOrg() {
    const newOrgName = this._orgRename?.orgName;
    const orgSlug = this.orgSlug;
    if (!newOrgName || !orgSlug)
      return;
    const newOrgSlug = slugify.default(newOrgName);
    //TODO: set org name
    await api.organization.changeOrganizationSettings.POST({
      orgSlug,
      newOrgSlug,
      newOrgName,
    });
    this._orgRename = undefined;
    this._orgName?.setStatus(true, 'Organization renamed');
    await this._router?.navigate(PageOrganizationSettings.url({
      orgSlug: newOrgSlug,
    }));
  }

  private async _deleteOrg() {
    const { orgSlug } = this;
    if (!orgSlug || !this._router)
      return;
    const value = prompt(`Type in "${orgSlug}" to confirm deletion`)
    if (value?.toLowerCase() !== (orgSlug).trim().toLowerCase())
      return;
    await api.organization.delete.POST({ orgSlug });
    this._router.navigate(PageUser.url());
  }

  render() {
    if (this._org && !this._org.access)
      return html`<page-http-error code=403></page-http-error>`;

    return html`
      <org-header .orgSlug=${this.orgSlug} submenu=settings></org-header>
      <app-body user-role=${this._org?.access}>
        ${OrgAlerts.maybeRender(this._org)}
        <h1>Organization Settings</h1>
       
        <form-section label="Organization name">
          <pick-org-name
            ?disabled=${!this._org?.access || this._org.access === 'member'}
            .orgSlug=${this.orgSlug}
            .initial=${live(this._org?.orgName)}
            @org-name=${(event: TaskEvent<OrgNamePicked>) => this._orgRename = event.data }
          >
            <sl-button ?disabled=${!this._orgRename} @click=${() => this._renameOrg()}>Rename</sl-button>
          </pick-org-name>
        </form-section>
        <sl-divider></sl-divider>

        <form-section is-editor>
          <sl-button ?disabled=${this._org?.access !== 'owner'} @click=${this._deleteOrg.bind(this)} variant=danger>Delete Organization</sl-button>
        </form-section>

      </app-body>
      <app-footer></app-footer>
    `
  }

  static styles = [githubMarkdownCSS, docLinkStyles, pageStyles, css`

    [user-role=member] *:is([is-admin], [is-owner]) {
      display: none;
    }

    [user-role=admin] *:is([is-owner]) {
      display: none;
    }
  `];
}
