import { FlakinessReport } from "@flakiness/report";
import { WireTypes } from "@flakiness/server/common/wireTypes.js";

export namespace githubLinks {
  export function newProjectSecret(project: WireTypes.Project) {
    return `https://github.com/${project.sourceOwner}/${project.sourceRepo}/settings/secrets/actions/new`;
  }

  export function projectUrl(project: WireTypes.Project) {
    return `https://github.com/${project.sourceOwner}/${project.sourceRepo}`;
  }

  export function fileUrl(project: WireTypes.Project, commitId: FlakinessReport.CommitId, gitFilePath: FlakinessReport.GitFilePath, lineNumber?: FlakinessReport.Number1Based) {
    return `https://github.com/${project.sourceOwner}/${project.sourceRepo}/blob/${commitId}/${gitFilePath}?plain=1${lineNumber ? '#L' + lineNumber : ''}`;
  }

  export function testUrl(project: WireTypes.Project, testStats: WireTypes.TestStats) {
    return fileUrl(project, testStats.commitId, testStats.test.filePath, testStats.lineNumber);
  }

  export function commitUrl(project: WireTypes.Project, commitId: FlakinessReport.CommitId) {
    return `https://github.com/${project.sourceOwner}/${project.sourceRepo}/commit/${commitId}`;
  }

}
