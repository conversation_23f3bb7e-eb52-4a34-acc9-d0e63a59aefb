
export namespace githubUtils {
  export function parseGithubURL(rawUrl: string): { error: string } | { repo: string, owner: string } {
    if (rawUrl.startsWith('**************:')) {
      rawUrl = rawUrl.substring(rawUrl.indexOf(':') + 1);
      if (rawUrl.endsWith('.git'))
        rawUrl = rawUrl.slice(0, -4);
      const [owner, repo] = rawUrl.split('/');
      if (!owner || !repo)
        return { error: 'Failed to parse github URL' };
      return { owner, repo };
    }
    let url: URL;
    try {
      url = new URL(rawUrl);
    } catch (e) {
      return { error: 'failed to parse URL' };
    }
    if (url.host !== 'github.com')
      return { error: 'Only github.com projects are supported at this time' };
    let [, owner, repo] = url.pathname.split('/');
    if (!owner || !repo)
      return { error: 'Failed to parse github URL' };
    if (repo.endsWith('.git'))
      repo = repo.substring(0, repo.length - '.git'.length);
    return { owner, repo };
  }
}
