import { css, html, LitElement, nothing } from 'lit';
import { customElement, query, state } from 'lit/decorators.js';
import { api } from './api.js';

import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { ContextType } from '@lit/context';
import { Task } from '@lit/task';
import { SlSwitch } from '@shoelace-style/shoelace';
import slugify from 'slugify';
import { docLinkStyles, pageStyles } from './components/cssstyles.js';
import { OrgNamePicked } from './components/pick-org-name.js';
import { consume, contexts, FKOrgChanged, TaskEvent } from './contexts.js';
import { githubMarkdownCSS } from './githubMarkdownCSS.js';
import { PageOrganizationBilling } from './page-organization-billing.js';
import { PageOrganization } from './page-organization.js';
import { RouteConfig, URLState } from './router.js';

const pageState = new URLState({
  planId: URLState.option<string|undefined>({
    name: 'plan_id',
    default: undefined,
    encode: filter => filter,
    decode: filter => filter,
  }),
});

@customElement('page-new-organization')
export class PageNewOrganization extends LitElement {

  static url(): string {
    return new URL('/new-organization', window.location.href).href;
  }

  static routes(): RouteConfig[] {
    return [{
      path: [ '/new-organization', ],
      render: () => html`<page-new-organization></page-new-organization>`,
    }];
  }

  private _plansTask = new Task(this, {
    args: () => [] as const,
    task: async ([], { signal }) => {
      return await api.productPlans.listPlans.GET(undefined, { signal });
    }
  });

  @consume({ context: contexts.user, subscribe: true }) private _user?: ContextType<typeof contexts.user>;
  @consume({ context: contexts.router, subscribe: true }) private _router?: ContextType<typeof contexts.router>;
  @consume({ context: contexts.serverInfo, subscribe: true }) private _serverInfo?: ContextType<typeof contexts.serverInfo>;

  @state() private _projectNameResult?: TaskEvent<OrgNamePicked>;
  @state() private _isCreatingOrganzation: boolean = false;

  @query('#annual') private _htmlAnnual?: SlSwitch;

  private _urlState = pageState.bind(this);

  render() {
    if (!this._user)
      return;
    const allPlans = (this._plansTask.value ?? []);
    const selectedPlan = allPlans.find(plan => plan.id === this._urlState.planId);
    const showAnnual = (selectedPlan && selectedPlan.billing === 'year') || !!this._htmlAnnual?.checked;
    const plans = showAnnual ? allPlans.filter(plan => plan.billing === 'year') : allPlans.filter(plan => plan.billing === 'month');
    const planIsNotSelected = (this._serverInfo?.enabledBilling && !this._urlState.planId);
    return html`
      <app-header .customTitle=${"New Organization"}></app-header>
      <app-body>
        <fk-callout variant=tip>
          <i>Organizations</i> are groups of users with shared permissions that contain your projects. Think of them as your company, team, or personal workspace where you manage access control and billing.
        </fk-callout>

        <form @submit=${this._submitForm}>
          <v-box>
          <h1>Create a new organization</h1>

          <pick-org-name
            @org-name=${(event: TaskEvent<OrgNamePicked>) => this._projectNameResult = event }
          ></pick-org-name>

          
          ${this._serverInfo?.enabledBilling ? html`
            <h1>Choose Your Plan</h1>
            <h-box>
              <x-filler></x-filler>
              <sl-switch id=annual ?checked=${selectedPlan ? selectedPlan.billing === 'year' : this._htmlAnnual?.checked} @sl-input=${() => {
                this._urlState.planId = undefined;
                this.requestUpdate();
              }}>Annual Billing <sl-tag pill size=small variant=success>1 month free</sl-tag></sl-switch>
            </h-box>
            <product-plans-view
              no-enterprise
              plan-selector
              .selectedPlanId=${this._urlState.planId}
              .plans=${plans}
              @fk-select=${async (event: CustomEvent<WireTypes.ProductPlan>) => {
                this._urlState.planId = event.detail.id;
              }}
            ></product-plans-view>
          ` :
          nothing}
          <sl-button ?disabled=${!this._formData() || this._isCreatingOrganzation || planIsNotSelected } variant=success type=submit>
            ${!this._formData() ? html`
              Choose unique organization name
            ` : planIsNotSelected ? html`
              Select your plan
            ` : this._isCreatingOrganzation && this._serverInfo?.enabledBilling ? html`
              Starting free trial...
            ` : html`
              Create organization
            `}
          </sl-button>
          </v-box>
        </form>
      </app-body>
      <app-footer></app-footer>
    `
  }

  private _formData() {
    const orgName = this._projectNameResult?.data?.orgName;
    return orgName ? { orgName } : undefined;
  }

  private async _submitForm(event: SubmitEvent) {
    this._isCreatingOrganzation = true;
    using raii = {
      [Symbol.dispose]: () => this._isCreatingOrganzation = false,
    }
    event.preventDefault();

    const formData = this._formData();

    if (!formData)
      return;

    const orgSlug = slugify.default(formData.orgName, { lower: true });
    await api.user.createOrganization.GET({
      orgName: formData.orgName,
      orgSlug,
    });

    if (this._urlState.planId) {
      const url = await api.billing.setPlan.POST({ orgSlug, planId: this._urlState.planId });
      if (url) {
        window.open(url, '_blank');
      } else {
        this._router?.navigate(PageOrganization.url({ orgSlug }));
        this.dispatchEvent(new FKOrgChanged({ orgSlug }));
      }
    } else {
      if (this._serverInfo?.enabledBilling)
        this._router?.navigate(PageOrganizationBilling.url({ orgSlug }));
      else
        this._router?.navigate(PageOrganization.url({ orgSlug }));
    }
  }

  static styles = [githubMarkdownCSS, docLinkStyles, pageStyles, css`
    footer {
      display: grid;
      grid-template-columns: 1fr auto;

      & sl-button {
        grid-column: 2;
      }
    }

    .project-details {
      color: var(--sl-color-neutral-400);
    }

    sl-button {
      margin-top: var(--sl-spacing-2x-large);
    }

    sl-tab-panel::part(base) {
      padding: 0;
    }
  `];
}
