import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';

import { ContextType } from '@lit/context';
import { Task } from '@lit/task';
import { docLinkStyles, pageStyles } from './components/cssstyles.js';
import { ProjectAlerts } from './components/project-alerts.js';
import { ProjectContext } from './components/project-context.js';
import { consume, contexts } from './contexts.js';
import { createReportsDataProvider, createUnparsedReportsDataProvider } from './data-table-providers/runsProvider.js';
import { githubMarkdownCSS } from './githubMarkdownCSS.js';
import { RouteConfig } from './router.js';
import { assert } from './utils.js';

@customElement('page-project-runs')
export class PageProjectRuns extends LitElement {

  static url(options: { orgSlug: string, projectSlug: string, }): string {
    return new URL(['', options.orgSlug, options.projectSlug, 'runs'].join('/'), window.location.href).href;
  }

  static routes(): RouteConfig[] {
    return [{
      path: [
        '/:org/:project/runs',
      ],
      render: (groups) => ProjectContext.render({
        orgSlug: groups?.org,
        projectSlug: groups?.project,
      }, html`
        <page-project-runs
          .orgSlug=${groups?.org}
          .projectSlug=${groups?.project}
        ></page-project-runs>
      `),
    }];
  }

  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;
  @consume({ context: contexts.projectReferences, subscribe: true }) private _branches?: ContextType<typeof contexts.projectReferences>;
  @consume({ context: contexts.user }) private _user?: ContextType<typeof contexts.user>;
  @consume({ context: contexts.router, subscribe: true }) private _router?: ContextType<typeof contexts.router>;

  @property({ attribute: false }) projectSlug?: string;
  @property({ attribute: false }) orgSlug?: string;

  private _dataTableProviderTask = new Task(this, {
    args: () => [
      this._project
    ] as const,
    task: async ([project], { signal }) => {
      assert(project);
      return createReportsDataProvider({
        orgSlug: project.org.orgSlug,
        projectSlug: project.projectSlug,
      });
    }
  });

  private _unparsedReportsDataProviderTask = new Task(this, {
    args: () => [
      this._project
    ] as const,
    task: async ([project], { signal }) => {
      assert(project);
      return createUnparsedReportsDataProvider({
        orgSlug: project.org.orgSlug,
        projectSlug: project.projectSlug,
      });
    }
  });

  render() {
    if (!this._project)
      return nothing;

    return html`
      <project-header
        .orgSlug=${this.orgSlug}
        .projectSlug=${this.projectSlug}
        .submenu=${"runs"}
      ></project-header>
      <app-body wide user-role=${this._project.access}>
        ${ProjectAlerts.maybeRender(this._project)}
        <div class=header>Processed Runs</div>
        <data-table
          .pageNumber=${0}
          .pageSize=${20}
          .provider=${this._dataTableProviderTask.value}
        ></data-table>  
        <div class=header>Unparsed Reports</div>
        <data-table
          .pageNumber=${0}
          .pageSize=${20}
          .provider=${this._unparsedReportsDataProviderTask.value}
        ></data-table>  

      </app-body>
      <app-footer></app-footer>
    `
  }

  static styles = [githubMarkdownCSS, docLinkStyles, pageStyles, css`
    .header {
      font-size: var(--sl-font-size-x-large);
    }
  `];
}
