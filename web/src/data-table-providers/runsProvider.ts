import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { html, TemplateResult } from 'lit';
import { styleMap } from 'lit/directives/style-map.js';
import { api } from '../api.js';
import { DataColumn, DataTableProvider } from '../components/data-table.js';
import { PageRun } from '../page-run.js';

function createRunIdColumn(options: {
  orgSlug: string,
  projectSlug: string,
}): DataColumn<WireTypes.RunProcessingStatus, ''> {
  return {
    sortAxis: undefined,
    width: 'auto',
    renderHeader() { return html`Run #`; },
    renderElement(stats): TemplateResult {
      const href = PageRun.url({
        orgSlug: options.orgSlug,
        projectSlug: options.projectSlug,
        reportId: stats.runId,
      });
      return html`<a style=${styleMap({
        'padding': 'var(--sl-spacing-x-small) 0',
      })} href=${href}>${stats.runId}</a>`;
    },
  };
}

function createRunStartDate(options: {
  orgSlug: string,
  projectSlug: string,
}): DataColumn<WireTypes.RunProcessingStatus, ''> {
  return {
    sortAxis: undefined,
    width: 'auto',
    renderHeader() { return html`Start Time`; },
    renderElement(stats): TemplateResult {
      const href = PageRun.url({
        orgSlug: options.orgSlug,
        projectSlug: options.projectSlug,
        reportId: stats.runId,
      });
      return html`
        <a href=${href}>
          <sl-format-date
            month=long
            day=numeric
            year=numeric
            date=${new Date(stats.startTimestampMs).toISOString()}>
          </sl-format-date>
        </a>
      `;
    },
  };
}

const COLUMN_DURATION: DataColumn<WireTypes.RunProcessingStatus, ''> = {
  sortAxis: undefined,
  width: '1fr',
  renderHeader() { return html`Factual Duration`; },
  renderElement({ duration }): TemplateResult {
    return html`<time-interval .ms=${duration ?? 0}></time-interval>`;
  },
};

const COLUMN_ERROR: DataColumn<WireTypes.UnparsedReport, ''> = {
  sortAxis: undefined,
  width: '1fr',
  renderHeader() { return html`Processing Error`; },
  renderElement({ error }): TemplateResult {
    return html`<span style=${styleMap({
      'white-space': 'nowrap',
      'overflow': 'hidden',
      'text-overflow': 'ellipsis',
    })}>${error}</span>`;
  },
};

const COLUMN_RUN_ID: DataColumn<WireTypes.UnparsedReport, ''> = {
  sortAxis: undefined,
  width: 'auto',
  renderHeader() { return html`Run Id`; },
  renderElement({ runId }): TemplateResult {
    return html`<span style=${styleMap({
        'padding': 'var(--sl-spacing-x-small) 0',
      })}>#${runId}</span>`;
  },
};

export function createReportsDataProvider(options: {
  orgSlug: string,
  projectSlug: string,
}): DataTableProvider<WireTypes.RunProcessingStatus, ''> {
  return {
    columns: [
      createRunIdColumn({ ...options }),
      createRunStartDate({ ...options }),
      COLUMN_DURATION,
    ],
    noDataMessage: 'No Runs',
    defaultSortColumn: undefined,
    loader: (pageOptions, sortOptions, signal) => {
      return api.history.listProcessedReports.GET({
        orgSlug: options.orgSlug,
        projectSlug: options.projectSlug,
        pageOptions: pageOptions,
      }, { signal });
    }
  }
}


export function createUnparsedReportsDataProvider(options: {
  orgSlug: string,
  projectSlug: string,
}): DataTableProvider<WireTypes.UnparsedReport, ''> {
  return {
    columns: [
      COLUMN_RUN_ID,
      COLUMN_ERROR,
    ],
    noDataMessage: 'No Runs',
    defaultSortColumn: undefined,
    loader: (pageOptions, sortOptions, signal) => {
      return api.history.listUnparsedReports.GET({
        orgSlug: options.orgSlug,
        projectSlug: options.projectSlug,
        pageOptions: pageOptions,
      }, { signal });
    }
  }
}
