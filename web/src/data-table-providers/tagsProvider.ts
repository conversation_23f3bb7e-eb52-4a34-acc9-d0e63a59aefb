import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { consume, ContextType } from '@lit/context';
import { css, html, LitElement, nothing, TemplateResult } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { api } from '../api.js';
import { linkStyles } from '../components/cssstyles.js';
import { DataColumn, DataLoader, DataTableProvider } from '../components/data-table.js';

import { Matcher } from '@flakiness/server/common/fql/matcher.js';
import { TestsReport } from '@flakiness/server/common/stats/testsReport.js';
import { contexts } from '../contexts.js';

@customElement('data-table-tag-cell')
class DataTableTagCell extends LitElement {
  @property({ attribute: false }) tag?: string;
  @consume({ context: contexts.fql, subscribe: true }) private _fql?: ContextType<typeof contexts.fql>;
  @consume({ context: contexts.linkFQL, subscribe: true }) private _link?: ContextType<typeof contexts.linkFQL>;

  render() {
    const text = this.tag;
    if (!text)
      return nothing;
    const hl = this._fql?.highlightTag(text);
    const url = text && this._fql && this._link && this._link.render(this._fql.toggleFilter(Matcher.TAG_CONTAINS.create([text])).serialize());
    return html`
      <section>
        <a href=${url ?? nothing}><span class=hashsign ?is-selected=${!!hl?.length}>#</span><hl-span .hl=${hl} .text=${text}></hl-span></a>
      </section>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      overflow: hidden;
    }
    section {
      padding: var(--sl-spacing-small) 0;
    }
    .hashsign[is-selected] {
      background-color: var(--fk-color-highlight);
    }
    a {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  `]
}

export const tagColumns = {
  name: {
    sortAxis: 'name',
    width: '1fr',
    sortOrder: ['asc', 'desc'],
    renderHeader() { return html`Tags`; },
    renderElement({ tag }): TemplateResult {
      return html`<data-table-tag-cell .tag=${tag}></data-table-tag-cell>`;
    },
  },
  taggedTimelines: {
    sortAxis: 'timelines',
    renderHeader() { return html`Tagged Timelines`; },
    renderElement(stats): TemplateResult {
      return html`<int-num .x=${stats.impactedTimelines}></int-num .x=${stats.impactedTimelines}>`;
    },
  },
  taggedTests: {
    sortAxis: 'tests',
    renderHeader() { return html`Tagged Tests`; },
    renderElement(stats): TemplateResult {
      return html`<int-num .x=${stats.impactedTests}></int-num .x=${stats.impactedTests}>`;
    },
  },
} satisfies Record<string, DataColumn<WireTypes.TagStats, WireTypes.TagStatsSortAxis>>;

const DEFAULT_SORT_OPTIONS = {
  axis: 'name' as WireTypes.TagStatsSortAxis,
  direction: 'desc' as WireTypes.SortDirection,
}

export function createTagStatsLoaderRemote(options: WireTypes.ReportOptions): DataLoader<WireTypes.TagStats, WireTypes.TagStatsSortAxis> {
  return (pageOptions, sortOptions, signal) => {
    return api.report.tagStats.POST({
      ...options,
      pageOptions: pageOptions,
      sortOptions: sortOptions ?? DEFAULT_SORT_OPTIONS,
    }, { signal });
  }
}

export function createTagStatsLoaderLocal(statsReport: TestsReport): DataLoader<WireTypes.TagStats, WireTypes.TagStatsSortAxis> {
  return async (pageOptions, sortOptions, signal) => {
    sortOptions ??= DEFAULT_SORT_OPTIONS;
    return statsReport.tags.page(pageOptions, sortOptions.axis, sortOptions.direction);
  }
}

export function createTagStatsDataProvider(options: {
  columns?: DataColumn<WireTypes.TagStats, WireTypes.TagStatsSortAxis>[],
  loader: DataLoader<WireTypes.TagStats, WireTypes.TagStatsSortAxis>,  
}): DataTableProvider<WireTypes.TagStats, WireTypes.TagStatsSortAxis> {
  return {
    loader: options.loader,
    columns: options.columns ?? [tagColumns.name, tagColumns.taggedTests, tagColumns.taggedTimelines],
    defaultSortColumn: tagColumns.taggedTests,
    noDataMessage: 'No Tags',
  };
}
