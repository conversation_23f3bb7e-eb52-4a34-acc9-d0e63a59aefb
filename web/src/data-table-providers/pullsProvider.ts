import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { html, nothing, TemplateResult } from 'lit';
import { styleMap } from 'lit/directives/style-map.js';
import { Temporal } from 'temporal-polyfill';
import { api } from '../api.js';
import { DataColumn, DataTableProvider } from '../components/data-table.js';
import { PageReport } from '../page-report.js';

function isGoodToGo(pr: WireTypes.PullRequest) {
  return pr.commitStats && pr.commitStats.testStats.regressed === 0 && pr.commitStats.testStats.unexpected === 0;
}

function hasRegressions(pr: WireTypes.PullRequest) {
  return pr.commitStats && pr.commitStats.testStats.regressed > 0;
}

function createNameColumn(project: WireTypes.Project): DataColumn<WireTypes.PullRequest, WireTypes.PullRequestSortAxis> {
  return {
    sortAxis: undefined,
    width: '1fr',
    renderHeader() { return html`Pull Request`; },
    renderElement(pr): TemplateResult {
      const tag = isGoodToGo(pr) ? html`<sl-tag size=small pill variant=success>Mergeable</sl-tag>` :
        hasRegressions(pr) ? html`<sl-tag pill size=small  variant=danger>Has Regressions</sl-tag>` :
        nothing;

      return html`
        <h-box style=${styleMap({
          'padding': 'var(--sl-spacing-small) 0',
          'width': '100%',
        })}>
          <fk-pr-icon style=${styleMap({
            'font-size': 'var(--sl-font-size-large)',
          })} .pr=${pr}></fk-pr-icon>

          <v-box style="gap: var(--sl-spacing-2x-small)">
            <h-box style=${styleMap({
              'font-weight': 'var(--sl-font-weight-semibold)',
            })}>
              <a href=${PageReport.url({ orgSlug: project.org.orgSlug, projectSlug: project.projectSlug, pullRequest: pr.number })}>
              ${pr.title}
              </a>
              ${tag}
            </h-box>
            <div style=${styleMap({
              'font-size': 'var(--sl-font-size-small)',
              'color': 'var(--sl-color-neutral-600)',
            })}>
              #${pr.number} opened <sl-relative-time date=${new Date(pr.createdTimestamp).toISOString()}></sl-relative-time>
              by 
              <sl-avatar image=${pr.avatar_url} label=${pr.author} loading=lazy style=${styleMap({
                '--size': '16px',
              })}></sl-avatar>
              ${pr.author}
            </div>
          </v-box>
          
        </h-box>
      `;
    },
  };
}

export const columns = {
  outcomes: {
    sortAxis: undefined,
    width: 'auto',
    renderHeader() { return html`Test Results`; },
    renderElement(pr): TemplateResult {
      return html`
        <outcomes-bar style="width: 100%;" .counts=${pr.commitStats?.testStats}></outcomes-bar>
      `;
    },
  },
} satisfies Record<string, DataColumn<WireTypes.PullRequest, WireTypes.PullRequestSortAxis>>;

const DEFAULT_SORT_OPTIONS = {
  axis: 'created' as WireTypes.PullRequestSortAxis,
  direction: 'desc' as WireTypes.SortDirection,
}

export function createPullsDataProvider(options: {
  project: WireTypes.Project,
  prState: WireTypes.PullRequestState,
  testState: 'all' | 'tested' | 'untested',
}): DataTableProvider<WireTypes.PullRequest, WireTypes.PullRequestSortAxis> {
  const name = createNameColumn(options.project);
  return {
    loader: (pageOptions, sortOptions, signal) => api.project.pulls.GET({
      orgSlug: options.project.org.orgSlug,
      projectSlug: options.project.projectSlug,
      prState: options.prState,
      testState: options.testState,
      sortOptions: sortOptions ?? DEFAULT_SORT_OPTIONS,
      pageOptions,
      timeZoneId: Temporal.Now.timeZoneId(),
      regressionWindowDays: options.project.regressionWindowDays,
    }),
    rowOutcome: pr => {
      if (isGoodToGo(pr))
        return 'expected';
      if (hasRegressions(pr))
        return 'regressed';
      return undefined;
    },
    columns: [name, columns.outcomes],
    defaultSortColumn: name,
    noDataMessage: 'No Pull Requests',
  };
}
