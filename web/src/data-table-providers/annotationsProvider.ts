import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { css, html, LitElement, nothing, TemplateResult } from 'lit';
import { api } from '../api.js';
import { DataColumn, DataLoader, DataTableProvider } from '../components/data-table.js';

import { Matcher } from '@flakiness/server/common/fql/matcher.js';
import { TestsReport } from '@flakiness/server/common/stats/testsReport.js';
import { consume, ContextType } from '@lit/context';
import { customElement, property } from 'lit/decorators.js';
import { linkStyles } from '../components/cssstyles.js';
import { contexts } from '../contexts.js';

@customElement('data-table-annotation-cell')
class DataTableAnnotationCell extends LitElement {
  @property({ attribute: false }) annotation?: WireTypes.Annotation;
  @property({ attribute: false }) isType?: boolean;
  @consume({ context: contexts.fql, subscribe: true }) private _fql?: ContextType<typeof contexts.fql>;
  @consume({ context: contexts.linkFQL, subscribe: true }) private _link?: ContextType<typeof contexts.linkFQL>;

  render() {
    const annotation = this.annotation;
    if (!annotation)
      return nothing;
    const text = this.isType ? annotation.type : annotation.description ?? '';
    const hl = this._fql?.highlightAnnotation(annotation)?.get(text);
    const url = annotation && this._fql && this._link && this._link.render(this._fql.toggleFilter(Matcher.ANNOTATION_CONTAINS.create([text])).serialize());
    return html`
      <section>
        <a href=${url ?? nothing}>
          ${this.isType ? html`<span class=hashsign ?is-selected=${!!hl?.length}>@</span>` : nothing}<hl-span .hl=${hl} .text=${text}></hl-span>
        </a>
      </section>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      overflow: hidden;
    }
    section {
      padding: var(--sl-spacing-small) 0;
    }
    .hashsign[is-selected] {
      background-color: var(--fk-color-highlight);
    }
    a {
      display: block;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  `]
}

const annotationColumns = {
  name: {
    sortAxis: 'name',
    width: 'auto',
    sortOrder: ['asc', 'desc'],
    renderHeader() { return html`Type`; },
    renderElement({ annotation }): TemplateResult {
      return html`<data-table-annotation-cell .annotation=${annotation} .isType=${true}></data-table-annotation-cell>`;
    },
  },
  description: {
    sortAxis: undefined,
    width: '1fr',
    sortOrder: ['asc', 'desc'],
    renderHeader() { return html`Description`; },
    renderElement({ annotation }): TemplateResult {
      return html`<data-table-annotation-cell .annotation=${annotation}></data-table-annotation-cell>`;
    },
  },
  annotatedTimelines: {
    sortAxis: 'timelines',
    renderHeader() { return html`Annotated Timelines`; },
    renderElement(stats): TemplateResult {
      return html`<int-num .x=${stats.annotatedTimelines}></int-num>`;
    },
  },
  annotatedTests: {
    sortAxis: 'tests',
    renderHeader() { return html`Annotated Tests`; },
    renderElement(stats): TemplateResult {
      return html`<int-num .x=${stats.annotatedTests}></int-num>`;
    },
  },
} satisfies Record<string, DataColumn<WireTypes.AnnotationStats, WireTypes.AnnotationStatsSortAxis>>;

const DEFAULT_SORT_OPTIONS = {
  axis: 'name' as WireTypes.AnnotationStatsSortAxis,
  direction: 'desc' as WireTypes.SortDirection,
}

export function createAnnotationStatsLoaderRemote(options: WireTypes.ReportOptions): DataLoader<WireTypes.AnnotationStats, WireTypes.AnnotationStatsSortAxis> {
  return (pageOptions, sortOptions, signal) => {
    return api.report.annotationStats.POST({
      ...options,
      pageOptions: pageOptions,
      sortOptions: sortOptions ?? DEFAULT_SORT_OPTIONS,
    }, { signal });
  }
}

export function createAnnotationStatsLoaderLocal(statsReport: TestsReport): DataLoader<WireTypes.AnnotationStats, WireTypes.AnnotationStatsSortAxis> {
  return async (pageOptions, sortOptions, signal) => {
    sortOptions ??= DEFAULT_SORT_OPTIONS;
    return statsReport.annotations.page(pageOptions, sortOptions.axis, sortOptions.direction);
  }
}

export function createAnnotationStatsDataProvider(options: {
  loader: DataLoader<WireTypes.AnnotationStats, WireTypes.AnnotationStatsSortAxis>,  
}): DataTableProvider<WireTypes.AnnotationStats, WireTypes.AnnotationStatsSortAxis> {
  return {
    loader: options.loader,
    columns: [annotationColumns.name, annotationColumns.description, annotationColumns.annotatedTests, annotationColumns.annotatedTimelines],
    defaultSortColumn: annotationColumns.name,
    noDataMessage: 'No Annotations',
  };
}
