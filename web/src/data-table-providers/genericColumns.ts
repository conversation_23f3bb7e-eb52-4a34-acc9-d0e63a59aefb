import { WireTypes } from "@flakiness/server/common/wireTypes.js";
import * as d3 from 'd3';
import { html, nothing, TemplateResult } from "lit";
import ms from "ms";
import { DataColumn } from "../components/data-table.js";
import { DurationsChart2 } from "../components/durations-chart2.js";
import { FKPopupDismissed, FKPopupRequested } from "../components/fk-popup.js";
import { consumeDOMEvent } from "../utils.js";

export function createGenericOutcomeColumn<ELEMENT>(options: {
  outcome: WireTypes.Outcome,
  testStats: (el: ELEMENT) => WireTypes.OutcomesCount,
  href?: (el: ELEMENT) => string|undefined,
}): DataColumn<ELEMENT, WireTypes.Outcome> {
  return {
    sortAxis: options.outcome,
    renderHeader() {
      if (options.outcome === 'expected')
        return html`Passed`;
      if (options.outcome === 'unexpected')
        return html`Failed`;
      if (options.outcome === 'flaked')
        return html`Flaked`;
      if (options.outcome === 'regressed')
        return html`New Failures`;
      if (options.outcome === 'skipped')
        return html`Skipped`;
      throw new Error(`Unknown outcome "${options.outcome}"`);
    },
    renderElement(element: ELEMENT): TemplateResult {
      const testStats = options.testStats(element);
      return html`
        <a href=${options.href?.call(null, element) ?? nothing}>
          <int-num .x=${testStats[options.outcome]}></int-num>
        </a>
      `;
    },
  }
}

export function createHistorySnippetColumn<ELEMENT>(options: {
  days: Date[],
  dailyOutcomes: (e: ELEMENT) => (WireTypes.DayOutcome)[],
  onHistoryDaySelected?: (stats: ELEMENT, day: Date, outcome: WireTypes.DayOutcome) => void,
}) {
  return {
    sortAxis: 'history' as const,
    width: 'auto',
    renderHeader() { return html`Daily History`; },
    renderElement(element: ELEMENT): TemplateResult {
      return html`
        <history-snippet dimmed
          .outcomes=${options.dailyOutcomes(element).toReversed()}
          .days=${options.days.toReversed()}
          @fk-select=${((e: CustomEvent<{ day: Date, outcome: WireTypes.DayOutcome }>) => {
            options.onHistoryDaySelected?.call(null, element, e.detail.day, e.detail.outcome );
            consumeDOMEvent(e);
          })}
        ></history-snippet>
      `;
    }
   } satisfies DataColumn<ELEMENT, any>;
}

export function createDurationTrendColumn<ELEMENT extends { durationMs: number, durationChangeMs: number }>() {
  return {
    sortAxis: 'duration_trend' as const,
    renderHeader() { return html`Trend`; },
    renderElement({ durationMs, durationChangeMs }: ELEMENT): TemplateResult {
      return html`
        <fk-trend .ms=${durationMs} .baseline=${durationMs - durationChangeMs}></fk-trend>
      `;
    },
  } satisfies DataColumn<ELEMENT, any>;
}

export function createDayDurationChartsColumn<ELEMENT>(options: {
  days: Date[],
  name: string,
  dayStats: (e: ELEMENT) => WireTypes.DayStats[],
  onHistoryDaySelected?: (element: ELEMENT, day: Date) => void,
}) {
  return {
    sortAxis: 'history' as const,
    width: 'auto',
    renderHeader() { return html`${options.name}`; },
    renderElement(element: ELEMENT): TemplateResult {
      return html`
        <fk-popup placement=top-start distance=10>${DurationsChart2.render({
          data: options.dayStats(element).map((dayStat, id) => ({
            date: options.days.at(id)!,
            outcome: dayStat.outcome as WireTypes.Outcome,
            duration: dayStat.duration,
          })).toReversed(),
          click: (event) => {
            options.onHistoryDaySelected?.call(null, element, event.detail.datum.date );
            consumeDOMEvent(event);
          },
          hover: (event) => {
            const today = d3.timeDay.floor(new Date());
            const target = d3.timeDay.floor(event.detail.datum.date);
            const diffDays = Math.round((+today - +target) / ms('1 day'));

            let label: string;
            if (diffDays === 0) {
              label = 'Today';
            } else if (diffDays === 1) {
              label = '1 day ago';
            } else {
              label = `${diffDays} days ago`;
            }

            const format = d3.timeFormat('%a, %b %e, %Y'); // "Mon, Jul 12, 2025"
            const datum = event.detail.datum;
            const formatted = format(datum.date);

            const popover = html`
              <v-box style="gap: var(--sl-spacing-2x-small); padding: var(--sl-spacing-small);">
                <h-box style="gap: var(--sl-spacing-2x-small); font-size: var(--sl-font-size-small);">
                  <span style="font-weight: bold;">${formatted}</span>
                  <span>•</span>
                  <span>${label}</span>
                </h-box>
                <h-box style="gap: var(--sl-spacing-2x-small);">
                  Duration: ${datum.duration !== undefined ? html`<time-interval .ms=${datum.duration}></time-interval>` : 'none'}
                </h-box>
                <h-box style="gap: var(--sl-spacing-2x-small);">
                  Outcome: ${datum.outcome}
                </h-box>

              </v-box>
            `;
            event.currentTarget?.dispatchEvent(new FKPopupRequested(popover, event.detail.bar));
          },
          leave: (event) => {
            event.currentTarget?.dispatchEvent(new FKPopupDismissed());
          },
          style: {
            'border-bottom': '1px solid var(--fk-color-border)',
            'max-width': '120px',
            height: '30px',
          },
        })}</fk-popup>
      `;
    }
   } satisfies DataColumn<ELEMENT, any>;
}
