import { TestsReport } from '@flakiness/server/common/stats/testsReport.js';
import { Timeline } from '@flakiness/server/common/timeline/timeline.js';
import { wireOutcomesToOutcome, WireTypes } from '@flakiness/server/common/wireTypes.js';
import { consume, ContextType } from '@lit/context';
import { css, html, LitElement, nothing, TemplateResult } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { api } from '../api.js';
import { linkStyles } from '../components/cssstyles.js';
import { DataColumn, DataLoader, DataTableProvider } from '../components/data-table.js';
import { contexts } from '../contexts.js';
import { createDurationTrendColumn, createGenericOutcomeColumn } from './genericColumns.js';

@customElement('timeline-stats-view')
class TimelineStatsView extends LitElement {
  @consume({ context: contexts.linkTimeline, subscribe: true }) private _linkTimeline?: ContextType<typeof contexts.linkTimeline>;

  @property({ attribute: false }) timeline?: WireTypes.JSONTimeline;

  render() {
    if (this.timeline === undefined)
      return nothing;
    const link = this._linkTimeline?.render(Timeline.deserialize(this.timeline));
    return html`
      <timeline-name .timeline=${this.timeline ?? ''} .href=${link ?? nothing}></timeline-name>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      overflow: hidden;
    }
  `];
}

function createOutcomeColumn(outcome: WireTypes.Outcome): DataColumn<WireTypes.TimelineStats, WireTypes.TimelineStatsSortAxis> {
  return createGenericOutcomeColumn<WireTypes.TimelineStats>({
    outcome,
    testStats: timelineStats => timelineStats.testStats,
  });
}

export const timelineStatsColumns = {
  name: {
    sortAxis: 'name',
    sortOrder: ['asc', 'desc'],
    width: '1fr',
    renderHeader() { return html`Name`; },
    renderElement(timelineStats): TemplateResult {
      return html`<timeline-stats-view style="margin: var(--sl-spacing-small) 0;" .timeline=${timelineStats.timeline}></timeline-stats-view>`;
    },
  },
  duration: {
    sortAxis: 'total_time',
    sortOrder: ['asc', 'desc'],
    renderHeader() { return html`Duration`; },
    renderElement({ durationMs }): TemplateResult {
      return html`<time-interval .ms=${durationMs}></time-interval>`;
    },
  },
} satisfies Record<string, DataColumn<WireTypes.TimelineStats, WireTypes.TimelineStatsSortAxis>>;

const DEFAULT_SORT_OPTIONS = {
  axis: 'failures' as WireTypes.TimelineStatsSortAxis,
  direction: 'desc' as WireTypes.SortDirection,
}

export function createTimelineStatsLoaderRemote(options: WireTypes.ReportOptions): DataLoader<WireTypes.TimelineStats, WireTypes.TimelineStatsSortAxis> {
  return (pageOptions, sortOptions, signal) => {
    return api.report.timelineStats.POST({
      ...options,
      pageOptions: pageOptions,
      sortOptions: sortOptions ?? DEFAULT_SORT_OPTIONS,
    }, { signal });
  }
}

export function createTimelineStatsLoaderLocal(statsReport: TestsReport): DataLoader<WireTypes.TimelineStats, WireTypes.TimelineStatsSortAxis> {
  return async (pageOptions, sortOptions, signal) => {
    sortOptions ??= DEFAULT_SORT_OPTIONS;
    return statsReport.timelines.page(pageOptions, sortOptions.axis, sortOptions.direction);
  }
}

export function createTimelineStatsDataProvider(options: {
  project?: WireTypes.Project,
  loader: DataLoader<WireTypes.TimelineStats, WireTypes.TimelineStatsSortAxis>,
  durationsChartColumn?: DataColumn<WireTypes.TimelineStats, WireTypes.TimelineStatsSortAxis>,
}): DataTableProvider<WireTypes.TimelineStats, WireTypes.TimelineStatsSortAxis> {
  const statusColumn: DataColumn<WireTypes.TimelineStats, WireTypes.TimelineStatsSortAxis> = {
    sortAxis: 'outcome',
    width: 'auto',
    renderHeader() { return html`S`; },
    renderElement(stats: WireTypes.TimelineStats): TemplateResult {
      return html`<fk-outcome .outcome=${wireOutcomesToOutcome(stats.testStats, options.project?.acceptableFlakinessRatio ?? 0)}></fk-outcome>`;
    },
  }

  return {
    columns: [
      statusColumn,
      timelineStatsColumns.name,
      options.durationsChartColumn,
      createOutcomeColumn('regressed'),
      createOutcomeColumn('unexpected'),
      createOutcomeColumn('flaked'),
      createOutcomeColumn('expected'),
      createOutcomeColumn('skipped'),
      timelineStatsColumns.duration,
      options.durationsChartColumn ? createDurationTrendColumn<WireTypes.TimelineStats>() : undefined,
    ].filter(c => !!c),
    defaultSortColumn: statusColumn,
    noDataMessage: 'No Timelines',
    loader: options.loader,
  };
}
