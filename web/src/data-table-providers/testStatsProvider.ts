import { FlakinessReport } from '@flakiness/report';
import { Ranges } from '@flakiness/server/common/ranges.js';
import { Stats } from '@flakiness/server/common/stats/stats.js';
import { TestsReport } from '@flakiness/server/common/stats/testsReport.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { Multimap } from '@flakiness/shared/common/multimap.js';
import { ContextType } from '@lit/context';
import { css, html, LitElement, nothing, TemplateResult } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { api } from '../api.js';
import { linkStyles } from '../components/cssstyles.js';
import { DataColumn, DataLoader, DataTableProvider } from '../components/data-table.js';
import { consume, contexts } from '../contexts.js';
import { githubLinks } from '../githubLinks.js';
import { createDurationTrendColumn } from './genericColumns.js';

// Tests Provider ---------------------------

@customElement('data-table-test-stats-cell')
class DataTableTestCell extends LitElement {
  @property({ attribute: false }) testStats?: WireTypes.TestStats;
  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;
  @consume({ context: contexts.linkTest, subscribe: true }) private _linkTest?: ContextType<typeof contexts.linkTest>;
  @consume({ context: contexts.fql, subscribe: true }) private _fql?: ContextType<typeof contexts.fql>;

  render() {
    if (!this.testStats)
      return nothing;
    const test = this.testStats.test;
    const elements: TemplateResult[] = [];
    const hl = this._fql?.highlightTest(test);
    for (let i = 0; i < test.titles.length; ++i) {
      const title = test.titles[i];
      const isSuite = i < test.titles.length - 1;
      if (!title.trim().length) {
        elements.push(html`<hl-span class=empty-suite ?x-suite=${isSuite} .text=${'∅'}></hl-span>`);
        elements.push(html`<sl-icon name=chevron-right></sl-icon>`)
        continue;
      }
      const highlight = hl?.get(title);

      const isJvmFqn = isSuite && this.testStats.category === FlakinessReport.CATEGORY_JUNIT && title.includes('.');
      if (isJvmFqn) {
        let shortTitle = title.split('.').pop()!;
        const offset = title.length - shortTitle.length;
        elements.push(html`
          <sl-tooltip>
            <hl-span slot=content .text=${title} .hl=${highlight}></hl-span>
            <sl-icon class=prefix name=box-seam ?hl=${highlight?.some(x => x < offset)}></sl-icon>
            <hl-span ?x-suite=${isSuite} .text=${shortTitle} .hl=${highlight ? Ranges.offset(highlight, -offset) : undefined}></hl-span>
          </sl-tooltip>
        `);
        elements.push(html`<sl-icon name=chevron-right></sl-icon>`)
      } else {
        elements.push(html`<hl-span ?x-suite=${isSuite} .text=${title} .hl=${highlight}></hl-span>`);
        elements.push(html`<sl-icon name=chevron-right></sl-icon>`)
      }
    }
    elements.pop();
    
    const annotationTexts = new Multimap<string, string|undefined>();
    for (const annotation of this.testStats?.annotations ?? [])
      annotationTexts.set(annotation.type, annotation.description);
    return html`
      <v-box style="gap: var(--sl-spacing-2x-small);">
        <h-box style="gap: var(--sl-spacing-2x-small);">
          <a class=test-name href=${this._linkTest?.render(this.testStats) ?? nothing}>
            ${elements}
          </a>
          ${(this.testStats.tags ?? []).map(tag => html`<test-tag .tag=${tag} .fql=${this._fql}></test-tag>`)}
          ${[...annotationTexts].map(([type, descriptions]) => html`<test-annotation .annotation=${{
            type,
            description: [...descriptions].filter(d => !!d).join('\n'),
            annotationId: 'dummy' as Stats.AnnotationId,
          } satisfies WireTypes.Annotation}></test-annotation>`)}
        </h-box>
        <h-box>
          ${this.testStats.lineNumber > 0 && test.filePath ? html`
            <div class=filepath>
              <a-ext class=configpath href=${this._project ? githubLinks.fileUrl(this._project, this.testStats.commitId, test.filePath, this.testStats.lineNumber) : nothing}>
                <hl-span .text=${test.filePath} .hl=${hl?.get(test.filePath)}></hl-span><span ?is-selected=${this._fql?.hasLineFilters() && this._fql?.acceptsLine(this.testStats.lineNumber)} class=test-line-number>:${this.testStats.lineNumber}</span>
              </a-ext>
            </div>
          ` : nothing}
          <h-box class=artifact-icons>
            ${this.testStats.hasImage ? html`
              <a href=${this._linkTest ? this._linkTest.render(this.testStats) + '#section-images' : nothing}>
                <artifact-icon .type=${"image"}></artifact-icon>
              </a>
            ` : nothing}
            ${this.testStats.hasVideo ? html`
              <a href=${this._linkTest ? this._linkTest.render(this.testStats) + '#section-videos' : nothing}>
                <artifact-icon .type=${"video"}></artifact-icon>
              </a>
            ` : nothing}
            ${this.testStats.hasTrace ? html`
              <a href=${this._linkTest ? this._linkTest.render(this.testStats) + '#section-traces' : nothing}>
                <artifact-icon .type=${"pw-trace"}></artifact-icon>
              </a>
            ` : nothing}
          </h-box>
        </h-box>
      </v-box>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      overflow: hidden;
    }

    sl-icon {
      flex: none;
    }

    hl-span {
      flex: auto;
      min-width: 2em;
      overflow: hidden;
      text-overflow: ellipsis;
      flex-shrink: 1;

      &[x-suite] {
        flex-shrink: 999999;
      }

      &.empty-suite {
        flex-shrink: 0;
        min-width: auto;
      }
    }

    .prefix {
      flex: none;
      color: var(--sl-color-neutral-500);
      &[hl] {
        background-color: var(--fk-color-highlight);
      }
    }

    sl-tooltip {
      --max-width: 100vw;
    }
    v-box {
      padding: var(--sl-spacing-small) 0;
    }
    .test-name {
      overflow: hidden;
      display: flex;
      align-items: center;
      gap: var(--sl-spacing-2x-small);
      text-overflow: ellipsis;
      font-weight: var(--sl-font-weight-semibold);
    }
    .filepath {
      color: var(--sl-color-neutral-500);
      font-size: var(--sl-font-size-small);
    }
    .test-line-number[is-selected] {
      background-color: var(--fk-color-highlight);
    }
    .artifact-icons {
      font-size: var(--sl-font-size-small);
      color: var(--sl-color-neutral-500);
    }
    sl-icon { font-size: var(--sl-font-size-2x-small); }
  `];
}

export const testStatsColumns = {
  name: {
    sortAxis: 'name',
    width: 'minmax(0, 1fr)',
    sortOrder: ['asc', 'desc'],
    renderHeader() { return html`Test name`; },
    renderElement(stats: WireTypes.TestStats): TemplateResult {
      return html`<data-table-test-stats-cell .testStats=${stats}></data-table-test-stats-cell>`;
    },
  },
  timeline: {
    sortAxis: 'timeline_name',
    sortOrder: ['asc', 'desc'],
    width: 'minmax(0, auto)',
    renderHeader() { return html`Environment`; },
    renderElement(stats: WireTypes.TestStats): TemplateResult {
      return html`<timeline-stats-view style="padding: var(--sl-spacing-small) 0;" .timeline=${stats.timeline}></timeline-stats-view>`;
    },
  },
  duration: {
    sortAxis: 'duration',
    renderHeader() { return html`Duration`; },
    renderElement({ durationMs, reportBreakdown: daily }: WireTypes.TestStats): TemplateResult {
      return html`
        <time-interval .ms=${durationMs}></time-interval>
      `;
    },
  },
} satisfies Record<string, DataColumn<WireTypes.TestStats, WireTypes.TestStatsSortAxis>>;

const DEFAULT_SORT_OPTIONS = {
  axis: 'name' as WireTypes.TestStatsSortAxis,
  direction: 'desc' as WireTypes.SortDirection,
}

export function createTestStatsLoaderLocal(statsReport: TestsReport): DataLoader<WireTypes.TestStats, WireTypes.TestStatsSortAxis> {
  return async (pageOptions, sortOptions, signal) => {
    sortOptions ??= DEFAULT_SORT_OPTIONS;
    return statsReport.tests.page(pageOptions, sortOptions.axis, sortOptions.direction);    
  }
}

export function createTestStatsLoaderRemote(options: WireTypes.ReportOptions): DataLoader<WireTypes.TestStats, WireTypes.TestStatsSortAxis> {
  return async (pageOptions, sortOptions, signal) => {
    const result = await api.report.testStats.POST({
      ...options,
      pageOptions: pageOptions,
      sortOptions: sortOptions ?? DEFAULT_SORT_OPTIONS,
    }, { signal });
    return result;
  }
}

export function createTestStatsDataProvider(options: {
  loader: DataLoader<WireTypes.TestStats, WireTypes.TestStatsSortAxis>,
  breakdownColumn?: DataColumn<WireTypes.TestStats, WireTypes.TestStatsSortAxis>,
  historyColumn?: DataColumn<WireTypes.TestStats, WireTypes.TestStatsSortAxis>,
  sort: WireTypes.TestStatsSortAxis,
  statusColumnName?: TemplateResult<1>,
  runLinker: (testStats: WireTypes.TestStats) => string,
}): DataTableProvider<WireTypes.TestStats, WireTypes.TestStatsSortAxis> {

  const statusColumn: DataColumn<WireTypes.TestStats, WireTypes.TestStatsSortAxis> = {
    sortAxis: 'outcome',
    width: 'auto',
    renderHeader() { return options.statusColumnName ?? html`S`; },
    renderElement(testStats: WireTypes.TestStats): TemplateResult {
      return html`
        <h-box style="justify-content: center; flex: auto;">
          <a href=${options.runLinker(testStats)}><fk-outcome .outcome=${testStats.outcome}></fk-outcome></a>
        </h-box>
      `;
    },
  }
  const columns = [
    options.historyColumn,
    statusColumn,
    testStatsColumns.name,
    options.breakdownColumn,
    testStatsColumns.timeline,
    testStatsColumns.duration,
    options.breakdownColumn || options.historyColumn ? createDurationTrendColumn<WireTypes.TestStats>() : undefined,
  ].filter(c => !!c);
  return {
    columns,
    loader: options.loader,
    defaultSortColumn: columns.find(column => column.sortAxis === options.sort),
    noDataMessage: 'No Tests',
  };
}
