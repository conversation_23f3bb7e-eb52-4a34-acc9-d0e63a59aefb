import { LitElement, css, html } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { linkStyles, pageStyles } from './components/cssstyles.js';

@customElement('page-http-error')
export class PageHTTPError extends LitElement {

  @property({}) code?: string;
  @property({}) text?: string;

  override render() {
    let title = '404 Not Found';
    let subtitle = 'This is not the web page you are looking for.';
    if (this.code === '402') {
      title = 'Payment Required';
      subtitle = 'Please make sure the organization billing is up-to-date.';
    } else if (this.code === '403') {
      title = '403 Forbidden';
      subtitle = '';
    }
    return html`
      <app-header></app-header>
      <app-body>
        <div class=title>${this.title}</div>
        <div class=header>${this.text ?? subtitle}</div>
      </app-body>
      <app-footer></app-footer>
    `
  }

  static styles = [linkStyles, pageStyles, css`
    app-body {
      color: var(--sl-color-neutral-400);
      padding-top: 100px;
      
      .title {
        font-size: var(--sl-font-size-3x-large);
      }

      .header {
        font-size: var(--sl-font-size-x-large);
      }
    }
  `];
}
