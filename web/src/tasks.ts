import { Stats } from "@flakiness/server/common/stats/stats.js";
import { WireTypes } from "@flakiness/server/common/wireTypes.js";
import { Task, TaskConfig, TaskStatus } from "@lit/task";
import { ReactiveControllerHost, ReactiveElement } from "lit";
import { api } from "./api.js";
import { assert } from "./utils.js";


export class TimedTask<T extends ReadonlyArray<unknown> = ReadonlyArray<unknown>, R = unknown> extends Task<T, R> {
  private _start: number = 0;
  private _timeout?: NodeJS.Timeout;
  constructor(host: ReactiveElement, rerenderTimeout: number, task: TaskConfig<T, R>) {
    super(host, {
      ...task,
      task: async (...args) => {
        if (this._timeout)
          clearTimeout(this._timeout);

        this._timeout = setTimeout(() => host.requestUpdate(), rerenderTimeout);
        this._start = Date.now();

        const result = await task.task(...args);
        clearTimeout(this._timeout);
        this._timeout = undefined;
        return result;
      }
    });
  }

  duration() {
    return this.status === TaskStatus.PENDING ? Date.now() - this._start : 0;
  }
}


export type HEADCommitOptions = WireTypes.ListCommitOptions & {
  since?: Date,
  until?: Date,
}

export namespace tasks {
  export function headRef(host: ReactiveControllerHost, options: {
    head: () => string|undefined,
    project: () => WireTypes.Project|undefined,
    defaultBranch: () => WireTypes.Ref|undefined,
  }) {
    return new Task(host, {
      args: () => [
        options.project(), options.defaultBranch(), options.head(),
      ] as const,
      task: async ([project, defaultBranch, head], { signal }) => {
        if (!head || head === defaultBranch?.name)
          return defaultBranch;
        if (head) {
          assert(project);
          const response = await api.project.resolveRef.GET({
            orgSlug: project.org.orgSlug,
            projectSlug: project.projectSlug,
            head,
          }, { signal });
          assert(response);
          return response;
        }
        assert(defaultBranch);
        return defaultBranch;
      }
    });
  }

  export function billing(host: ReactiveControllerHost, options: {
    orgSlug: () => string|undefined,
  }) {
    return new Task(host, {
      args: () => [options.orgSlug()] as const,
      task: async ([orgSlug], { signal }) => {
        assert(orgSlug);
        return await api.billing.status.GET({ orgSlug }, { signal });
      }
    });
  }

  export function orgMembers(host: ReactiveControllerHost, options: {
    orgSlug: () => string|undefined,
  }) {
    return new Task(host, {
      args: () => [options.orgSlug()] as const,
      task: async ([orgSlug], { signal }) => {
        assert(orgSlug);
        const result = await api.organization.listMembers.GET({ orgSlug }, { signal });
        return result.members;
      }
    });
  }

  export function orgProjects(host: ReactiveControllerHost, options: {
    orgSlug: () => string|undefined,
  }) {
    return new Task(host, {
      args: () => [options.orgSlug()] as const,
      task: async ([orgSlug], { signal }) => {
        assert(orgSlug);
        return await api.organization.listProjects.GET({ orgSlug }, { signal }) ?? [];
      }
    });
  }

  export function allEnvironments(host: ReactiveControllerHost, options: {
    project: () => WireTypes.Project|undefined,
    commitOptions: () => WireTypes.ListCommitOptions|undefined,
    testId?: () => Stats.TestId|undefined,
  }) {
    return new Task(host, {
        args: () => [
          options.project(), options.commitOptions(), options.testId?.call(null)
        ] as const,
        argsEqual: ([
          project1, commitOptions1, testId1,
        ], [
          project2, commitOptions2, testId2,
        ]) => {
          return testId1 === testId2 && project1 === project2 && isEqualCommitOptions(commitOptions1, commitOptions2);
        },
        task: async ([project, commitOptions, testId], { signal }) => {
          assert(project && commitOptions);
          return await api.report.allEnvironments.GET({
            orgSlug: project.org.orgSlug,
            projectSlug: project.projectSlug,
            commitOptions,
            testId,
          }, { signal });
        }
      }
    );
  }
}

function isEqualCommitOptions(c1: WireTypes.ListCommitOptions|undefined, c2: WireTypes.ListCommitOptions|undefined): boolean {
  // Both undefined are equal
  if (!c1 && !c2)
    return true;
  // Either undefined is not equal
  if (!c1 || !c2)
    return false;
  return c1.head === c2.head &&
         c1.headOffset === c2.headOffset &&
         c1.maxCount === c2.maxCount &&
         c1.sinceTimestamp === c2.sinceTimestamp &&
         c1.untilTimestamp === c2.untilTimestamp;
}
