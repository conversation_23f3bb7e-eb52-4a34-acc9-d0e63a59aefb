import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { consume, ContextType } from '@lit/context';
import { Task } from '@lit/task';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, state } from 'lit/decorators.js';
import { api } from './api.js';
import { linkStyles, pageStyles } from './components/cssstyles.js';
import { DataColumn, DataTableProvider } from './components/data-table.js';
import { contexts } from './contexts.js';
import { RouteConfig } from './router.js';
import { humanReadableMs } from './utils.js';

const jobColumnTypes: Record<string, DataColumn<WireTypes.Job, string>> = {
  status: {
    sortAxis: undefined,

    width: 'auto',

    renderHeader() {
      return html`<span></span>`;
    },

    renderElement(job) {
      if (!job.executionResult)
        return html`<span></span>`;
      if (job.executionResult === 'passed')
        return html`<sl-tooltip content="The job was successful"><sl-icon name=check-circle-fill style="color: green"></sl-icon></sl-tooltip>`;
      if (job.executionResult === 'failed')
        return html`<sl-tooltip content="Job failed with error: ${job.executionError}"><sl-icon name=x-circle-fill style="color: red"></sl-icon></sl-tooltip>`;
      if (job.executionResult === 'stalled')
        return html`<sl-tooltip content="The job has stalled"><sl-icon name=exclamation-triangle-fill style="color: orange"></sl-icon></sl-tooltip>`;
      return html`<sl-tooltip content="unknown job result - ${job.executionResult}"><sl-icon name=question-circle-fill></sl-icon></sl-tooltip>`;
    },
  },

  id: {
    sortAxis: undefined,

    width: 'auto',

    renderHeader() {
      return html`<span>id</span>`;
    },

    renderElement(job) {
      return html`<span>${job.id}</span>`;
    },
  },

  workerName: {
    sortAxis: undefined,

    width: 'auto',

    renderHeader() {
      return html`<span>worker name</span>`;
    },

    renderElement(job) {
      return html`<span>${job.workerName ?? '<unset>'}</span>`;
    },
  },

  queueName: {
    sortAxis: undefined,

    width: 'auto',

    renderHeader() {
      return html`<span>queue name</span>`;
    },

    renderElement(job) {
      return html`<span>${job.queueName}</span>`;
    },
  },

  duration: {
    sortAxis: undefined,

    width: 'auto',

    renderHeader() {
      return html`<span>duration</span>`;
    },

    renderElement(job) {
      return html`${job.executionDurationSeconds ? humanReadableMs(job.executionDurationSeconds * 1000) :
        job.executionTimestampSeconds ? humanReadableMs(Date.now() - job.executionTimestampSeconds * 1000) :
        nothing
      }`;
    },
  },

  submittedAt: {
    sortAxis: undefined,

    width: 'auto',

    renderHeader() {
      return html`<span>job submitted</span>`;
    },

    renderElement(job) {
      return html`<sl-relative-time month="long" day="numeric" year="numeric" hour="numeric" minute="numeric" date=${new Date(job.submittedTimestampSeconds * 1000).toISOString()}></sl-relative-time>`;
    },
  },

  queuedFor: {
    sortAxis: undefined,

    width: 'auto',

    renderHeader() {
      return html`<span>queued for</span>`;
    },

    renderElement(job) {
      if (!job.executionTimestampSeconds || !job.submittedTimestampSeconds)
        return html``;
      return html`${humanReadableMs((job.executionTimestampSeconds - job.submittedTimestampSeconds) * 1000)}`;
    },
  },

  retry: {
    sortAxis: undefined,

    width: 'auto',

    renderHeader() {
      return html`<span>retry</span>`;
    },

    renderElement(job) {
      if (job.retry === 0)
        return html``;
      return html`Retry #${job.retry}`;
    },
  },

  concurrencyId: {
    sortAxis: undefined,

    width: 'auto',

    renderHeader() {
      return html`<span>concurrency ID</span>`;
    },

    renderElement(job) {
      return job.concurrencyId ? html`
        <sl-tooltip content=${job.concurrencyId}>
          <div style="max-width: 12ch; overflow: hidden; font-family: var(--sl-font-mono); font-size: var(--sl-font-size-small); text-overflow: ellipsis;">${job.concurrencyId}</div>
        </sl-tooltip>
      ` : html``;
      
    },
  },

  jobId: {
    sortAxis: undefined,

    width: '10em',

    renderHeader() {
      return html`<span>job ID</span>`;
    },

    renderElement(job) {
      return html`
        <sl-tooltip content=${job.jobId}>
          <div style="overflow: hidden; font-family: var(--sl-font-mono); font-size: var(--sl-font-size-small); text-overflow: ellipsis;">${job.jobId}</div>
        </sl-tooltip>
      `;
    },
  },

  executeAfter: {
    sortAxis: undefined,

    width: 'auto',

    renderHeader() {
      return html`<span>Delay</span>`;
    },

    renderElement(job) {
      if (!job.executeAfterTimestampSeconds)
        return html``;
      const date = new Date(job.executeAfterTimestampSeconds * 1000).toISOString();
      return html`
        <h-box style="gap: var(--sl-spacing-2x-small);">
          <div>
            Execute <sl-tooltip>
            <sl-format-date slot=content month="long" day="numeric" year="numeric" hour="numeric" minute="numeric" hour-format="12" date=${date}></sl-format-date>
            <sl-relative-time date=${date}></sl-relative-time>
            </sl-tooltip>
          </div>
          ${job.queueName === 'cronjobs' ? html`
            <sl-icon-button name="skip-forward-btn" @click=${async () => {
              await api.administration.runCronJobImmediately.GET({
                jobId: job.jobId,
              });
            }}></sl-icon-button>
          ` : nothing}
        </h-box>
      `;
    },
  },

  data: {
    sortAxis: undefined,

    width: '1fr',

    renderHeader() {
      return html`<span>data</span>`;
    },

    renderElement(job) {
      return html`
        <sl-tooltip hoist>
          <pre style="overflow: hidden; text-overflow: ellipsis;" slot="content">${JSON.stringify(job.data, null, 2)}</pre>

          <sl-icon-button name=info-circle></sl-icon-button>
        </sl-tooltip>
      `;
    },
  },
}

const jobColumns = [...Object.values(jobColumnTypes)];

@customElement('page-administration-queues')
export class PageAdministrationQueues extends LitElement {
  static routes(): RouteConfig[] {
    return [{
      path: [
        '/administration/queues',
      ],
      render: () => PageAdministrationQueues.page(),
    }];
  }

  static url(): string {
    return new URL('/administration/queues', window.location.href).href;
  }

  static page() {
    return html`<page-administration-queues></page-administration-queues>`;
  }

  @consume({ context: contexts.serverInfo, subscribe: true }) @state() private _serverInfo?: ContextType<typeof contexts.serverInfo>;

  private _jobs = new Task(this, {
    args: () => [],
    task: async ([], { signal }) => {
      return {
        queuedProvider: {
          columns: jobColumns,
          loader: async (pageOptions, sortOptions, signal) => {
            const result = await api.administration.jobs.GET({
              pageSize: pageOptions.size,
              activePage: 0,
              archivedPage: 0,
              queuedPage: pageOptions.number,
            });
            return result.queued;
          },
          noDataMessage: 'No Queued Jobs',
        } satisfies DataTableProvider<WireTypes.Job, string>,
        activeProvider: {
          columns: jobColumns,
          loader: async (pageOptions, sortOptions, signal) => {
            const result = await api.administration.jobs.GET({
              pageSize: pageOptions.size,
              activePage: pageOptions.number,
              archivedPage: 0,
              queuedPage: 0,
            });
            return result.active;
          },
          noDataMessage: 'No Active Jobs',
        } satisfies DataTableProvider<WireTypes.Job, string>,
        archivedProvider: {
          columns: jobColumns,
          loader: async (pageOptions, sortOptions, signal) => {
            const result = await api.administration.jobs.GET({
              pageSize: pageOptions.size,
              activePage: 0,
              archivedPage: pageOptions.number,
              queuedPage: 0,
            });
            return result.archived;
          },
          noDataMessage: 'No Archived Jobs',
        } satisfies DataTableProvider<WireTypes.Job, string>,
      }
    },
  });

  override render() {
    return html`
      <administration-header submenu=queues></administration-header>
      <app-body wide>
        <h1>Queued jobs</h1>
        <data-table
          .provider=${this._jobs.value?.queuedProvider}
          .pageNumber=${0}
          .pageSize=${20}
        ></data-table>
        <h1>Active jobs</h1>
        <data-table
          .provider=${this._jobs.value?.activeProvider}
          .pageNumber=${0}
          .pageSize=${20}
        ></data-table>
        <h1>Archived jobs</h1>
        <data-table
          .provider=${this._jobs.value?.archivedProvider}
          .pageNumber=${0}
          .pageSize=${20}
        ></data-table>
      </app-body>
      <app-footer></app-footer>
    `
  }

  static styles = [pageStyles, linkStyles, css`
    :host {
      display: flex;
      flex-direction: column;
      gap: var(--sl-spacing-medium);
    }
  `];
}
