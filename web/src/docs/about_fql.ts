import { html } from "lit";
import { markdown } from "../markdown.js";

const query = (text: string) => html`
  <fql-search readonly q=${text}></fql-search>
`

export const about_fql = markdown`

## FQL

**F**lakiness **Q**uery **L**anguage (FQL) is a simple language you can use to filter data on flakiness.io.

You can use FQL to filter **tests**, **environments** and **errors**:

* Regular words filter **tests**.
* Words prefixed with \`@\` filter **environments**.
* Words prefixed with \`$\` filter **errors**.

For example, the following will search for all tests with "page-click" inside:

${query('page-click')}

And this will filter all environments that have \`linux\` as part of some of their value:

${query('@linux')} 

Finally, the following will filter all errors that have \`undefined\` as part of their message:

${query('$undefined')}

Multiple space-separated filters are treated as logical AND. For example, this will
filter tests that have both \`element\` and \`drag\` as part of their file, their parent suite title or their title:

${query('element drag')}

And the following will search for all installation tests that had "denied" as part of their message on Debian:

${query('installation $denied @debian')}

Naturally, if you'd like to search for something that includes space or other special character, you can wrap the search
term in quotes.

The following will filter tests that have both \`click\` and that we run on Windows 10.

${query('click @"windows 10"')}

`;
