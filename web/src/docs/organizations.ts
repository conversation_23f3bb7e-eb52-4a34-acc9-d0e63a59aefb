import { markdown } from "../markdown.js";

export const resource_access = () => markdown`

## Access & Permissions

### Entities & Roles

Flakiness access & permissions model is based on the role-based access control.

First of all, let's establish a dictionary of common terms:
* **Users**: Individuals who log in via GitHub.
* **Deployment**: Flakiness service deployment, like [https://flakiness.io](https://flakiness.io) or your self-hosted version.
* **Organizations**: Groups of users with a hierarchy of permissions.
* **Projects**: Associated with organization, where the main data is stored.

When a user tries to access an organization or a project, the set of things
he can or cannot do is defined by their role in the project or organization.

Flakiness has roles for accessing deployment, organizations and projects.

> **NOTE** In case of [flakiness.io](https://flakiness.io), only a handful of _Degu Labs, Inc_ employers have deployment-scope roles.

Deployment-level roles:

* **SuperAdmin**: has full access to the deployment; this role can be assigned only when the service is being deployed by deployment operators.
  - Create, modify, or delete organizations
  - Assign **Org Admins** to organizations
  - Access to service health stats at [deployment backoffice](${window.location.origin}/administration)

Organization roles:

- **Org Admin** - full control over the organization:
  * Change organization settings
  * Add/remove users in the organization
  * Assign organization members
  * Assign project poles
  * Create/delete projects in the organization
  * Automatically receive **Editor** role for all projects in the organization
- **Org Member**
  * Automatically receive **Viewer** access to all projects in the organization

Project roles:

- **Editor** - full control over the project:
  * Change project visibility
  * Add/remove contributors
  * Add/delete test results (has access to read-write project token)
  * Delete project
- **Viewer**
  * Read-only access to test results & history

Users outside of organization can be given project roles inside organization. However, they
will use organization license seat.

### Project Visibility

Project visibility defines project role for users that don't belong to the organization,
including non-logged-in users.

There are 2 levels of project visibility:
* **Public visibility**: everybody, including non-logged-in users, is getting a **Viewer** role
* **Private visibility**: the service will respond with "404 Not Found" to the users that don't belong to the organization. 

`

export const pricing = () => markdown`

## Pricing

Flakiness.io bills organizations per "license seats" and total volume of data they upload to the deployment.

License seats billing:
- 1 license seat costs **$10 / month**.
- Each orgnaization member takes 1 license seat
- Non-organization user that is added to organization projects takes 1 license seat.

Data billing:
- Each organization is given **10Gb / month** of total free data.
- If organization uploads over that limit, then it is billed **$0.05/GB-month**.
- Each month, we check how much data organization consumes, and bill the organization for extra data.
- Organizations can configure data retention policy per-project to automatically delete old data.

Organization billing status can be either **good**, **expired** or **canceled**.
* If organization payed for the services, than it's billing status is **good**.
* If flakiness.io attempted to charge organization, but the transaction didn't go through,
  than its billing status transitions to **expired**. In this status, organization is still able to access the dashboard,
  but is not able to upload new data.
* If organization is in **expired** status for 3 months, it's billing status becomes **canceled**.
  In this status, organization is no longer able to access the dashboard.
* If organization's billing is in **canceled** status for 3 months, then it gets permanently removed.
`





