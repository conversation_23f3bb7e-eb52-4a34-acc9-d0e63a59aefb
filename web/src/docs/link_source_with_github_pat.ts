import { WireTypes } from "@flakiness/server/common/wireTypes.js";
import { markdown } from "../markdown.js";

export const link_source_with_github_pat = (serverInfo: WireTypes.ServerInfo) => markdown`

If installing the [Flakiness.io GitHub App](${serverInfo.githubAppPublicURL}) isn't possible, you can alternatively authenticate repositories using a GitHub Personal Access Token (PAT).

To set this up:

1. **Create a New Token**  
   Generate a new token with the \`repo\` scope by following this link: [Create new token](https://github.com/settings/tokens/new?scopes=repo).

2. **Add Token to Flakiness.io**  
   Enter this token in the form below, along with the repository you'd like to link.

This alternative method allows you to securely connect repositories to Flakiness.io when using the GitHub App isn't an option.
`;




