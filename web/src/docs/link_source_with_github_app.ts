import { WireTypes } from "@flakiness/server/common/wireTypes.js";
import { markdown } from "../markdown.js";

export const link_source_with_github_app = (serverInfo: WireTypes.ServerInfo) => markdown`

Flakiness.io provides a seamless way to connect and authenticate your GitHub repositories using the [Flakiness.io GitHub App](${serverInfo.githubAppPublicURL}). This integration is the recommended method for linking repositories.

To get started, follow these steps:

1. **Install the GitHub App**  
   Visit the [Flakiness.io GitHub App installation page](${serverInfo.githubAppPublicURL}) to install the app on your preferred GitHub organization, repository, or account.

2. **Configure Repository Access**  
   Once installed, grant the app access to the specific repositories you'd like to use with Flakiness.io.

After setup, any authorized user can link repositories to Flakiness.io projects.

`;




