import { ContextType, consume } from '@lit/context';
import { LitElement, css, html } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { pageStyles } from './components/cssstyles.js';
import { contexts } from './contexts.js';
import { resetcss } from './resetcss.js';

@customElement('page-landing')
export class PageLanding extends LitElement {
  static page() {
    return html`<page-landing></page-landing>`;
  }

  @property() redirect?: string;
  @consume({ context: contexts.serverInfo, subscribe: true }) private _serverInfo?: ContextType<typeof contexts.serverInfo>;

  override render() {
    return html`
      <app-header .customTitle=${"Flakiness.io"}></app-header>
      <app-body>
        <h-box>
          <h1 class="title">
            <img src="./logo.svg" class="logo">
            <span>flakiness.io</span>
          </h1>
          <sup>beta</sup>
        </h-box>
        <p>Test Analytics for <a class=playwright href="https://playwright.dev">🎭 Playwright Test</a></p>
      </app-body>
      <app-footer></app-footer>
    `
  }

  static styles = [resetcss, pageStyles, css`
    sup {
    }

    cloud-pricing, pricing-card {
      margin-top: var(--sl-spacing-large);
    }

    .playwright {
      background-color: yellow;
      text-decoration: none;
      padding: 4px 10px;
      border-radius: 4px;
    }

    .title {
      font-family: "Raleway", sans-serif;
      font-optical-sizing: auto;
      font-weight: 400;
      align-items: baseline;
      font-style: normal;
      display: flex;
      gap: 10px;
      font-size: 48px;

      .logo {
        width: 32px;
      }
    }
  `];
}
