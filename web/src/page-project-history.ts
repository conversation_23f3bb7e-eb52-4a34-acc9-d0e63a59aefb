import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';

import { FlakinessReport } from '@flakiness/sdk/browser';
import { TimelineSplit } from '@flakiness/server/common/timeline/timelineSplit.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { ContextType } from '@lit/context';
import { Task } from '@lit/task';
import { timeDay } from 'd3';
import { Temporal } from 'temporal-polyfill';
import { docLinkStyles, pageStyles } from './components/cssstyles.js';
import { ExecutionCalendarLinker } from './components/execution-calendar.js';
import { FKHeadSelectEvent } from './components/head-selector.js';
import { ProjectAlerts } from './components/project-alerts.js';
import { ProjectContext } from './components/project-context.js';
import { consume, contexts } from './contexts.js';
import { githubMarkdownCSS } from './githubMarkdownCSS.js';
import { PageRun } from './page-run.js';
import { RouteConfig, URLState } from './router.js';
import { tasks } from './tasks.js';
import { assert } from './utils.js';

const pageState = new URLState({
  /* report time interval options */
  head: URLState.option<string|undefined>({
    name: 'head',
    default: undefined,
    encode: head => head,
    decode: head => head,
  }),
  since: URLState.dateOption({
    name: 'since',
    default: undefined,
  }),
  until: URLState.dateOption({
    name: 'until',
    default: undefined,
  }),
  timelineSplit: URLState.option<TimelineSplit>({
    name: 'split',
    default: TimelineSplit.DEFAULT,
    encode: split => JSON.stringify(split.serialize()),
    decode: text => TimelineSplit.deserialize(JSON.parse(text) as WireTypes.JSONTimelineSplit),
  }),
});

@customElement('page-project-history')
export class PageProjectHistory extends LitElement {

  static url(options: { orgSlug: string, projectSlug: string, }): string {
    return new URL(['', options.orgSlug, options.projectSlug, 'history'].join('/'), window.location.href).href;
  }

  static routes(): RouteConfig[] {
    return [{
      path: [
        '/:org/:project/history',
      ],
      render: (groups) => ProjectContext.render({
        orgSlug: groups?.org,
        projectSlug: groups?.project,
      }, html`
        <page-project-history
          .orgSlug=${groups?.org}
          .projectSlug=${groups?.project}
        ></page-project-history>
      `),
    }];
  }

  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;
  @consume({ context: contexts.projectReferences, subscribe: true }) private _branches?: ContextType<typeof contexts.projectReferences>;
  @consume({ context: contexts.user }) private _user?: ContextType<typeof contexts.user>;
  @consume({ context: contexts.router, subscribe: true }) private _router?: ContextType<typeof contexts.router>;

  @property({ attribute: false }) projectSlug?: string;
  @property({ attribute: false }) orgSlug?: string;

  private _urlState = pageState.bind(this, () => {
  });

  private _headRefTask = tasks.headRef(this, {
    head: () => this._urlState.head,
    project: () => this._project,
    defaultBranch: () => this._branches?.defaultBranch,
  });

  private _reportTimeInterval = new Task(this, {
    args: () => [
      this._headRefTask.value, this._urlState.since?.getTime(), this._urlState.until?.getTime(),
    ] as const,
    task: async([headRef, sinceTimestamp, untilTimestamp]) => {
      assert(headRef);
      // If either since or until is not set, then default to today.
      if (!sinceTimestamp || !untilTimestamp) {
        // Default to 7 days report.
        const day = timeDay.floor(new Date());
        return {
          since: timeDay.offset(day, -6),
          until: timeDay.offset(day, 1),
        }
      }
      // Otherwise, just the since and until that we're given.
      return {
        since: new Date(sinceTimestamp),
        until: new Date(untilTimestamp),
      }
    }
  });

  private _executionCalendarLinker = new Task(this, {
    args: () => [
      window.location.href,
    ] as const,
    task: async ([], { signal }) => {
      const pageLinker = pageState.createLinkRenderer({
        base: this._urlState,
      });

      const calendarLinker: ExecutionCalendarLinker = (options) => pageLinker({
        since: options.since,
        until: options.until,
      });
      return calendarLinker;
    },
  });

  private _reportOptionsTask = new Task(this, {
    args: () => [
      this._project,
      this._headRefTask.value,
      this._reportTimeInterval.value,
      this._urlState.timelineSplit,
    ] as const,
    task: async ([project, headRef, reportTimeInterval, timelineSplit], { signal }) => {
      assert(project && headRef && reportTimeInterval && timelineSplit);

      return {
        orgSlug: project.org.orgSlug,
        projectSlug: project.projectSlug,
        fql: '',
        timelineSplit: timelineSplit.serialize(),
        commitOptions: {
          head: headRef.commit.commitId,
          sinceTimestamp: +reportTimeInterval.since as FlakinessReport.UnixTimestampMS|undefined,
          untilTimestamp: +reportTimeInterval.until as FlakinessReport.UnixTimestampMS|undefined,
        },
        timeZoneId: Temporal.Now.timeZoneId(),
        regressionWindowDays: project.regressionWindowDays,
        dailyReportBreakdown: false,
        historyBuckets: 0,
        acceptableFlakinessRate: project.acceptableFlakinessRatio,
      } satisfies WireTypes.ReportOptions;
    }
  });

  private _allEnvironments = tasks.allEnvironments(this, {
    commitOptions: () => this._reportOptionsTask.value?.commitOptions,
    project: () => this._project,
  });

  private _runLinker = new Task(this, {
    args: () => [this.orgSlug, this.projectSlug, this._urlState.timelineSplit] as const,
    task: async ([orgSlug, projectSlug, timelineSplit], { signal }) => {
      assert(orgSlug && projectSlug && timelineSplit);
      return {
        render: (runStats: WireTypes.RunStats) => PageRun.url({
          orgSlug,
          projectSlug,
          reportId: runStats.run.runId,
          timelineSplit,
        }),
      }
    }
  });

  render() {
    if (!this._project)
      return nothing;

    return html`
      <project-header
        .orgSlug=${this.orgSlug}
        .projectSlug=${this.projectSlug}
        .submenu=${"history"}
      ></project-header>
      <app-body wide user-role=${this._project.access}>
        ${ProjectAlerts.maybeRender(this._project)}

        <h-box style="align-items: start;">
          <v-box>
            <div class=header>History</div>
            <h-box>
              <head-selector
                .head=${this._headRefTask.value}
                @fk-select=${(event: FKHeadSelectEvent) => {
                  this._urlState.head = event.head.name;
                }}
              ></head-selector>
              <timeline-selector multiple
                .envs=${this._allEnvironments.value}
                .split=${this._urlState.timelineSplit}
                @fk-select=${(event: CustomEvent<TimelineSplit>) => {
                  this._urlState.timelineSplit = event.detail;
                }}
              ></timeline-selector>
            </h-box>
          </v-box>
          <x-filler></x-filler>
          <execution-calendar
            .linker=${this._executionCalendarLinker.value}
            .split=${this._urlState.timelineSplit}
            .head=${this._headRefTask.value}
            .since=${this._reportTimeInterval.value?.since}
            .until=${this._reportTimeInterval.value?.until}
            @rangeselected=${(event: CustomEvent<{ since: Date, until: Date }>) => {
              this._urlState.since = event.detail.since;
              this._urlState.until = event.detail.until;
            }}
          ></execution-calendar>
        </h-box>
        <sl-divider style="--spacing: 0;"></sl-divider>
        <commit-history
          .project=${this._project}
          .head=${this._headRefTask.value}
          .since=${this._reportTimeInterval.value?.since}
          .until=${this._reportTimeInterval.value?.until}
          .split=${this._urlState.timelineSplit}
          .runlinker=${this._runLinker.value}
        ></commit-history>
      </app-body>
      <app-footer></app-footer>
    `
  }

  static styles = [githubMarkdownCSS, docLinkStyles, pageStyles, css`
    .header {
      font-size: var(--sl-font-size-x-large);
    }
  `];
}
