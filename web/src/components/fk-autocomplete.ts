import { SlFocusEvent, SlPopup } from '@shoelace-style/shoelace';
import { LitElement, css, html } from 'lit';
import { customElement, property, query } from 'lit/decorators.js';
import { live } from 'lit/directives/live.js';

@customElement('fk-autocomplete')
export class FkAutocomplete extends LitElement {
  @property({ type: Boolean }) open?: boolean;

  @query('sl-popup') private _popup?: SlPopup;
  @query('slot[name=input]') private _input?: HTMLSlotElement;
  
  connectedCallback() {
    super.connectedCallback();
    document.addEventListener('mouseup', this._onMouseUp);
  }

  disconnectedCallback() {
    super.disconnectedCallback();
    document.removeEventListener('mouseup', this._onMouseUp);
  }

  private _onMouseUp = (event: MouseEvent) => {
    // Close when clicking outside of the containing element
    const path = event.composedPath();

    if (this._input && !path.includes(this._input) && this._popup) {
      this.open = false;
    }
  }

  override render() {
    return html`
      <sl-popup
        ?active=${live(this.open)}
        @keydown=${(e: KeyboardEvent) => {
          if (e.key === 'Escape' && this.open) {
            this.open = false;
            e.stopPropagation();
            e.stopImmediatePropagation();
            e.preventDefault();
          }
        }}
        @sl-focus=${(event: SlFocusEvent) => {
          this.open = true;
        }}
        placement="bottom-start"
        sync=width
      >
        <slot name=input slot=anchor></slot>
        <slot></slot>
      </sl-popup>
    `;
  }

  static styles = [css`
    sl-popup::part(popup) {
      z-index: var(--sl-z-index-dropdown);
    }
  `]
}
