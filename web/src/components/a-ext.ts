import { LitElement, css, html, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { linkStyles } from './cssstyles.js';

@customElement('a-ext')
class AExt extends LitElement {
  @property({ type: String }) href?: string;
  @property({ type: String }) target?: string;

  render() {
    return html`
      <a href=${this.href ?? nothing} target=${this.target ?? '_blank'}>
        <span><slot></slot></span>
        ${this.href ? html`<sl-icon library=boxicons name=bx-link-external></sl-icon>` : nothing}
      </a>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      display: inline-block;
    }

    a {
      display: inline-flex;
      align-items: center;
      width: 100%;
    }

    span {
      overflow: hidden;
      text-overflow: ellipsis;
    }

    sl-icon {
      flex: none;
      margin-left: var(--sl-spacing-2x-small);
    }
  `];
}
