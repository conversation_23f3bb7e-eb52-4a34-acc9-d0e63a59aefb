import { FlakinessReport } from '@flakiness/report';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { LitElement, css, html, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { githubLinks } from '../githubLinks.js';
import { linkStyles } from './cssstyles.js';

@customElement('a-location')
class ALocation extends LitElement {
  @property({ type: String }) project?: WireTypes.Project;
  @property({ type: String }) location?: FlakinessReport.Location;
  @property({ type: String }) commitId?: FlakinessReport.CommitId;

  render() {
    if (!this.project || !this.location || !this.commitId)
      return nothing;
    return html`
      <a-ext href=${githubLinks.fileUrl(this.project, this.commitId, this.location.file, this.location.line)}>${this.location.file}:${this.location.line}</a-ext>
    `;
  }

  static styles = [linkStyles, css`
  `];
}
