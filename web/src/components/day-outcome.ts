import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { LitElement, css, html } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';

@customElement('day-outcome')
class DayOutcome extends LitElement {
  @property({ type: Boolean }) selected?: false;
  @property({ type: Boolean }) dimmed?: false;
  @property({ type: String }) size?: string;

  @property({ attribute: false }) outcome?: WireTypes.DayOutcome;

  render() {
    return html`
      <span class=${classMap({
        day: true,
        selected: !!this.selected,
        [this.outcome ?? 'loading']: true,
      })}></span>
    `;
  }

  static styles = [css`
    :host {
      display: block;
      width: 12px;
      height: 12px;
    }

    :host([size=large]) {
      width: 14px;
      height: 14px;
    }

    :host([dimmed]) .day {
      --base-color: color-mix(in srgb, var(--day-color) 50%, white 50%);
      outline: 1px solid var(--outline-color, color-mix(in srgb, var(--base-color), black 8%));
      background-color: var(--base-color);
    }

    :host([selected][size=large]) .day {
      outline-width: 2px;
    }

    :host([selected]) .day {
      outline: 2px solid var(--sl-color-neutral-800);
    }

    :host([selected][dimmed]) .day {
      outline-color: var(--day-color);
    }

    .day {
      box-sizing: border-box;
      outline: 1px solid var(--outline-color, color-mix(in oklch, var(--day-color), black 8%));
      background-color: color-mix(in oklch, var(--day-color), white 40%);

      &.selected {
      }

      &.regressed {
        --day-color: var(--fk-color-outcome-regressed);
      }
      &.expected {
        --day-color: var(--fk-color-outcome-expected);
      }
      &.unexpected {
        --day-color: var(--fk-color-outcome-unexpected);
      }
      &.flaked {
        --day-color: var(--fk-color-outcome-flaked);
      }
      &.skipped {
        --day-color: var(--fk-color-outcome-skipped);
      }

      &.untested {
        --day-color: var(--sl-color-neutral-100);
        --outline-color: var(--sl-color-neutral-300);
      }

      &.idle {
        --day-color: var(--sl-color-neutral-100);
        --outline-color: var(--sl-color-neutral-100);
      }
    }

    .day {
      display: block;
      color: white;
      border-radius: 1px;
      align-items: center;
      justify-content: center;
      font-size: var(--sl-font-size-2x-small);
      width: 100%;
      height: 100%;

      &.loading {
        --day-color: var(--sl-color-neutral-100);
        --loading-color: var(--sl-color-neutral-100);
        background: linear-gradient(
          45deg,
          color-mix(in srgb, var(--loading-color) 100%, transparent) 0%,
          color-mix(in srgb, var(--loading-color) 0%, transparent) 12.5%,
          color-mix(in srgb, var(--loading-color) 100%, transparent) 25%,
          color-mix(in srgb, var(--loading-color) 0%, transparent) 37.5%,
          color-mix(in srgb, var(--loading-color) 100%, transparent) 50%,
          color-mix(in srgb, var(--loading-color) 0%, transparent) 62.5%,
          color-mix(in srgb, var(--loading-color) 100%, transparent) 75%,
          color-mix(in srgb, var(--loading-color) 0%, transparent) 87.5%,
          color-mix(in srgb, var(--loading-color) 100%, transparent) 100%
        );
        background-size: 200% 200%;
        background-position: 0% 0%;
        animation: shimmer 1s linear infinite, fadeIn 200ms ease-in forwards;
        opacity: 0;
        outline: none;
      }
    }

    @keyframes shimmer {
      0% {
        background-position: 0% 0%;
      }
      100% {
        background-position: 0% 100%;
      }
    }

    @keyframes fadeIn {
      to {
        opacity: 1;
      }
    }

  `];
}
