import { css } from "lit";

export const linkStyles = css`
  a {
    text-decoration: none;

    &[href]:hover {
      text-decoration: underline;
    }

    &:visited, &:link {
      color: inherit;
    }
  }
`

// All pages must be "full-screen": this way, we can put footer in the very end of the page.
export const pageStyles = css`
  :host {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
`;

export const docLinkStyles = css`
  a {
    text-decoration: none;

    &[href]:hover {
      text-decoration: underline;
    }

    &:visited, &:link {
      color: #0969da;
    }
  }
`