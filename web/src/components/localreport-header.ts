import { consume, ContextType } from '@lit/context';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';
import { contexts } from '../contexts.js';
import { PageOrganization } from '../page-organization.js';
import { PageReport } from '../page-report.js';
import { linkStyles } from './cssstyles.js';

@customElement('localreport-header')
export class LocalReportHeader extends LitElement {
  @consume({ context: contexts.project, subscribe: true }) @state() private _project?: ContextType<typeof contexts.project>;

  @property({ attribute: false }) orgSlug?: string;
  @property({ attribute: false }) projectSlug?: string;

  override render() {
    const orgSlug = this.orgSlug;
    const projectSlug = this.projectSlug;
    return html`
      <app-header>
        <sl-breadcrumb>
          <span slot="separator">/</span>
          ${this.orgSlug ? html`
            <sl-breadcrumb-item href=${this.orgSlug ? PageOrganization.url({ orgSlug: this.orgSlug }) : nothing }>${this.orgSlug ?? nothing}</sl-breadcrumb-item>
          ` : nothing}
          ${orgSlug && projectSlug ? html`
            <sl-breadcrumb-item href=${orgSlug && projectSlug && PageReport.url({ orgSlug, projectSlug })}>
              <h-box style="gap: var(--sl-spacing-2x-small);"><span><b>${projectSlug}</b></span><project-visibility value=${this._project?.visibility}></project-visibility></h-box>
            </sl-breadcrumb-item>
          ` : nothing}
        </sl-breadcrumb>
      </app-header>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      display: block;
    }

    project-visibility {
      font-size: var(--sl-font-size-small);
    }

    sl-breadcrumb-item::part(base) {
      font-size: var(--sl-font-size-medium);
      font-weight: var(--sl-font-weight-normal);
    }

    sl-breadcrumb-item::part(label) {
      color: var(--sl-color-neutral-800);
    }

    sl-tag::part(base) {
      height: 16px;
    }
  `];
}
