import { LitElement, css, html } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { unsafeHTML } from 'lit/directives/unsafe-html.js';
import { ansi2html } from '../ansi2html.js';
import { linkStyles } from './cssstyles.js';

@customElement('ansi-text')
class ANSIText extends LitElement {
  @property({ type: String, attribute: false }) text?: string;

  render() {
    return html`
      <pre>${unsafeHTML(ansi2html(this.text ?? ''))}</pre>
    `;
  }

  static styles = [linkStyles, css`
    pre {
      margin: 0;
      overflow: auto;
      background-color: var(--sl-color-neutral-50);
      padding: var(--sl-spacing-medium);
      border-radius: var(--sl-border-radius-medium);

      --vscode-terminal-ansiBlack: #000000;
      --vscode-terminal-ansiRed: #cd3131;
      --vscode-terminal-ansiGreen: #00bc00;
      --vscode-terminal-ansiYellow: #949800;
      --vscode-terminal-ansiBlue: #0451a5;
      --vscode-terminal-ansiMagenta: #bc05bc;
      --vscode-terminal-ansiCyan: #0598bc;
      --vscode-terminal-ansiWhite: #555555;
      --vscode-terminal-ansiBrightBlack: #666666;
      --vscode-terminal-ansiBrightRed: #cd3131;
      --vscode-terminal-ansiBrightGreen: #14ce14;
      --vscode-terminal-ansiBrightYellow: #b5ba00;
      --vscode-terminal-ansiBrightBlue: #0451a5;
      --vscode-terminal-ansiBrightMagenta: #bc05bc;
      --vscode-terminal-ansiBrightCyan: #0598bc;
      --vscode-terminal-ansiBrightWhite: #a5a5a5;
    }
  `];
}
