import { LitElement, css, html } from "lit";
import { customElement, property } from "lit/decorators.js";
import { linkStyles } from "./cssstyles.js";

@customElement('app-body')
class AppBody extends LitElement {
  @property({ type: Boolean }) wide?: boolean;
  render() {
    return html`
      <section ?wide=${this.wide}>
        <v-box>
          <slot></slot>
        </v-box>
      </section>
    `
  }

  static styles = [linkStyles, css`
    :host {
      display: flex;
      flex-direction: column;
    }

    v-box {
      padding: var(--sl-spacing-large) 0;
      gap: var(--sl-spacing-large);
      grid-column: 2;
    }

    section {
      display: grid;
      grid-template-columns: 1fr min(75ch, 100%) 1fr;

      &[wide] {
        grid-template-columns: 1fr min(1280px, 100%) 1fr;
      }
    }
  `];
}
