import { LitElement, css, html, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { linkStyles } from './cssstyles.js';
import { Artifact } from './test-run.js';


/**
 * ImageDiff must have a "diff" and some 2 other expectations, such as "actual", "expected" or "previous".
 * At least that's what Playwright Test gives us.
 */
export type ImageExpectation = Artifact & {
  category: string,
};
export type ImageDiff = {
  name: string,
  diff?: Artifact,
  expectation1: ImageExpectation;
  expectation2: ImageExpectation;
}

@customElement('image-diff')
class ImageDiffElement extends LitElement {

  @property() diff?: ImageDiff;
  render() {
    const imageDiff = this.diff;
    if (!imageDiff)
      return nothing;
    return html`
        <fk-tabbar>
          
          ${imageDiff.diff ? html`
            <fk-tab name="diff">Diff</fk-tab>
            <fk-tabpane name="diff">
              <img class=screenshot-diff src=${imageDiff.diff.url}>
            </fk-tabpane>
          ` : nothing}

          <fk-tab name="expectation-1">${capitalize(imageDiff.expectation1.category)}</fk-tab>
          <fk-tab name="expectation-2">${capitalize(imageDiff.expectation2.category)}</fk-tab>
          <fk-tab name="slider">Slider</fk-tab>

          <fk-tabpane name="expectation-1">
            <img class=screenshot-diff
              alt=${imageDiff.expectation1.category}
              src=${imageDiff.expectation1.url}
            >
          </fk-tabpane>
          <fk-tabpane name="expectation-2">
            <img class=screenshot-diff
              alt=${imageDiff.expectation2.category}
              src=${imageDiff.expectation2.url}
            >
          </fk-tabpane>
            <fk-tabpane name="slider">
              <sl-image-comparer class=screenshot-diff>
                <img
                  slot="before"
                  alt=${imageDiff.expectation1.category}
                  src=${imageDiff.expectation1.url}    
                />
                <img
                  slot="after"
                  alt=${imageDiff.expectation2.category}
                  src=${imageDiff.expectation2.url}
                />
            </sl-image-comparer>
          </fk-tabpane>
        </fk-tabbar>
        <v-box>
          ${imageDiff.diff ? html`
            <artifact-link .artifact=${imageDiff.diff}></artifact-link>
          ` : nothing}
          <artifact-link .artifact=${imageDiff.expectation1}></artifact-link>
          <artifact-link .artifact=${imageDiff.expectation2}></artifact-link>
        </v-box>
    `
  }

  static styles = [linkStyles, css`
    .screenshot-diff {
      margin: var(--sl-spacing-small);
      max-width: 800px;
      box-shadow: 0 0 5px var(--sl-color-neutral-400);

      img {
        max-width: 100%;
      }
    }

    sl-image-comparer::part(divider) {
      border: 1px solid var(--sl-color-neutral-400);
    }

    sl-image-comparer::part(handle) {
      border: 2px solid var(--sl-color-neutral-400);
    }
  `];
}

function capitalize(word: string): string {
  return word.charAt(0).toUpperCase() + word.slice(1);
}