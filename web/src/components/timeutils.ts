import ms from "ms";

const monthIndexToName = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

type IntervalOptions = {
  point: Date,
  from?: number,
  to?: number,
}

type Interval = {
  since: Date,
  until: Date,
}

class TimeUtilsClass {
  weekNumber(point: Date) {
    // Jan, 4 is always in the first week
    let yearStart = new Date(point.getFullYear(), 0, 4);
    if (yearStart.getDay() !== 1)
      yearStart = new Date(yearStart.getFullYear(), yearStart.getMonth(), yearStart.getDate() - yearStart.getDay() + 1);
    if (point < yearStart) {
      yearStart = new Date(point.getFullYear() - 1, 0, 4);
      if (yearStart.getDay() !== 1)
        yearStart = new Date(yearStart.getFullYear(), yearStart.getMonth(), yearStart.getDate() - yearStart.getDay() + 1);
    }
    return { year: yearStart.getFullYear(), week: (Math.floor(((+point) - (+yearStart)) / ms('7 days'))) + 1 + 1 };
  }

  quarterNumber(point: Date) {
    return ((point.getMonth()/3)|0) + 1;
  }

  readonly step = {
    day: (date: Date, count: number = 1) => {
      date = new Date(date);
      date.setDate(date.getDate() + count);
      return date;
    },

    week: (date: Date, count: number = 1) => {
      date = new Date(date);
      date.setDate(date.getDate() + 7 * count);
      return date;
    },
  
    month: (date: Date, count: number = 1) => {
      date = new Date(date);
      date.setMonth(date.getMonth() + count);
      return date;
    },
  
    quarter: (date: Date, count: number = 1) => {
      date = new Date(date);
      date.setMonth(date.getMonth() + 3 * count);
      return date;
    },
  }

  readonly floor = {
    day: (date: Date) => {
      return new Date(date.getFullYear(), date.getMonth(), date.getDate());
    },

    week: (date: Date) => {
      return new Date(date.getFullYear(), date.getMonth(), date.getDate() - date.getDay());
    },
  
    month: (date: Date) => {
      return new Date(date.getFullYear(), date.getMonth(), 1);
    },
  
    quarter: (date: Date) => {
      return new Date(date.getFullYear(), ((date.getMonth() / 3)|0) * 3, 1);
    }
  }

  private _ceil = (floor: (date: Date) => Date, next: (date: Date) => Date, date: Date) => {
    return new Date(+next(floor(date)) - 1);
  }

  readonly ceil = {
    day: this._ceil.bind(this, this.floor.day, this.step.day),
    week: this._ceil.bind(this, this.floor.week, this.step.week),
    month: this._ceil.bind(this, this.floor.month, this.step.month),
    quarter: this._ceil.bind(this, this.floor.quarter, this.step.quarter),
  }

  private _interval = (step: (date: Date, count: number) => Date, { point, from = 0, to = 0 }: IntervalOptions): Interval => {
    return {
      since: step(point, from),
      until: step(point, to + 1),
    };
  }

  readonly interval = {
    days: this._interval.bind(this, this.step.day),
    weeks: this._interval.bind(this, this.step.week),
    months: this._interval.bind(this, this.step.month),
    quarters: this._interval.bind(this, this.step.quarter),
  }

  readonly format = {
    day: (date: Date) => {
      return `${monthIndexToName[date.getMonth()]} ${date.getDate()}, ${date.getFullYear()}`
    },
  
    month: (date: Date) => {
      return `${monthIndexToName[date.getMonth()]} ${date.getFullYear()}`
    },
  
    week: (date: Date) => {
      const { year, week } = this.weekNumber(date);
      return `Week ${week}, ${year}`;
    },
  
    quarter: (date: Date) => {
      return `Quarter ${this.quarterNumber(date)} ${date.getFullYear()}`
    },

    dayInterval: ({ since, until }: Interval) => {
      until = new Date((+until) - 1);
      return `${this.format.day(since)} – ${this.format.day(until)}`;
    },

    monthInterval: ({ since, until }: Interval) => {
      until = new Date((+until) - 1);
      return `${this.format.month(since)} – ${this.format.month(until)}`;
    },
  }

  private _splitter(next: (date: Date) => Date, floor: (date: Date) => Date, { since, until }: Interval) {
    const result: Interval[] = [];
    let slidingPoint = next(floor(since));
    while (slidingPoint < until) {
      result.push({ since, until: slidingPoint });
      since = slidingPoint;
      slidingPoint = next(since);
    }
    result.push({ since, until });
    return result;
  }

  readonly split = {
    intoDays: this._splitter.bind(this, this.step.day, this.floor.day),
    intoWeeks: this._splitter.bind(this, this.step.week, this.floor.week),
    intoMonths: this._splitter.bind(this, this.step.month, this.floor.month),
    intoQuarters: this._splitter.bind(this, this.step.quarter, this.floor.quarter),
  }
}

export const TimeUtils = new TimeUtilsClass();

export type CommitsScale = 'commit'|'day'|'week'|'month'|'year'|'alltime';

export class CommitsRange {
  static commits(count: number ) { return new CommitsRange('commit', count); }
  static days(count: number ) { return new CommitsRange('day', count); }
  static weeks(count: number ) { return new CommitsRange('week', count); }
  static months(count: number ) { return new CommitsRange('month', count); }
  static years(count: number ) { return new CommitsRange('year', count); }
  static alltime() { return new CommitsRange('alltime', 0); }

  constructor(
    public type: 'commit'|'day'|'week'|'month'|'year'|'alltime',
    public count: number,
  ) {}

  serialize() {
    if (this.type === 'alltime')
      return 'alltime';
    return this.count + '.' + this.type + (this.count > 1 ? 's' : '');
  }

  name() {
    if (this.type === 'alltime')
      return 'all time';
    if (this.type === 'commit' && this.count === 1)
      return 'HEAD';
    return this.count + ' ' + this.type + (this.count > 1 ? 's' : '');
  }

  isTimeRange() {
    return this.type === 'year' || this.type === 'month' || this.type === 'week' || this.type === 'day';
  }

  sinceDate(point: Date): Date | undefined {
    if (this.type === 'year') 
      return TimeUtils.step.month(point, -12 * this.count);
    if (this.type === 'month')
      return TimeUtils.step.month(point, -this.count);
    if (this.type === 'week')
      return TimeUtils.step.week(point, -this.count);
    if (this.type === 'day')
      return TimeUtils.step.day(point, -this.count);
    return undefined;
  }

  static deserialize(text: string) {
    if (text.toLowerCase() === 'alltime')
      return new CommitsRange('alltime', 0);
    let [countText, type] = text.toLowerCase().split('.');
    const count = parseInt(countText, 10);
    if (isNaN(count) || count < 0 || count > 1000)
      return undefined;
    if (type.endsWith('s'))
      type = type.substring(0, type.length - 1);
    if (type !== 'week' && type !== 'commit' && type !== 'day' && type !== 'month' && type !== 'year')
      return undefined;
    return new CommitsRange(type, count);
  }
}
