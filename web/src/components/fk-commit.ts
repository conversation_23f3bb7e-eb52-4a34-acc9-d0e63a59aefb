import type { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { timeFormat } from 'd3';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { PageReport } from '../page-report.js';
import { linkStyles } from './cssstyles.js';

const FULL_DATETIME_FORMAT = timeFormat('%a, %b %e, %Y %H:%M:%S');


@customElement('fk-commit')
class FKCommit extends LitElement {
  @property({ attribute: false }) project?: WireTypes.Project;
  @property({ attribute: false }) commit?: WireTypes.Commit;

  override render() {
    const commit = this.commit;
    if (!commit)
      return nothing;
    const title = commit.message.split('\n')[0].trim();
    const shortsha = commit.commitId.substring(0, 7);
    return html`
      <v-box style="gap: var(--sl-spacing-2x-small);">
        <a class=commit-title href=${this.project && this.commit ? PageReport.commitUrl({
          orgSlug: this.project.org.orgSlug,
          projectSlug: this.project.projectSlug,
          commitId: this.commit?.commitId,
        }) : nothing}>${title}</a>
        <h-box class=commit-description>
          <sl-avatar image=${commit.avatar_url} label=${commit.author} loading=lazy></sl-avatar>
          ${commit.author} commited 
          <sl-relative-time date=${new Date(commit.timestamp).toISOString()}></sl-relative-time>
          <span>•</span>
          <h-box class=shortsha style="gap: var(--sl-spacing-2x-small); position: relative;">
            <span>${shortsha}</span>
            <sl-copy-button
              value=${commit.commitId}
            ></sl-copy-button>
          </h-box>
        </h-box>
      </v-box>
    `
  }

  static styles = [linkStyles, css`
    :host {
      display: block;
      min-width: 10em;
    }

    .commit-title {
      font-weight: var(--sl-font-weight-semibold);
      font-size: var(--sl-font-size-medium);
      white-space: nowrap;
    }

    .commit-description {
      gap: var(--sl-spacing-2x-small);
      font-size: var(--sl-font-size-small);
      color: var(--sl-color-neutral-500);

      sl-avatar {
        --size: 16px;
      }
    }

    .shortsha {
      font-family: var(--sl-font-mono);
      font-size: var(--sl-font-size-x-small);
      color: var(--sl-color-neutral-500);

      sl-copy-button {
        position: absolute;
        right: -20px;
        visibility: hidden;
      }

      &:hover sl-copy-button {
        visibility: visible;
      }
    }
  `];
}
