import { LitElement, css, html, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { linkStyles } from './cssstyles.js';

@customElement('int-num')
class IntNum extends LitElement {
  @property({ attribute: false }) x?: number;

  render() {
    return html`<span class=${this.x === 0 ? 'zero' : nothing}>${this.x ?? nothing}</span>`
  }

  static styles = [linkStyles, css`
    .zero {
      color: var(--sl-color-neutral-300);
    }
  `];
}
