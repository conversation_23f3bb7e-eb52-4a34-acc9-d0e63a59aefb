import type { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { timeDay, timeFormat } from 'd3';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import ms from 'ms';
import { linkStyles } from './cssstyles.js';

const OUTCOME_DESCRIPTIONS = {
  regressed: 'Some tests had new failures',
  unexpected: 'Some tests failed, but none were new failures',
  flaked: 'Some tests both passed and failed, with no new failures',
  expected: 'All tests passed—no failures or flakes',
  skipped: 'All tests were skipped—no passes, failures, or flakes',
  untested: 'Commits were made, but no tests were run',
  idle: 'No commits were made this day',
};

@customElement('history-snippet')
class HistorySnippet extends LitElement {
  @property({ type: Boolean }) dimmed: boolean = false;
  @property({ type: String }) size?: string;
  
  @property({ attribute: false }) selectedIndex: number = -1;
  @property({ attribute: false }) outcomes?: WireTypes.DayOutcome[];
  @property({ attribute: false }) days?: Date[];

  @state() private _hoveredDay?: Date;
  @state() private _hoveredOutcome?: WireTypes.DayOutcome;
  @state() private _showTooltip = false;

  private _renderPopover(date?: Date, outcome?: WireTypes.DayOutcome) {
    if (!date || !outcome)
      return nothing;
    const today = timeDay.floor(new Date());
    const target = timeDay.floor(date);
    const diffDays = Math.round((+today - +target) / ms('1 day'));

    let label: string;
    if (diffDays === 0) {
      label = 'Today';
    } else if (diffDays === 1) {
      label = '1 day ago';
    } else {
      label = `${diffDays} days ago`;
    }

    const format = timeFormat('%a, %b %e, %Y'); // "Mon, Jul 12, 2025"
    const formatted = format(date);

    return html`
      <h-box class=hover-popup>
        <v-box style="gap: var(--sl-spacing-2x-small);">
          <h-box style="gap: var(--sl-spacing-2x-small); font-size: var(--sl-font-size-small);">
            <span style="font-weight: bold;">${formatted}</span>
            <span>•</span>
            <span>${label}</span>
          </h-box>
          <h-box style="gap: var(--sl-spacing-2x-small);">
            <span>${OUTCOME_DESCRIPTIONS[outcome]}</span>
          </h-box>
        </v-box>
      </h-box>
    `;
  }

  override render() {
    const outcomes = this.outcomes;
    const days = this.days;
    if (!outcomes || !days)
      return nothing;
    // Select last meaningful outcome
    return html`
      <sl-popup ?active=${this._showTooltip} distance=10 strategy="fixed" placement=top-start>
        ${this._renderPopover(this._hoveredDay, this._hoveredOutcome)}
        <section slot=anchor
          @mouseleave=${this._onMouseLeaveSnippet}
        >
          ${outcomes.map((outcome, idx) => html`
            <button class=${classMap({
              day: true,
              [outcome]: true,
            })}
              @mouseenter=${this._onMouseEnter.bind(this, outcome, days[idx])}
              @click=${this._onClick.bind(this, outcome, days[idx])}
            ><day-outcome size=${this.size} ?selected=${idx === this.selectedIndex} ?dimmed=${this.dimmed} .outcome=${outcome}></day-outcome></button>
          `)}
        </section>
      </sl-popup>
    `;
  }

  private _onMouseEnter(outcome: WireTypes.DayOutcome, day: Date, e: MouseEvent) {
    this._hoveredDay = day;
    this._hoveredOutcome = outcome;
    this._showTooltip = true;
  }

  private _onClick(outcome: WireTypes.DayOutcome, day: Date, e: MouseEvent) {
    this.dispatchEvent(new CustomEvent<{ day: Date, outcome: WireTypes.DayOutcome }>('fk-select', {
      detail: {
        day,
        outcome,
      }
    }))
  }

  private _onMouseLeaveSnippet(e: MouseEvent) {
    this._showTooltip = false;
  }

  static styles = [linkStyles, css`
    :host {
      display: flex;
      align-items: center;
    }

    .hover-popup {
      background: white;
      padding: var(--sl-spacing-small);
      border: 1px solid var(--sl-color-neutral-300);
      border-radius: var(--sl-border-radius-medium);
      pointer-events: none;
      outline: 2px solid white;
    }

    section {
      display: grid;
      grid-template-columns: repeat(7, auto);
      grid-template-rows: auto;
      gap: 4.5px;
      padding: 4px; /* make sure outline border is visible */
    }

    .day {
      all: unset;
      cursor: pointer;

      &:hover {
        filter: brightness(0.8);
      }
    }
  `];
}
