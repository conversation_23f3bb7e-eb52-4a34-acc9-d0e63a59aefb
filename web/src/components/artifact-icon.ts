import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';

@customElement('artifact-icon')
class ArtifactIcon extends LitElement {
  @property({ attribute: false }) type?: 'video'|'pw-trace'|'image';

  override render() {
    if (this.type === 'video')
      return html`<sl-icon name=play-circle></sl-icon>`;
    if (this.type === 'image')
      return html`<sl-icon name=image></sl-icon>`;
    if (this.type === 'pw-trace')
      return html`<sl-icon name=layout-three-columns></sl-icon>`;
    return nothing;
  }

  static styles = [css`
    :host {
      display: flex;
    }
  `];
}