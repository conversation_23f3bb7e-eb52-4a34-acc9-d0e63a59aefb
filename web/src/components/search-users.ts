import { LitElement, css, html, nothing } from 'lit';
import { customElement, property, query, state } from 'lit/decorators.js';

import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { Task } from '@lit/task';
import { SlInputEvent, SlSelectEvent, type SlInput } from '@shoelace-style/shoelace';
import { api } from '../api.js';
import { getInitials } from '../utils.js';


export class UserSelectedEvent extends Event {
  constructor(public user: WireTypes.User) {
    super('user-selected', {
      bubbles: true,
      composed: true,
      cancelable: false,
    });
  }
}

@customElement('search-users')
export class SearchUsers extends LitElement {

  @query('sl-input#search-people') _searchPeopleInput?: SlInput;
  @property({}) placeholder?: string;

  @state() private _searchQuery?: string;

  private _peopleWithQuery = new Task(this, {
    args: () => [this._searchQuery],
    task: async ([query], { signal }) => {
      if (!query)
        return [];
      return await api.user.findUsers.GET({
        query,
      });
    }
  });

  private _renderSuggestions() {
    const people = this._peopleWithQuery.value;
    if (!people || !people.length)
      return nothing;
    return html`
      <sl-menu id=suggestions @sl-select=${async (event: SlSelectEvent) => {
          if (this._searchPeopleInput)
            this._searchPeopleInput.value = '';
          this.dispatchEvent(new UserSelectedEvent((event.detail.item as any).user as WireTypes.User));
      }}>
        ${people.map(user => html`
          <sl-menu-item value=${user.userId} .user=${user}>
            <sl-avatar slot=prefix image=${user?.avatarUrl} initials=${getInitials(user?.userName ?? '')}></sl-avatar>
            ${user.userName}
          </sl-menu-item>
        `)}
      </sl-menu>
    `
  }

  override render() {
    return html`
      <fk-autocomplete>
        <sl-input
            id=search-people
            size=medium
            placeholder=${this.placeholder ?? ''}
            slot=input
            autocomplete=off
            @sl-input=${(event: SlInputEvent) => this._searchQuery = (event.target as SlInput).value }
        ></sl-input>
        ${this._renderSuggestions()}
      </fk-autocomplete>
    `;
  }

  static styles = [css`
    #suggestions {
      sl-avatar {
        --size: 1.5rem;
      }
    }

    #general-access {
    }
  `]
}
