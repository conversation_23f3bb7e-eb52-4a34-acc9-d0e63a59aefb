import { LitElement, PropertyValueMap, css, html } from "lit";
import { customElement, property } from "lit/decorators.js";
import { Disposable, consumeDOMEvent, disposeAll, onDOMEvent } from "../utils.js";

@customElement('split-view')
export class SplitView extends LitElement {
  @property({ attribute: 'show-sidebar', type: Boolean }) showSidebar: boolean = false;
  @property({ attribute: 'sidebar-position' }) sidebarPosition: 'top'|'bottom'|'left'|'right' = 'left';
  @property({ type: Number }) size: number = 400;

  render() {
    return html`
      <div class=split-element sidebar-position=${this.sidebarPosition} ?show-sidebar=${this.showSidebar}>
        <div class=side-pane>
          <slot name=side-pane></slot>
        </div>
        <div class=resizer></div>
        <div class=main-pane>
          <slot name=main-pane></slot>
        </div>
      </div>
    `
  }

  toggleSidebar() {
    this.showSidebar = !this.showSidebar;
  }

  protected firstUpdated(_changedProperties: PropertyValueMap<any> | Map<PropertyKey, unknown>): void {
    this.registerResizer(this.renderRoot.querySelector('.resizer')!);
    this.style.setProperty('--side-pane-size', this.size + 'px');
  }

  registerResizer(resizerElement: HTMLElement) {
    const domEvents: Disposable[] = [];
    const axis = this.sidebarPosition === 'left' || this.sidebarPosition === 'right' ? 'pageX' : 'pageY';
    const coeff = this.sidebarPosition === 'bottom' || this.sidebarPosition === 'right' ? -1 : 1;
    let initialCoordinate = 0;

    const initialize = (event: MouseEvent) => {
      consumeDOMEvent(event);
      disposeAll(domEvents);
      initialCoordinate = event[axis];
      domEvents.push(
        onDOMEvent(document, 'mousemove', event => update(event, false /* commit */)),
        onDOMEvent(document, 'mouseup', event => update(event, true /* commit */)),
        onDOMEvent(document, 'mouseleave', event => update(event, true /* commit */)),
      );
    };

    const update = (event: MouseEvent, commit: boolean) => {
      consumeDOMEvent(event);
      const delta = (event[axis] - initialCoordinate) * coeff;
      this.style.setProperty('--side-pane-size', (this.size + delta) + 'px');
      if (!commit)
        return;

      if (delta)
        this.size += delta;
      disposeAll(domEvents);
      domEvents.push(onDOMEvent(resizerElement, 'mousedown', initialize));
    };

    domEvents.push(onDOMEvent(resizerElement, 'mousedown', initialize));
  }

  static styles = css`
    :host {
      display: block;
      width: 100%;
      height: 100%;
    }
    .split-element {
      box-sizing: border-box;
      display: flex;
      width: 100%;
      height: 100%;
      overflow: hidden;

      &[sidebar-position=left] { flex-direction: row; }
      &[sidebar-position=right] { flex-direction: row-reverse; }
      &[sidebar-position=top] { flex-direction: column; }
      &[sidebar-position="bottom"] { flex-direction: column-reverse; }

      & > .resizer {
        display: none;
        flex: none;
        background: var(--sl-color-neutral-200);
      }

      & > .main-pane {
        display: block;
        flex: auto;
        position: relative;
        contain: strict;
        overflow: auto;
      }
      
      & > .side-pane {
        display: none;
        flex: none;
        position: relative;
        contain: strict;
        overflow: auto;
      }

      &[show-sidebar] .resizer, &[show-sidebar] .side-pane {
        display: block;
      }

      /* horizontal */
      &[sidebar-position=left] .resizer, &[sidebar-position=right] .resizer {
        height: 100%;
        width: 5px;
        cursor: col-resize;
        
      }

      &[sidebar-position=left] .side-pane, &[sidebar-position=right] .side-pane {
        width: var(--side-pane-size);
        height: 100%;
      }
      /* vertical */
      &[sidebar-position=top] .resizer, &[sidebar-position=bottom] .resizer {
        width: 100%;
        height: 5px;
        cursor: row-resize;
      }

      &[sidebar-position=top] .side-pane, &[sidebar-position=bottom] .side-pane {
        width: 100%;
        height: var(--side-pane-size);
      }
    }
  `
}
