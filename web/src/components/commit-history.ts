import { FlakinessReport } from '@flakiness/sdk/browser';
import type { Stats } from '@flakiness/server/common/stats/stats.js';
import { TimelineSplit } from '@flakiness/server/common/timeline/timelineSplit.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { Task } from '@lit/task';
import { timeDay, timeFormat } from 'd3';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { Temporal } from 'temporal-polyfill';
import { api } from '../api.js';
import { LinkRenderer } from '../contexts.js';
import { assert, loadAllPages } from '../utils.js';
import { linkStyles } from './cssstyles.js';

const formatCommitHeader = timeFormat("Commits on %b %e, %Y");

const PAGE_SIZE = 20; // As github does this

@customElement('commit-history')
export class CommitHistory extends LitElement {
  @property({ attribute: false }) project?: WireTypes.Project;
  @property({ attribute: false }) head?: WireTypes.Ref;
  @property({ attribute: false }) since?: Date;
  @property({ attribute: false }) until?: Date;
  @property({ attribute: false }) testId?: Stats.TestId;
  @property({ attribute: false }) split?: TimelineSplit;
  @property({ attribute: false }) runlinker?: LinkRenderer<WireTypes.RunStats>;
  @property({ attribute: false }) selectedCommitId?: string;

  @state() private _page: number = 0;

  private _allCommitStatsTask = new Task(this, {
    args: () => [
      this.project, this.head, this.testId, this.split, this.since, this.until,
    ] as const,
    task: async ([project, headRef, testId, timelineSplit, since, until], { signal }) => {
      assert(project && headRef && timelineSplit && since && until);
      const commitStats: WireTypes.CommitStats[] = await loadAllPages(pageOptions => api.history.commitStats.POST({
        commitOptions: {
          head: headRef.commit.commitId,
          sinceTimestamp: since.getTime() as FlakinessReport.UnixTimestampMS,
          untilTimestamp: until.getTime() as FlakinessReport.UnixTimestampMS,
        },
        orgSlug: project.org.orgSlug,
        projectSlug: project.projectSlug,
        timelineSplit: timelineSplit.serialize(),
        testId,
        timeZoneId: Temporal.Now.timeZoneId(),
        regressionWindowDays: project.regressionWindowDays,
        pageOptions: pageOptions,
      }, { signal }));
      return commitStats;
    }
  });

  private _renderCommitStatsList(allCommitStats?: WireTypes.CommitStats[]) {
    if (!allCommitStats)
      return nothing;

    const index = allCommitStats.findIndex(stats => stats.commit.commitId === this.selectedCommitId);
    if (index !== -1)
      allCommitStats = allCommitStats.slice(index);
    const selectedCommitId = index === -1 ? allCommitStats.at(0)?.commit.commitId : this.selectedCommitId;
    const commitsToRender = 20 * (this._page + 1);
    const hasMore = allCommitStats.length > commitsToRender;
    allCommitStats = allCommitStats.slice(0, commitsToRender);

    const baselines = new Map<Stats.CommitId, number>();

    let lastDuration: number|undefined;
    for (const c of allCommitStats.toReversed()) {
      if (c.durationMs === undefined)
        continue;
      if (lastDuration)
        baselines.set(c.commit.commitId, lastDuration);
      lastDuration = c.durationMs;
    }

    const days: {
      day: Date,
      commitStats: WireTypes.CommitStats[],
    }[] = [];
    for (const commitStat of allCommitStats) {
      const day = timeDay.floor(new Date(commitStat.commit.timestamp));
      if (!days.length || +days.at(-1)!.day !== +day)
        days.push({ day, commitStats: []});
      days.at(-1)!.commitStats.push(commitStat);
    }
    return html`
      <section class=all-days-list style="position: relative;">
        ${days.map(({ day, commitStats }) => html`
          <h-box class=day-title>
            <sl-icon library=boxicons name='bx-git-commit'></sl-icon>
            <div class=day-date>${formatCommitHeader(day)}</div>
          </h-box>
          <div class=day-section>
            <div class=divider>
              <div class=line></div>
            </div>
            <div class=day-commits>
              ${commitStats.map(stats => this._renderCommitStats(stats, selectedCommitId, baselines.get(stats.commit.commitId)))}
            </div>
          </div>
        `)}
        ${hasMore ? html`
          <div class=previous-commits>
            <sl-button size=large @click=${() => ++this._page}>Show More</sl-button>
          </div>
        ` : nothing}
      </section>
    `;
  }

  private _renderCommitStats(commitStats: WireTypes.CommitStats, selectedCommitId?: string, baseline?: number) {
    return html`
      <h-box class=${classMap({
        'commit-stats': true,
        regressed: commitStats.testStats.regressed > 0,
      })} style="gap: var(--sl-spacing-large);"> 
        <runstats-selector
          size=small
          .runs=${commitStats.runs}
          .runlinker=${this.runlinker}
        ></runstats-selector>

        <fk-commit .project=${this.project} .commit=${commitStats.commit}></fk-commit>

        <outcomes-bar .counts=${commitStats.testStats}></outcomes-bar>
        ${commitStats.durationMs === undefined ? nothing :
          baseline ?
            html`<fk-trend .ms=${commitStats.durationMs} .baseline=${baseline} duration></fk-trend>` :
            html`<time-interval .ms=${commitStats.durationMs}></time-interval>`
        }
      </h-box>
      ${commitStats.commit.commitId === selectedCommitId ? html`<div class=head-label>FROM →</div>` : nothing}
    `
  }

  override render() {
    const commitStats = this._allCommitStatsTask.value;
    if (!commitStats || !commitStats.length) {
      return html`
        <h1 style="color: var(--fk-color-border);">No Commits</h1>
      `
    }
    const hasCommitId = this.selectedCommitId && commitStats.some(stats => stats.commit.commitId === this.selectedCommitId);
    return html`
      <v-box>
        <commits-minimap
          .commitStats=${commitStats.toReversed()}
          .selectedCommitId=${hasCommitId ? this.selectedCommitId : commitStats.at(0)?.commit.commitId}
          @fk-select=${(e: CustomEvent<WireTypes.CommitStats>) => {
            this.selectedCommitId = e.detail.commit.commitId;
            this._page = 0;
          }}
        >
          <div slot=header>
            Tested ${commitStats.filter(x => x.runs.length > 0).length} out of ${commitStats.length} commits
          </div>
        </commits-minimap>
        ${this._renderCommitStatsList(commitStats)}
      </v-box>
    `;
  }

  static styles = [linkStyles, css`
    * {
      box-sizing: border-box;
    }

    /** define grid structure */
    .all-days-list {
      display: grid;
      grid-template-columns: auto auto 1fr auto auto auto;

      .day-title { grid-column: 1/-1; }

      .day-section {
        grid-column: 1/-1;
        display: grid;
        grid-template-columns: subgrid;

        .divider { grid-column: 1; }

        .day-commits {
          grid-column: 2/-1;
          display: grid;
          grid-template-columns: subgrid;

          .commit-stats {
            grid-column: 1/-2;
            display: grid;
            grid-template-columns: subgrid;

            runstats-selector { grid-column: 1/2; justify-self: center; }
            fk-commit { grid-column: 2/3; }
            outcomes-bar { grid-column: 3/4; justify-self: stretch; }
            fk-trend, time-duration { grid-column: 4/5; justify-self: end; }
          }
          
          .head-label { grid-column: -2/-1; }
        }
      }
    }

    commits-minimap {
      background: white;
      z-index: 100;
    }

    fk-trend, time-duration {
      justify-self: end;
    }

    .commit-stats {
      padding: var(--sl-spacing-small) var(--sl-spacing-medium);
      border-top: 1px solid var(--fk-color-border);
      border-right: 1px solid var(--fk-color-border);
      border-left: 1px solid var(--fk-color-border);

      &:first-of-type {
        border-top-left-radius: var(--sl-border-radius-large);
      }

      &:last-of-type {
        border-bottom-left-radius: var(--sl-border-radius-large);
        border-bottom-right-radius: var(--sl-border-radius-large);
        border-bottom: 1px solid var(--fk-color-border);
      }


      &.regressed {
        background-color: color-mix(in oklch, var(--fk-color-outcome-regressed), white 90%);
      }
      &.unexpected {
        background-color: color-mix(in oklch, var(--fk-color-outcome-unexpected), white 90%);
      }
    }

    .day-title {
      color: var(--sl-color-neutral-500);
      margin: var(--sl-spacing-large) 0;
    }

    .day-commits {
    }

    .head-label {
      writing-mode: sideways-rl;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--sl-color-neutral-700);
      color: white;
      font-size: var(--sl-font-size-x-small);
      font-weight: var(--sl-font-weight-bold);
      padding: var(--sl-spacing-2x-small);
      border-top-right-radius: var(--sl-border-radius-medium);
      border-bottom-right-radius: var(--sl-border-radius-medium);
    }

    .previous-commits {
      pointer-events: none;
      display: flex;
      align-items: end;
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: 200px;
      background: linear-gradient(
        to bottom,
        oklch(100% 0 0 / 0) 0%,
        oklch(100% 0 0 / 1) 75%,
        oklch(100% 0 0 / 1) 100%
      );

      sl-button {
        pointer-events: auto;
        flex: auto;
      }
    }

    .divider {
      align-self: stretch;
      width: 14px;
      display: flex;
      align-items: stretch;
      justify-content: center;
      margin-right: var(--sl-spacing-medium);

      .line {
        width: 2px;
        --spill-top: var(--sl-spacing-small);
        --spill-bottom: var(--sl-spacing-small);
        margin-top: calc(0px - var(--spill-top));
        height: calc(100% + var(--spill-top) + var(--spill-bottom));
        background: color-mix(in srgb, var(--sl-color-neutral-100) 50%, var(--sl-color-neutral-200) 50%);
      }
    }

    .day-section {
      &:last-of-type .line {
        --spill-bottom: 0;
      }
    }
  `];
}
