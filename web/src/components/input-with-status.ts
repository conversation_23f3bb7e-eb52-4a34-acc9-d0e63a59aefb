import { SlInput } from '@shoelace-style/shoelace';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property, query, state } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { linkStyles } from './cssstyles.js';

@customElement('input-with-status')
export class InputWithStatus extends LitElement {

  @property({}) name?: string;
  @property({}) placeholder?: string;
  @property({}) autocomplete?: string;
  @property({}) initial?: string;
  @property({}) type?: string;
  @property({ type: Boolean }) disabled?: boolean;

  @property({ type: String }) prefixText?: string;

  @query('sl-input') private _input?: SlInput;

  @state() private _status?: {
    success: boolean,
    message: string,
  };

  setStatus(success: boolean, message: string) {
    this._status = { success, message };
    this._input?.setCustomValidity(success ? '' : message);
  }

  value(): string {
    return this._input?.value ?? '';
  }

  message(): string | undefined {
    return this._status?.message;
  }

  success(): boolean {
    return this._status?.success ?? false;
  }

  input() {
    return this._input;
  }

  override render() {
    const value = this._status;
    return html`
      <section>
        <h-box style="gap: 0; flex: auto;">
          ${this.prefixText ? html`
            <div class=prefix-text>${this.prefixText}</div>
          `: nothing}
          <sl-input
            style="flex: auto;"
            class=${classMap({
              prefixed: !!this.prefixText,
            })}
            ?disabled=${this.disabled}
            autocomplete=${this.autocomplete}
            type=${this.type}
            placeholder=${this.placeholder}
            name=${this.name}
            value=${this.initial}
          >
            <slot name=prefix slot=prefix></slot>
          </sl-input>
        </h-box>
        <div>
          <slot></slot>
        </div>
        ${
          value?.success === false ? html`
            <div class="validation-message failed">
              <sl-icon name=exclamation-triangle-fill></sl-icon> ${value.message}
            </div>
          ` : value?.success ? html`
            <div class="validation-message ok">
              <sl-icon name=check-circle-fill></sl-icon> ${value.message}
            </div>
          ` : html`
            <div class="validation-message invisible">
              <sl-icon name=check-circle-fill></sl-icon>
            </div>
          `
        }
      </section>
    `;
  }

  static styles = [linkStyles, css`
    section {
      margin-top: var(--sl-spacing-large);
      display: grid;
      gap: 0 var(--sl-spacing-small);
      grid-template-columns: 1fr auto;
      grid-template-rows: auto auto;
      align-content: start;
      justify-content: start;
    }

    .validation-message {
      display: flex;
      align-items: center;
      gap: var(--sl-spacing-x-small);

      margin-top: var(--sl-spacing-small);
      font-size: var(--sl-font-size-small);
      line-height: var(--sl-line-height-denser);
      color: var(--sl-color-neutral-400);

      &.failed { 
        color: var(--sl-color-danger-600);
      }

      &.ok { 
        color: var(--sl-color-success-600);
      }

      &.invisible {
        visibility: hidden;
      }
    }

    sl-input[data-user-invalid]::part(base) {
      border-color: var(--sl-color-danger-600);
    }

    sl-input:focus-within[data-user-invalid]::part(base) {
      border-color: var(--sl-color-danger-600);
      box-shadow: 0 0 0 var(--sl-focus-ring-width) var(--sl-color-danger-300);
    }

    .prefix-text {
      align-self: center;
      height: var(--sl-input-height-medium);
      display: flex;
      align-items: center;
      box-sizing: border-box;
      border: solid var(--sl-input-border-width) var(--sl-input-border-color);
      border-radius: var(--sl-input-border-radius-medium);
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      padding-left: var(--sl-spacing-medium);
      padding-right: var(--sl-spacing-x-small);
      border-right: 0;
      background-color: var(--sl-color-neutral-100);
      color: var(--sl-color-neutral-500);
    }

    .prefixed::part(input) {
      padding-left: var(--sl-spacing-x-small);
    }

    .prefixed::part(base) {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  `];
}