import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { linkStyles } from './cssstyles.js';
import { Artifact } from './test-run.js';

@customElement('artifact-link')
class ArtifactLink extends LitElement {
  @property({ attribute: false }) artifact?: Artifact;

  override render() {
    const artifact = this.artifact;
    if (!artifact)
      return nothing;
    return html`
      <a href=${artifact.url} download=${artifact.fileName}>
        <sl-icon name=file-earmark-zip></sl-icon>
        <span>${artifact.fileName}</span>
      </a>
    `;
  }

  static styles = [linkStyles, css`
    a {
      display: flex;
      gap: var(--sl-spacing-2x-small);
      align-items: center;
      font-size: var(--sl-font-size-small);

      span {
        color: #00E !important;
        text-decoration: underline;
      }
    }
  `];
}