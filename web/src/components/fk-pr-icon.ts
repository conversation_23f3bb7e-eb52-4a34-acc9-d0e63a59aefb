import type { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { linkStyles } from './cssstyles.js';

@customElement('fk-pr-icon')
class FKPullRequestIcon extends LitElement {
  @property({ attribute: false }) pr?: WireTypes.PullRequest;

  override render() {
    const pr = this.pr;
    return html`
      <sl-icon name=${pr ? "bx-git-pull-request" : nothing} library=boxicons class=${classMap({
        open: pr?.state === 'open',
        closed: pr?.state === 'closed',
        merged: pr?.state === 'merged',
      })}></sl-icon>
    `;
  }

  static styles = [linkStyles, css`
    sl-icon {
      display: block;
    }

    .open {
      color: green;
    }

    .merged {
      color: #9c27b0;
    }

    .closed {
      color: red;
    }
  `];
}
