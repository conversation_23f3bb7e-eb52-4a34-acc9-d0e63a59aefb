import { FlakinessReport } from '@flakiness/sdk/browser';
import { Timeline } from '@flakiness/server/common/timeline/timeline.js';
import { TimelineSplit } from '@flakiness/server/common/timeline/timelineSplit.js';
import { wireOutcomesToOutcome, WireTypes } from '@flakiness/server/common/wireTypes.js';
import { Multimap } from '@flakiness/shared/common/multimap.js';
import { consume, ContextType } from '@lit/context';
import { Task } from '@lit/task';
import { SlDialog } from '@shoelace-style/shoelace';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, query, queryAll, state } from 'lit/decorators.js';
import { Temporal } from 'temporal-polyfill';
import { api } from '../api.js';
import { contexts } from '../contexts.js';
import { PageRun } from '../page-run.js';
import { tasks } from '../tasks.js';
import { assert, consumeDOMEvent, loadAllPages, renderDatesRange } from '../utils.js';
import { linkStyles } from './cssstyles.js';
import { DurationsChart } from './durations-chart.js';
import { FKCommitStats } from './fk-commitstats.js';
import { FKHeadSelectEvent } from './head-selector.js';


@customElement('execution-history')
export class ExecutionHistory extends LitElement {
  @state() private _head?: WireTypes.Ref;
  @state() private _timeline?: Timeline;

  @state() private _since?: Date;
  @state() private _until?: Date;

  @state() private _testStats?: WireTypes.TestStats;

  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;
  @consume({ context: contexts.projectReferences, subscribe: true }) private _branches?: ContextType<typeof contexts.projectReferences>;
  @consume({ context: contexts.router, subscribe: true }) private _router?: ContextType<typeof contexts.router>;

  @query('sl-dialog') private _dialog?: SlDialog;

  private _runLinker = new Task(this, {
    args: () => [this._project, this._timeline] as const,
    task: async ([project, timeline], { signal }) => {
      assert(project && timeline);
      return {
        render: (runStats: WireTypes.RunStats) => PageRun.url({
          orgSlug: project.org.orgSlug,
          projectSlug: project.projectSlug,
          reportId: runStats.run.runId,
          timelineSplit: TimelineSplit.fromTimeline(timeline),
          testId: this._testStats ? this._testStats.test.testId : undefined,
          testTimeline: this._testStats ? Timeline.deserialize(this._testStats.timeline) : undefined,
        }),
      }
    }
  });

  private _allCommitStatsTask = new Task(this, {
    args: () => [
      this._project, this._testStats, this._timeline, this._since?.getTime(), this._until?.getTime(), this._head
    ] as const,
    task: async ([project, testStats, timeline, since, until, head], { signal }) => {
      assert(project && timeline && since && until && head);
      const commitStats: WireTypes.CommitStats[] = await loadAllPages(pageOptions => api.history.commitStats.POST({
        commitOptions: {
          head: head.commit.commitId,
          sinceTimestamp: since as FlakinessReport.UnixTimestampMS,
          untilTimestamp: until as FlakinessReport.UnixTimestampMS,
        },
        orgSlug: project.org.orgSlug,
        projectSlug: project.projectSlug,
        timelineSplit: TimelineSplit.fromTimeline(timeline).serialize(),
        testId: testStats?.test.testId,
        timeZoneId: Temporal.Now.timeZoneId(),
        regressionWindowDays: project.regressionWindowDays,
        pageOptions: pageOptions,
      }, { signal }));
      return commitStats;
    }
  });

  private _allEnvironments = tasks.allEnvironments(this, {
    commitOptions: () => this._head && this._since && this._until ? {
      head: this._head.commit.commitId,
      sinceTimestamp: +this._since as FlakinessReport.UnixTimestampMS,
      untilTimestamp: +this._until as FlakinessReport.UnixTimestampMS,
    } : undefined,
    testId: () => this._testStats?.test.testId,
    project: () => this._project,
  });

  show(options: {
    testStats?: WireTypes.TestStats,
    since: Date,
    until: Date,
    timeline: Timeline,
    head: WireTypes.Ref,
  }) {
    this._timeline = options.timeline;
    this._testStats = options.testStats;
    this._head = options.head;
    this._since = options.since;
    this._until = options.until;
    this._dialog?.show();
  }

  hide() {
    this._dialog?.hide();
  }

  private _renderExecutionHistory() {
    const testStats = this._testStats;
    const project = this._project;
    const timeline = this._timeline;
    const since = this._since;
    const until = this._until;
    const commit = this._head?.commit;
    if (!project || timeline === undefined || !commit || !since || !until)
      return nothing;

    return html`
      <v-box style="height: 100%;">
        <h-box style="align-items: start;">
          <v-box>
            <teststats-title .project=${project} .testStats=${testStats}></teststats-title>
            <h-box>
              <head-selector
                .head=${this._head}
                @fk-select=${(event: FKHeadSelectEvent) => {
                  this._head = event.head;
                }}
              ></head-selector>
              <timeline-selector
                .envs=${this._allEnvironments.value}
                .split=${TimelineSplit.fromTimeline(timeline)}
                @fk-select=${(event: CustomEvent<TimelineSplit>) => {
                  const timelines = event.detail.timelines(this._allEnvironments.value ?? [])
                  if (timelines.length === 1)
                    this._timeline = timelines[0];
                }}
              ></timeline-selector>
            </h-box>
          </v-box>
          <x-filler></x-filler>
          <execution-calendar
            .head=${this._head}
            .testId=${this._testStats?.test.testId}
            .split=${this._timeline ? TimelineSplit.fromTimeline(this._timeline) : undefined}
            .until=${this._until}
            .since=${this._since}
            @rangeselected=${(event: CustomEvent<{ since: Date, until: Date }>) => {
              this._since = event.detail.since;
              this._until = event.detail.until;
            }}
          ></execution-calendar>
        </h-box>

        <div class=day-date>Commits from ${renderDatesRange(since, until)}</div>
        ${DurationsChart.render({
          data: this._allCommitStatsTask.value?.map(s => ({
            outcome: wireOutcomesToOutcome(s.testStats, this._project?.acceptableFlakinessRatio ?? 0) ?? 'untested' as const,
            duration: s.runs.length ? s.durationMs : undefined,
            stats: s,
          })).toReversed(),
          click: (event) => {
            this._revealCommitStats(event.detail.stats);
            consumeDOMEvent(event);
          },
          style: {
            width: `min(${(this._allCommitStatsTask.value?.length ?? 0) * 10}px, 100%)`,
            height: '50px',
            flex: 'none',
          },
        })}
        
        <h-box style="gap: var(--sl-spacing-large);">
          <h-box>
            <sl-icon library=boxicons name='bx-git-commit'></sl-icon>
            
          </h-box>

          ${this._renderQuickSelector(this._allCommitStatsTask.value)}
        </h-box>

        ${this._renderCommitStats(this._allCommitStatsTask.value)}
      </v-box>
    `
  }

  @queryAll('fk-commitstats') private _renderedCommitStats?: NodeListOf<FKCommitStats>;
  private _lastRevealed?: WireTypes.CommitStats;

  private _renderQuickSelector(commitStats?: WireTypes.CommitStats[]) {
    const outcomeToStats = new Multimap((commitStats ?? []).map(stats => [wireOutcomesToOutcome(stats.testStats, this._project?.acceptableFlakinessRatio ?? 0), stats]));
    return html`
      <sl-button-group @mouseleave=${() => this._lastRevealed = undefined}>
        ${this._renderQuickSelectorButton('new failures', outcomeToStats.getAll('regressed'))}
        ${this._renderQuickSelectorButton('failed', outcomeToStats.getAll('unexpected'))}
        ${this._renderQuickSelectorButton('flaked', outcomeToStats.getAll('flaked'))}
        ${this._renderQuickSelectorButton('passed', outcomeToStats.getAll('expected'))}
        ${this._renderQuickSelectorButton('skipped', outcomeToStats.getAll('skipped'))}
      </sl-button-group>
    `;
  }

  private _renderQuickSelectorButton(title: string, stats: WireTypes.CommitStats[]) {
    return html`
      <sl-button @click=${() => this._revealNextCommitStats(stats)} size=small ?disabled=${stats.length === 0}>${stats.length} ${title}</sl-button>
    `;
  }

  private _revealCommitStats(stats: WireTypes.CommitStats) {
    if (!this._renderedCommitStats)
      return;
    const toReveal = [...this._renderedCommitStats].find(element => element.stats === stats);
    toReveal?.scrollIntoView({ behavior: 'instant' });
    toReveal?.animate([
      { backgroundColor: 'var(--sl-color-warning-100)' },
      { backgroundColor: 'transparent' }
    ], {
      duration: 1000,
      iterations: 1,
      easing: 'ease-in-out'
    });
  }

  private _revealNextCommitStats(stats: WireTypes.CommitStats[]) {
    if (!this._renderedCommitStats)
      return;
    const fromIndex = this._lastRevealed ? stats.findIndex(x => x === this._lastRevealed) : -1;
    const prev = fromIndex <= 0 ? stats.length - 1 : fromIndex - 1;
    this._lastRevealed = stats[prev];
    const toReveal = [...this._renderedCommitStats].find(element => element.stats === this._lastRevealed);
    toReveal?.scrollIntoView({ behavior: 'instant' });
    toReveal?.animate([
      { backgroundColor: 'var(--sl-color-warning-100)' },
      { backgroundColor: 'transparent' }
    ], {
      duration: 1000,
      iterations: 1,
      easing: 'ease-in-out'
    });
  }

  private _renderCommitStats(commitStats?: WireTypes.CommitStats[]) {
    if (!commitStats)
      return nothing;

    return html`
      <div class=day-commits>
        ${commitStats.map(stats => html`
          <fk-commitstats
            .stats=${stats}
            .project=${this._project}
            .runlinker=${this._runLinker.value}
          ></fk-commitstats>
        `)}
      </div>
    `;
  }

  render() {
    return html`
      <sl-dialog>${this._renderExecutionHistory()}</sl-dialog>
    `;
  }

  static styles = [linkStyles, css`
    sl-dialog {
      --width: 80vw;
    }

    fk-commitstats {
      grid-column: 1/-1;
      border: none;
      border-radius: 0;
    }

    fk-commitstats + fk-commitstats {
      border-top: 1px solid var(--fk-color-border);
    }

    .day-date {
      font-size: var(--sl-font-size-large);
    }

    .day-commits {
      border: 1px solid var(--fk-color-border);
      border-radius: var(--sl-border-radius-large);
      overflow: auto;
    }

    sl-dialog::part(header) {
      display: none;
    }

    sl-dialog::part(panel) {
      height: 100vh;
    }
  `];
}
