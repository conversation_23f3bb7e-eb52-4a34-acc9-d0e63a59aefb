import { css, html, LitElement } from 'lit';
import { customElement, property } from 'lit/decorators.js';

@customElement('split-circle')
export class SplitCircle extends LitElement {
  @property({ type: Boolean }) split?: boolean;

  render() {
    return html`
      <h-box>
        ${this.split ? html`
          <span class=half-circle-left></span>
          <span class=half-circle-right></span>
        ` : html`
          <span class=full-circle></span>
        `}
      </h-box>
    `;
  }

  static styles = css`
    h-box {
      gap: 0;
    }

    .half-circle-right {
      box-sizing: border-box;
      display: inline-block;
      width: 0.5em;
      height: 1em;
      border: 1px solid var(--sl-color-neutral-700);
      background-color: var(--sl-color-neutral-100);
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0.25em;
      border-top-right-radius: 0.25em;
      
      margin-left: 1px;
    }

    .half-circle-left {
      box-sizing: border-box;
      display: inline-block;
      width: 0.5em;
      height: 1em;
      border: 1px solid var(--sl-color-neutral-700);
      background-color: var(--sl-color-neutral-100);
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      border-bottom-left-radius: 0.25em;
      border-top-left-radius: 0.25em;

      margin-right: 1px;
    }

    .full-circle {
      box-sizing: border-box;
      display: inline-block;
      width: 1em;
      height: 1em;
      border: 1px solid black;
      border-radius: 1em;

      margin: 0 1px;
    }

  `;
}
