import { SlDialog } from '@shoelace-style/shoelace';
import { LitElement, TemplateResult, css, html } from 'lit';
import { customElement, property, query } from 'lit/decorators.js';
import { linkStyles } from './cssstyles.js';

function renderPrimitiveValue(value: any) {
  if (typeof value === 'string')
    return html`<span class=value-string>${JSON.stringify(value)}</span>`;
  if (typeof value === 'number')
    return html`<span class=value-number>${value}</span>`;
  if (typeof value === 'boolean')
    return html`<span class=value-bool>${value}</span>`;
  if (value === null)
    return html`<span class=value-null>null</span>`;
  if (value === undefined)
    return html`<span class=value-undef>undefined</span>`;
  if (typeof value === 'object')
    return renderObject(value);
  return html`<span>${value}</span>`
}

function renderObject(obj: any): TemplateResult<1>[] {
  return Object.entries(obj).map(([key, value]) => {
    if (typeof value !== 'object') {
      return html`
        <h-box class=row>
          <sl-icon style="visibility: hidden;"></sl-icon>
          <div>
            <span class=key>${key}</span><span class=separator>: </span>${renderPrimitiveValue(value)}
          </div>
        </h-box>
      `;
    }
    return html`
      <h-box class=row>
        <sl-icon name=chevron-down></sl-icon><span class=key>${key}</span>
      </h-box>
      <div style="margin-left: var(--sl-spacing-large);">
        ${renderObject(value)}
      </div>
    `
  });
}

@customElement('json-viewer')
class JSONViewer extends LitElement {
  @property({ attribute: false }) json?: any;

  @query('sl-dialog') private _dialog?: SlDialog;

  render() {
    return html`
      <sl-dialog label="JSON Viewer">
        ${renderObject(this.json)}
      </sl-dialog>
      <button class=header @click=${() => { this._dialog?.show(); } }>json</button>
    `;
  }

  static styles = [linkStyles, css`
    sl-dialog {
      --width: 50vw;
    }

    .row {
      gap: var(--sl-spacing-2x-small);
      line-height: var(--sl-line-height-normal);
    }

    .key {
      color: rgb(142, 0, 75);
      font-weight: bold;
    }

    .value-string {
      color: rgb(220, 54, 46);
      white-space: pre;
    }

    .value-bool {
      color: rgb(8, 66, 160);
    }

    .value-number {
      color: rgb(8, 66, 160);
    }

    sl-tree-item::part(base) {
      user-select: auto;
    }

    .separator {
      font-weight: bold;
    }

    .header {
      appearance: none;
      border: none;
      background: none;
      margin: 0px;
      padding: 0px;
      text-decoration: underline dashed;
      color: var(--sl-color-neutral-400) !important;

      &:hover {
        text-decoration: underline solid;
        cursor: pointer;
        color: var(--sl-color-neutral-900) !important;
      }
    }
  `];
}
