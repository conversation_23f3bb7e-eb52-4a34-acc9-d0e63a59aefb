import { Matcher } from '@flakiness/server/common/fql/matcher.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { ContextType, consume } from '@lit/context';
import { LitElement, css, html, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { contexts } from '../contexts.js';

@customElement('report-outcomes')
class ReportOutcomes extends LitElement {
  @property({ attribute: false }) outcomes?: WireTypes.OutcomesCount;
  @property({ type: <PERSON><PERSON><PERSON>, attribute: 'hide-regressions' }) hideRegressions?: boolean;

  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;
  @consume({ context: contexts.fql, subscribe: true }) private _fql?: ContextType<typeof contexts.fql>;
  @consume({ context: contexts.linkFQL, subscribe: true }) private _link?: ContextType<typeof contexts.linkFQL>;

  private _renderRate(name: string, from: number, total: number) {
    const rate = total === 0 ? 0 : Math.round(from / total * 10000) / 100;
    return html`${rate}% ${name} rate (${from}/${total})`;
  }

  override render() {
    const {
      regressed = 0,
      unexpected = 0,
      expected = 0,
      skipped = 0,
      flaked = 0
    } = this.outcomes ?? {};
    const total = regressed + expected + unexpected + skipped + flaked;

    const fql = this._fql;
    const link = this._link;
    
    function statusLink(outcome: WireTypes.Outcome) {
      if (!fql || !link)
        return nothing;
      const q = fql.isMatchingFilter(Matcher.outcomes[outcome]) ? fql.clearStatusFilters() : fql.clearStatusFilters().toggleFilter(Matcher.outcomes[outcome]);
      return link.render(q.serialize());
    }

    const isUnexpected = fql && fql.hasStatusFilters() && fql.acceptsOutcome('unexpected');
    const isExpected = fql && fql.hasStatusFilters() && fql.acceptsOutcome('expected');
    const isSkipped = fql && fql.hasStatusFilters() && fql.acceptsOutcome('skipped');
    const isFlaked = fql && fql.hasStatusFilters() && fql.acceptsOutcome('flaked');
    const isRegressed = fql && fql.hasStatusFilters() && fql.acceptsOutcome('regressed');

    const flakinessRate = flaked / total;
    const acceptableFlakiness = (this._project?.acceptableFlakinessRatio ?? 0);
    const hasUnacceptableFlakiness = flakinessRate > acceptableFlakiness;
    const regressionWindow = this._project?.regressionWindowDays ?? 0;
    return html`
      <h-box id=content class=${classMap({
        regressed: regressed > 0,
        failed: unexpected > 0,
        flaked: hasUnacceptableFlakiness,
        passed: expected > 0 || flaked > 0,
        skipped: true,
      })}>
        <a class=chip ?selected=${isExpected} href=${statusLink('expected')}>
          <div ?empty=${!expected} class=pass-title>Passed</div>
          <div
            class=pass-value
            ?empty=${!expected}
            ?no-color=${unexpected > 0 || hasUnacceptableFlakiness }
          >${expected}</div>
          <div ?empty=${!expected} class=pass-hint>${this._renderRate('pass', expected, total)}</div>
        </a>
        ${this.hideRegressions ? nothing : html`
          <a class=chip ?selected=${isRegressed} href=${statusLink('regressed')}>
            <h-box style="gap: var(--sl-spacing-x-small)" ?empty=${!regressed} class=regressed-title>
              New Failures
              <sl-tooltip>
                <div slot=content>
                  ${regressed === 1 ? html`
                    <p><b>One test</b> failed for the first time in <b>last ${regressionWindow} days</b>.</p>
                  ` : regressed > 1 ? html`
                    <p><b>${regressed} tests</b> failed for the first time in <b>last ${regressionWindow} days</b>.</p>
                  ` : html`
                    <p>No tests failed for the first time over the <b>last ${regressionWindow} days</b>.</p>
                  `}
                  <p>Project's regression detection window can be adjusted in project settings.</p>
                </div>
                <sl-icon name=info-circle></sl-icon>
              </sl-tooltip>
            </h-box>
            <div
              class=regressed-value
              ?empty=${!regressed}
            >${regressed}</div>
            <div ?empty=${!regressed} class=regressed-hint>${this._renderRate('regressed', regressed, total)}</div>
          </a>
        `}
        <a class=chip ?selected=${isUnexpected} href=${statusLink('unexpected')}>
          <h-box style="gap: var(--sl-spacing-x-small)" ?empty=${!unexpected} class=fail-title>
            Failed
            <sl-tooltip>
              <div slot=content>
                ${unexpected === 1 ? html`
                  <p><b>One test</b> failed, but it had other failures in the previous <b>${regressionWindow} days</b>.</p>
                ` : unexpected > 1 ? html`
                  <p><b>${unexpected} tests</b> failed, but they had other failures in the previous <b>${regressionWindow} days</b>.</p>
                ` : html`
                  <p>No test failures.</p>
                `}
                <p>Project's regression detection window can be adjusted in project settings.</p>
              </div>
              <sl-icon name=info-circle></sl-icon>
            </sl-tooltip>
          </h-box>
          <div
            class=fail-value
            ?empty=${!unexpected}
          >${unexpected}</div>
          <div ?empty=${!unexpected} class=fail-hint>${this._renderRate('failure', unexpected, total)}</div>
        </a>
        <a class=chip ?selected=${isFlaked} href=${statusLink('flaked')}>
          <h-box style="gap: var(--sl-spacing-x-small)" ?empty=${!flaked} class=flake-title>
            Flaked
            <sl-tooltip>
              <div slot=content>
                <p>Flakiness rate of <b>${(flakinessRate * 100).toFixed(2)}%</b> ${hasUnacceptableFlakiness ? `is over` : `is under`} the acceptable flakiness rate of <b>${(acceptableFlakiness * 100).toFixed(2)}%</b></p>
                <p>Project's acceptable flakiness rate can be adjusted in project settings.</p>
              </div>
              <sl-icon name=info-circle></sl-icon>
            </sl-tooltip>
          </h-box>
          <div
            ?no-color=${!hasUnacceptableFlakiness}
            class=flake-value
            ?empty=${!flaked}
          >${flaked}</div>
          <div ?empty=${!flaked} class=flake-hint>${this._renderRate('flaked', flaked, total)}</div>
        </a>
        <a class=chip ?selected=${isSkipped} href=${statusLink('skipped')}>
          <div ?empty=${!skipped} class=skip-title>Skipped</div>
          <div
            class=skip-value
            ?empty=${!skipped}
          >${skipped}</div>
          <div ?empty=${!skipped} class=skip-hint>${this._renderRate('skipped', skipped, total)}</div>
        </a>
      </h-box>
    `;
  }

  static styles = [css`
    :host {
      display: block;
    }

    #content {
      padding: var(--sl-spacing-medium);
      border-radius: var(--sl-border-radius-medium);
      box-shadow: 0px 0px var(--sl-border-radius-medium) 0 var(--color);
      background-color: color-mix(in oklch, var(--color), white 90%);

      &.skipped { --color: var(--fk-color-outcome-skipped); }
      &.passed { --color: var(--fk-color-outcome-passed); }
      &.flaked { --color: var(--fk-color-outcome-flaked); }
      &.failed { --color: var(--fk-color-outcome-failed); }
      &.regressed { --color: var(--fk-color-outcome-regressed); }

    }

    a {
      text-decoration: none;

      &[href]:hover {
        text-decoration: none;
      }

      &:visited, &:link {
        color: inherit;
      }
    }

    .chip {
      padding: var(--sl-spacing-2x-small);
      display: flex;
      flex-direction: column;
      gap: var(--sl-spacing-3x-small);
    }

    .pass-title { }
    .pass-value {
      color: var(--fk-color-outcome-expected);
    }
    .pass-hint { }

    .regressed-title { }
    .regressed-value { color: var(--fk-color-outcome-regressed); }
    .regressed-hint { }

    .fail-title { }
    .fail-value { color: var(--fk-color-outcome-unexpected); }
    .fail-hint { }

    .skip-title { }
    .skip-value { color: color-mix(in oklch, var(--fk-color-outcome-skipped) 50%, black 50%); }
    .skip-hint { }

    .flake-title { }
    .flake-value { color: var(--fk-color-outcome-flaked); }
    .flake-hint { }

    [no-color] {
      color: unset;
    }

    [empty] {
      color: var(--sl-color-neutral-200) !important;
    }

    [selected] {
      border-radius: var(--sl-border-radius-small);
      background-color: color-mix(in oklch, yellow , white 70%);
    }

    .pass-title, .fail-title, .skip-title, .flake-title, .regressed-title {
      font-size: var(--sl-font-size-medium);
      font-weight: var(--sl-font-weight-semibold);
    }

    .pass-value, .fail-value, .skip-value, .flake-value, .regressed-value {
      font-size: var(--sl-font-size-2x-large);
    }

    .pass-hint, .fail-hint, .skip-hint, .flake-hint, .regressed-hint {
      font-size: var(--sl-font-size-small);
      color: var(--sl-color-neutral-500);
    }
  `];
}
