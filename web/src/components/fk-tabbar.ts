import { css, html, LitElement, nothing } from 'lit';
import { customElement, property, query } from 'lit/decorators.js';
import { linkStyles } from './cssstyles.js';

@customElement('fk-tabbar')
export class FKTabbar extends LitElement {

  @query('slot[name=fk-tabbar-nav]') private _navSlot?: HTMLSlotElement;
  @query('slot:not([name=fk-tabbar-nav], [name=suffix])') private _contentSlot?: HTMLSlotElement;
  @property({ type: String, reflect: true }) selected?: string;
  @property({ type: Boolean, attribute: 'no-divider' }) noDivider?: boolean;

  private _selectActiveTab() {
    const navElements = this._navSlot?.assignedElements() ?? [];
    const tabs = (navElements.filter(element => element.tagName.toLowerCase() === 'fk-tab') as FKTab[])
      .filter(tab => !tab.hidden);
    // If nothing is selected, then select the first one.
    if (!this.selected) {
      for (const tab of tabs)
        tab.active = false;
      if (tabs.length)
        tabs[0].active = true;
    } else {
      for (const tab of tabs)
        tab.active = tab.name === this.selected;
    }
    const selectedName = tabs.find(tab => tab.active)?.name;

    const contentElements = this._contentSlot?.assignedElements() ?? [];
    const tabpanes = (contentElements.filter(element => element.tagName.toLowerCase() === 'fk-tabpane') as FKTabPane[])
    for (const tabpane of tabpanes)
      tabpane.hidden = true;

    const selectedPane = tabpanes.find(pane => pane.name === selectedName) ?? tabpanes.find(pane => pane.name === undefined);
    if (selectedPane)
      selectedPane.hidden = false;
  }

  render() {
    return html`
      <h-box class=tabbar-header @fk-tab-selected=${(event: FKTabSelected) => {
        this.selected = event.name;
      }}>
        <slot name=fk-tabbar-nav @slotchange=${this._selectActiveTab}></slot>
        <x-filler></x-filler>
        <slot name=suffix></slot>
      </h-box>
      ${this.noDivider ? nothing : html`<sl-divider></sl-divider>`}
      <slot @slotchange=${this._selectActiveTab}></slot>
    `;
  }

  override updated() {
    this._selectActiveTab();
  }

  static styles = [css`
    :host {
      display: block;
    }

    .tabbar-header {
      flex-wrap: wrap;
      gap: var(--sl-spacing-2x-small);
    }

    sl-divider {
      --spacing: var(--sl-spacing-small);
      --width: 2px;
    }
  `]
}

class FKTabSelected extends Event {
  static readonly eventName = 'fk-tab-selected';
  readonly name: string;

  constructor(name: string) {
    super(FKTabSelected.eventName, {
      bubbles: true,
      composed: true,
      cancelable: false,
    });
    this.name = name;
  }
}

@customElement('fk-tab')
export class FKTab extends LitElement {
  @property({ type: String }) href?: string;
  @property({ type: String }) name: string = '';
  @property({ type: Boolean }) active?: boolean;

  render() {
    return html`
      <a ?active=${this.active} href=${this.href} @click=${this.href ? nothing : () => {
        this.dispatchEvent(new FKTabSelected(this.name));
      }}>
        <h-box>
          <slot name=prefix></slot>
          <slot></slot>
          <slot name=suffix></slot>
        </h-box>
      </a>
    `;
  }

  constructor() {
    super();
    this.setAttribute('slot', 'fk-tabbar-nav');
  }

  static styles = [linkStyles, css`  
    h-box {
      gap: var(--sl-spacing-small);
    }

    a {
      display: block;
      padding: var(--sl-spacing-small) var(--sl-spacing-small);
    }

    a[active], a:hover {
      background-color: var(--fk-color-canvas);
      border-radius: var(--sl-border-radius-medium);
      outline: 1px solid var(--fk-color-border);
    }

    a:hover {
      text-decoration: none !important;
    }
  `]
}

@customElement('fk-tabpane')
export class FKTabPane extends LitElement {
  @property({ type: String }) name?: string;

  render() {
    return html`
      <section>
        <slot></slot>
      </section>
    `;
  }

  static styles = [css`
  `]
}
