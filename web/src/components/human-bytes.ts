import { LitElement, css, html, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { humanReadableBytes } from '../utils.js';

@customElement('human-bytes')
class HumanBytes extends LitElement {
  @property({ type: Number }) bytes?: number;

  override render() {
    if (this.bytes === undefined)
      return nothing;
    return html`${humanReadableBytes(this.bytes)}`;
  }

  static styles = [css`
    :host {
      display: inline-flex;
      align-items: center;
    }
  `];
}