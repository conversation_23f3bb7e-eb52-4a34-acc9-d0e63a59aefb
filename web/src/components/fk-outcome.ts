import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { LitElement, css, html } from 'lit';
import { customElement, property } from 'lit/decorators.js';

@customElement('fk-outcome')
class FKOutcome extends LitElement {
  @property({ type: String }) outcome?: WireTypes.Outcome;

  render() {
    if (this.outcome === 'regressed')
      return html`<sl-icon class=regressed name=fire></sl-icon>`;
    if (this.outcome === 'expected')
      return html`<sl-icon class=expected name=check-lg></sl-icon>`;
    if (this.outcome === 'unexpected')
      return html`<sl-icon class=unexpected name=x-circle-fill></sl-icon>`;
    if (this.outcome === 'skipped')
      return html`<sl-icon class=skipped name=dash-circle></sl-icon>`;
    if (this.outcome === 'flaked')
      return html`<sl-icon class=flaked name=exclamation-triangle-fill></sl-icon>`;
    return html`<sl-icon name=circle class=skipped></sl-icon>`;
  }

  static styles = [css`
    :host {
      display: flex;
    }

    .regressed {
      color: var(--fk-color-outcome-regressed);
    }

    .expected {
      color: var(--fk-color-outcome-expected);
    }

    .unexpected {
      color: var(--fk-color-outcome-unexpected);
    }

    .skipped {
      color: var(--fk-color-outcome-skipped);
    }

    .flaked {
      color: var(--fk-color-outcome-flaked);
    }
  `];
}
