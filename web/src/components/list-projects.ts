import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';

import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { Task } from '@lit/task';
import ms from 'ms';
import { api } from '../api.js';
import { PageProjectUsage } from '../page-project-usage.js';
import { PageReport } from '../page-report.js';
import { assert, humanReadableBytes, humanReadableInteger } from '../utils.js';
import { linkStyles } from './cssstyles.js';

@customElement('list-projects-item')
export class ListProjectsItem extends LitElement {
  @property({ attribute: false }) project?: WireTypes.Project;

  private _metrics = new Task(this, {
    args: () => [this.project] as const,
    task: async ([project], { signal }) => {
      assert(project);
      const dailyMetrics = await api.project.metrics.GET({ orgSlug: project.org.orgSlug, projectSlug: project.projectSlug, }, { signal });
      let totalRuns = 0;
      let totalTestRuns = 0;
      let totalBytes = 0;
      for (const m of dailyMetrics) {
        totalRuns += m.runsCount;
        totalTestRuns += m.testsCount;
        totalBytes += m.totalBytes;
      }
      return { dailyMetrics: dailyMetrics, totalRuns, totalTestRuns, totalBytes };
    },
  });

  override render() {
    if (!this.project)
      return nothing;
    const project = this.project;
    const metrics = this._metrics.value;
    return html`
      <h-box>
        <v-box style="gap: var(--sl-spacing-x-small)">
          <h-box>
            <a class=header href=${PageReport.url({ orgSlug: project.org.orgSlug, projectSlug: project.projectSlug })}>${project.projectSlug}</a>
            <sl-tag pill size=small>${project.visibility === 'private' ? 'Private' : 'Public'}</sl-tag>
          </h-box>
          <h-box class=description>
            <h-box style="gap: var(--sl-spacing-2x-small)">
              <sl-icon name=play-circle></sl-icon>
              <a href=${PageProjectUsage.url({ orgSlug: project.org.orgSlug, projectSlug: project.projectSlug })}>
                ${metrics ? humanReadableInteger(metrics.totalRuns) + ' runs' : html`<sl-skeleton effect=pulse style="width: 5ch" ></sl-skeleton>`}
              </a>
            </h-box>
            <span>•</span>
            <h-box style="gap: var(--sl-spacing-2x-small)">
              <sl-icon name=list-check></sl-icon>
              <a href=${PageProjectUsage.url({ orgSlug: project.org.orgSlug, projectSlug: project.projectSlug })}>
                ${metrics ? humanReadableInteger(metrics.totalTestRuns) + ' test runs' : html`<sl-skeleton effect=pulse style="width: 5ch" ></sl-skeleton>`}
              </a>
            </h-box>
            <span>•</span>
            <h-box style="gap: var(--sl-spacing-2x-small)">
              <sl-icon name=floppy></sl-icon>
              <a href=${PageProjectUsage.url({ orgSlug: project.org.orgSlug, projectSlug: project.projectSlug })}>
                ${metrics ? humanReadableBytes(metrics.totalBytes) : html`<sl-skeleton effect=pulse style="width: 5ch" ></sl-skeleton>`}
              </a>
            </h-box>
            <span>•</span>
            <h-box style="gap: var(--sl-spacing-2x-small)">
              ${project.lastUploadTimestamp && Date.now() - ms('1 day') < project.lastUploadTimestamp ? html`
                Updated <sl-relative-time date=${new Date(project.lastUploadTimestamp).toISOString()}></sl-relative-time>
                ` : project.lastUploadTimestamp ? html`
                Updated on <sl-format-date month="long" day="numeric" year="numeric" date=${new Date(project.lastUploadTimestamp).toISOString()}></sl-format-date>
              ` : html`No uploads`}
            </h-box>
          </h-box>
        </v-box>

        <x-filler></x-filler>
        <sl-tooltip content=${`Run uploads in the last 30 days`}>
          <sparkline-chart
            .metrics=${metrics?.dailyMetrics ?? []}
            .days=${30}
          ></sparkline-chart>
        </sl-tooltip>
      </h-box>
    `;
  }

  static styles = [linkStyles, css`
    .header {
      font-weight: var(--sl-font-weight-semibold);
    }

    .description {
      font-size: var(--sl-font-size-small);
      color: var(--sl-color-neutral-500);
      white-space: nowrap;
    }
  `];
}

@customElement('list-projects')
export class ListProjects extends LitElement {

  @property({ attribute: false }) projects?: WireTypes.Project[];

  override render() {
    if (!this.projects || !this.projects.length)
      return nothing;
    const projects = this.projects.toSorted((p1, p2) => (p2.lastUploadTimestamp ?? 0) - (p1.lastUploadTimestamp ?? 0));
    return html`
      <section>
        ${projects.map(project => html`<list-projects-item .project=${project}></list-projects-item>`)}
      </section>
    `;
  }

  static styles = [linkStyles, css`
    section {
      display: grid;
      grid-template-rows: 1fr;
      border-radius: var(--sl-border-radius-medium);
      border: 1px solid var(--sl-panel-border-color);
    }
    list-projects-item + list-projects-item {
      border-top: 1px solid var(--sl-panel-border-color);
    }
    list-projects-item {
      padding: var(--sl-spacing-small);
    }
    list-projects-item:hover {
      background-color: var(--fk-color-tablerow-hover);
    }

    .no-items {
      color: var(--sl-color-neutral-400);
      font-size: var(--sl-font-size-medium);
    }
  `]
}
