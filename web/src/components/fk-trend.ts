import { LitElement, css, html, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';

@customElement('fk-trend')
class FKTrend extends LitElement {
  @property({ attribute: false }) baseline?: number;
  @property({ attribute: false }) ms?: number;
  @property({ type: Boolean }) icon?: boolean;
  @property({ type: Boolean }) duration?: boolean;

  override render() {
    const durationMs = this.ms;
    const baseline = this.baseline;
    if (durationMs === undefined || baseline === undefined)
      return nothing;
    const durationChangeMs = durationMs - baseline;
    const icon = this.icon ? html`<sl-icon name=${durationChangeMs >= 0 ? 'graph-up-arrow' : 'graph-down-arrow'}></sl-icon>` : nothing;
    let scale = undefined;
    const durationTrend = durationMs / (durationMs - durationChangeMs);
    if (durationTrend > 1000) {
      scale = `>x1000`;
    } else if (durationTrend > 2) {
      scale = `x${durationTrend.toFixed(2)}`;
    } else if (durationTrend > 1.01) {
      scale = `+${(durationTrend - 1) * 100|0}%`
    } else if (0.99 < durationTrend && durationTrend < 1.01) {
      scale = `±0%`
    } else {
      scale = `-${(1-durationTrend)*100|0}%`;
    }
    return html`
      <span class=${classMap({
        slowdown: durationTrend > 1.2,
        speedup: durationTrend < 0.8,
      })}>
        ${icon}
        <span>${durationChangeMs < 0 ? '-' : '+'}<time-interval .ms=${Math.abs(durationChangeMs)}></time-interval> (${scale})</span>
        ${this.duration ? html` <span class=separator>—</span> <time-interval .ms=${durationMs}></time-interval>` : nothing}
      </span>
    `;
  }

  static styles = [css`
    :host {
      display: inline-flex;
      align-items: center;
      white-space: nowrap;
    }

    .separator {
      font-weight: var(--sl-font-weight-light);
      color: var(--sl-color-neutral-500);
    }

    .speedup {
      font-weight: var(--sl-font-weight-semibold);
      color: var(--sl-color-success-700);
    }

    .slowdown {
      font-weight: var(--sl-font-weight-semibold);
      color: var(--sl-color-danger-700);
    }

    sl-icon {
      margin-right: var(--sl-spacing-2x-small);
    }
  `];
}