import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import * as d3 from 'd3';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { StyleInfo, styleMap } from 'lit/directives/style-map.js';
import { linkStyles } from './cssstyles.js';

export type DurationsChartSeparator = {
  separator: string,
}

export type DurationsChartDatum = {
  outcome?: WireTypes.Outcome,
  duration?: number,
  weight?: number,
  selected?: boolean,
}

@customElement('durations-chart2')
export class DurationsChart2<T extends DurationsChartDatum> extends LitElement {

  static groupConsequtive<T, X>(predata: T[], prop: (d: T) => X) {
    const data: T[][] = [];
    let last: X|undefined;
    for (const datum of predata) {
      const x = prop(datum);
      if (!last || last !== x)
        data.push([datum]);
      else
        data.at(-1)!.push(datum);
      last = x;
    }
    return data;
  }

  static render<T extends DurationsChartDatum>(options: {
    data?: (T|DurationsChartSeparator)[],
    hover?: (e: CustomEvent<{ datum: T, bar: HTMLElement }>) => void,
    leave?: (e: CustomEvent<{ datum: T, bar: HTMLElement }>) => void,
    click?: (e: CustomEvent<{ datum: T, bar: HTMLElement }>) => void,
    style?: Readonly<StyleInfo>,
    fancy?: boolean,
  }) {
    return html`
      <durations-chart2
        ?fancy=${options.fancy}
        .data=${options.data}
        style=${options.style ? styleMap(options.style) : nothing}
        @fk-select=${options.click}
        @fk-hover-show=${options.hover}
        @fk-hover-hide=${options.leave}
      ></durations-chart2>
    `;
  }

  @property({ type: Boolean }) fancy?: boolean;
  @property({ attribute: false }) data?: (T|DurationsChartSeparator)[];

  private _dispatchEvent(type: string, datum: T, e: MouseEvent) {
    this.dispatchEvent(new CustomEvent<{ datum: T, bar: HTMLElement }>(type, { detail: {
      datum, bar: e.currentTarget as HTMLDivElement,
    }}));
  }

  override render() {
    const data = this.data ?? [];
    const filtered: T[] = data.filter(d => 'duration' in d) as T[];
    const vmax = d3.max(filtered.map(d => d.duration ?? 0)) ?? 0;
    const heightScale = d3.scaleLinear()
      .domain([-0.1 * vmax, vmax * 1.5]) // small headroom
      .range([0, 100]);

    return html`
      <h-box class=datapoints style=${styleMap({
      })}>
        ${data.map(datum => 'separator' in datum ? html`<span class=separator>${datum.separator}</span>` : html`
          <div class=${classMap({
            datum: true,
            selected: !!datum.selected,
          })}
            @click=${(e: MouseEvent) => this._dispatchEvent('fk-select', datum, e)}
            @mouseenter=${(e: MouseEvent) => this._dispatchEvent('fk-hover-show', datum, e)}
            @mouseleave=${(e: MouseEvent) => this._dispatchEvent('fk-hover-hide', datum, e)}
            style=${styleMap({
              'width': 15 + 8 * Math.log(datum.weight ?? 1) + 'px',
              'max-width': 15 + 8 * Math.log(datum.weight ?? 1) + 'px',
            })}
          >
            ${datum.selected ? html`
              <div class=pin></div>
              <div class=head>
                <div class=label>← FROM</div>
              </div>
            ` : nothing}
            <div class=${classMap({
              level: true,
              [datum.outcome ?? 'untested']: true,
            })} style=${styleMap({
              height: datum.duration ? heightScale(datum.duration) + '%' : undefined,
            })}></div>
          </div>
        `)}
      </h-box>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      display: block;
    }

    .datapoints {
      max-width: 100%;
      height: 100%;
      gap: 0.5px;

      .separator {
        writing-mode: sideways-lr;
        margin: 0 var(--sl-spacing-2x-small);
        flex: none;
        font-size: var(--sl-font-size-small);
      }

      .datum {
        min-width: 1px;
        height: 100%;
        flex: auto;
        position: relative;
        cursor: pointer;

        .head {
          position: relative;
          top: -5px;
          display: flex;
          align-items: center;
          justify-content: center;

          .label {
            white-space: nowrap;
            padding: var(--sl-spacing-2x-small) var(--sl-spacing-x-small);
            background-color: var(--sl-color-neutral-700);
            color: white;
            font-weight: bold;
            font-size: var(--sl-font-size-2x-small);
            border-radius: var(--sl-border-radius-medium);
          }
        }

        .pin {
          position: absolute;
          left: calc(50% - 1px);
          width: 2px;
          top: 0;
          bottom: 0;
          background-color: var(--sl-color-neutral-700);
        }

        &.selected {
          z-index: 100;

          .level:not(.untested, .idle) {
            box-shadow: 0px 0px 0px 4px var(--sl-color-neutral-700);
            outline: white solid 2px;
            border-radius: 2px;
          }
        }

        &:hover:not(.selected) {
          filter: brightness(0.9);
          background: white;
        }

        .level {
          box-sizing: border-box;
          background: color-mix(in oklch, var(--color), white 60%);
          position: absolute;
          left: 0;
          right: 0;
          bottom: 0;
          border-top: 2px solid var(--color);
          border-radius: 2px 2px 0 0;
        }
      }
    }

    :host([fancy]) .level {
      border-top: 3px solid var(--color);
      /* border-radius: 7px 3px 0 0; */
    }

    .expected {
      --color: var(--fk-color-outcome-expected);
    }
    .flaked {
      --color: var(--fk-color-outcome-flaked);
    }
    .regressed {
      --color: var(--fk-color-outcome-regressed);
    }
    .skipped {
      --color: var(--fk-color-outcome-skipped);
    }
    .unexpected {
      --color: var(--fk-color-outcome-unexpected);
    }
    .idle, .untested {
      --color: transparent;
    }
  `];
}
