import { css, html, LitElement } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { PageAdministrationBilling } from '../page-administration-billing.js';
import { PageAdministrationQueues } from '../page-administration-queues.js';
import { linkStyles } from './cssstyles.js';

@customElement('administration-header')
class AdministrationHeader extends LitElement {

  @property({ }) submenu: string = 'general';


  override render() {
    return html`
      <app-header>
        <sl-breadcrumb>
          <span slot="separator">/</span>
          <sl-breadcrumb-item href=${PageAdministrationBilling.url()}>Administration</sl-breadcrumb-item>
        </sl-breadcrumb>

        <app-header-submenu 
          href=${PageAdministrationQueues.url()}
          ?active=${this.submenu === 'queues'}
        ><sl-icon name=list-task></sl-icon>Jobs</app-header-submenu>
        <app-header-submenu 
          href=${PageAdministrationBilling.url()}
          ?active=${this.submenu === 'billing'}
        ><sl-icon name=credit-card></sl-icon>Product Plans</app-header-submenu>
      </app-header>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      display: block;
    }

    sl-breadcrumb-item::part(base) {
      font-size: var(--sl-font-size-medium);
      font-weight: var(--sl-font-weight-normal);
    }

    sl-breadcrumb-item::part(label) {
      color: var(--sl-color-neutral-800);
    }
  `];
}
