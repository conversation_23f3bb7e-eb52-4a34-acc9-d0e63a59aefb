import { pagedResponse } from '@flakiness/server/common/pagedResponse.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { consume, ContextType } from '@lit/context';
import { Task } from '@lit/task';
import { SlDropdown, SlInput } from '@shoelace-style/shoelace';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property, query, state } from 'lit/decorators.js';
import { api } from '../api.js';
import { linkStyles } from '../components/cssstyles.js';
import { contexts } from '../contexts.js';
import { TimedTask } from '../tasks.js';
import { assert } from '../utils.js';

export class FKHeadSelectEvent extends Event {
  static readonly eventName = 'fk-select';
  readonly head: WireTypes.Ref;

  constructor(head: WireTypes.Ref) {
    super(FKHeadSelectEvent.eventName, {
      bubbles: true,
      composed: true,
      cancelable: false,
    });
    this.head = head;
  }
}

@customElement('head-selector')
class HeadSelector extends LitElement {

  @property({ attribute: false }) head?: WireTypes.Ref;
  
  @consume({ context: contexts.project, subscribe: true }) private _project?: ContextType<typeof contexts.project>;
  @consume({ context: contexts.projectReferences, subscribe: true }) private _refs?: ContextType<typeof contexts.projectReferences>;

  @state() private _branchFilter?: string;

  @query('sl-dropdown') private _dropdown?: SlDropdown;
  @query('sl-input') private _input?: SlInput;

  private _branchesTask = new TimedTask(this, 500, {
    args: () => [
      this._refs, this._project, this._branchFilter,
    ] as const,
    task: async ([refs, project, filter], { signal }) => {
      assert(project && refs);
      const pageOptions: WireTypes.PageOptions = {
        number: 0,
        size: 5,
      };
      if (!filter) {
        const result = {
          ...refs.branches,
          elements: pagedResponse(refs.branches.elements, pageOptions).elements,
        };
        if (!result.elements.some(b => b.name === refs.defaultBranch.name)) {
          result.elements.shift();
          result.elements.unshift(refs.defaultBranch);
        }
        return result;
      }
      const result = await api.project.filterBranches.GET({
        filter: filter ?? '',
        orgSlug: project.org.orgSlug,
        projectSlug: project.projectSlug,
        pageOptions,
      }, { signal });
      return result;
    }
  });

  private _ref = new Task(this, {
    args: () => [
      this._refs, this._project, this.head,
    ] as const,
    task: async ([refs, project, head], { signal }) => {
      assert(project && refs);
      if (!head || head.name === refs.defaultBranch.name)
        return refs.defaultBranch;
      return await api.project.resolveRef.GET({
        orgSlug: project.org.orgSlug,
        projectSlug: project.projectSlug,
        head: head.name,
      });
    }
  });

  render() {
    const defaultBranch = this._refs?.defaultBranch;
    const branches = this._branchesTask.value;
    if (!branches || !defaultBranch)
      return nothing;

    const head = this.head ?? defaultBranch;
    const isCommit = this._ref.value?.type === 'commit';
    return html`
      <sl-dropdown placement=bottom-start distance=10>
        <sl-button slot="trigger" caret>
          <sl-icon slot=prefix library=boxicons name=${isCommit ? 'bx-git-commit' : 'bx-git-branch'}></sl-icon>
          ${head?.type === 'commit' ? head.name.slice(0, 7) : head?.name ?? nothing}
        </sl-button>
        <v-box class=head-picker>
          <sl-input
            value=${this._branchFilter}
            placeholder='Find a branch...'
            @input=${(e: InputEvent) => {
              this._branchFilter = this._input?.value;
            }}>
            ${this._branchesTask.duration() < 500 ? html`
              <sl-icon name=search slot=prefix></sl-icon>
              ` : html`
              <sl-spinner slot=prefix></sl-spinner>
            `}
          </sl-input>
          <sl-divider style="--spacing: var(--sl-spacing-3x-small)"></sl-divider>
          ${branches.elements.length ? html`
            <section class=branches>${branches.elements.map(branch => html`
              <button class=branch @click=${() => this._onSelect(branch)}>
                <sl-icon name=${branch.name.toLowerCase() === head?.name.toLowerCase() ? 'check' : nothing}></sl-icon>
                <div>
                  <hl-span .text=${branch.name} .hl=${higlight(branch.name, this._branchFilter)}></hl-span>
                  ${branch.name === defaultBranch.name ? html`<sl-tag pill size=small>default</sl-tag>` : nothing}
                </div>
                <sl-tooltip>
                  <p slot=content>
                    Last change by <b>${branch.commit.author}</b> on <sl-format-date month="long" day="numeric" year="numeric" hour="numeric" minute="numeric" date=${new Date(branch.commit.timestamp).toISOString()}></sl-format-date>
                  </p>
                  <h-box class=last-updated>
                    <sl-avatar image=${branch.commit.avatar_url} label=${branch.commit.author} loading=lazy></sl-avatar>
                    <sl-relative-time date=${new Date(branch.commit.timestamp).toISOString()}></sl-relative-time>
                  </h-box>
                </sl-tooltip>
              </button>
            `)}</section>
          ` : html`
            <h-box class=nothing-to-show>Nothing to show</h-box>
          `}
          <sl-divider style="--spacing: var(--sl-spacing-3x-small)"></sl-divider>
          <div class=totals>Showing ${branches.elements.length} out of ${branches.totalElements} branches</div>
        </v-box>
      </sl-dropdown>
    `;
  }

  private _onSelect(ref: WireTypes.Ref) {
    this._dropdown?.hide();
    this.dispatchEvent(new FKHeadSelectEvent(ref));
  }

  static styles = [linkStyles, css`
    .head-picker {
      background-color: white;
      padding: var(--sl-spacing-small) 0;
      border: 1px solid var(--fk-color-border);
      box-shadow: var(--fk-shadow-floating-small);
      border-radius: var(--sl-border-radius-large);
    }

    sl-input, .totals, .branches {
      margin: 0 var(--sl-spacing-small);
    }

    .last-updated {
      margin-left: var(--sl-spacing-large);
      font-size: var(--sl-font-size-small);
      gap: var(--sl-spacing-2x-small);

      sl-avatar {
        --size: 16px;
      }
    }

    .nothing-to-show {
      justify-content: center;
      padding: var(--sl-spacing-medium) 0;
    }

    .branches {
      display: grid;
      grid-template-columns: auto 1fr auto;
      gap: var(--sl-spacing-2x-small) 0;

      .branch {
        appearance: none;
        border: none;
        margin: none;
        padding: none;
        background: none;
        text-align: start;
        font-family: unset;
        font-size: unset;

        display: grid;
        grid-template-columns: subgrid;
        grid-column: 1/-1;
        align-items: center;
        padding: var(--sl-spacing-x-small);
        border-radius: var(--sl-border-radius-medium);
        cursor: pointer;

        &:hover {
          background-color: var(--sl-color-neutral-100);
        }
      }
    }

    .totals {
      font-size: var(--sl-font-size-small);
      color: var(--sl-color-neutral-600);
    }
  `];
}

function higlight(text: string, needle: string|undefined) {
  if (!needle)
    return undefined;
  const i = text.toLowerCase().indexOf(needle);
  return (i !== -1) ? [i, i + needle.length] : undefined;
}