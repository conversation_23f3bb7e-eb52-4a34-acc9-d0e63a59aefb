import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { consume, ContextType } from '@lit/context';
import { Task } from '@lit/task';
import { SlSwitch } from '@shoelace-style/shoelace';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property, query, state } from 'lit/decorators.js';
import { api } from '../api.js';
import { contexts, FKOrgChanged } from '../contexts.js';
import { linkStyles } from './cssstyles.js';

@customElement('product-plans')
class ProductPlans extends LitElement {
  @property({ attribute: false }) orgSlug?: string;
  @property({ type: Boolean, attribute: 'show-plan-id' }) showPlanId?: boolean;
  @consume({ context: contexts.user, subscribe: true }) private _user?: ContextType<typeof contexts.user>;
  @consume({ context: contexts.billing, subscribe: true }) private _billing?: ContextType<typeof contexts.billing>;

  private _plansTask = new Task(this, {
    args: () => [this.orgSlug] as const,
    task: async ([orgSlug], { signal }) => {
      const [allPlans, orgPlans] = await Promise.all([
        api.productPlans.listPlans.GET(undefined, { signal }),
        orgSlug ? api.productPlans.tailoredProductPlans.GET({ orgSlug }, { signal }) : [],
      ]);
      return { allPlans, orgPlans };
    }
  });

  @query('#annual') private _fAnnual?: SlSwitch;

  @state() private _isLoadingStripe: boolean = false;
  @state() private _isAnnualBilling: boolean = false;

  render() {
    const billing = this._billing;
    const subscription = billing?.subscription;
    const hasAnnualPlans = (this._plansTask.value?.allPlans ?? []).some(plan => plan.billing === 'year');
    const commonPlans = (this._plansTask.value?.allPlans ?? []).filter(plan => this._isAnnualBilling ? plan.billing === 'year' : plan.billing === 'month');

    return html`
      <v-box style="gap: var(--sl-spacing-large);">
        ${hasAnnualPlans ? html`
          <h-box>
            <x-filler></x-filler>
            <sl-switch id=annual @sl-input=${() => {
              this._isAnnualBilling = this._fAnnual?.checked ?? false;
            }}>Annual Billing <sl-tag pill size=small variant=success>1 month free</sl-tag></sl-switch>
          </h-box>
        ` : nothing}
        <product-plans-view
          ?no-enterprise=${!!this._plansTask.value?.orgPlans.length}
          ?no-subscribe=${!this.orgSlug}
          ?show-del=${this._user?.isSuperUser}
          ?show-add=${this._user?.isSuperUser}
          ?show-plan-id=${this.showPlanId}
          .loading=${this._isLoadingStripe}
          .subscription=${subscription}
          .plans=${this._plansTask.value ? [
            ...commonPlans,
            ...this._plansTask.value.orgPlans,
          ] : []}
          @fk-select=${async (event: CustomEvent<WireTypes.ProductPlan>) => {
            if (!this.orgSlug)
              return;
            try {
              this._isLoadingStripe = true;
              if (event.detail.id === subscription?.plan.id) {
                const url = await api.billing.customerPortal.GET({ orgSlug: this.orgSlug });
                window.open(url, '_blank');
                return;
              }
              const url = await api.billing.setPlan.POST({
                orgSlug: this.orgSlug,
                planId: event.detail.id,
              });
              if (url)
                window.open(url, '_blank');
              else
                this.dispatchEvent(new FKOrgChanged({ orgSlug: this.orgSlug }));
            } finally {
              this._isLoadingStripe = false;
            }
          }}
          @fk-remove=${async (event: CustomEvent<WireTypes.ProductPlan>) => {
            const plan = event.detail;
            await api.productPlans.delete.POST({
              planPublicId: plan.id,
            });
            this._plansTask.run();
          }}
        >
          ${this._user?.isSuperUser && !this._plansTask.value?.orgPlans.length ? html`
            <new-product-plan
              .orgSlug=${this.orgSlug}
              @fk-select=${async (event: CustomEvent<WireTypes.ProductPlan>) => {
                await this._plansTask.run();
              }}
            ></new-product-plan>
          ` : nothing}
        </product-plans>
      </v-box>
    `;
  }

  static styles = [linkStyles, css`

  `];
}
