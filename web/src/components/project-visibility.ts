import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { css, html, LitElement } from 'lit';
import { customElement, property } from 'lit/decorators.js';

@customElement('project-visibility')
class ProjectVisibility extends LitElement {
  @property({ }) value?: WireTypes.ProjectVisibility;

  override render() {
    if (this.value === 'private')
      return html`<sl-icon name=lock></sl-icon>`;
    if (this.value === 'public')
      return html`<sl-icon name=globe2></sl-icon>`;
    return html`<sl-icon name=globe2 hidden></sl-icon>`;
  }

  static styles = [css`
    [hidden] {
      visibility: hidden;
    }

    :host {
      display: flex;
    }
  `];
}