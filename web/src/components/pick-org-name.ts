import { css, html, LitElement } from 'lit';
import { customElement, property, query } from 'lit/decorators.js';
import slugify from 'slugify';
import { api } from '../api.js';
import { EventTask } from '../contexts.js';
import { assert } from '../utils.js';
import { InputWithStatus } from './input-with-status.js';

export type OrgNamePicked = {
  orgName: string,
}

@customElement('pick-org-name')
export class PickOrgName extends LitElement {

  @query('input-with-status') private _input?: InputWithStatus;

  @property({ attribute: false }) initial?: string;
  @property({ type: Boolean }) disabled?: boolean;

  private _validateOrgName = new EventTask(this, 'org-name', {
    autoRun: false,
    args: () => [this._input?.value()] as const,
    task: async ([orgName], { signal }): Promise<OrgNamePicked> => {
      assert(orgName !== undefined);
      if (!orgName.length) {
        this._input?.setStatus(false, 'Organization name must not be blank');
      } else if (!(/^[.0-9a-z_-]+$/i).test(orgName)) {
        this._input?.setStatus(false, 'Organization name can only contain ASCII letters, digits, and the characters ., -, and _.');
      } else {
        // Small cool-down to not spam server with requests.
        await new Promise(x => setTimeout(x, 200));
        signal.throwIfAborted();
        const orgSlug = slugify.default(orgName);
        console.log(orgSlug);
        const orgExists = await api.user.findOrganization.GET({ orgSlug }, { signal });
        if (orgExists)
          this._input?.setStatus(false, `"${orgSlug}" is already taken`);
        else 
          this._input?.setStatus(true, `"${orgSlug}" is available`);
      }
      assert(this._input?.success());
      return { orgName };
    }
  });

  setStatus(success: boolean, message: string) {
    this._input?.setStatus(success, message);
  }

  override render() {
    return html`
      <input-with-status
        placeholder="Organization name"
        autocomplete=off
        ?disabled=${this.disabled}
        initial=${this.initial}
        @sl-input=${() => this._validateOrgName.run()}
      ><slot></slot></input-with-status>
    `;
  }

  static styles = [css``];
}