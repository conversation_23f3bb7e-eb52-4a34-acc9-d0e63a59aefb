import { css, html, LitElement } from 'lit';
import { customElement } from 'lit/decorators.js';

@customElement('h-box')
export class HBox extends LitElement {
  static styles = css`
    :host {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: var(--sl-spacing-small);
      flex-wrap: nowrap;
      min-width: 0;
    }
  `;

  render() {
    return html`
      <slot></slot>
    `;
  }
}
