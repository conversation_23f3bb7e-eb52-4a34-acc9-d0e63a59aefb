import type { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { linkStyles } from './cssstyles.js';

@customElement('project-alerts')
export class ProjectAlerts extends LitElement {
  @property({ attribute: false }) project?: WireTypes.Project;

  static maybeRender(project?: WireTypes.Project) {
    if (!project || project.sourceLastFetchHTTPCode === 200)
      return nothing;
    return html`<project-alerts .project=${project}></project-alerts>`
  }

  override render() {
    if (!this.project)
      return nothing;
    if (this.project.sourceLastFetchHTTPCode === 200)
      return nothing;
    if (this.project?.sourceLastFetchHTTPCode === undefined) {
      return html`
        <fk-callout variant="tip">
          <strong>Commit History is loading</strong><br />
          It might take a few minutes for the commit history to load.
        </fk-callout>
      `;
    }
    if (this.project.sourceLastFetchHTTPCode === 401) {
      return html`
        <fk-callout variant="warn">
          <strong>Git Repository Access Denied</strong><br />
          Please re-link source to this project.
        </fk-callout>
      `;
    }
    return html`
      <fk-callout variant="danger">
        <strong>Failed to access Git Repository </strong><br />
        Something went wrong; please reach out to us at <fk-contact-us></fk-contact-us>
      </fk-callout>
    `;
  }

  static styles = [linkStyles, css`
  `];
}