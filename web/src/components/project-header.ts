import { consume, ContextType } from '@lit/context';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';
import { contexts } from '../contexts.js';
import { PageOrganization } from '../page-organization.js';
import { PageProjectCollaborators } from '../page-project-collaborators.js';
import { PageProjectHistory } from '../page-project-history.js';
import { PageProjectPulls } from '../page-project-pulls.js';
import { PageProjectRuns } from '../page-project-runs.js';
import { PageProjectSettings } from '../page-project-settings.js';
import { PageProjectSuperadm } from '../page-project-superadm.js';
import { PageProjectUsage } from '../page-project-usage.js';
import { PageReport } from '../page-report.js';
import { linkStyles } from './cssstyles.js';

@customElement('project-header')
export class ProjectHeader extends LitElement {
  @consume({ context: contexts.project, subscribe: true }) @state() private _project?: ContextType<typeof contexts.project>;
  @consume({ context: contexts.user, subscribe: true }) @state() private _user?: ContextType<typeof contexts.user>;
  @consume({ context: contexts.projectCollaborators, subscribe: true }) @state() private _projectCollaborators?: ContextType<typeof contexts.projectCollaborators>;
  @consume({ context: contexts.projectPulls, subscribe: true }) @state() private _projectPulls?: ContextType<typeof contexts.projectPulls>;

  @property({ attribute: false }) orgSlug?: string;
  @property({ attribute: false }) projectSlug?: string;
  @property({ attribute: false }) submenu?: string;

  override render() {
    const orgSlug = this.orgSlug;
    const projectSlug = this.projectSlug;
    return html`
      <subscription-trial-banner .orgSlug=${this.orgSlug}></subscription-trial-banner>
      <app-header>
        <sl-breadcrumb>
          <span slot="separator">/</span>
          ${this.orgSlug ? html`
            <sl-breadcrumb-item href=${this.orgSlug ? PageOrganization.url({ orgSlug: this.orgSlug }) : nothing }>${this.orgSlug ?? nothing}</sl-breadcrumb-item>
          ` : nothing}
          ${orgSlug && projectSlug ? html`
            <sl-breadcrumb-item href=${orgSlug && projectSlug && PageReport.url({ orgSlug, projectSlug })}>
              <h-box style="gap: var(--sl-spacing-2x-small);"><span><b>${projectSlug}</b></span><project-visibility value=${this._project?.visibility}></project-visibility></h-box>
            </sl-breadcrumb-item>
          ` : nothing}
        </sl-breadcrumb>

        ${this.orgSlug && this.projectSlug ? html`
          <app-header-submenu
            href=${PageReport.url({ orgSlug: this.orgSlug, projectSlug: this.projectSlug })}
            ?active=${this.submenu === 'tests'}
          ><sl-icon name=ui-checks></sl-icon>Tests</app-header-submenu>
          <app-header-submenu
            href=${PageProjectPulls.url({ orgSlug: this.orgSlug, projectSlug: this.projectSlug })}
            ?active=${this.submenu === 'pulls'}
          ><sl-icon name=bx-git-pull-request library=boxicons></sl-icon>Pull Requests${
            this._projectPulls === undefined
              ? html`<sl-skeleton effect=pulse style="width: 2ch"></sl-skeleton>`
              : html`<sl-tag size=small pill>${this._projectPulls.totalElements}</sl-tag>`
          }</app-header-submenu>
          <app-header-submenu
            href=${PageProjectHistory.url({ orgSlug: this.orgSlug, projectSlug: this.projectSlug })}
            ?active=${this.submenu === 'history'}
          ><sl-icon name=bx-git-branch library=boxicons></sl-icon>History</app-header-submenu>
          <app-header-submenu
            href=${PageProjectRuns.url({ orgSlug: this.orgSlug, projectSlug: this.projectSlug })}
            ?active=${this.submenu === 'runs'}
          ><sl-icon name=play-circle></sl-icon>Runs</app-header-submenu>
          <app-header-submenu
            href=${PageProjectCollaborators.url({ orgSlug: this.orgSlug, projectSlug: this.projectSlug })}
            ?active=${this.submenu === 'collaborators'}
          ><sl-icon name=people></sl-icon>Collaborators${
            this._projectCollaborators === undefined
              ? html`<sl-skeleton effect=pulse style="width: 2ch"></sl-skeleton>`
              : html`<sl-tag size=small pill>${this._projectCollaborators.length}</sl-tag>`
          }</app-header-submenu>
          <app-header-submenu
            href=${PageProjectUsage.url({ orgSlug: this.orgSlug, projectSlug: this.projectSlug })}
            ?active=${this.submenu === 'usage'}
          ><sl-icon name=speedometer2></sl-icon>Usage</app-header-submenu>
          <app-header-submenu
            href=${PageProjectSettings.url({ orgSlug: this.orgSlug, projectSlug: this.projectSlug })}
            ?active=${this.submenu === 'settings'}
          ><sl-icon name=gear></sl-icon>Settings</app-header-submenu>
          ${this._user?.isSuperUser ? html`
            <app-header-submenu
              href=${PageProjectSuperadm.url({ orgSlug: this.orgSlug, projectSlug: this.projectSlug })}
              ?active=${this.submenu === 'superadm'}
            ><sl-icon name=tools></sl-icon>SuperAdm</app-header-submenu>
          ` : nothing}
        ` : nothing}
      </app-header>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      display: block;
    }

    project-visibility {
      font-size: var(--sl-font-size-small);
    }

    sl-breadcrumb-item::part(base) {
      font-size: var(--sl-font-size-medium);
      font-weight: var(--sl-font-weight-normal);
    }

    sl-breadcrumb-item::part(label) {
      color: var(--sl-color-neutral-800);
    }

    sl-tag::part(base) {
      height: 16px;
    }
  `];
}
