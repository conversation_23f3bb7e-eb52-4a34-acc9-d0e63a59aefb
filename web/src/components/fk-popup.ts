import { SlPopup } from '@shoelace-style/shoelace';
import { LitElement, TemplateResult, css, html } from 'lit';
import { customElement, property, query, state } from 'lit/decorators.js';

export class FKPopupRequested extends Event {
  static readonly eventName = 'fk-popup-show';

  constructor(public readonly template: TemplateResult<1>, public readonly anchor?: HTMLElement) {
    super(FKPopupRequested.eventName, {
      bubbles: true,
      composed: true,
      cancelable: false,
    });
  }
}

export class FKPopupDismissed extends Event {
  static readonly eventName = 'fk-popup-hide';

  constructor() {
    super(FKPopupDismissed.eventName, {
      bubbles: true,
      composed: true,
      cancelable: false,
    });
  }
}

@customElement('fk-popup')
export class FKPopup extends LitElement {
  @property() strategy?: string;
  @property() placement?: string;
  @property({ type: Number }) distance?: number;
  @property({ type: Boolean }) shift?: boolean;
  @property({ type: Boolean }) arrow?: boolean;

  @state() _popupContent?: TemplateResult<1>;
  @query('sl-popup') _popup?: SlPopup;

  show(content: TemplateResult<1>, anchor: Element) {
    if (!this._popup)
      return;
    this._popupContent = content;  
    this._popup.anchor = anchor;
  }

  override render() {
    return html`
      <sl-popup
        strategy=${this.strategy ?? 'fixed'}
        placement=${this.placement ?? 'top'}
        distance=${this.distance}
        ?shift=${this.shift}
        ?arrow=${this.arrow}
        ?active=${!!this._popupContent}
        @fk-popup-show=${(e: FKPopupRequested) => this._popupContent = e.template }
        @fk-popup-hide=${() => this._popupContent = undefined }
        @mouseleave=${() => this._popupContent = undefined }
      >
        <div class=hover-popup>
          ${this._popupContent}
        </div>
        <slot slot="anchor">
          <slot></slot>
        </slot>
      </sl-popup>
    `;
  }

  static styles = [css`
    :host {
      display: inline-block;
    }
    .hover-popup {
      background: white;
      border: 1px solid var(--sl-color-neutral-300);
      border-radius: var(--sl-border-radius-medium);
      pointer-events: none;
    }
  `];
}