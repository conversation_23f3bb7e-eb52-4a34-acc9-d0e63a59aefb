import { LitElement, css, html, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { humanReadableMs } from '../utils.js';

@customElement('time-interval')
class TimeInterval extends LitElement {
  @property({ attribute: false }) ms?: number;
  @property({ type: Boolean }) icon?: boolean;

  override render() {
    if (this.ms === undefined)
      return nothing;
    const icon = this.icon ? html`<sl-icon name=clock-history></sl-icon>` : nothing;
    return html`${icon}${humanReadableMs(this.ms)}`;
  }

  static styles = [css`
    :host {
      display: inline-flex;
      align-items: center;
      white-space: nowrap;
    }

    sl-icon {
      margin-right: var(--sl-spacing-2x-small);
    }
  `];
}