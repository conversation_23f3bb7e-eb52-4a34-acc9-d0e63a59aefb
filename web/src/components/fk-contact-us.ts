import { LitElement, css, html } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { linkStyles } from './cssstyles.js';

@customElement('fk-contact-us')
class FKContactUs extends LitElement {
  @property({ type: String }) href?: string;
  @property({ type: String }) target?: string;

  render() {
    return html`
      <a href="mailto:<EMAIL>"><EMAIL></a>
    `;
  }

  static styles = [linkStyles, css`
    a {
      display: inline-flex;
      align-items: center;
    }
  `];
}
