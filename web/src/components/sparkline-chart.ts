import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { Task } from '@lit/task';
import * as d3 from 'd3';
import { css, html, LitElement, PropertyValues } from 'lit';
import { customElement, property, query } from 'lit/decorators.js';
import { assert } from '../utils.js';
import { linkStyles } from './cssstyles.js';


type Datum = {
  date: Date,
  value: number,
}

@customElement('sparkline-chart')
export class SparklineChart extends LitElement {
  
  @property({ attribute: false }) metrics?: WireTypes.DailyMetrics[];
  @property({ attribute: false }) days: number = 30;

  @query("svg") private _chart?: SVGElement;

  private _dataTask = new Task(this, {
    args: () => [this.metrics] as const,
    task: async ([allMetrics], { signal }) => {
      assert(allMetrics);
      const dayToMetric = new Map<number, WireTypes.DailyMetrics>();
      for (const metric of allMetrics)
        dayToMetric.set(+d3.utcDay.floor(new Date(metric.dayTimestampMs)), metric);

      const since = d3.utcDay.offset(d3.utcDay.floor(new Date()), -this.days);
      const until = d3.utcDay.ceil(new Date());
      const range = d3.utcDay.range(since, until);
      return range.map(day => ({
        date: day,
        value: dayToMetric.get(+day)?.runsCount ?? 0,
      } as Datum));
    },
  });

  protected override updated(changedProperties: PropertyValues): void {
    this._updateChart();
  }

  override render() {
    return html`
      <sl-resize-observer @sl-resize=${() => this._updateChart() }>
        <svg id=chart width=120 height=25>
          <defs>
            <linearGradient id=gradient x1="0" x2="0" y1="1" y2="0">
              <stop offset="0%" stop-color="var(--gradient-stop-1)"></stop>
              <stop offset="10%" stop-color="var(--gradient-stop-2)"></stop>
              <stop offset="25%" stop-color="var(--gradient-stop-3)"></stop>
              <stop offset="50%" stop-color="var(--gradient-stop-4)"></stop>
            </linearGradient>
            <mask id=mask x=0 y=0 width=120 height=25>
              <path id=timeseries></path>
            </mask>
          </defs>
          <rect x=0 y=0 width=120 height=25></rect>
        </svg>
      </sl-resize-observer>
    `;
  }

  private _updateChart() {
    const data = this._dataTask.value;
    const chartBoundingBox = this._chart?.getBoundingClientRect();
    const chart = this._chart;

    if (!data?.length || !chartBoundingBox || !chartBoundingBox.width || !chartBoundingBox.height || !chart)
      return;

    const WIDTH = chartBoundingBox.width;
    const HEIGHT = chartBoundingBox.height;

    const PADDING_TOP = 1;
    const PADDING_RIGHT = 1;
    const PADDING_BOTTOM = 1;
    const PADDING_LEFT = 1;

    const xScale = d3.scaleLinear<number, number>()
      .domain([0, data.length - 1])
      .range([PADDING_LEFT, WIDTH - PADDING_RIGHT]);
    
    const yExtent = d3.extent(data.map(d => d.value)) as [number, number];
    const yMaxExtent = Math.abs(yExtent[1]) < 1e-9 ? 1 : Math.ceil(yExtent[1]*100 * 1.3)/100;
    const yScale = d3.scaleLinear<number>()
      .domain([0, yMaxExtent])
      .range([HEIGHT - PADDING_BOTTOM, PADDING_TOP]);

    const lineGenerator = d3.line<Datum>()
      .x((d, i) => xScale(i))
      .y(d => yScale(d.value))
      .curve(d3.curveNatural);

    d3.select(chart)
      .select('#timeseries')
      .datum(data)
      .attr('fill', 'none')
      .attr('stroke', 'green')
      .attr('stroke-width', '2')
      .attr('d', lineGenerator);
  }

  static styles = [linkStyles, css`
    :host {
      display: block;
      flex: none;

      --gradient-stop-1: #aceebb;
      --gradient-stop-2: #4ac26b;
      --gradient-stop-3: #2da44e;
      --gradient-stop-4: #116329;
    }

    rect {
      stroke: none;
      fill: url(#gradient);
      mask: url(#mask);
    }

    path {
      stroke: #8cc665;
    }

    #chart {
      height: 100%;
      width: 100%;
      position: relative;
    }
  `];
}

