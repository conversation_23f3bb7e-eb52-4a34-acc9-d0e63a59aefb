import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { styleMap } from 'lit/directives/style-map.js';

@customElement('outcomes-bar')
export class OutcomesBar extends LitElement {
  @property({ attribute: false }) counts?: WireTypes.OutcomesCount;

  render() {
    const counts = this.counts;
    if (!counts)
      return nothing;
    const outcomes: WireTypes.Outcome[] = (['regressed', 'unexpected', 'flaked', 'expected', 'skipped'] as const).filter(outcome => !!counts[outcome]);
    return html`
      <v-box>
        <h-box style="gap: var(--sl-spacing-x-small); font-size: var(--sl-font-size-small); justify-content: center;">
          ${outcomes.map((outcome, idx) => html`
            ${idx === 0 ? nothing : html`<span style="color: var(--sl-color-neutral-300)">•</span>`}
            <h-box style="gap: var(--sl-spacing-2x-small);">
              <fk-outcome outcome=${outcome}></fk-outcome>
              <span>${counts[outcome]}</span>
            </h-box>
          `)}
        </h-box>
        <h-box class=barchart>
          ${outcomes.map(outcome => html`
            <div class=${classMap({
              [outcome]: true,
              segment: true,
            })} style=${styleMap({
              'flex-grow': (counts[outcome]),
            })}></div>
          `)}
        </h-box>
      </v-box>
    `;
  }

  static styles = css`
    :host {
    }

    .barchart {
      gap: 1px;
      border-radius: 4px;
      overflow: hidden;
      box-sizing: border-box;
    }

    .segment {
      background-color: color-mix(in oklch, white 50%, var(--color));
      min-width: 14px;
      height: 7px;
    }

    .regressed {
      --color: var(--fk-color-outcome-regressed);
    }

    .expected {
      --color: var(--fk-color-outcome-expected);
    }

    .unexpected {
      --color: var(--fk-color-outcome-unexpected);
    }

    .skipped {
      --color: var(--fk-color-outcome-skipped);
    }

    .flaked {
      --color: var(--fk-color-outcome-flaked);
    }
  `;
}
