import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { SlRadioGroup } from '@shoelace-style/shoelace';
import { css, html, LitElement } from 'lit';
import { customElement, property, query } from 'lit/decorators.js';
import { linkStyles } from './cssstyles.js';

export class ProjectVisibilityEvent extends Event {
  constructor(
    public readonly visibility: WireTypes.ProjectVisibility,
  ) {
    super('project-visibility', {
      bubbles: true,
      composed: true,
      cancelable: false,
    });
  }
}

@customElement('pick-project-visibility')
export class PickProjectVisibility extends LitElement {

  @property({ attribute: false }) initial: WireTypes.ProjectVisibility = 'private';
  @property({ type: <PERSON><PERSON>an }) disabled?: boolean;

  @query('sl-radio-group') private _visibilityInput?: SlRadioGroup;

  private _onChange() {
    const visibility = this.visibility();
    if (visibility)
      this.dispatchEvent(new ProjectVisibilityEvent(visibility));
  }

  visibility(): WireTypes.ProjectVisibility {
    return (this._visibilityInput?.value as WireTypes.ProjectVisibility|undefined) ?? 'private';
  }

  override render() {
    return html`
      <sl-radio-group @sl-input=${this._onChange} name="a" value=${this.initial}>
        <sl-radio ?disabled=${this.disabled} value=${'private' satisfies WireTypes.ProjectVisibility}>
          <h-box style="gap: var(--sl-spacing-2x-small);"><project-visibility value=private></project-visibility>Private</h-box>
          <div class=description>Access is granted to members of the organization. Project access must be granted explicitly to users outside of the organization.</div>
        </sl-radio>
        <sl-radio ?disabled=${this.disabled} value=${'public' satisfies WireTypes.ProjectVisibility}>
          <h-box style="gap: var(--sl-spacing-2x-small);"><project-visibility value=public></project-visibility>Public</h-box>
          <div class=description>The project can be accessed without any authentication.</div>
        </sl-radio>
      </sl-radio-group>
    `;
  }

  static styles = [linkStyles, css`
    sl-radio + sl-radio {
      margin-top: var(--sl-spacing-small);
    }

    .description {
      color: var(--sl-color-neutral-500);
    }
  `];
}
