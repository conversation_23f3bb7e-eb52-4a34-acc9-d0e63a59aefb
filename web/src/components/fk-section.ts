import { css, html, LitElement } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { linkStyles } from './cssstyles.js';

@customElement('fk-section')
class FKSection extends LitElement {
  @property({ type: Boolean, reflect: true }) open?: boolean;

  override render() {
    return html`
      <div class=header @click=${() => { this.open = !this.open; } }>
        <sl-icon name=${this.open ? 'chevron-down' : 'chevron-right'}></sl-icon>
        <slot name=title></slot>
      </div>
      <div class=body><slot></slot></div>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      display: block;
      overflow: auto;
      border: 1px solid var(--fk-color-border);
      border-radius: var(--sl-border-radius-large);
      overflow: auto;
    }

    .header {
      background-color: var(--fk-color-canvas);
      display: flex;
      gap: var(--sl-spacing-small);
      align-items: center;
      cursor: pointer;
      padding: var(--sl-spacing-small);
    }

    .body {
      display: none;
      border-top: 1px solid var(--fk-color-border);
      padding: var(--sl-spacing-small);
    }

    :host([open]) {
      .header {
      }

      .body {
        display: block;
      }
    }
  `];
}
