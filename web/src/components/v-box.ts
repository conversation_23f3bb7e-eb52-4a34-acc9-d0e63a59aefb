import { css, html, LitElement } from 'lit';
import { customElement } from 'lit/decorators.js';

@customElement('v-box')
export class VBox extends LitElement {

  static styles = css`
    :host {
      display: flex;
      flex-direction: column;

      align-items: normal;
      justify-content: flex-start;
      gap: var(--sl-spacing-small);
      flex-wrap: nowrap;
      min-width: 0;
    }
  `;

  render() {
    return html`
      <slot></slot>
    `;
  }
}

declare global {
  interface HTMLElementTagNameMap {
    'v-box': VBox;
  }
}