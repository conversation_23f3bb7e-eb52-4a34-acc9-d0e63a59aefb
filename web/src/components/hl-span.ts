import { Ranges } from '@flakiness/server/common/ranges.js';
import { LitElement, TemplateResult, css, html, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';

export function highlight(text: string, highlight?: Ranges.Ranges<number>) {
  if (!highlight)
    return html`${text}`;
  const elements: (TemplateResult|string)[] = [];
  let lastIndex = 0;

  for (let i = 0; i < highlight.length; i += 2) {
    const from = highlight[i];
    const to = highlight[i + 1];
    if (lastIndex < from)
      elements.push(text.substring(lastIndex, from));
    if (from < to)
      elements.push(html`<mark>${text.substring(from, to)}</mark>`);
    lastIndex = to;
  }
  if (lastIndex < text.length)
    elements.push(text.substring(lastIndex));
  return html`${elements}`;
}

@customElement('hl-span')
export class HighlightableSpan extends LitElement {
  @property({ attribute: false }) hl?: Ranges.Ranges<number>;
  @property({ attribute: false }) text?: string;

  override render() {
    if (!this.text)
      return nothing;
    if (!this.hl)
      return this.text;
    return highlight(this.text, this.hl);
  }

  static styles = [css`
    mark {
      background-color: var(--fk-color-highlight);
    }
  `]
}
