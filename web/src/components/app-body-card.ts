import { LitElement, css, html } from "lit";
import { customElement } from "lit/decorators.js";
import { linkStyles } from "./cssstyles.js";

@customElement('app-body-card')
class AppBodyCard extends LitElement {

  render() {
    return html`
      <section class="card" part=base>
        <slot></slot>
      </section>
    `
  }

  static styles = [linkStyles, css`
    :host {
      display: flex;
      flex: auto;
      align-items: center;
      justify-content: center;
    }

    .card {
      box-shadow: var(--sl-shadow-x-large);
      gap: var(--sl-spacing-x-large);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: white;
      border: 1px solid var(--sl-color-neutral-200);
      border-radius: 24px;
      padding: var(--sl-spacing-2x-large);
    }
  `];
}
