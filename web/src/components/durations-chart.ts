import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import * as d3 from 'd3';
import { css, html, LitElement, nothing, PropertyValues, TemplateResult } from 'lit';
import { customElement, property, query, state } from 'lit/decorators.js';
import { StyleInfo, styleMap } from 'lit/directives/style-map.js';
import { linkStyles } from './cssstyles.js';

type Datum = {
  outcome: WireTypes.DayOutcome,
  duration?: number,
}

@customElement('durations-chart')
export class DurationsChart extends LitElement {

  static render<T extends Datum>(options: {
    popover?: (datum: T) => TemplateResult<1>,
    data?: T[],
    click?: (e: CustomEvent<T>) => void,
    style?: Readonly<StyleInfo>,
  }) {
    return html`
      <durations-chart
        .data=${options.data}
        .renderPopover=${options.popover}
        style=${options.style ? styleMap(options.style) : nothing}
        @fk-select=${options.click}
      ></durations-chart>
    `;
  }

  @property({ attribute: false }) renderPopover?: (data: Datum) => TemplateResult<1>;
  @property({ attribute: false }) data?: Datum[];

  @query('svg') private _chart?: SVGSVGElement;

  protected override updated(_: PropertyValues) { this._updateChart(); }

  @state() private _hoveredDatum?: Datum;
  @state() private _showTooltip = false;

  connectedCallback(): void {
    super.connectedCallback();
    this._updateChart();
  }

  private _renderPopover(datum?: Datum) {
    if (!datum || !this.renderPopover)
      return nothing;

    return html`
      <h-box class=hover-popup>
        ${this.renderPopover(datum)}
      </h-box>
    `;
  }

  override render() {
    return html`
      <sl-popup ?active=${this._showTooltip} distance=10 strategy="fixed" placement=top-start>
        ${this._renderPopover(this._hoveredDatum)}
        <section slot=anchor @mouseleave=${this._onMouseLeaveChart}>
          <sl-resize-observer @sl-resize=${() => this._updateChart()}>
            <svg id=chart>
              <g class="bars"></g>
              <path class="underline"></path>
            </svg>
          </sl-resize-observer>
        </section>
      </sl-popup>
    `;
  }

  private _onMouseEnter(datum: Datum) {
    this._hoveredDatum = datum;
    this._showTooltip = true;
  }

  private _onClick(datum: Datum) {
    this.dispatchEvent(new CustomEvent<Datum>('fk-select', {
      detail: datum
    }));
  }

  private _onMouseLeaveChart(e: MouseEvent) {
    this._showTooltip = false;
    this._hoveredDatum = undefined;
  }

  private _updateChart() {
    const chart = this._chart;
    const data = this.data;
    if (!chart || !data?.length)
      return;

    // Use the last N runs, sorted by date ascending (older → newer)
    const { width, height } = chart.getBoundingClientRect();
    if (!width || !height) return;

    const W = Math.max(10, Math.floor(width));
    const H = Math.max(10, Math.floor(height));
    const P = { top: 1, right: 1, bottom: 1, left: 1 };

    // Scales
    const x = d3.scaleBand<number>()
      .domain(d3.range(data.length))
      .range([P.left, W - P.right])
      .paddingInner(0.05)
      .paddingOuter(0.05);

    const vals = data.map(d => d.duration ?? 0);
    const vmax = d3.max(vals) ?? 1;
    const y = d3.scaleLinear()
      .domain([-0.1 * vmax, vmax * 1.2]) // small headroom
      .range([H - P.bottom, P.top]);

    // Resize root SVG
    d3.select(chart)
      .attr('width', W)
      .attr('height', H)
      .attr('viewBox', `0 0 ${W} ${H}`);

    // ---- Bars ----
    const gBars = d3.select(chart).select<SVGGElement>('g.bars');
    gBars
      .selectAll<SVGRectElement, Datum>('rect.bar')
      .data(data, (_d, i) => i)
      .join('rect')
      .attr('class', d => d.outcome)
      .classed('bar', true)
      .classed('is-hovered', d => d === this._hoveredDatum)
      .attr('x', (_d, i) => (x(i) ?? 0))
      .attr('y', d => y(d.duration ?? 0))
      .attr('width', Math.max(1, x.bandwidth()))
      .attr('height', d => Math.max(0.5, H - P.bottom - y(d.duration ?? 0)));
    gBars
      .selectAll<SVGRectElement, Datum>('path.topstroke')
      .data(data, (_d, i) => i as any)
      .join('path')
      .attr('class', d => d.outcome)
      .classed('topstroke', true)
      .classed('is-hovered', d => d === this._hoveredDatum)
      .attr('d', (d, i) => {
          const x0 = x(i) ?? 0;
          const x1 = x0 + Math.max(1, x.bandwidth());
          const y0 = y(d.duration ?? 0);
          return `M${x0},${y0} H${x1}`;
        })
      ;
    gBars
      .selectAll<SVGRectElement, Datum>('rect.hoverbar')
      .data(data, (_d, i) => i)
      .join('rect')
      .on('mouseenter', (event, d) => this._onMouseEnter(d))
      .on('click', (event, d) => this._onClick(d))
      .classed('hoverbar', true)
      .attr('x', (_d, i) => (x(i) ?? 0))
      .attr('y', d => 0)
      .attr('width', Math.max(1, x.bandwidth()))
      .attr('height', d => Math.max(0.5, H - P.bottom));

    // ---- Underline ----
    const underlineY = H - P.bottom;
    d3.select(chart)
      .select<SVGPathElement>('path.underline')
      .attr('d', `M${P.left},${underlineY} H${W - P.right}`);
  }

  static styles = [linkStyles, css`
    :host {
      display: block;
      flex: auto;
    }

    section {
      width: 100%;
      height: 100%;
    }

    .selection {
      fill: none;
      stroke-width: 1;
      stroke: var(--sl-color-neutral-200);
    }

    #chart {
      width: 100%;
      height: 100%;
    }

    .underline {
      fill: none;
      stroke-width: 1;
      stroke: var(--sl-color-neutral-300);
    }

    .expected {
      --color: var(--fk-color-outcome-expected);
    }
    .flaked {
      --color: var(--fk-color-outcome-flaked);
    }
    .regressed {
      --color: var(--fk-color-outcome-regressed);
    }
    .skipped {
      --color: var(--fk-color-outcome-skipped);
    }
    .unexpected {
      --color: var(--fk-color-outcome-unexpected);
    }
    .idle, .untested {
      --color: transparent;
    }

    .hoverbar {
      cursor: pointer;
      fill: transparent;

      &:hover {
        fill: color-mix(in oklch, black, transparent 90%);
      }
    }

    .bar {
      shape-rendering: geometricPrecision;
      fill: color-mix(in oklch, var(--color), white 60%);

      &.is-hovered {
        filter: brightness(0.8);
      }
    }

    .topstroke {
      stroke: var(--color);
      stroke-width: 2px;
      &.is-hovered {
        filter: brightness(0.8);
      }
    }

    .trend {
      stroke: currentColor; /* inherits text color; tweak via CSS where used */
    }

    .hover-popup {
      background: white;
      padding: var(--sl-spacing-small);
      border: 1px solid var(--sl-color-neutral-300);
      border-radius: var(--sl-border-radius-medium);
      pointer-events: none;
      outline: 2px solid white;
    }
  `];
}
