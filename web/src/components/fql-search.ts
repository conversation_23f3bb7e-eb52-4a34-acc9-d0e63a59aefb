import { Query, QueryParseOptions } from '@flakiness/server/common/fql/query.js';
import CodeMirror from 'codemirror';
import 'codemirror/addon/display/placeholder';
import 'codemirror/addon/edit/closebrackets';
import { LitElement, PropertyValueMap, PropertyValues, css, html, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { codemirrorCSS } from '../codemirrorCSS.js';
import { linkStyles } from './cssstyles.js';

type HighlightToken = {
  from: number, to: number, cls: string,
}

function getHighlightTokens(query: string, options: QueryParseOptions): HighlightToken[] {
  const { matchers, unparsed } = Query.parseMatchers(query, options);
  const tokens: HighlightToken[] = [];
  for (const matcher of matchers) {
    // This should never happen.
    if (!matcher.sourceInfo)
      continue;
    tokens.push({
      from: matcher.sourceInfo.first.from,
      to: matcher.sourceInfo.last.to,
      cls: 'fql-' + matcher.extractor.type,
    });
  }
  for (const token of unparsed) {
    tokens.push({
      from: token.from,
      to: token.to,
      cls: `fql-unparsed`,
    });
  }
  tokens.sort((t1, t2) => t1.from - t2.from);
  return tokens;
}

CodeMirror.defineMode('fql', (config, options) => {
  return {
    startState: () => ({
    }),
    token: (stream, state) => {
      if (!state.tokens)
        state.tokens = getHighlightTokens(stream.string, options as QueryParseOptions).reverse();
      const tokens: HighlightToken[] = state.tokens;
      while (tokens.length && tokens.at(-1)!.to < stream.pos)
        tokens.pop();

      const token = tokens.at(-1);
      if (!token) {
        stream.skipToEnd();
        return null;
      }
      if (stream.pos < token.from) {
        while (stream.pos !== token!.from) stream.next();
        return null;  
      }
      while (stream.pos < token.to) stream.next();
      tokens.pop();
      return token.cls;
    },
  }
})

export class FqlChangedEvent extends Event {
  static readonly eventName = 'fql-changed';
  readonly query: string;

  constructor(query: string) {
    super(FqlChangedEvent.eventName, {
      bubbles: true,
      composed: true,
      cancelable: false,
    });
    this.query = query;
  }
}

@customElement('fql-search')
export class FQLSearch extends LitElement {

  @property({ attribute: 'q' }) query?: string;
  @property({ attribute: 'readonly', type: Boolean }) readonly: boolean = false;
  @property({ attribute: 'disallow-tests', type: Boolean }) disallowTests?: boolean;

  private _editor?: CodeMirror.Editor;

  protected willUpdate(_changedProperties: PropertyValues): void {
    if (_changedProperties.has('disallowTests')) {
      this._editor?.setOption('mode', {
        name: 'fql', disallowTestFilters: this.disallowTests,
      } as any)
    }
  }

  render() {
    return html`
      <v-box style="gap: var(--sl-spacing-x-small)">
        <div id=editor class=${classMap({
          active: !!this.query,
        })}>
          <sl-icon name=search></sl-icon>
          <sl-resize-observer id=editor-host @sl-resize=${this._onResize}>
          </sl-resize-observer>
          ${this.readonly ? nothing : html`
            <sl-icon-button @click=${() => {
              this._editor?.setValue('');
              this._setQuery('');
            }} tabindex=-1 name=x-circle-fill></sl-icon-button>
          `}
        </div>
        <a class=hint href="/docs/concepts/fql/">
          <sl-icon name=info-circle></sl-icon>
          Learn more about FQL
        </a>
      </v-box>
    `;
  }

  protected updated(_changedProperties: PropertyValueMap<any> | Map<PropertyKey, unknown>): void {
    if (this._editor && this._editor.getValue() !== this.query)
      this._editor.setValue(this.query ?? '');

    this._editor?.setOption('readOnly', this.readonly ? 'nocursor' : false);
  }

  protected firstUpdated(_changedProperties: PropertyValueMap<any> | Map<PropertyKey, unknown>): void {
    this._editor = CodeMirror(this.renderRoot.querySelector('#editor-host')!, {
      value: this.query ?? '',
      lineNumbers: false,
      lineWrapping: false,
      scrollbarStyle: 'null',
      placeholder: 'filter tests',
      autoCloseBrackets: true,
      mode: {
        name: 'fql',
        disallowTestFilters: !!this.disallowTests,
      },
    } as any);

    this._editor.setOption('extraKeys', {
      Enter: (instance) => {
        this._setQuery(instance.getValue(), true /* forceDispatch */);
      },
      'Tab': false,
      'Shift-Tab': false,
      'Meta-D': false,
    });

    // Optional: Handle paste to strip new lines
    this._editor.on("beforeChange", function(instance, change) {
      if (!change.update)
        return true;
      const newtext = change.text.join("").replace(/\n/g, ""); // remove ALL \n !
      change.update(change.from, change.to, [newtext]);
      return true;
    });

    this._editor.on("blur", (instance, change)  => {
      this._setQuery(instance.getValue());
    });
  }

  private _setQuery(query: string, forceDispatch?: boolean) {
    if (!forceDispatch && this.query === query)
      return;
    this.query = query;
    this.dispatchEvent(new FqlChangedEvent(query));
  }

  private _onResize() {
    // Hack to support FQL Search in sl-display: since the display has animation that
    // zooms in, we'll schedule one more refresh after the zoom to properly update editor.
    setTimeout(() => this._editor?.refresh(), 1000);
    this._editor?.refresh();
  }

  static styles = [linkStyles, css`
    :host {
      flex: auto;
    }

    .hint {
      display: flex;
      gap: var(--sl-spacing-2x-small);
      align-items: center;
      font-size: var(--sl-font-size-x-small);
      color: var(--sl-color-neutral-400) !important;
    }

    #editor {
      --fk-selection-color: #BBDEFB;
      height: 100%;
      display: flex;
      border-radius: var(--sl-border-radius-large);
      height: calc(var(--sl-input-height-medium) - var(--sl-input-border-width)* 2);
      padding-left: var(--sl-input-spacing-medium);
      align-items: center;
      line-height: normal;
      
      background-color: var(--sl-input-background-color);
      border: solid var(--sl-input-border-width) var(--sl-input-border-color);
      transition: var(--sl-transition-fast) color, var(--sl-transition-fast) border, var(--sl-transition-fast) box-shadow, var(--sl-transition-fast) background-color;

      &:hover {
        background-color: var(--sl-input-background-color-hover);
        border-color: var(--sl-input-border-color-hover);
      }
      &:has(.CodeMirror-focused) {
        background-color: var(--sl-input-background-color-focus);
        border-color: var(--sl-input-border-color-focus);
        box-shadow: 0 0 0 var(--sl-focus-ring-width) var(--sl-input-focus-ring-color);
      }

      &.active {;
      }

      .CodeMirror-placeholder {
        color: var(--sl-input-placeholder-color);
      }

      .CodeMirror {
        font-family: var(--sl-font-sans);
        font-size: var(--sl-font-size-medium);
        color: var(--sl-input-color);
      }
      
      sl-icon {
        margin-right: var(--sl-spacing-small);
      }
    }

    sl-icon-button {
      margin: 0 var(--sl-spacing-small);
      
      &::part(base) {
        transition: var(--sl-transition-fast) color;
        color: var(--sl-input-icon-color);
      }

      &::part(base):hover {
        color: var(--sl-input-icon-color-hover);
      }
    }

    #editor:has(.CodeMirror-empty) sl-icon-button {
      visibility: hidden;
    }

    .cm-fql-test, .cm-fql-env, .cm-fql-status, .cm-fql-error {
    }

    .cm-fql-test {
      /* background-color: rgb(0, 255, 0, 0.1); */
    }

    .cm-fql-env {
      background-color: rgba(0, 0, 255, 0.05);
    }

    .cm-fql-status {
      background-color: rgba(255, 255, 0, 0.33);
    }

    .cm-fql-error {
      background-color: rgba(255, 0, 0, 0.15);
    }

    .cm-fql-annotation {
      background-color: #e1f5fe;
    }

    .cm-fql-tag {
      background-color: #eeeeee;
    }

    .cm-fql-unparsed {
      color: red;
      border-bottom-style: dotted;
      font-weight: bold;
    }

  `, codemirrorCSS]
}
