import { Query } from '@flakiness/server/common/fql/query.js';
import { LitElement, css, html, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { linkStyles } from './cssstyles.js';
import { highlight } from './hl-span.js';

@customElement('test-tag')
class TestTag extends LitElement {
  @property({ attribute: false }) tag?: string;
  @property({ attribute: false }) fql?: Query;

  render() {
    if (!this.tag)
      return nothing;
    const hl = this.fql?.highlightTag(this.tag);
    return html`<a class=body><span class=hash ?is-matching=${hl && hl.length > 0}>#</span>${highlight(this.tag, hl)}</a>`
  }

  static styles = [linkStyles, css`
    .body {
      color: var(--sl-color-neutral-400) !important;
    }

    [is-matching] {
      background-color: var(--fk-color-highlight);
    }
  `];
}
