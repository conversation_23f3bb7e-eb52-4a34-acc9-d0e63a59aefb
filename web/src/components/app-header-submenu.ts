import { css, html, LitElement } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { linkStyles } from './cssstyles.js';

@customElement('app-header-submenu')
class AppHeaderSubmenu extends LitElement {
  @property({  }) href?: string;
  @property({ type: Boolean }) active?: boolean;

  @property({ reflect: true }) slot: string = 'second-line';

  override render() {
    return html`
      <a class=${classMap({ active: !!this.active })} href=${this.href}>
        <span class=hoverable-button>
          <slot></slot>
        </span>
      </a>
    `;
  }

  static styles = [linkStyles, css`
    a {
      display: block;
      padding: 0 0 var(--sl-spacing-2x-small) 0;
      position: relative;
      text-decoration: none !important;

      .hoverable-button {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: var(--sl-spacing-small);
        padding: var(--sl-spacing-small);
        border-radius: var(--sl-border-radius-medium);

        &:hover {
          background-color: var(--sl-color-neutral-100);
        }
      }

      &.active {
        font-weight: var(--sl-font-weight-semibold);
      }

      &.active::after {
        background: #fd8c73;
        border-radius: var(--sl-border-radius-medium);
        bottom: -1px;
        content: "";
        height: 2px;
        position: absolute;
        right: 50%;
        transform: translate(50%, -50%);
        width: 100%;
        z-index: 1;
      }
    }
  `];
}
