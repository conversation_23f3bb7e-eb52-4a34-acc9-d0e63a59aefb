import { consume, ContextType } from '@lit/context';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { Temporal } from 'temporal-polyfill';
import { contexts } from '../contexts.js';
import { PageOrganizationBilling } from '../page-organization-billing.js';
import { linkStyles } from './cssstyles.js';

@customElement('subscription-trial-banner')
class SubscriptionTrialBanner extends LitElement {
  @property({ attribute: false }) orgSlug?: string;

  @consume({ context: contexts.org, subscribe: true }) private _org?: ContextType<typeof contexts.org>;
  @consume({ context: contexts.billing, subscribe: true }) private _billing?: ContextType<typeof contexts.billing>;

  render() {
    const sub = this._billing?.subscription;
    const org = this._org;
    const trialEnd = sub?.trialEnd;
    
    if (!org || !sub || !trialEnd || !sub.missingPaymentMethod || !this.orgSlug)
      return nothing;

    const endInstant = Temporal.Instant.fromEpochMilliseconds(trialEnd);
    const nowInstant = Temporal.Now.instant();

    if (Temporal.Instant.compare(endInstant, nowInstant) <= 0)
      return html`<div>Trial has ended</div>`;

    const duration = endInstant.since(nowInstant);

    let timeText = '';
    const totalDays = duration.total({ unit: 'days' });
    const totalHours = duration.total({ unit: 'hours' });
    const totalMinutes = duration.total({ unit: 'minutes' });

    if (totalDays >= 1) {
      const days = Math.ceil(totalDays);
      timeText = `${days} day${days !== 1 ? 's' : ''}`;
    } else if (totalHours >= 1) {
      const hours = Math.floor(totalHours);
      timeText = `${hours} hour${hours !== 1 ? 's' : ''}`;
    } else {
      const minutes = Math.floor(totalMinutes);
      timeText = `${minutes} minute${minutes !== 1 ? 's' : ''}`;
    }

    return html`
      <section>
        <div>
        Free Trial for organization <strong>${org.orgName}</strong> will end in <strong>${timeText}</strong>. Please update your <a href=${PageOrganizationBilling.url({ orgSlug: this.orgSlug })}>payment method</a> to avoid disruption.</div>
        </div>
      </section>
    `
  }

  static styles = [linkStyles, css`
    section {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: var(--sl-spacing-2x-small);
      background-color: #ffc107;
    }

    a {
      text-decoration: underline;
    }
  `];
}
