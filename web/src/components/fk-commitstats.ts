import { wireOutcomesToOutcome, type WireTypes } from '@flakiness/server/common/wireTypes.js';
import { Multimap } from '@flakiness/shared/common/multimap.js';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { LinkRenderer } from '../contexts.js';
import { linkStyles } from './cssstyles.js';


@customElement('fk-commitstats')
export class FKCommitStats extends LitElement {
  @property({ attribute: false }) project?: WireTypes.Project;
  @property({ attribute: false }) stats?: WireTypes.CommitStats;
  @property({ attribute: false }) baseline?: number;
  @property({ attribute: false }) runlinker?: LinkRenderer<WireTypes.RunStats>;

  override render() {
    const commitStats = this.stats;
    if (!commitStats)
      return nothing;
    const classified = new Multimap<WireTypes.Outcome, WireTypes.RunStats>();
    for (const run of commitStats.runs)
      classified.set(wireOutcomesToOutcome(run.testStatsOutcomes, this.project?.acceptableFlakinessRatio ?? 0)!, run);

    const okRuns = [
      classified.getAll('expected'),
      classified.getAll('skipped'),
    ].flat();

    return html`
      <h-box class=container style="gap: var(--sl-spacing-large);"> 
        <runstats-selector size=small .outcome=${wireOutcomesToOutcome(commitStats.testStats, this.project?.acceptableFlakinessRatio ?? 0)} .runs=${commitStats.runs} .runlinker=${this.runlinker}></runstats-selector>

        <fk-commit .project=${this.project} .commit=${commitStats.commit}></fk-commit>

        <x-filler></x-filler>

        ${commitStats.durationMs === undefined ? nothing :
          this.baseline ?
            html`<fk-trend .ms=${commitStats.durationMs} .baseline=${this.baseline} duration></fk-trend>` :
            html`<time-interval .ms=${commitStats.durationMs}></time-interval>`
        }
      </h-box>
    `;
  }

  static styles = [linkStyles, css`
    runstats-selector {
      width: 12ex;
      text-align: center;
      flex: none;
    }

    fk-trend, time-interval {
      flex: none;
    }

    fk-commit {
      min-width: 40ch;
      flex: auto;
    }

    :host {
      display: block;
      border: 1px solid var(--fk-color-border);
      border-radius: var(--sl-border-radius-large);
    }

    .container {
      padding: var(--sl-spacing-small) var(--sl-spacing-medium);

      &:hover {
        background-color: var(--fk-color-tablerow-hover);
      }
    }
  `];
}

