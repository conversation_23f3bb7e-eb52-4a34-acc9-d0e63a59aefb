import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { LitElement, TemplateResult, css, html, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import ms from 'ms';
import { linkStyles } from './cssstyles.js';

const INFINITY = html`<sl-icon name=infinity></sl-icon>`;
const DASH = html`<sl-icon name=dash-lg></sl-icon>`;

@customElement('product-plans-view')
class ProductPlansView extends LitElement {
  @property({ attribute: false }) plans?: WireTypes.ProductPlan[];
  @property({ attribute: false }) selectedPlanId?: string;
  @property({ attribute: false }) subscription?: WireTypes.Subscription;
  @property({ attribute: false }) loading?: boolean;

  @property({ type: Boolean, attribute: 'no-enterprise' }) noEnterpriseColumn?: boolean;
  @property({ type: <PERSON><PERSON>an, attribute: 'no-subscribe' }) noSubscribeButtons?: boolean;
  @property({ type: Boolean, attribute: 'plan-selector' }) planSelect?: boolean;
  @property({ type: Boolean, attribute: 'show-del' }) showTrashButton?: boolean;
  @property({ type: Boolean, attribute: 'show-plan-id' }) showPlanId?: boolean;

  private _formatPrice(plan: WireTypes.ProductPlan): TemplateResult<1> {
    if (!plan.price)
      return html`<strong>Free</strong>`;
    if (plan.billing === 'month' || plan.billing === 'year') {
      const price = plan.billing === 'year' ? plan.price / 12 : plan.price;
      return html`
        <v-box style="gap: var(--sl-spacing-2x-small)">
          <div><strong>$${price} / month</strong> </div>
          <div style="font-size: var(--sl-font-size-small); color: var(--sl-color-neutral-500);">
            ${plan.billing === 'year' ? `$${plan.price} billed annually` : 'billed monthly'}
          </div>
        </v-box>
      `;
    }
    return html`<strong>$${plan.price}/${plan.billing}</strong>`;
  }

  private _formatTrial(plan: WireTypes.ProductPlan): TemplateResult<1> {
    const trialEnd = this.subscription?.trialEnd ?? 0;
    const isRunningTrial = Date.now() < trialEnd;
    if (plan.id === this.subscription?.plan.id) {
      return isRunningTrial ? html`
        <sl-tooltip>
          <span slot=content>Until <sl-format-date month="long" day="numeric" year="numeric" date=${new Date(trialEnd).toISOString()}></sl-format-date></span>
          <span>${Math.ceil((trialEnd - Date.now()) / ms('1 day'))} days left</span>
        </sl-tooltip>` : html`${plan.trialDays ? 'Free trial has ended' : DASH}
        `;
    }
    if (plan.trialDays)
      return html`${plan.trialDays} days`;
    return DASH;
  }

  private _formatSeats(plan: WireTypes.ProductPlan): TemplateResult<1> {
    if (plan.seats === undefined)
      return INFINITY;
    if (plan.id === this.subscription?.plan.id)
      return html`Used ${this.subscription.usedSeats} out of ${plan.seats}`
    return html`Up to ${plan.seats}`;
  }

  private _formatTestRuns(plan: WireTypes.ProductPlan): TemplateResult<1> {
    if (plan.testRuns === undefined || !plan.priceExtraTestRuns)
      return INFINITY;
    if (plan.id === this.subscription?.plan.id)
      return html`${this.subscription.usedTestRuns.toLocaleString()} out of ${plan.testRuns.toLocaleString()}/${plan.billing}`
    return html`${plan.testRuns.toLocaleString()}/${plan.billing}`;
  }

  private _formatAdditionalRuns(plan: WireTypes.ProductPlan): TemplateResult<1> {
    if (!plan.priceExtraTestRuns)
      return DASH;
    const pricePerThousand = plan.priceExtraTestRuns * 1000;
    return html`$${pricePerThousand} per 1,000<br>runs/${plan.billing}`;
  }

  private _formatStorage(plan: WireTypes.ProductPlan): TemplateResult<1> {
    // If extra storage price is 0, then this is unlimited usage.
    if (plan.storage === undefined || !plan.priceExtraStorage)
      return INFINITY;
    if (plan.id === this.subscription?.plan.id)
      return html`${this.subscription.usedStorage.toLocaleString()}GB out of ${plan.storage}GB`
    return html`${plan.storage}GB`;
  }

  private _formatAdditionalStorage(plan: WireTypes.ProductPlan): TemplateResult<1> {
    if (!plan.priceExtraStorage)
      return DASH;
    return html`$${(plan.priceExtraStorage).toFixed(2)} GB-${plan.billing}`;
  }

  private _renderActionButton(plan: WireTypes.ProductPlan): TemplateResult<1> {
    if (this.planSelect) {
      return html`
        <sl-button ?disabled=${plan.id === this.selectedPlanId} @click=${() => {
          this.dispatchEvent(new CustomEvent<WireTypes.ProductPlan>('fk-select', {
            detail: plan
          }));
        }}>Select plan</sl-button>
      `
    }
    if (this.subscription?.plan.id === plan.id) {
      return html`
        <sl-button ?disabled=${this.loading} variant=${
          this.subscription.missingPaymentMethod ? 'warning' :
          this.subscription.hasBillingIssues ? 'danger' : 'success'} @click=${() => {
          this.dispatchEvent(new CustomEvent<WireTypes.ProductPlan>('fk-select', {
            detail: plan
          }));
        }}>${this.subscription.missingPaymentMethod ? 'Update payment' :
          this.subscription.hasBillingIssues ? 'Payment required' : 'Current Plan'}</sl-button>
      `;
    }
    return html`
      <sl-button ?disabled=${this.loading} @click=${() => {
        if (this.subscription) {
          const result = confirm(`This will use your payment on-file to immediately switch your current plan.`);
          if (!result)
            return;
        }
        this.dispatchEvent(new CustomEvent<WireTypes.ProductPlan>('fk-select', {
          detail: plan
        }));
      }}>${this.subscription ? 'Switch' : 'Subscribe'}</sl-button>
    `;
  }

  private _renderFeatures() {
    return html`
      <x-column class=features-column>
        <h-box class=header></h-box>
        <h-box class=price>Price</h-box>
        <h-box class=trial>Free Trial</h-box>
        <h-box class=seats>Seats</h-box>
        <h-box class=storage style="gap: var(--sl-spacing-2x-small);">
          <span>Storage</span>
        </h-box>
        <h-box class=extra-storage style="gap: var(--sl-spacing-2x-small);">
          <span>Additional storage</span>
          <sl-tooltip>
            <div slot=content>
              Storage is billed using gigabyte-month (GB-month) as the billing metric. A GB-month is calculated by averaging the peak storage per day over a billing period (30 days).
              For example:
              <ul>
                <li>Storing 1 GB constantly for 30 days will be charged as 1 GB-month.</li>
                <li>Storing 3 GB constantly for 30 days will be charged as 3 GB-month.</li>
                <li>Storing 1 GB for 5 days, then 3 GB for the remaining 25 days will be charged as <code>1 GB * 5/30 month + 3 GB * 25/30 month = 2.66 GB-month</code></li>
              </ul>
            </div>
            <sl-icon-button name=question-circle></sl-icon>
          </sl-tooltip>
        </h-box>
        <h-box class=data-retention>Data Retention</h-box>
        <h-box class=deployment>Deployment options</h-box>
        <h-box class=support>Support</h-box>
        <h-box class=subscribe></h-box>
      </x-column>
    `;
  }

  private _renderEnterprise() {
    return html`
      <x-column class=enterprise-column>
        <h-box class="header">Enterprise</h-box>
        <h-box class=price>Custom Pricing</h-box>
        <h-box class=trial>Custom</h-box>
        <h-box class=seats>Custom Allocation</h-box>
        <h-box class=storage>Custom</h-box>
        <h-box class=extra-storage>Custom</h-box>
        <h-box class=data-retention>Custom</h-box>
        <h-box class=deployment>Cloud or Self-Hosted</h-box>
        <h-box class=support>Dedicated Support</h-box>
        <h-box class=subscribe>
          <div>Reach out <fk-contact-us></fk-contact-us></div>
        </h-box>
      </x-column>
    `;
  }

  private _renderPlan(plan: WireTypes.ProductPlan) {
    return html`
      <x-column class=${classMap({
        plan: true,
        selected: plan.id === (this.selectedPlanId ?? this.subscription?.plan.id),
        issues: !!this.subscription?.hasBillingIssues
      })}>
        <h-box class=header style="gap: var(--sl-spacing-2x-small)">
          <div>
            ${plan.name}
            ${this.showPlanId ? html`<sl-copy-button value=${plan.id} copy-label="copy plan id"></sl-copy-button>` : nothing}
          </div>
          ${plan.orgSlug ? html`<sl-tag size=small pill variant=success>special offer</sl-tag>` : nothing}
          <sl-icon class=btn-del name=trash @click=${() => {
            this.dispatchEvent(new CustomEvent<WireTypes.ProductPlan>('fk-remove', { detail: plan }));
          }}></sl-icon>
        </h-box>
        <h-box class=price>
          ${this._formatPrice(plan)}
        </h-box>
        <h-box class=trial>
          ${this._formatTrial(plan)}
        </h-box>
        <h-box class=seats>
          ${this._formatSeats(plan)}
        </h-box>
        <h-box class=storage>
          ${this._formatStorage(plan)}
        </h-box>
        <h-box class=extra-storage>
          ${this._formatAdditionalStorage(plan)}
        </h-box>
        <h-box class=data-retention>
          ${plan.maxDataRetentionDays} days
        </h-box>
        <h-box class=deployment>
          Cloud
        </h-box>
        <h-box class=support>
          Standard
        </h-box>
        <div class=subscribe>
          ${this._renderActionButton(plan)}
        </div>
      </x-column>
    `;
  }

  render() {
    const idToPlan = new Map<string, WireTypes.ProductPlan>();
    for (const plan of this.plans ?? [])
      idToPlan.set(plan.id, plan);
    if (this.subscription)
      idToPlan.set(this.subscription.plan.id, this.subscription.plan);

    const plans = [...idToPlan.values()].toSorted((a, b) => {
      if (a.price !== b.price)
        return a.price - b.price;
      if (a.testRuns !== b.testRuns)
        return (a.testRuns ?? 0) - (b.testRuns ?? 0);
      if (a.storage !== b.storage)
        return (a.storage ?? 0) - (b.storage ?? 0);
      if (a.priceExtraTestRuns !== b.priceExtraTestRuns)
        return a.priceExtraTestRuns - b.priceExtraTestRuns;
      if (a.priceExtraStorage !== b.priceExtraStorage)
        return a.priceExtraStorage - b.priceExtraStorage;
      if (a.trialDays !== b.trialDays)
        return (a.trialDays ?? 0) - (b.trialDays ?? 0);
      return a.name < b.name ? -1 : 1;
    });

    return html`
      <section>
        ${this._renderFeatures()}
        ${plans.map(plan => this._renderPlan(plan))}
        ${this._renderEnterprise()}

        <div class=add-button>
          <slot></slot>
        </div>
      </section>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      display: block;
    }

    :host(:not([show-del])) .btn-del {
      display: none;
    }

    :host([no-subscribe]) .subscribe {
      display: none;
    }

    :host([no-enterprise]) .enterprise-column {
      display: none;
    }

    section {
      display: grid;
      grid-template-rows: repeat(10, auto);
      position: relative;

      .add-button {
        position: absolute;
        visibility: hidden;
        top: var(--sl-spacing-2x-small);
        right: var(--sl-spacing-2x-small);
      }

      &:hover .add-button {
        visibility: visible;
      }
    }

    x-column {
      grid-row: 1/-1;
      position: relative;

      display: grid;
      grid-template-rows: subgrid;

      &.selected {
        background-color: var(--sl-color-warning-50);
        border: 1px solid var(--sl-color-warning-300);
        border-radius: var(--sl-border-radius-medium);

        &.issues {
          background-color: var(--sl-color-danger-50);
          border: 1px solid var(--sl-color-danger-300);
        }
      }

      &.plan {
      }

      & > * {
        padding: var(--sl-spacing-small);
        border-bottom: 1px solid var(--fk-color-border);
      }

      &.features-column > * {
        padding-left: 0;
        border-bottom: 1px solid var(--fk-color-border);
      }

      .subscribe {
        display: flex;
        padding-top: var(--sl-spacing-x-large);
        align-items: center;
        border-bottom: none;

        fk-contact-us {
          color: blue;
        }

        & > * {
          flex: auto;
        }
      }

      .header {
        font-weight: var(--sl-font-weight-bold);
        font-size: var(--sl-font-size-large);
        padding-bottom: var(--sl-spacing-large);
      }
    }

    .btn-del {
      color: var(--sl-color-neutral-300);
      cursor: pointer;
      position: absolute;
      display: none;
      right: var(--sl-spacing-small);

      &:hover {
        color: var(--sl-color-neutral-900);
      }
    }

    x-column:hover .btn-del {
      display: block;
    }
  `];
}
