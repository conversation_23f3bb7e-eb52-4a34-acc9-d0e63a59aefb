import { ContextType } from '@lit/context';
import { SlInput } from '@shoelace-style/shoelace';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, query } from 'lit/decorators.js';
import { api } from '../api.js';
import { consume, contexts, EventTask } from '../contexts.js';
import { link_source_with_github_pat } from '../docs/link_source_with_github_pat.js';
import { githubMarkdownCSS } from '../githubMarkdownCSS.js';
import { githubUtils } from '../githubUtils.js';
import { assert } from '../utils.js';
import { docLinkStyles } from './cssstyles.js';
import { InputWithStatus } from './input-with-status.js';

export type GithubPat = {
  owner: string,
  repo: string,
  accessToken: string,
}

@customElement('pick-github-pat')
class PickGithubPat extends LitElement {
  @consume({ context: contexts.serverInfo, subscribe: true }) private _serverInfo?: ContextType<typeof contexts.serverInfo>;

  @query('input-with-status') private _repoInput?: InputWithStatus;
  @query('sl-input') private _patInput?: SlInput;

  private _validateGithubAccess = new EventTask(this, 'github-pat', {
    autoRun: false,
    args: () => [this._repoInput?.value(), this._patInput?.value] as const,
    task: async ([repo, pat], { signal }): Promise<GithubPat> => {
      assert(repo !== undefined && pat !== undefined && pat.length > 0);
      // 1. Make sure source URL is parsable.
      const result = githubUtils.parseGithubURL(repo);
      let githubPat: GithubPat | undefined;
      if ('error' in result) {
        this._repoInput?.setStatus(false, result.error);
      } else {
        githubPat = {
          accessToken: pat,
          owner: result.owner,
          repo: result.repo,
        };
        const response = await api.github.checkPATAccess.GET(githubPat, { signal });
        if (response)
          this._repoInput?.setStatus(true, 'Access to repository');
        else
          this._repoInput?.setStatus(false, 'Failed to access repository'); 
      }
      this._patInput?.setCustomValidity(this._repoInput?.success() ? '' : this._repoInput?.message() ?? '');
      assert(githubPat);
      return githubPat;
    }
  });

  override render() {
    return html`
      ${this._serverInfo ? link_source_with_github_pat(this._serverInfo) : nothing}
      <input-with-status
        name="source_url"
        @sl-input=${() => this._validateGithubAccess.run()}
        type=url
        placeholder="https://github.com/owner/repo"
      >
        <sl-input name="access_token"
          required
          @sl-input=${() => this._validateGithubAccess.run()}
          @paste=${() => setTimeout(() => this._validateGithubAccess.run(), 0)}
          type=password
          placeholder="Access Token"
          password-toggle
        ></sl-input>            
      </input-with-status>
    `;
  }

  static styles = [docLinkStyles, githubMarkdownCSS, css`

    sl-input[data-user-invalid]::part(base) {
      border-color: var(--sl-color-danger-600);
    }

    sl-input:focus-within[data-user-invalid]::part(base) {
      border-color: var(--sl-color-danger-600);
      box-shadow: 0 0 0 var(--sl-focus-ring-width) var(--sl-color-danger-300);
    }
  `];
}