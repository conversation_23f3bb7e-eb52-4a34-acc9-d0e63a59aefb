import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { css, html, LitElement, nothing, TemplateResult } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { githubLinks } from '../githubLinks.js';
import { linkStyles } from './cssstyles.js';

@customElement('teststats-title')
class TestStatsTitle extends LitElement {
  @property({ attribute: false }) project?: WireTypes.Project;
  @property({ attribute: false }) testStats?: WireTypes.TestStats;

  render() {
    const stats = this.testStats;
    if (!stats)
      return nothing;
    const elements: TemplateResult[] = [];
    for (const title of stats.test.titles.slice(0, -1).filter(t => !!t)) {
      elements.push(html`<span>${title}</span>`);
      elements.push(html`<sl-icon name=chevron-right></sl-icon>`)
    }
    elements.pop();

    const testTitle = stats.test.titles.at(-1);
    const tags = (stats.tags ?? []).map(tag => html`<test-tag .tag=${tag}></test-tag>`);

    return html`
      <v-box style="gap: var(--sl-spacing-2x-small);">
        <span class=suite-titles>${elements}</span>
        <span class=test-title>${testTitle} ${tags}</span>
        ${stats.test.filePath ? html`
          <h-box style="gap: var(--sl-spacing-2x-small)">
            <a-ext href=${this.project ? githubLinks.testUrl(this.project, stats): nothing} class=test-link>${stats.test.filePath}:${stats.lineNumber}</a-ext>
            <sl-copy-button value=${`${stats.test.filePath}:${stats.lineNumber}`}></sl-copy-button>
          </h-box>
        ` : nothing}
      </v-box>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      display: block;
    }

    sl-copy-button {
      visibility: hidden;
    }

    *:hover > sl-copy-button {
      visibility: visible;
    }

    .suite-titles {
      font-size: var(--sl-font-size-small);
      color: var(--sl-color-neutral-500);
    }

    .test-title {
      font-size: var(--sl-font-size-2x-large);
    }

    .test-link {
      font-size: var(--sl-font-size-small);
      color: var(--sl-color-neutral-500);
    }

    sl-icon {
      font-size: 73%;
    }
  `];
}