import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';

import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { PageOrganizationMembers } from '../page-organization-members.js';
import { PageOrganization } from '../page-organization.js';
import { tasks } from '../tasks.js';
import { linkStyles } from './cssstyles.js';

@customElement('list-organizations-item')
export class ListOrganizationsItem extends LitElement {

  @property({ attribute: false }) org?: WireTypes.Organization;

  private _orgMembersTask = tasks.orgMembers(this, {
    orgSlug: () => this.org?.orgSlug,
  });

  private _orgProjectsTask = tasks.orgProjects(this, {
    orgSlug: () => this.org?.orgSlug,
  });

  override render() {
    if (!this.org)
      return nothing;
    return html`
      <h-box>
        <v-box style="gap: var(--sl-spacing-x-small)">
          <h-box class=item>
            
            <a href=${PageOrganization.url({ orgSlug: this.org.orgSlug })}>${this.org.orgSlug}</a>
            <icon-org-issues .org=${this.org}></icon-org-issues>
          </h-box>
          <h-box class=description>
            <h-box style="gap: var(--sl-spacing-2x-small)">
              <sl-icon src='/repo.svg'></sl-icon>
              <a href=${PageOrganization.url({ orgSlug: this.org.orgSlug })}>
                ${this._orgProjectsTask.render({
                  pending: () => html`<sl-skeleton effect=pulse style="width: 5ch" ></sl-skeleton>`,
                  complete: projects => projects.length === 1 ? `1 project` : `${projects.length} projects`,
                })}
              </a>
            </h-box>
            <span>•</span>
            <h-box style="gap: var(--sl-spacing-2x-small)">
              <sl-icon name=people></sl-icon>
              <a href=${PageOrganizationMembers.url({ orgSlug: this.org.orgSlug })}>
                ${this._orgMembersTask.render({
                  pending: () => html`<sl-skeleton effect=pulse style="width: 5ch" ></sl-skeleton>`,
                  complete: members => members.length === 0 ? `1 member` : `${members.length + 1} members`,
                })}
              </a>
            </h-box>
          </h-box>
        </v-box>

        <x-filler></x-filler>
        <span class=org-role>${this.org.access}</span>
      </h-box>
    `;
  }

  static styles = [linkStyles, css`
    .org-role {
      color: var(--sl-color-neutral-500);
      font-size: var(--sl-font-size-small);
    }

    .description {
      font-size: var(--sl-font-size-small);
      color: var(--sl-color-neutral-500);
    }
  `]
}

@customElement('list-organizations')
export class ListOrganizations extends LitElement {

  @property({ attribute: false }) orgs?: WireTypes.Organization[];

  override render() {

    return html`
      ${this.orgs?.length ? html`
        <section>
          ${this.orgs.map(org => html`
            <list-organizations-item .org=${org}></list-organizations-item>
          `)}
        </section>
      ` : html`<div class=no-items>No organizations</div>`}
    `;
  }

  static styles = [linkStyles, css`
    section {
      display: grid;
      grid-template-rows: 1fr;
      border-radius: var(--sl-border-radius-medium);
      border: 1px solid var(--sl-panel-border-color);
    }
    list-organizations-item + list-organizations-item {
      border-top: 1px solid var(--sl-panel-border-color);
    }
    list-organizations-item {
      padding: var(--sl-spacing-small);
    }
    list-organizations-item:hover {
      background-color: var(--fk-color-tablerow-hover);
    }
  `]
}
