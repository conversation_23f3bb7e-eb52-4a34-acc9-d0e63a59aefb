import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';

@customElement('icon-org-issues')
export class HBox extends LitElement {

  @property({ attribute: false }) org?: WireTypes.Organization;


  render() {
    if (!this.org?.restrictedCIUploads && !this.org?.restrictedProjectAccess)
      return nothing;
    return html`
      <sl-tooltip content="This organization has billing issues.">
        <sl-icon class=billing-issues name=exclamation-triangle></sl-icon>
      </sl-tooltip>
    `;
  }

  static styles = css`
    :host {
      display: flex;
    }

    sl-icon.billing-issues {
      color: var(--sl-color-warning-500);
    }
  `;
}
