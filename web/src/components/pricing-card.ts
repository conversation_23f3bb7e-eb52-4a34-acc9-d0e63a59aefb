import { css, html, LitElement, TemplateResult } from 'lit';
import { customElement, property, query, state } from 'lit/decorators.js';

import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { SlInputEvent, SlRange, SlSwitch } from '@shoelace-style/shoelace';

const tiers = [
  [30, 39],
  [20, 36],
  [50, 34],
  [Infinity, 30],
]

function computePrice(quantity: number, isAnnual: boolean): number {
  let price = 0;
  for (const [tierUsers, pricePerUser] of tiers) {
    const count = Math.min(quantity, tierUsers);
    price += count * pricePerUser;
    quantity -= count;
  }
  return isAnnual ? Math.ceil(price * 12 * 0.9) : price;
}

function tierPrice(monthly: number, isAnnual: boolean) {
  if (isAnnual)
    return `$` + Math.ceil(monthly * 12 * 0.9) + '/year';
  return `$` + monthly + '/month';
}

@customElement('pricing-card')
export class PricingCard extends LitElement {

  @query('sl-range') private _usersInput?: SlRange;
  @property({ attribute: false }) org?: WireTypes.Organization;
  @state() private _product: string = 'cloud';
  @state() private _users: number = 1;
  @state() private _isAnnual: boolean = false;

  override updated() {
    this._usersInput!.tooltipFormatter = value => `${value} users`;
  }

  override render() {
    const tierRows: TemplateResult[] = [];
    return html`
      <v-box style="gap: var(--sl-spacing-2x-large);">
        <h-box id=cloud-premise-toggle>
          <fk-tabbar selected=${this._product} no-divider>
            <fk-tab name=cloud @click=${() => { this._product = 'cloud'; } }>
              <h-box>
                <sl-icon name=cloud></sl-icon>
                <span>Cloud</span>
              </h-box>
            </fk-tab>
            <fk-tab name=premise @click=${() => { this._product = 'premise'; } }>
              <h-box>
                <sl-icon name=hdd-rack></sl-icon>
                <span>On-premise</span>
              </h-box>
            </fk-tab>
          </fk-tabbar>
        </h-box>

        <v-box>
          <h-box>
            <div>
              <div>
                <strong>Cloud</strong>
                (Hosted on our infrastructure)
              </div>
              <div>
                <span class=price>$${computePrice(this._users, this._isAnnual)}</span>
                <span class=price-suffix>/${this._isAnnual ? 'year' : 'month'}</span>
              </div>
            </div>
            <x-filler></x-filler>
            <v-box>
              <sl-switch ?checked=${this._isAnnual} @sl-input=${(event: SlInputEvent) => {
                this._isAnnual = (event.target as SlSwitch).checked;
              }}>Annual</sl-switch>
              <div>10% Discount</div>
            </v-box>
          </h-box>


          <sl-button style="align-self: center;width: 100px; display: block;" variant=success>Subscribe</sl-button>

          <v-box style="align-items: center;">
            <div>${this._users} user${this._users > 1 ? 's' : ''} per month</div>
            <sl-range @sl-input=${(event: SlInputEvent) => {
              this._users = (event.target as SlRange).value;
            }} min="1" max="21" step="1" class="range-with-custom-formatter"></sl-range>
          </v-box>

        </v-box>

        ${this._renderFeatures()}


        ${this._renderTiers()}
      </v-box>
    `;
  }

  private _renderFeatures() {
    return html`
      <v-box>
        <h-box>
          <sl-icon name=check-circle></sl-icon>
          <div>Secure hosting</div>
        </h-box>
        <h-box>
          <sl-icon name=check-circle></sl-icon>
          <div>60 GB storage included</div>
        </h-box>
        <h-box>
          <sl-icon name=check-circle></sl-icon>
          <div>1 year data retention</div>
        </h-box>
        <h-box>
          <sl-icon name=check-circle></sl-icon>
          <div>From 1 user</div>
        </h-box>
      </v-box>
    `;
  }

  private _renderTiers() {
    const tierRows: TemplateResult[] = [];
    let users = 0;
    for (const [tierUsers, pricePerUser] of tiers) {
      tierRows.push(html`
        <div class=row>
          <div class=tier>
            ${users === 0 ? `First` : `Next`} ${users + 1} – ${users + tierUsers} Users
          </div>
          <div class=tier-price>${tierPrice(pricePerUser, this._isAnnual)}</div>
        </div>
      `);
      users += tierUsers;
    }

    return html`
      <div class=table>
        <div class="row header">
          <div>Subscription plan</div>
          <div>${this._isAnnual ? 'Yearly' : 'Monthly'}, USD/user</div>
        </div>
        ${tierRows}
      </div>
    `
  }

  static styles = [css`
    :host {
      display: block;
    }

    .price {
      font-size: var(--sl-font-size-4x-large);
    }

    .price-suffix {
      color: var(--sl-color-neutral-500);
      align-self: bottom;
    }

    sl-range {
      flex: auto;
      --track-color-active: var(--sl-color-primary-600);
    }

    #cloud-premise-toggle {
      justify-content: center;
    }

    #content {
      display: flex;
      flex-direction: column;
    }

    .logo {
      width: 1em;
      height: 1em;
    }

    sl-icon[name=check-circle] {
      color: green;
      font-weight: bold;
    }

    .table {
      border: 1px solid var(--sl-color-neutral-300);
      border-radius: var(--sl-border-radius-large);
      display: grid;
      grid-template-columns: 1fr auto;

      .header {
        font-weight: var(--sl-font-weight-semibold);
        font-size: var(--sl-font-size-small);
      }

      .row {
        display: grid;
        grid-column: -1/1;
        grid-template-columns: subgrid;
        padding: var(--sl-spacing-x-small) var(--sl-spacing-large);
      }

      .row + .row {
        border-top: 1px solid var(--sl-color-neutral-300);
      }

      .tier-price {
        color: var(--sl-color-neutral-500);
      }
    }
  `]
}
