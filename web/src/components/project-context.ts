import { css, html, LitElement, TemplateResult } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { Temporal } from 'temporal-polyfill';
import { api } from '../api.js';
import { contexts, ContextTask, FKOrgChanged, FKProjectChanged } from '../contexts.js';
import { assert } from '../utils.js';
import { linkStyles } from './cssstyles.js';

@customElement('project-context')
export class ProjectContext extends LitElement {

  static render(options: {
    orgSlug?: string,
    projectSlug?: string,
  }, e: TemplateResult) {
    // Re-use the same TemplateResult so that the resulting element
    // could be efficiently rerendered by clients.
    return html`
      <project-context
        .orgSlug=${options.orgSlug}
        .projectSlug=${options.projectSlug}
      >${e}</project-context>
    `;
  }

  @property({ attribute: false }) orgSlug?: string;
  @property({ attribute: false }) projectSlug?: string;

  private _orgTask = new ContextTask(this, contexts.org, {
    args: () => [this.orgSlug] as const,
    task: async ([orgSlug], { signal }) => {
      assert(orgSlug);
      return await api.user.findOrganization.GET({ orgSlug }, { signal });
    }
  });

  private _orgBilling = new ContextTask(this, contexts.billing, {
    args: () => [this.orgSlug] as const,
    task: async ([orgSlug], { signal }) => {
      if (!orgSlug)
        return undefined;
      // The method will fail if the user has no access to the billing information.
      return await api.billing.status.GET({ orgSlug }, { signal }).catch(e => undefined);
    }
  });

  private _orgMembers = new ContextTask(this, contexts.orgMembers, {
    args: () => [this.orgSlug],
    task: async ([orgSlug], { signal }) => {
      assert(orgSlug);
      return await api.organization.listMembers.GET({ orgSlug }, { signal });
    }
  });

  private _projectTask = new ContextTask(this, contexts.project, {
    args: () => [this.orgSlug, this.projectSlug] as const,
    task: async ([orgSlug, projectSlug], { signal }) => {
      assert(orgSlug && projectSlug);
      return await api.project.findProject.GET({ orgSlug, projectSlug }, { signal });
    }
  });

  private _projectPullsTask = new ContextTask(this, contexts.projectPulls, {
    args: () => [this._projectTask.value, this.orgSlug, this.projectSlug] as const,
    task: async ([project, orgSlug, projectSlug], { signal }) => {
      assert(project && orgSlug && projectSlug);
      const result = await api.project.pulls.GET({
        orgSlug, projectSlug,
        prState: 'open',
        regressionWindowDays: project.regressionWindowDays,
        timeZoneId: Temporal.Now.timeZoneId(),
        testState: 'all',
        sortOptions: {
          axis: 'created',
          direction: 'desc',
        },
        pageOptions: {
          number: 0,
          size: 10,
        },
      }, { signal }).catch(e => {
        console.error(e);
        throw e;
      });
      assert(result);
      console.log(result);
      return result;
    },
  });

  private _projectReferencesTask = new ContextTask(this, contexts.projectReferences, {
    args: () => [this.orgSlug, this.projectSlug],
    task: async ([orgSlug, projectSlug], { signal }) => {
      assert(orgSlug && projectSlug);
      const result = await api.project.branches.GET({
        orgSlug, projectSlug,
        pageOptions: {
          number: 0,
          size: 10,
        },
      }, { signal }).catch(e => {
        console.error(e);
        throw e;
      });
      assert(result);
      return {
        defaultBranch: result.defaultBranch,
        branches: result.branches,
      };
    },
  });

  private _projectCollaborators = new ContextTask(this, contexts.projectCollaborators, {
    args: () => [this.orgSlug, this.projectSlug],
    task: async ([orgSlug, projectSlug], { signal }) => {
      assert(orgSlug && projectSlug);
      const result = await api.project.users.GET({
        orgSlug, projectSlug,
      }, { signal }).catch(e => {
        console.error(e);
        throw e;
      });
      assert(result);
      return result;
    },
  });

  override render(): unknown {
    const isNotFound = this._projectTask.render({
      complete: value => !value,
    }) || this._orgTask.render({
      complete: value => !value,
    });
    const isPaymentRequired = this._projectTask.render({
      error: (error: any) => error?.data?.code === 'PAYMENT_REQUIRED',
    });

    return html`
      <section @fk-project-changed=${(event: FKProjectChanged) => {
        if (event.orgSlug !== this.orgSlug || event.projectSlug !== this.projectSlug)
          return;
        this._projectTask.run();
        this._projectCollaborators.run();
      }} @fk-org-changed=${(event: FKOrgChanged) => {
        if (event.orgSlug !== this.orgSlug)
          return;
        this._orgTask.run();
        this._orgMembers.run();
        this._orgBilling.run();
      }}>
        ${isPaymentRequired ? html`<page-http-error code=402></page-http-error>` : 
          isNotFound ? html`<page-http-error code=404></page-http-error>` : html`<slot></slot>`}
      </section>
    `;
  }

  static styles = [linkStyles, css`
    :host, section {
      display: contents;
    }
  `];
}