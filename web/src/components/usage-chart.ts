import * as d3 from 'd3';
import { css, html, LitElement, PropertyValues } from 'lit';
import { customElement, property, query, state } from 'lit/decorators.js';
import { humanReadableBytes } from '../utils.js';
import { linkStyles } from './cssstyles.js';

@customElement('usage-chart')
export class UsageChart extends LitElement {
  
  @property({ attribute: false }) metrics: { timestamp: number, value: number }[] = [];
  @property({ attribute: false }) unit: string = '';
  @property({ attribute: false }) since?: Date;
  @property({ attribute: false }) until?: Date;

  @query("svg") private _chart?: SVGElement;

  @state() private _data: { date: Date, value: number }[] = [];
  @state() private _hoveredEntry?: { date: Date, value: number };

  private _xScale = d3.scaleBand<Date>();
  private _yScale = d3.scaleLinear<number>();

  protected override willUpdate(changedProperties: PropertyValues<this>): void {
    if (changedProperties.has('metrics') || changedProperties.has('unit') || changedProperties.has('since') || changedProperties.has('until'))
      this._data = this._computeData();
  }

  protected override updated(changedProperties: PropertyValues): void {
    this._updateChart();
  }

  private _computeData(): { date: Date, value: number }[] {
    if (!this.metrics || !this.since || !this.until)
      return [];

    const data = this.metrics.map(d => ({
      value: d.value,
      date: new Date(d.timestamp),
    }));

    // Group data by day
    const dataByDay = d3.rollup(
      data,
      group => group.reduce((sum, d) => sum + d.value, 0),
      d => d3.utcDay.floor(d.date)
    );

    // Create an array of all days in the range
    const allDays = d3.utcDay.range(this.since, this.until);
    return allDays.map(date => ({
      date,
      value: dataByDay.get(date) ?? 0
    }));
  }

  override render() {
    return html`
      <sl-resize-observer @sl-resize=${() => this._updateChart() }>
        <svg id=chart @mousemove=${this._onMouseMove} @mouseout=${this._onMouseOut}>
          <g id=timeseries></g>
          <g id=xaxis></g>
          <g id=yaxis></g>
        </svg>
      </sl-resize-observer>
    `;
  }

  private _onMouseMove(event: MouseEvent) {
    const [xFrom, xTo] = this._xScale.range();
    const [yTo, yFrom] = this._yScale.range();

    const [mx, my] = d3.pointer(event);
    if (mx < xFrom || xTo < mx || my < yFrom || yTo < my) {
      this._hoveredEntry = undefined;
      return;
    }

    // Compute distance from a bar to a point.
    const distance = (a: Date) => {
      const x1 = this._xScale(a)!;
      const x2 = x1 + this._xScale.bandwidth();
      if (mx < x1)
        return x1 - mx;
      if (mx > x2)
        return mx - x2;
      return 0;
    }
    this._hoveredEntry = this._data.reduce((a, b) => distance(a.date) < distance(b.date) ? a : b);
  }

  private _onMouseOut(event: MouseEvent) {
    this._hoveredEntry = undefined;
  }

  private _formatValue(d: number) {
    if (this.unit === 'bytes')
      return humanReadableBytes(d);
    return Number.isInteger(d) ? (d < 1000 ? d + '' : d3.format('.2s')(d)) : ''
  }

  private _updateChart() {
    const data = this._data;
    const chartBoundingBox = this._chart?.getBoundingClientRect();
    const chart = this._chart;

    if (!data.length || !chartBoundingBox || !chartBoundingBox.width || !chartBoundingBox.height || !chart)
      return;

    const WIDTH = chartBoundingBox.width;
    const HEIGHT = chartBoundingBox.height;

    const PADDING_TOP = 20;
    const PADDING_RIGHT = 20;
    const XAXIS_SIZE = 20;
    const YAXIS_SIZE = 50;

    const xScale = this._xScale
      .domain(data.map(d => d.date))
      .range([YAXIS_SIZE, WIDTH - PADDING_RIGHT])
      .padding(0.05);
    
    let period = 10;
    while ((data.length - 1) % period !== 0 && data.length % period < 2)
      ++period;
    // Assuming each label is 30px wide.
    const maxLabels = Math.floor(WIDTH / 40);
    period = Math.ceil(data.length / maxLabels);

    const xAxis = d3.axisBottom<Date>(xScale)
      .tickFormat((d, i) => d3.timeFormat("%b %d")(d))
      .tickValues(data.map((d, i) => (i % period === 0 ? d.date : null)).filter(d => d !== null))
      .tickSizeOuter(0);

    const yExtent = d3.extent(data.map(d => d.value)) as [number, number];
    const yMaxExtent = Math.abs(yExtent[1]) < 1e-9 ? 1 : Math.ceil(yExtent[1]*100 * 1.3)/100;
    const yScale = this._yScale
      .domain([0, yMaxExtent])
      .range([HEIGHT - XAXIS_SIZE, PADDING_TOP]);
    const yAxis = d3.axisLeft<number>(yScale);
    yAxis.tickFormat(this._formatValue.bind(this));
  
    if (this._hoveredEntry) {
      xAxis.tickValues([this._hoveredEntry.date]);
      xAxis.tickFormat((d, i) => d3.timeFormat("%b %d %Y")(d));
      yAxis.tickValues([this._hoveredEntry.value]);
    }

    d3.select(chart)
      .select<SVGGElement>('#xaxis')
      .call(xAxis)
      .attr('transform', `translate(0, ${HEIGHT - XAXIS_SIZE})`);
  
    d3.select(chart)
      .select<SVGGElement>('#yaxis')
      .call(yAxis)
      .attr('transform', `translate(${YAXIS_SIZE}, 0)`);

    d3.select(chart)
      .select('#timeseries')
      .selectAll<SVGRectElement, { date: Date, value: number }>('rect')
        .data(data)
        .join('rect')
        .attr("x", d => xScale(d.date)!)
        .attr("y", d => yScale(d.value))
        .attr("width", xScale.bandwidth())
        .attr("height", d => HEIGHT - XAXIS_SIZE - yScale(d.value))
        .attr('class', d => {
          if (!this._hoveredEntry)
            return '';
          return (d === this._hoveredEntry) ? '' : 'dimmed';
        })
  }

  static styles = [linkStyles, css`
    :host {
      display: flex;
      flex-direction: column;
      flex: auto;
    }

    :host[selected-commit-id] {
      cursor: pointer;
    }

    rect {
      rx: 2;
      ry: 2;
      fill: steelblue;
    }

    rect.dimmed {
      fill: var(--sl-color-neutral-300);
    }

    #chart {
      height: 100%;
      width: 100%;
      position: relative;
    }
  `];
}

