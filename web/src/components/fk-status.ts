import { LitElement, css, html } from 'lit';
import { customElement, property } from 'lit/decorators.js';

@customElement('fk-status')
class FKStatus extends LitElement {
  @property({ type: Boolean }) success?: boolean;

  render() {
    return html`
      <sl-icon class=${this.success ? 'success' : 'fail'} name=${this.success ? 'check-lg' : 'x-circle-fill'}></sl-icon>
    `;
  }

  static styles = [css`
    :host {
      display: flex;
    }

    sl-icon.success {
      color: var(--fk-color-outcome-expected);
    }

    sl-icon.fail {
      color: var(--fk-color-outcome-unexpected);
    }
  `];
}
