import { FlakinessReport } from '@flakiness/sdk/browser';
import { TimelineKey } from '@flakiness/server/common/timeline/timeline.js';
import { TimelineSplit } from '@flakiness/server/common/timeline/timelineSplit.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { Multimap } from '@flakiness/shared/common/multimap.js';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';

function aggregateEnvs(envs: WireTypes.RunEnvironment[]): WireTypes.AggregatedMetadata {
  const metadata = {
    configPath: new Set<FlakinessReport.GitFilePath>(),
    category: new Set<string>(),
    name: new Set<string>(),
    systemData: {
      osArch: new Set<string>(),
      osName: new Set<string>(),
    },
    userSuppliedData: new Multimap<string, any>(),
  }
  for (const env of envs) {
    if (env.configPath)
      metadata.configPath.add(env.configPath);
    if (env.category)
      metadata.category.add(env.category);
    if (env.name)
      metadata.name.add(env.name);
    metadata.systemData.osArch.add(env.systemData.osArch);
    metadata.systemData.osName.add(env.systemData.osName + ' ' + env.systemData.osVersion);
    for (const [key, value] of Object.entries(env.userSuppliedData ?? {}))
      metadata.userSuppliedData.set(key, value);
  }
  return {
    configPath: [...metadata.configPath],
    category: [...metadata.category],
    name: [...metadata.name],
    systemData: {
      osArch: [...metadata.systemData.osArch],
      osName: [...metadata.systemData.osName],
    },
    userSuppliedData: Object.fromEntries([...metadata.userSuppliedData].map(([key, values]) => [key, [...values]])),
  } satisfies WireTypes.AggregatedMetadata;
}


@customElement('parameters-table')
export class ParametersTable extends LitElement {
  @property({ attribute: false }) envs?: WireTypes.RunEnvironment[];
  @property({ attribute: false }) split?: TimelineSplit;

  @property({ type: Boolean }) multiple?: boolean;

  render() {
    const envs = this.envs;
    const split = this.split;
    if (!envs || !split)
      return nothing;
    const allMetadata = aggregateEnvs(envs);
    const metadata = aggregateEnvs(envs.filter(env => split.acceptsEnvironment(env)));

    type MetadataRow = {
      name: string,
      allValues: (string|number|boolean|undefined|null)[],
      values: any[],
      field: TimelineKey
    };

    const rows: MetadataRow[] = [];
    rows.push({ name: 'category', values: metadata.category, allValues: allMetadata.category, field: TimelineKey.systemKeys.CATEGORY });
    rows.push({ name: 'name', values: metadata.name, allValues: allMetadata.name, field: TimelineKey.systemKeys.NAME });
    rows.push({ name: 'config path', values: metadata.configPath, allValues: allMetadata.configPath, field: TimelineKey.systemKeys.CONFIG_PATH });
    rows.push({ name: 'OS Name', values: metadata.systemData.osName, allValues: allMetadata.systemData.osName, field: TimelineKey.systemKeys.OS_NAME });
    rows.push({ name: 'OS Arch', values: metadata.systemData.osArch, allValues: allMetadata.systemData.osArch, field: TimelineKey.systemKeys.OS_ARCH });
    for (const [name, allValues] of Object.entries(allMetadata.userSuppliedData).sort(([a], [b]) => a < b ? -1 : 1))
      rows.push({ name, values: metadata.userSuppliedData[name] ?? [], allValues: allValues, field: TimelineKey.createUserKey(name) });

    for (const row of rows)
      row.allValues.sort((v1, v2) => String(v1) < String(v2) ? -1 : 1);

    return html`
      <section id=parameters>
        <div class="row heading">
          <div class=name>Parameter</div>
          <div class=values>Values</div>
        </div>
      ${rows.map(row => html`
        <div class=${classMap({
          row: true,
          'has-split': split.isSplitBy(row.field),
        })}>
          <div class=${classMap({
            name: true,
          })}>
            <h-box style="gap: var(--sl-spacing-small);" @click=${this.multiple ? () => {
              this.split = split.isSplitBy(row.field) ? split.clearAndCollapse(row.field) : split.splitBy(row.field);
              this.dispatchEvent(new CustomEvent<TimelineSplit>('split-changed', {
                detail: this.split
              }));
            } : nothing}>
              ${this.multiple ? html`
                <split-circle ?split=${split.isSplitBy(row.field)}></split-circle>
              ` : nothing}
              <span>${row.name}</span>
            </h-box>
          </div>
          <h-box class=values>${row.allValues.map(value => {
            const isMatchingFilter = split.hasValue(row.field, String(value));
            const isOutsideOfSelection = !row.values.includes(value);            
            return html`
              <sl-button class=${classMap({ outside: isOutsideOfSelection })} size=small @click=${() => {
                let newSplit = !this.multiple && !isMatchingFilter ? split.clearAndCollapse(row.field) : split;
                newSplit = newSplit.toggleEnsureNonEmptySelection(row.field, String(value), envs);
                this.split = newSplit;
                this.dispatchEvent(new CustomEvent<TimelineSplit>('split-changed', {
                  detail: newSplit,
                }));
              }} variant=${isMatchingFilter ? 'primary' : 'default'}>${
                value === '' ? html`<span class=falsy-value>empty</span>` : 
                value === undefined ? html`<span class=falsy-value>undefined</span>` :
                value === null ? html`<span class=falsy-value>null</span>` : value
              }</sl-button>
            `;
          })}</h-box>
        </div>
      `)}
      </section>
    `
  }

  static styles = [css`

    sl-dialog {
      --width: min(120ch, 100%);
    }

    .outside {
      opacity: 0.5;
    }

    .btn-parameters {
      font-size: var(--sl-font-size-small);
      color: var(--sl-color-neutral-500);
      margin-bottom: var(--sl-spacing-2x-small);
    }

    #parameters {
      border: 1px solid var(--fk-color-border);
      border-radius: var(--sl-border-radius-large);
      overflow: auto;

      display: grid;
      grid-template-columns: auto 1fr;

      .row.heading {
        font-size: var(--sl-font-size-small);
        font-weight: var(--sl-font-weight-bold);
        background-color: var(--fk-color-canvas);  
      }

      .row {
        gap: var(--sl-spacing-small);
        padding: var(--sl-spacing-small);  
        grid-column: -1/1;
        display: grid;
        border-bottom: 1px solid var(--fk-color-border);
        align-items: center;
        grid-template-columns: subgrid;
        border-left: 2px solid transparent;

        &.has-split {
          border-left: 2px solid orange;
        }

        .name {
          cursor: pointer;
          user-select: none;

          &:hover {
            text-decoration: underline;
          }
        }

        .falsy-value {
          color: var(--sl-color-neutral-400);
        }

        .values {
          display: flex;
          flex-wrap: wrap;
          gap: var(--sl-spacing-2x-small);
        }
      }
    }
  `]
}
