import { Task } from '@lit/task';
import { timeDay, timeFormat, timeMonth } from 'd3';
import { LitElement, PropertyValues, css, html, nothing } from 'lit';
import { customElement, property, state } from 'lit/decorators.js';
import { classMap } from 'lit/directives/class-map.js';
import { styleMap } from 'lit/directives/style-map.js';
import { assert, consumeDOMEvent, midday } from '../utils.js';

function sortDates(date1: Date, date2: Date) {
  return date1 < date2 ? [date1, date2] : [date2, date1];
}

function clampDate(date: Date, min: Date|undefined, max: Date|undefined): Date {
  if (min && date < min)
    date = min;
  if (max && date > max)
    date = max;
  return date;
}

type DayOfMonth = {
  dayOfMonth: number,
  dayOfWeek: number,
  relativeWeek: number,
  since: Date,
  until: Date,
  midday: Date,
  outside: boolean,
}

function createDayOfMonth(since: Date, relativeWeek: number, outside: boolean): DayOfMonth {
  return {
    dayOfMonth: since.getDate(),
    dayOfWeek: since.getDay(),
    relativeWeek,
    since: since,
    until: timeDay.offset(since, 1),
    midday: midday(since),
    outside,
  }
}

function daysInAMonth(date: Date): DayOfMonth[] {
  const fromDate = timeMonth.floor(date);
  const toDate = timeMonth.offset(timeMonth.floor(date), 1);
  let weekCounter = 0;
  const days = timeDay.range(fromDate, toDate).map(d => createDayOfMonth(d, d.getDay() === 6 ? weekCounter++ : weekCounter, false));
  while (days[0].dayOfWeek !== 0) {
    const d = timeDay.offset(days[0].since, -1);
    days.unshift(createDayOfMonth(d, days[0].relativeWeek, true));
  }
  while (days.at(-1)!.dayOfWeek !== 6) {
    const day = days.at(-1)!;
    const d = timeDay.offset(day.since, 1);
    days.push(createDayOfMonth(d, day.relativeWeek, true));
  }
  return days;
}

export type CalendarRangeHighlighter = (days: { since: Date, until: Date }[], signal: AbortSignal) => Promise<(string|undefined)[]>;

@customElement('calendar-range')
export class CalendarRange extends LitElement {
  @property({ attribute: false }) since?: Date;
  @property({ attribute: false }) until?: Date;
  @property({ attribute: false }) min?: Date;
  @property({ attribute: false }) max?: Date;
  @property({ attribute: false }) maxRangeDays?: number;
  @property({ attribute: false }) highlighter?: CalendarRangeHighlighter;

  @state() private _isSelecting?: boolean;
  @state() private _selectionStart?: Date;
  @state() private _selectionEnd?: Date;

  // This is the right of the months.
  @state() private _position?: Date;

  protected willUpdate(p: PropertyValues): void {
    if (!this._position) {
      if (this.until)
        this._position = timeMonth.floor(this.until);
      else
        this._position = timeMonth.floor(new Date());
      return;
    }
  }

  cancelSelection() {
    this._selectionStart = undefined;
    this._selectionEnd = undefined;
    this._isSelecting = undefined;
  }

  revealDate(date: Date) {
    if (!this._position) {
      this._position = timeMonth.floor(date);
      return;
    }

    const focusMonth = timeMonth.floor(date);
    const focusNextMonth = timeMonth.offset(timeMonth.floor(date), 1);
    if (+focusNextMonth === +this._position || +focusMonth === +this._position) {
      // do nothing. it fits in the current view.
    } else if (focusMonth < this._position) {
      // scroll to the left
      this._position = focusMonth;
    } else {
      // scroll to the right
      this._position = focusMonth;
    }
  }

  disconnectedCallback(): void {
    this._selectionStart = undefined;
    this._selectionEnd = undefined;
    this._isSelecting = undefined;
  }

  private _highlightDataTask = new Task(this, {
    args: () => [this._position, this.highlighter] as const,
    task: async ([position, highlighter], { signal }) => {
      assert(position && highlighter);
      //TODO: this is a copy-paste from render; extract/unify!
      const date2 = position;
      const date1 = timeMonth.offset(timeMonth.floor(date2), -1);
      const days = [daysInAMonth(date1), daysInAMonth(date2)].flat().map(day => ({ since: day.since, until: day.until }));
      const data = await highlighter(days, signal);
      return new Map(data.map((result, i) => [+days[i].since, result]));
    }
  })

  override render() {
    if (!this._position)
      return nothing;
    let min = this.min;
    let max = this.max;

    if (this.maxRangeDays && this._isSelecting && this._selectionStart) {
      min = clampDate(timeDay.offset(this._selectionStart, -this.maxRangeDays + 1), this.min, this.max);
      max = clampDate(timeDay.offset(this._selectionStart, this.maxRangeDays - 1), this.min, this.max);
    }

    if (min)
      min = timeDay.floor(min);
    if (max)
      max = timeDay.ceil(max);

    let since: Date|undefined;
    let until: Date|undefined;
    if (this._isSelecting) {
      [since, until] = sortDates(this._selectionStart!, this._selectionEnd!);
    } else {
      since = this.since;
      until = this.until;
    }
    if (since)
      since = clampDate(timeDay.floor(since), min, max);
    if (until)
      until = clampDate(timeDay.ceil(until), min, max);

    const date2 = this._position;
    const date1 = timeMonth.offset(timeMonth.floor(date2), -1);

    const year1 = date1.getFullYear();
    const year2 = date2.getFullYear();

    const prevFocusDate = timeMonth.ceil(timeMonth.offset(this._position, -1))
    const nextFocusDate = timeMonth.floor(timeMonth.offset(this._position, 1));

    return html`
      <v-box>
        <h-box>
          <sl-icon-button ?disabled=${!!min && min > prevFocusDate} @click=${(event: MouseEvent) => {
            this._position = prevFocusDate;
            consumeDOMEvent(event);
          }} name=chevron-left></sl-icon-button>
          <x-filler></x-filler>
          <span class=year-title>${year1 === year2 ? html`${year1}` : html`${year1}–${year2}`}</span>
          <x-filler></x-filler>
          <sl-icon-button ?disabled=${!!max && max < nextFocusDate} @click=${(event: MouseEvent) => {
            this._position = nextFocusDate;
            consumeDOMEvent(event);
          }} name=chevron-right></sl-icon-button>
        </h-box>
        <h-box style="align-items: start; justify-content: start;">
          ${this._renderMonth(date1, min, max, since, until)}
          ${this._renderMonth(date2, min, max, since, until)}
        </h-box>
      </v-box>
    `;
  }

  private _dayClicked(day: Date) {
    if (!this._isSelecting) {
      this._isSelecting = true;
      this._selectionStart = day;
      this._selectionEnd = day;
      this.dispatchEvent(new CustomEvent<Date>('rangestart', {
        detail: day,
      }))
      return;
    }
    [this.since, this.until] = sortDates(this._selectionStart!, day);
    this._selectionStart = undefined;
    this._selectionEnd = undefined;
    this._isSelecting = false;
    this.dispatchEvent(new CustomEvent<Date>('rangeend', {
      detail: day,
    }));
  }

  private _dayHovered(day: Date) {
    this._selectionEnd = day;
  }

  private _renderDay(day: DayOfMonth, min: Date | undefined, max: Date | undefined, since: Date | undefined, until: Date | undefined) {
    const isDisabled = (!!min && day.midday < min) || (!!max && day.midday > max);
    const today = midday(new Date());
    const isToday = day.since < today && today < day.until;
    const emphasizedText = this._highlightDataTask.value?.get(+day.since);

    const isHighlighted = !!emphasizedText;
    const render = html`
      <span class=${classMap({
        day: true,
        disabled: isDisabled,
        'outside-month': day.outside,
        rstart: !!since && +day.since === +since,
        rend: !!until && +day.until === +until,
        'is-highlighted': isHighlighted,
        'is-today': isToday,
        rinside: !!since && !!until && since < day.midday && day.midday < until,
      })} style=${styleMap({
        'grid-column': day.dayOfWeek + 1,
        'grid-row': day.relativeWeek + 3,
      })}
        @click=${isDisabled ? nothing : (e: MouseEvent) => {
          this._dayClicked(day.midday);
          consumeDOMEvent(e);
        }}
        @mouseover=${isDisabled ? nothing : this._isSelecting ? (e: MouseEvent) => {
          this._dayHovered(day.midday);
          consumeDOMEvent(e);
        } : nothing}
      ><span class=day-text>${day.dayOfMonth}</span></span>
    `;
    if ((isHighlighted || isToday) && !this._isSelecting) {
      const text: string[] = [];
      if (emphasizedText)
        text.push(emphasizedText);
      if (isToday)
        text.push('Today');
      return html`<sl-tooltip content=${text.join('\n')}>${render}</sl-tooltip>`;
    }
    return render;
  }

  private _renderMonth(date: Date, min: Date | undefined, max: Date | undefined, since: Date | undefined, until: Date | undefined) {
    const days = daysInAMonth(date);
    return html`
      <section class=month>
        <span class=month-title>${timeFormat('%B')(date)}</span>
        ${['S', 'M', 'T', 'W', 'T', 'F', 'S'].map((t, index) => html`<span class=${classMap({
          'day-title': true,
          day: true,
          weekend: index === 0 || index === 6,
        })}>${t}</span>`)}
        ${days.map(day => this._renderDay(day, min, max, since, until))}
      </section>
    `;
  }

  static styles = [css`
    :host {
      display: flex;
    }
    .month {
      display: inline-grid;
      grid-template-columns: auto;
      grid-template-rows: auto;
      row-gap: var(--sl-spacing-3x-small);

      .month-title {
        grid-row: 1;
        grid-column: 1/8;
        align-self: center;
        justify-self: center;
        color: var(--sl-color-neutral-700);
        font-weight: var(--sl-font-weight-bold);
        font-size: var(--sl-font-size-small);
      }
    }

    .year-title {
      font-size: var(--sl-font-size-large);
    }
    
    .day-title {
      grid-row: 2;
      font-weight: var(--sl-font-weight-bold);
      font-size: var(--sl-font-size-small);
      color: var(--sl-color-neutral-700);
      border-bottom: 1px solid var(--fk-color-border);

      &.weekend {
        color: #af0000;
      }
    }

    .day {
      user-select: none;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: var(--sl-spacing-x-small);
      cursor: pointer;

      &.outside-month {
        color: var(--sl-color-neutral-300);
      }

      &.is-highlighted, &.is-today {
        position: relative;
      }

      &.is-highlighted::before {
        content: "";
        position: absolute;
        inset: 10%;
        border: 1px solid var(--fk-color-border);
        border-radius: var(--sl-border-radius-small);
        background-color: #ffffb2;
      }

      &.is-today::after {
        content: "";
        position: absolute;
        inset: 10%;
        border: 3px double var(--fk-color-border);
        border-radius: var(--sl-border-radius-small);
      }

      &.disabled {
        color: var(--sl-color-neutral-300);
        background-color: var(--sl-color-neutral-50);
        cursor: not-allowed;
      }
      
      &.rinside {
        background-color: var(--sl-color-primary-600);
        color: white;
      }

      &.rinside.is-highlighted {
        color: var(--fk-color-text);
      }

      &.rstart {
        background-color: var(--sl-color-primary-700);
        border-top-left-radius: var(--sl-border-radius-medium);
        border-bottom-left-radius: var(--sl-border-radius-medium);
      }

      &.rend {
        background-color: var(--sl-color-primary-700);
        border-top-right-radius: var(--sl-border-radius-medium);
        border-bottom-right-radius: var(--sl-border-radius-medium);
      }

      .day-text {
        position: relative;
      }
    }

    sl-icon-button {
      border: 1px solid var(--fk-color-border);
      background-color: #fff;
      border-radius: 3px;
    }
  `]
}