import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';

import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { getInitials } from '../utils.js';

@customElement('fk-user')
export class <PERSON><PERSON>ser extends LitElement {

  @property({ attribute: false }) user?: WireTypes.User;

  override render() {
    const user = this.user;

    if (!user)
      return nothing;

    return html`
      <section>
        <sl-avatar image=${user.avatarUrl} initials=${getInitials(user.userName)}></sl-avatar>
        <div class=user-name>${user.userName}</div>
        <div class=user-login>${user.userLogin}</div>
        <div class=slot><slot></slot></div>
      </section>
    `;
  }

  static styles = [css`
    section {
      display: grid;
      grid-template-columns: auto 1fr auto;
      grid-template-rows: auto auto;
      column-gap: var(--sl-spacing-small);
      align-items: center;

      sl-avatar {
        grid-row: 1 / -1;
        --size: 2.5rem;
      }

      .user-name {
        grid-column: 2 / 3;
      }
      .user-login {
        grid-column: 2 / 3;
      }

      .slot {
        grid-row: 1 / -1;
        grid-column: 3/4;
      }
    }
  `]
}
