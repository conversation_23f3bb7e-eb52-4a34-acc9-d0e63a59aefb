import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { consume, ContextType } from '@lit/context';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, state } from 'lit/decorators.js';
import { contexts } from '../contexts.js';
import { linkStyles } from './cssstyles.js';

@customElement('app-footer')
class AppFooter extends LitElement {
  @consume({ context: contexts.user, subscribe: true }) @state() private _user?: ContextType<typeof contexts.user>;
  @consume({ context: contexts.serverInfo, subscribe: true }) @state() private _serverInfo?: ContextType<typeof contexts.serverInfo>;


  render() {
    return html`
      <v-box style="align-items: center;">
        <div>© 2024–2025 Degu Labs Inc – All Rights Reserved – Reach out <fk-contact-us></fk-contact-us> – <a-ext href="https://github.com/flakiness/flakiness-feedback">Found a Bug?</a-ext></div>
        ${this._user?.isSuperUser ? html`
          <h-box>
            <div>
              Server: <strong>${this._serverInfo?.hostname}</strong>
            </div>
            <div>
              Flakiness.io <b><a-ext href=${`https://github.com/flakiness/flakiness/commits/${COMMIT_SHA}`}>v${PACKAGE_JSON_VERSION}</a-ext></b>
            </div>
            <div>
              Uptime: <strong><time-interval .ms=${this._serverInfo?.uptimeMs}></time-interval></strong>
            </div>
            <div>
              RAM: ${renderMemory(this._serverInfo)}
            </div>
          </h-box>
        ` : nothing}
      </v-box>
    `;
  }

  static styles = [linkStyles, css`
    :host {
      display: flex;
      flex: auto;
      align-items: end;
      justify-content: center;
      color: var(--sl-color-neutral-500);
    }

    a-ext {
      display: inline-block;
    }

    v-box {
      margin: var(--sl-spacing-2x-large) 0;
      align-items: center;
    }
  `];
}

function renderMemory(serverInfo?: WireTypes.ServerInfo) {
  if (!serverInfo)
    return nothing;
  const { usedBytes, totalBytes } = serverInfo.memory;
  return html`<strong>${(usedBytes / totalBytes * 100).toFixed(2)}%</strong> (<human-bytes bytes=${usedBytes}></human-bytes> / <human-bytes bytes=${totalBytes}></human-bytes>)`;
}
