import { Matcher } from '@flakiness/server/common/fql/matcher.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { ContextType } from '@lit/context';
import { css, html, LitElement, nothing } from 'lit';
import { customElement, property } from 'lit/decorators.js';
import { linkStyles } from '../components/cssstyles.js';
import { consume, contexts } from '../contexts.js';

@customElement('status-selector')
class StatusSelector extends LitElement {
  @consume({ context: contexts.fql, subscribe: true }) private _fql?: ContextType<typeof contexts.fql>;
  @consume({ context: contexts.linkFQL, subscribe: true }) private _link?: ContextType<typeof contexts.linkFQL>;
  
  @property({ type: Boolean, attribute: 'hide-regressions' }) hideRegressions?: boolean;

  private _url(value?: WireTypes.Outcome) {
    if (!this._fql || !this._link)
      return undefined;
    let fql = this._fql.clearStatusFilters();
    if (value)
      fql = fql.toggleFilter(Matcher.outcomes[value]);
    return this._link.render(fql.serialize());
  }

  render() {
    return html`
      <sl-button-group>
      ${this.hideRegressions ? nothing : html`
        <sl-button href=${this._url('regressed') ?? nothing}><sl-icon name=fire></sl-icon></sl-button>
      `}
      <sl-button href=${this._url() ?? nothing}>All</sl-button>
        <sl-button href=${this._url('unexpected') ?? nothing}>Failed</sl-button>
        <sl-button href=${this._url('flaked') ?? nothing}>Flaked</sl-button>
        <sl-button href=${this._url('skipped') ?? nothing}>Skipped</sl-button>
        <sl-button href=${this._url('expected') ?? nothing}>Passed</sl-button>
      </sl-button-group>
    `;
  }

  static styles = [linkStyles, css`
  `];
}