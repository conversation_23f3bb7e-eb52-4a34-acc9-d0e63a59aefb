import hljs from 'highlight.js/lib/common';
import { css, html, nothing } from 'lit';
import { unsafeHTML } from 'lit/directives/unsafe-html.js';
import { marked } from 'marked';
import { githubHighlightCSS } from './githubHighlightCSS.js';
import { githubMarkdownCSS } from './githubMarkdownCSS.js';

export const markdownStyles = [
  githubHighlightCSS,
  githubMarkdownCSS, 
  css`
    .md-code-block {
      display: block;
      
      &::part(body) {
        padding: 0;
      }

      pre {
        margin: 0;
        padding: var(--sl-spacing-medium) var(--sl-spacing-large);
      }
    }
  `
];

// sha256 of "flakiness.io rules markdown!";
const separator = '2029cf1746e8fe574b219f0c294f5831175983ee3ed1ad77a8e57d7c580a67d1';
const whitespaceRegex = /^\s*$/;

const cache = new WeakMap<TemplateStringsArray, TemplateStringsArray>();
export function markdown(strings: TemplateStringsArray, ...values: any[]): ReturnType<typeof html> {
  let entry = cache.get(strings);
  if (!entry) {
    const markup = marked(trimLeadingIndent(strings.join(separator))) as string;
    entry = markup.split(separator) as unknown as TemplateStringsArray;
    (entry as any).raw = entry;
    cache.set(strings, entry);
  }
  return html(entry, ...values);
}

markdown.ts = codeblock.bind(null, 'typescript');
markdown.bash = codeblock.bind(null, 'bash');
markdown.yaml = codeblock.bind(null, 'yaml');

function codeblock(language: string, text: string, title?: string): ReturnType<typeof html> {
  return html`
    <sl-card class=md-code-block>
      ${title ? html`<div slot=header>${title}</div>` : nothing}
      <pre><code>${unsafeHTML(hljs.highlight(trimLeadingIndent(text), { language }).value)}</code></pre>
    </sl-card>
  `
}

function trimLeadingIndent(text: string) {
  const lines = text.split('\n');
  if (lines.length <= 1)
    return text.trim();
  if (whitespaceRegex.test(lines[0]))
    lines.shift();
  if (lines.length && whitespaceRegex.test(lines[lines.length - 1]))
    lines.pop();
  let minIndent = Infinity;
  for (const line of lines) {
    if (whitespaceRegex.test(line))
      continue;
    const match = line.match(/^\s*/);
    if (!match)
      continue;
    minIndent = Math.min(match[0].length, minIndent);
  }
  if (Object.is(minIndent, Infinity))
    return lines.join('\n');
  return lines.map(line => line.substring(minIndent)).join('\n');
}