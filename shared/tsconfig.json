{"include": ["src"], "exclude": ["lib"], "compilerOptions": {"allowSyntheticDefaultImports": true, "experimentalDecorators": true, "declarationMap": true, "emitDeclarationOnly": true, "composite": true, "allowJs": true, "checkJs": true, "strict": true, "target": "ESNext", "isolatedModules": true, "resolveJsonModule": true, "moduleResolution": "NodeNext", "esModuleInterop": true, "module": "NodeNext", "lib": ["esnext", "DOM", "DOM.Iterable"], "types": ["node"], "outDir": "./types"}}