import { expect, test } from '@playwright/test';
import express from 'express';
import { gunzipSync, gzipSync } from 'zlib';
import { z } from 'zod/v4';
import { TypedHTTP } from "../src/common/typedHttp.js";
import { createTypedHttpExpressMiddleware } from "../src/node/typedHttpExpress.js";

test.describe('typedHttp with Express server', () => {
  test('types should be inferred', async () => {
    type RootContext = { foo: number };
    const t = TypedHTTP.Router.create<RootContext>();
    const routes = {
      post: {
        nothing: t.get({
          handler: async ({ input, ctx }) => {
            return TypedHTTP.error('nothing works');
          },
        }),
        fetch: t.get({
          input: z.object({ postId: z.number() }),
          handler: async ({ input, ctx }) => {
            // Make sure context is typed appropriately.
            ctx.foo;
            // @ts-expect-error
            ctx.bar;
            ctx.foo.toFixed();
            return { id: 1, title: 'Hello world!', };
          }
        }),
      },
    };

    function createTestProxy() {
      return new Proxy(() => {}, {
        get(target, prop: string | symbol) {
          return createTestProxy();
        },

        apply(target, thisArg, args) {
          return {};
        }
      });
    }
    const api = createTestProxy() as any as TypedHTTP.Client<typeof routes>;
    // 1. Check methods are inferred properly.
    // @ts-expect-error
    api.post.fetch.POST;
    api.post.fetch.GET;

    // 2. Check arguments are inferred properly.
    // @ts-expect-error GET requires arguments.
    api.post.fetch.GET();
    // @ts-expect-error
    api.post.fetch.GET({ });
    // @ts-expect-error
    api.post.fetch.GET({ userId: 'foo' });
    // @ts-expect-error
    api.post.fetch.GET({ postId: '12' });
    api.post.fetch.GET({ postId: 12 });

    // 3. Check return type is inferred properly.
    const result = await api.post.fetch.GET({ postId: 12 }, { signal: new AbortController().signal });
    // @ts-expect-error
    result.foo;
    result.id;
    result.title;

    await api.post.nothing.GET(undefined, { signal: new AbortController().signal });
    await api.post.nothing.GET();
  });

  test('client itself must not be thennable to avoid promise confusion', async () => {
    const routes = { then: {} };
    const api = TypedHTTP.createClient<typeof routes>(`http://localhost:3000/api/`, fetch);
    expect(typeof api.then).not.toBe('function');
  });

  test('should handle GET and POST requests through Express server', async () => {
    // Setup routes
    type RootContext = { foo: number };
    const t = TypedHTTP.Router.create<RootContext>();
    const router = {
      posts: {
        list: t.get({
          input: z.object({ userId: z.number() }),
          handler: async ({ input }) => {
            return [
              { id: 1, title: 'Hello GET', userId: input.userId }
            ];
          }
        }),
      },
      post: {
        create: t.post({
          input: z.object({ userId: z.number(), text: z.string() }),
          handler: async (input) => {
          }
        }),
      }
    };

    // Create Express server
    const app = express();
    app.use('/api', express.json(), createTypedHttpExpressMiddleware({
      router: router,
      createRootContext: async () => {
        return { foo: 42 };
      }
    }));

    using server = deferCleanup(app.listen(0), () => server.close());
    const port = (server.address() as any).port;

    const api = TypedHTTP.createClient<typeof router>(`http://localhost:${port}/api/`, fetch);
    expect(await api.posts.list.GET({ userId: 123 })).toEqual([
      { id: 1, title: 'Hello GET', userId: 123 }
    ]);
    expect(await api.post.create.POST({ userId: 12, text: 'foo' })).toEqual(undefined);
  });

  test('should handle etag caching', async () => {
    // Setup routes with etag support
    type RootContext = { foo: number };
    const t = TypedHTTP.Router.create<RootContext>();
    
    let getCallCount = 0;
    
    const routes = {
      post: t.get({
        input: z.object({ postId: z.number() }),
        etag: async () => 'test-etag-123',
        handler: async ({ input }) => {
          getCallCount++;
          return {
            text: 'some post content',
            title: 'Post Title',
            postId: input.postId,
          };
        }
      })
    };

    // Create Express server
    const app = express();
    app.use('/api', express.json(), createTypedHttpExpressMiddleware({
      router: routes,
      createRootContext: async () => {
        return { foo: 42 };
      }
    }));

    using server = deferCleanup(app.listen(0), () => server.close());
    const port = (server.address() as any).port;
    const api = TypedHTTP.createClient<typeof routes>(`http://localhost:${port}/api/`, fetch);

    // Test initial request
    const response1 = await fetch(api.post.GET.prepare({ postId: 1 }).url);
    expect(response1.status).toBe(200);
    expect(response1.headers.get('etag')).toBe('test-etag-123');
    const data1 = await response1.json();
    expect(data1).toEqual({
      text: 'some post content',
      title: 'Post Title',
      postId: 1,
    });
    expect(getCallCount).toBe(1);

    // Test request with matching etag - should return 304
    const response2 = await fetch(api.post.GET.prepare({ postId: 1 }).url, {
      headers: {
        'if-none-match': 'test-etag-123'
      }
    });
    expect(response2.status).toBe(304);
    expect(getCallCount).toBe(1);

    // Test request with different etag - should return 200
    const response3 = await fetch(api.post.GET.prepare({ postId: 1 }).url, {
      headers: {
        'if-none-match': 'different-etag'
      }
    });
    expect(response3.status).toBe(200);
    expect(getCallCount).toBe(2);
  });

  test('should handle redirects', async () => {
    // Setup routes with redirect
    type RootContext = { foo: number };
    const t = TypedHTTP.Router.create<RootContext>();
    
    const routes = {
      redirect: t.rawMethod('GET', {
        input: z.object({ url: z.string() }),
        handler: async ({ input }) => {
          return TypedHTTP.redirect(input.url);
        }
      }),
    };

    // Create Express server
    const app = express();
    app.use('/api', express.json(), createTypedHttpExpressMiddleware({
      router: routes,
      createRootContext: async () => {
        return { foo: 42 };
      }
    }));

    using server = deferCleanup(app.listen(0), () => server.close());
    const port = (server.address() as any).port;
    const baseUrl = `http://localhost:${port}`;

    // Test redirect response
    const response = await fetch(`${baseUrl}/api/redirect?input=${encodeURIComponent(JSON.stringify({ url: 'https://example.com' }))}`, {
      redirect: 'manual'
    });

    expect(response.status).toBe(302);
    expect(response.headers.get('location')).toBe('https://example.com');
  });

  test('should return HTTP 500 for unexpected errors', async () => {
    type RootContext = { foo: number };
    const t = TypedHTTP.Router.create<RootContext>();
    
    const routes = {
      crash: t.get({
        input: z.object({}),
        handler: async () => {
          throw new Error('Unexpected error');
        }
      })
    };

    const app = express();
    app.use('/api', express.json(), createTypedHttpExpressMiddleware({
      router: routes,
      createRootContext: async () => ({}),
    }));

    using server = deferCleanup(app.listen(0), () => server.close());
    const port = (server.address() as any).port;

    const response = await fetch(`http://localhost:${port}/api/crash?input=${encodeURIComponent(JSON.stringify({}))}`);
    expect(response.status).toBe(500);
  });

  test('should handle TypedHTTP.HttpError properly', async () => {
    type RootContext = { foo: number };
    const t = TypedHTTP.Router.create<RootContext>();

    const routes = {
      badRequest: t.get({
        input: z.object({}),
        handler: async () => {
          throw TypedHTTP.HttpError.withCode('BAD_REQUEST');
        }
      }),
      notFound: t.get({
        input: z.object({}),
        handler: async () => {
          throw TypedHTTP.HttpError.withCode('NOT_FOUND', 'This resource does not exist');
        }
      }),
    };

    const app = express();
    app.use('/api', express.json(), createTypedHttpExpressMiddleware({
      router: routes,
      createRootContext: async () => ({ foo: 42 })
    }));

    using server = deferCleanup(app.listen(0), () => server.close());
    const port = (server.address() as any).port;

    // Test 400 error
    const response1 = await fetch(`http://localhost:${port}/api/badRequest?input=${encodeURIComponent(JSON.stringify({}))}`);
    expect(response1.status).toBe(400);
    const data1 = await response1.text();
    expect(data1).toEqual('Bad Request');

    // Test 404 error
    const response2 = await fetch(`http://localhost:${port}/api/notFound?input=${encodeURIComponent(JSON.stringify({}))}`);
    expect(response2.status).toBe(404);
    const data2 = await response2.text();
    expect(data2).toEqual('This resource does not exist');
  });

  test('should pass input to resolveContext function', async () => {
    type RootContext = { foo: number };
    type HandlerContext = { foo: number; userId: number };
    
    let receivedInput: any;
    
    const t = TypedHTTP.Router.create<RootContext>().use<HandlerContext>(async (options) => {
      receivedInput = options.input;
      return { ...options.ctx, userId: options.input.userId };
    });
    
    const routes = {
      user: t.get({
        input: z.object({ userId: z.number() }),
        handler: async ({ input, ctx }) => {
          return { userId: ctx.userId };
        }
      })
    };

    const app = express();
    app.use('/api', express.json(), createTypedHttpExpressMiddleware({
      router: routes,
      createRootContext: async () => ({ foo: 42 })
    }));

    using server = deferCleanup(app.listen(0), () => server.close());
    const port = (server.address() as any).port;

    const response = await fetch(`http://localhost:${port}/api/user?input=${encodeURIComponent(JSON.stringify({ userId: 123 }))}`);
    expect(response.status).toBe(200);
    expect(await response.json()).toEqual({ userId: 123 });
    expect(receivedInput).toEqual({ userId: 123 });
  });

  test('should call onBeforeResponse callback with timings', async () => {
    type RootContext = { foo: number };
    const t = TypedHTTP.Router.create<RootContext>();
    
    let onBeforeResponseOptions: any;
    
    const routes = {
      test: t.get({
        input: z.object({ value: z.string() }),
        handler: async ({ input }) => {
          return { result: input.value };
        }
      })
    };

    const app = express();
    app.use('/api', express.json(), createTypedHttpExpressMiddleware({
      router: routes,
      createRootContext: async () => ({ foo: 42 }),
      onBeforeResponse: (options) => {
        onBeforeResponseOptions = options;
      }
    }));

    using server = deferCleanup(app.listen(0), () => server.close());
    const port = (server.address() as any).port;

    const response = await fetch(`http://localhost:${port}/api/test?input=${encodeURIComponent(JSON.stringify({ value: 'test' }))}`);
    expect(response.status).toBe(200);
    expect(onBeforeResponseOptions.timings.contextCreation.since).toBeGreaterThan(0);
    expect(onBeforeResponseOptions.timings.contextCreation.until).toBeGreaterThan(0);
    expect(onBeforeResponseOptions.timings.handler.since).toBeGreaterThan(0);
    expect(onBeforeResponseOptions.timings.handler.until).toBeGreaterThan(0);
    expect(onBeforeResponseOptions.ctx.foo).toBe(42);
  });

  test('should handle no-input method calls with AbortController', async () => {
    type RootContext = { foo: number };
    const t = TypedHTTP.Router.create<RootContext>();
    
    let receivedSignal: AbortSignal | undefined;
    
    const routes = {
      ping: t.get({
        handler: async () => {
          return { message: 'pong' };
        }
      })
    };

    const app = express();
    app.use('/api', express.json(), createTypedHttpExpressMiddleware({
      router: routes,
      createRootContext: async () => ({ foo: 42 })
    }));

    using server = deferCleanup(app.listen(0), () => server.close());
    const port = (server.address() as any).port;

    // Test with custom fetch that captures the signal
    const customFetch = async (url: URL, options: any) => {
      receivedSignal = options.signal;
      return fetch(url, options);
    };

    const api = TypedHTTP.createClient<typeof routes>(`http://localhost:${port}/api/`, customFetch);
    const controller = new AbortController();
    
    const result = await api.ping.GET(undefined, { signal: controller.signal });

    expect(result).toEqual({ message: 'pong' });
    expect(receivedSignal).toBe(controller.signal);
  });

  test('should generate fetchable URL with GET.prepare()', async () => {
    type RootContext = { foo: number };
    const t = TypedHTTP.Router.create<RootContext>();
    
    const routes = {
      posts: {
        get: t.get({
          input: z.object({ postId: z.number() }),
          handler: async ({ input }) => {
            return { id: input.postId, title: 'Test Post' };
          }
        }),
        ping: t.get({
          handler: async () => {
            return { message: 'pong' };
          }
        })
      }
    };

    const app = express();
    app.use('/api', express.json(), createTypedHttpExpressMiddleware({
      router: routes,
      createRootContext: async () => ({ foo: 42 })
    }));

    using server = deferCleanup(app.listen(0), () => server.close());
    const port = (server.address() as any).port;

    const api = TypedHTTP.createClient<typeof routes>(`http://localhost:${port}/api/`, fetch);

    // Test with input
    const prepared1 = api.posts.get.GET.prepare({ postId: 123 });
    const response1 = await fetch(prepared1.url, prepared1);
    expect(response1.status).toBe(200);
    expect(await response1.json()).toEqual({ id: 123, title: 'Test Post' });

    // Test without input
    const prepared2 = api.posts.ping.GET.prepare();
    const response2 = await fetch(prepared2.url, prepared2);
    expect(response2.status).toBe(200);
    expect(await response2.json()).toEqual({ message: 'pong' });
  });

  test('should be able to send raw buffers', async () => {
    type RootContext = {};
    const t = TypedHTTP.Router.create<RootContext>();
    
    const routes = {
      buffer: t.rawMethod('GET', {
        handler: async () => {
          return TypedHTTP.ok(gzipSync(Buffer.from('hello world')));
        }
      })
    };

    const app = express();
    app.use('/api', express.json(), createTypedHttpExpressMiddleware({
      router: routes,
      createRootContext: async () => ({ })
    }));

    using server = deferCleanup(app.listen(0), () => server.close());
    const port = (server.address() as any).port;

    const api = TypedHTTP.createClient<typeof routes>(`http://localhost:${port}/api/`, fetch);

    // Test with input
    const response = await api.buffer.GET();

    expect(gunzipSync(response).toString('utf-8')).toBe('hello world');
  });

  test('should validate input', async () => {
    type RootContext = {};
    const t = TypedHTTP.Router.create<RootContext>();
    
    const routes = {
      x2: t.get({
        input: z.object({
          x: z.number().min(10).max(100),
        }),
        handler: async ({ input }) => {
          return input.x * 2;
        }
      })
    };

    const app = express();
    app.use('/api', express.json(), createTypedHttpExpressMiddleware({
      router: routes,
      createRootContext: async () => ({ })
    }));

    using server = deferCleanup(app.listen(0), () => server.close());
    const port = (server.address() as any).port;

    const api = TypedHTTP.createClient<typeof routes>(`http://localhost:${port}/api/`, fetch);

    // Test with input
    const e = await api.x2.GET({ x: 1 }).then(x => undefined).catch(e => e);
    expect(e?.message).toContain('expected number to be >=10');
  });
});

function deferCleanup<T extends {}>(obj: T, callback: () => void): Disposable & T {
  (obj as any)[Symbol.dispose] = callback;
  return obj as any;
}
