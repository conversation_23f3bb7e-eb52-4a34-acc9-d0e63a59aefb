import util from 'util';
import zlib from 'zlib';

const asyncBrotliCompress = util.promisify(zlib.brotliCompress);
export const brotliDecompressAsync = util.promisify(zlib.brotliDecompress);
export const brotliDecompressSync = zlib.brotliDecompressSync;

const brotliOptions: zlib.BrotliOptions = {
  chunkSize: 32 * 1024,
  params: {
    [zlib.constants.BROTLI_PARAM_QUALITY]: 6,
    [zlib.constants.BROTLI_PARAM_MODE]: zlib.constants.BROTLI_MODE_TEXT,
  }
};

export function compressTextSync(text: string|Buffer) {
  return zlib.brotliCompressSync(text, brotliOptions);
}

export async function compressTextAsync(text: string|Buffer) {
  return asyncBrotliCompress(text, brotliOptions);
}
