import debug from 'debug';
import type express from 'express';
import { z } from 'zod/v4';
import { TypedHTTP } from "../common/typedHttp.js";

const log = debug('fk:tHttp');

export function createTypedHttpExpressMiddleware<T extends Record<string, any>, RootContext>(options: {
  router: T,
  createRootContext: (options: { req: express.Request, res: express.Response, input: any }) => Promise<RootContext>,
  onBeforeResponse?: (options: {
    req: express.Request,
    res: express.Response,
    ctx?: RootContext,
    timings: {
      contextCreation?: {
        since: number,
        until: number,
      },
      handler?: {
        since: number,
        until: number,
      },
      etag?: {
        since: number,
        until: number,
      },
    }
  }) => void,
}) {
  return async (req: express.Request, res: express.Response, next: express.NextFunction) => {
    const startProcessing = performance.now();
    const pathSegments = req.path.split('/').filter(Boolean);
    const method = req.method as TypedHTTP.Method;

    let current: any = options.router;
    for (const segment of pathSegments) {
      current = current?.[segment];
      if (!current)
        return next();
    }

    // Get the HTTP route for this method
    const httpRoute = current[method] as TypedHTTP.Route<any, any, any, any> | undefined;
    if (!httpRoute)
      return next();

    // Parse input (query for GET, body for others)
    const rawInput = method === 'GET'
      ? req.query.input && typeof req.query.input === 'string' ? JSON.parse(req.query.input) : undefined
      : req.body;

    // Validate input
    const validation = httpRoute.input?.safeParse(rawInput);
    if (validation && !validation.success) {
      serializeHttpResponse(req, res, TypedHTTP.HttpError.withCode('BAD_REQUEST', z.prettifyError(validation.error)));
      return;
    }
    const input = validation?.data;

    // Execute route
    const mBeforeContext = performance.now();
    let mAfterContext: number = 0;
    let mAfterEtag: number = 0;
    let mAfterHander: number = 0;
    let rootCtx: RootContext|undefined;
    let etag: string|undefined;
    const result = await Promise.resolve()
      .then(async () => {
        return await options.createRootContext({ req, res, input });
      })
      .then(async ctx => {
        rootCtx = ctx;
        const handlerCtx = await httpRoute.resolveContext({ ctx: rootCtx, input }).finally(() => {
          mAfterContext = performance.now();
        });
        etag = await httpRoute.etag?.({ ctx: handlerCtx, input, method }).finally(() => {
          mAfterEtag = performance.now();
        });
        if (etag && req.get('if-none-match') === etag)
          return { status: TypedHTTP.StatusCodes.Redirection.NOT_MODIFIED };

        const result = await httpRoute.handler({
          input: input,
          ctx: handlerCtx,
          method
        }).finally(() => {
          mAfterHander = performance.now();
        })
        return result;
      }).catch(e => {
        if (e instanceof TypedHTTP.HttpError)
          return TypedHTTP.error(e.message, e.status);
        console.error(e);
        return TypedHTTP.error(e.message, TypedHTTP.StatusCodes.ServerErrors.INTERNAL_SERVER_ERROR);
      });

    using timing = {
      [Symbol.dispose]: () => {
        log(`${result.status} ${method} ${req.path} ${formatTime(performance.now() - startProcessing)}`);
      }
    }

    options.onBeforeResponse?.({
      req,
      res,
      ctx: rootCtx,
      timings: {
        contextCreation: mAfterContext > 0 ? {
          since: mBeforeContext,
          until: mAfterContext,
        } : undefined,
        handler: mAfterHander > 0 && mAfterContext > 0 ? {
          since: mAfterContext,
          until: mAfterHander,
        } : undefined,
        etag: mAfterEtag > 0 && mAfterContext > 0 ? {
          since: mAfterContext,
          until: mAfterEtag,
        } : undefined,
      },
    });
    serializeHttpResponse(req, res, result, etag);
  };
}

function serializeHttpResponse(req: express.Request, res: express.Response, result: TypedHTTP.HttpResponse<any>, etag?: string) {
  if (TypedHTTP.isInformationalResponse(result)) {
    res.status(result.status).end();
  } else if (TypedHTTP.isSuccessResponse(result)) {
    res.status(result.status);
    if (result.contentType)
      res.set('content-type', result.contentType);
    if (etag) {
      res.set('Cache-Control', 'private, max-age=0, must-revalidate, stale-while-revalidate=60');
      res.set('etag', etag);
    }
    if (result.data !== undefined && result.contentType === 'application/json')
      res.json(result.data);
    else if (result.data)
      res.send(result.data);
    else
      res.end();
  } else if (TypedHTTP.isRedirectResponse(result)) {
    if (result.url)
      res.redirect(result.status, result.url);
    else
      res.status(result.status).end();
  } else if (TypedHTTP.isErrorResponse(result)) {
    res.status(result.status).send(result.message);
  } else {
    throw new Error('Invalid response type');
  }
}

function formatTime(ms: number) {
  return ms < 1000 ? `${ms.toFixed(2)}ms` : (ms / 1000).toFixed(2) + 's';
}