import { randomUUID } from "crypto";

export function randomUUIDBase62(): string {
  const BASE62_CHARSET = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let num = BigInt('0x' + randomUUID().replace(/-/g, ''));
  if (num === 0n)
    return BASE62_CHARSET[0];

  const chars = [];
  while (num > 0n) {
    const remainder = Number(num % 62n);
    num /= 62n;
    chars.push(BASE62_CHARSET[remainder]);
  }
  
  return chars.reverse().join('');
}