import stableObjectHash from 'stable-hash';
import xxhash from "xxhash-wasm";

const xxHasher = await xxhash();

// This is sha256('flakiness.io').substring(0, 10)
const ID_SEPARATOR = `388c03a1c6`;

export function createAbortPromise(signal: AbortSignal): { promise: Promise<void>, [Symbol.dispose]: () => void } {
  if (signal.aborted)
    return { promise: Promise.resolve(), [Symbol.dispose]: () => {} };
  let abortListener: (this: AbortSignal, ev: Event) => any;
  const promise = new Promise<void>(resolve => {
    abortListener = () => resolve();
    signal.addEventListener('abort', abortListener, { once: true });
  });
  return {
    promise,
    [Symbol.dispose]: () => signal.removeEventListener('abort', abortListener),
  }
}

export function mb(bytes: number) {
  const suffixes = ['B', 'KB', 'MB', 'GB'];
  for (const suffix of suffixes) {
    if (bytes < 1024)
      return bytes.toFixed(2) + suffix;
    bytes /= 1024;
  }
  return bytes.toFixed(2) + 'PB';
}


export async function raceWithSignal<T>(promise: Promise<T>, signal?: AbortSignal): Promise<T> {
  if (!signal)
    return await promise;
  using abortPromise = createAbortPromise(signal);
  return await Promise.race([
    abortPromise.promise,
    promise,
  ]) as Promise<T>;
}

export function xxHash(data: string | string[]): string {
  if (Array.isArray(data))
    return xxHasher.h64ToString(data.join(ID_SEPARATOR));
  return xxHasher.h64ToString(data);
}

export function xxHashBigint(data: string | string[]): bigint {
  if (Array.isArray(data))
    return xxHasher.h64(data.join(ID_SEPARATOR));
  return xxHasher.h64(data);
}

export function xxHashObjectBigint(obj: any): bigint {
  return xxHashBigint(stableObjectHash(obj));
}

export function xxHashObject(obj: any): string {
  return xxHash(stableObjectHash(obj));
}

/**
 * Brands a type by intersecting it with a type with a brand property based on
 * the provided brand string.
 */
export type Brand<T, Brand extends string> = T & {
  readonly [B in Brand as `__${B}_brand`]: never;
};

export function randomUUID(): string {
  return crypto.randomUUID();
}

export async function retryUntilDeadline<T>(operation: () => T, deadline: number): Promise<T> {
  const timeouts = [100, 200, 500, 500];
  const defaultTimeout = 1000;
  let lastError;
  while (Date.now() < deadline) {
    try {
      return await operation();
    } catch (e) {
      lastError = e;
      await new Promise(x => setTimeout(x, timeouts.shift() ?? defaultTimeout));
    }
  }
  throw lastError;
}

interface Measure {
  (title: string): void; // Just an example
  reset(): void;
  disableLog(): void;
  timestamp(): number;
  duration(): number;
}

export function measure(prefix?: string) {
  let timestamp = Date.now();
  if (prefix)
    prefix = prefix.trim() + ' ';
  else 
    prefix = '';

  let logEnabled = true;
  const m: Measure = (title: string) => {
    if (!logEnabled)
      return;
    const suffix = ': ' + (Date.now() - timestamp) + 'ms';
    console.log(prefix + title + suffix);
    timestamp = Date.now();
  }
  m.reset = () => {
    timestamp = Date.now();
  }
  m.disableLog = () => {
    logEnabled = false;
  }
  m.timestamp = () => timestamp;
  m.duration = () => Date.now() - timestamp;

  return m;
}


