import type * as z from 'zod/v4';

export namespace TypedHTTP {
  export const StatusCodes = {
    Informational: {
      CONTINUE: 100,
      SWITCHING_PROTOCOLS: 101,
      PROCESSING: 102,
      EARLY_HINTS: 103,
    },
    Success: {
      OK: 200,
      CREATED: 201,
      ACCEPTED: 202,
      NON_AUTHORITATIVE_INFORMATION: 203,
      NO_CONTENT: 204,
      RESET_CONTENT: 205,
      PARTIAL_CONTENT: 206,
      MULTI_STATUS: 207,
    },
    Redirection: {
      MULTIPLE_CHOICES: 300,
      MOVED_PERMANENTLY: 301,
      MOVED_TEMPORARILY: 302,
      SEE_OTHER: 303,
      NOT_MODIFIED: 304,
      USE_PROXY: 305,
      TEMPORARY_REDIRECT: 307,
      PERMANENT_REDIRECT: 308,
    },
    ClientErrors: {
      BAD_REQUEST: 400,
      UNAUTHORIZED: 401,
      PAYMENT_REQUIRED: 402,
      FORBIDDEN: 403,
      NOT_FOUND: 404,
      METHOD_NOT_ALLOWED: 405,
      NOT_ACCEPTABLE: 406,
      PROXY_AUTHENTICATION_REQUIRED: 407,
      REQUEST_TIMEOUT: 408,
      CONFLICT: 409,
      GONE: 410,
      LENGTH_REQUIRED: 411,
      PRECONDITION_FAILED: 412,
      REQUEST_TOO_LONG: 413,
      REQUEST_URI_TOO_LONG: 414,
      UNSUPPORTED_MEDIA_TYPE: 415,
      REQUESTED_RANGE_NOT_SATISFIABLE: 416,
      EXPECTATION_FAILED: 417,
      IM_A_TEAPOT: 418,
      INSUFFICIENT_SPACE_ON_RESOURCE: 419,
      METHOD_FAILURE: 420,
      MISDIRECTED_REQUEST: 421,
      UNPROCESSABLE_ENTITY: 422,
      LOCKED: 423,
      FAILED_DEPENDENCY: 424,
      UPGRADE_REQUIRED: 426,
      PRECONDITION_REQUIRED: 428,
      TOO_MANY_REQUESTS: 429,
      REQUEST_HEADER_FIELDS_TOO_LARGE: 431,
      UNAVAILABLE_FOR_LEGAL_REASONS: 451,
    },
    ServerErrors: {
      INTERNAL_SERVER_ERROR: 500,
      NOT_IMPLEMENTED: 501,
      BAD_GATEWAY: 502,
      SERVICE_UNAVAILABLE: 503,
      GATEWAY_TIMEOUT: 504,
      HTTP_VERSION_NOT_SUPPORTED: 505,
      INSUFFICIENT_STORAGE: 507,
      NETWORK_AUTHENTICATION_REQUIRED: 511
    },
  } as const;

  // Combine Client and Server errors into one object for easier lookup.
  const AllErrorCodes = {
    ...StatusCodes.ClientErrors,
    ...StatusCodes.ServerErrors,
  };

  // A type representing the string keys of all possible errors (e.g., "NOT_FOUND").
  type ErrorCode = keyof typeof AllErrorCodes;

  // A type representing the numeric status codes for all possible errors (e.g., 404).
  type HttpErrorStatusCode = typeof AllErrorCodes[ErrorCode];

  export class HttpError extends Error {
    public static withCode(code: ErrorCode, message?: string): HttpError {
      const statusCode = AllErrorCodes[code];
      // Create a user-friendly default message from the code name.
      // e.g., "INTERNAL_SERVER_ERROR" becomes "Internal Server Error"
      const defaultMessage = code
        .split('_')
        .map(word => word.charAt(0) + word.slice(1).toLowerCase())
        .join(' ');
      return new HttpError(statusCode, message ?? defaultMessage);
    }

    constructor(
      public readonly status: ServerErrorStatusCode | ClientErrorStatusCode,
      message: string,
    ) {
      super(message);
    }
  }

  // Extract the type of success status codes
  export type InformationalStatusCode = typeof StatusCodes.Informational[keyof typeof StatusCodes.Informational];
  export type SuccessStatusCode = typeof StatusCodes.Success[keyof typeof StatusCodes.Success];
  export type RedirectStatusCode = typeof StatusCodes.Redirection[keyof typeof StatusCodes.Redirection];
  export type ClientErrorStatusCode = typeof StatusCodes.ClientErrors[keyof typeof StatusCodes.ClientErrors];
  export type ServerErrorStatusCode = typeof StatusCodes.ServerErrors[keyof typeof StatusCodes.ServerErrors];

  export type Method = 'GET'|'HEAD'|'POST'|'PUT'|'DELETE'|'CONNECT'|'OPTIONS'|'TRACE'|'PATCH';
  const allHttpMethods: Method[] = ['GET', 'HEAD', 'POST', 'PUT', 'DELETE', 'CONNECT', 'OPTIONS', 'TRACE', 'PATCH'];

  export type InformationalResponse = {
    status: InformationalStatusCode,
  }
  export type SuccessResponse<TOutput> = {
    status: SuccessStatusCode,
    contentType?: string,
    data?: TOutput,
  }

  export type RedirectResponse = {
    status: RedirectStatusCode,
    url?: string,
  }

  export type ErrorResponse = {
    status: ClientErrorStatusCode | ServerErrorStatusCode,
    message: string,
  }

  export type HttpResponse<T> = InformationalResponse | SuccessResponse<T> | RedirectResponse | ErrorResponse;

  export function isInformationalResponse<T>(response: HttpResponse<T>): response is InformationalResponse {
    return response.status >= 100 && response.status < 200;
  }

  export function isSuccessResponse<T>(response: HttpResponse<T>): response is SuccessResponse<T> {
    return response.status >= 200 && response.status < 300;
  }

  export function isRedirectResponse<T>(response: HttpResponse<T>): response is RedirectResponse {
    return response.status >= 300 && response.status < 400;
  }

  export function isErrorResponse<T>(response: HttpResponse<T>): response is ErrorResponse {
    return response.status >= 400 && response.status < 600;
  }

  export function info<TOutput>(status: InformationalStatusCode): HttpResponse<TOutput> {
    return { status };
  }

  export function ok<TOutput>(data?: TOutput, contentType?: string, status?: SuccessStatusCode): HttpResponse<TOutput> {
    return {
      status: status ?? StatusCodes.Success.OK,
      contentType,
      data: data,
    };
  }

  export function redirect<TOutput>(url: string, status: RedirectStatusCode = 302): HttpResponse<TOutput> {
    return { status, url, };
  }

  export function error(message: string, status: ServerErrorStatusCode | ClientErrorStatusCode = StatusCodes.ServerErrors.INTERNAL_SERVER_ERROR): ErrorResponse {
    return { status, message, };
  }

  export type Handler<TInput extends z.ZodType | undefined, TOutput, HandlerContext> =
    (options: {
      input: TInput extends z.ZodType ? z.infer<TInput> : void,
      ctx: HandlerContext,
      method: Method
    }) => Promise<HttpResponse<TOutput>>;

  export type SimpleHandler<TInput extends z.ZodType | undefined, TOutput, HandlerContext> =
    (options: {
      input: TInput extends z.ZodType ? z.infer<TInput> : void,
      ctx: HandlerContext,
      method: Method
    }) => Promise<TOutput>;

  export type ETagGenerator<TInput extends z.ZodType | undefined, HandlerContext> =
    (options: {
      input: TInput extends z.ZodType ? z.infer<TInput> : void,
      ctx: HandlerContext,
      method: Method
    }) => Promise<string|undefined>;

  export type ContextResolver<RootContext, HandlerContext> = (options: { ctx: RootContext, input: any }) => Promise<HandlerContext>;

  export type Route<TInput extends z.ZodType | undefined, TOutput, RootContext = never, HandlerContext = RootContext> = {
    method: Method,
    input: TInput,
    resolveContext: ContextResolver<RootContext, HandlerContext>,
    etag: ETagGenerator<TInput, HandlerContext>|undefined,
    handler: Handler<TInput, TOutput, HandlerContext>,
  };

  type InferInput<T> = T extends Route<infer I extends z.ZodType, any, any, any>
    ? I extends z.ZodType
      ? z.infer<I>
      : void
    : never;
  type InferOutput<T> = T extends Route<any, infer O, any, any> ? O : never;

  export class Router<RootContext, HandlerContext = RootContext> {
    static create<RootContext>() {
      return new Router<RootContext, RootContext>(async e => e.ctx);
    }

    constructor(private _resolveContext: ContextResolver<RootContext, HandlerContext>) {
    }

    rawMethod<TInput extends z.ZodType, TOutput, TMethod extends Method>(method: TMethod, route: {
      input: TInput,
      etag?: ETagGenerator<TInput, HandlerContext>,
      handler: Handler<TInput, TOutput, HandlerContext>
    }): { [k in TMethod]: Route<TInput, TOutput, RootContext, HandlerContext> };
    rawMethod<TOutput, TMethod extends Method>(method: TMethod, route: {
      handler: Handler<undefined, TOutput, HandlerContext>,
      etag?: ETagGenerator<undefined, HandlerContext>,
    }): { [k in TMethod]: Route<undefined, TOutput, RootContext, HandlerContext> };
    rawMethod(method: Method, route: {
      input?: z.ZodType,
      etag?: ETagGenerator<any, any>,
      handler: Handler<any, any, any> }
    ) {
      return {
        [method]: {
          method,
          input: route.input,
          etag: route.etag,
          resolveContext: this._resolveContext,
          handler: route.handler,      
        }
      };
    }

    get<TInput extends z.ZodType, TOutput>(route: {
      input: TInput,
      etag?: ETagGenerator<TInput, HandlerContext>,
      handler: SimpleHandler<TInput, TOutput, HandlerContext>
    }): { GET: Route<TInput, TOutput, RootContext, HandlerContext> };
    get<TOutput>(route: {
      etag?: ETagGenerator<undefined, HandlerContext>,
      handler: SimpleHandler<undefined, TOutput, HandlerContext>
    }): { GET: Route<undefined, TOutput, RootContext, HandlerContext> };
    get(route: any) {
      return this.rawMethod('GET', {
        ...route,
        handler: (...args) => Promise.resolve(route.handler(...args)).then(result => TypedHTTP.ok(result, 'application/json')),
      }) as any;
    }

    post<TInput extends z.ZodType, TOutput>(route: {
      input: TInput,
      etag?: ETagGenerator<TInput, HandlerContext>,
      handler: SimpleHandler<TInput, TOutput, HandlerContext>
    }): { POST: Route<TInput, TOutput, RootContext, HandlerContext> };
    post<TOutput>(route: {
      etag?: ETagGenerator<undefined, HandlerContext>,
      handler: SimpleHandler<undefined, TOutput, HandlerContext>
    }): { POST: Route<undefined, TOutput, RootContext, HandlerContext> };
    post(route: any) {
      return this.rawMethod('POST', {
        ...route,
        handler: (...args) => Promise.resolve(route.handler(...args)).then(result => TypedHTTP.ok(result, 'application/json')),
      }) as any;
    }

    use<NewHContext>(resolveContext: ContextResolver<HandlerContext, NewHContext>): Router<RootContext, NewHContext> {
      return new Router<RootContext, NewHContext>(async (options) => {
        const m = await this._resolveContext(options);
        return resolveContext({ ...options, ctx: m });
      });
    }
  }

  export type Client<T> = {
    [K in keyof T as T[K] extends undefined ? never : K]: T[K] extends Route<any, any, any, any>
      ? InferInput<T[K]> extends void
        ? {
          (input?: undefined, options?: { signal?: AbortSignal }): Promise<InferOutput<T[K]>>;
          prepare: (input?: undefined, options?: { signal?: AbortSignal }) => {
            url: URL,
            method: Method,
            body?: any,
            headers?: Record<string, string>,
            signal?: AbortSignal,
          },
        }
      : {
          (input: InferInput<T[K]>, options?: { signal?: AbortSignal }): Promise<InferOutput<T[K]>>;
          prepare: (input: InferInput<T[K]>, options?: { signal?: AbortSignal }) => {
            url: URL,
            method: Method,
            body?: any,
            headers?: Record<string, string>,
            signal?: AbortSignal,
          },
        }
      : T[K] extends Record<string, any>
        ? Client<T[K]>
        : never
  };

  export function createClient<T extends Record<string, any>>(base: string|URL, fetchCallback: (url: URL, options: {
    method: Method,
    body?: any,
    headers?: Record<string, string>,
    signal?: AbortSignal,
  }) => Promise<Response>): Client<T> {

    function buildUrl(method: Method, path: string[], input?: any, options?: any) {
      const url = new URL(path.join('/'), base);
      const signal = options?.signal as AbortSignal|undefined;
      let body = undefined;
      if (method === 'GET' && input)
        url.searchParams.set('input', JSON.stringify(input));
      else if (method !== 'GET' && input)
        body = JSON.stringify(input);
      return {
        url,
        method,
        headers: body ? { 'Content-Type': 'application/json' } : undefined,
        body,
        signal,
      };
    }

    function fetcher(method: Method, path: string[], input?: any, methodOptions?: any) {
      const options = buildUrl(method, path, input, methodOptions);
      return fetchCallback(options.url, {
        method: options.method,
        body: options.body,
        headers: options.headers,
        signal: options.signal,
      }).then(async response => {
        if (response.status >= 200 && response.status < 300) {
          if (response.headers.get('content-type')?.includes('application/json')) {
            const text = await response.text();
            return text.length ? JSON.parse(text) : undefined;
          }
          return await response.arrayBuffer();
        }
        if (response.status >= 400 && response.status < 600) {
          const text = await response.text();
          if (text)
            throw new Error(`HTTP request failed with status ${response.status}: ${text}`);
          else
            throw new Error(`HTTP request failed with status ${response.status}`);
        }
      });
    }

    function createProxy(path: string[] = []) {
      return new Proxy({}, {
        get(target, prop: string | symbol) {
          if (typeof prop === 'symbol')
            return undefined;

          if (allHttpMethods.includes(prop as Method)) {
            const f = fetcher.bind(null, prop as Method, path);
            (f as any).prepare = buildUrl.bind(null, prop as Method, path);
            return f;
          }

          const newPath = [...path, prop];
          return createProxy(newPath);
        },
      });
    }
    return createProxy() as any as Client<T>;
  }
}
