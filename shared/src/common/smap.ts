export type SMapSelector<E> = {
  [K in keyof E]?: E[K];
};

type RequiredKeys<E, K extends keyof E> = {
  [P in K]-?: E[P];
};

export class SMap<E extends {}> {
  private _entries: E[];
  private _indexes = new Map<string, Index<E>>();
  constructor(entries: E[] = []) {
    this._entries = entries;
  }

  [Symbol.iterator] = () => this._entries[Symbol.iterator]()

  map(callback: (e: E) => unknown) { return this._entries.map(callback); }
  filter(callback: (e: E) => boolean) { return this._entries.filter(callback); }
  slice(from: number, to: number) { return this._entries.slice(from, to); }
  get size() { return this._entries.length; }

  has(selector: SMapSelector<E>) {
    const entries = this.getAll(selector);
    return entries.length > 0;
  }

  uniqueValues<T extends keyof E>(key: T) {
    const index = this._ensureIndex([key]);
    return [...index._data.keys()] as E[T][];
  }

  get(selector: SMapSelector<E>): E | undefined {
    const entries = this.getAll(selector);
    return entries.length ? entries[0] : undefined;
  }

  getAll(selector: SMapSelector<E>): E[] {
    const keys = Object.entries(selector).filter(([key, value]) => value !== undefined).map(([key]) => key) as (keyof E)[];
    return this._ensureIndex(keys).getAll(selector);
  }

  iterateBuckets<K extends keyof E>(keys: K[], callback: (s: RequiredKeys<E, K>, elements: E[]) => void) {
    const index = this._ensureIndex(keys);
    index.iterateBuckets(index._data, {}, 0, callback);
  }

  mapBuckets<K extends keyof E, R>(keys: K[], callback: (s: RequiredKeys<E, K>, elements: E[]) => R): R[] {
    const index = this._ensureIndex(keys);
    const result: R[] = [];
    index.iterateBuckets(index._data, {}, 0, (selector: RequiredKeys<E, K>, elements: E[]) => {
      result.push(callback(selector, elements));
    });
    return result;
  }

  _ensureIndex(keys: (keyof E)[]) {
    keys.sort();
    const indexId = JSON.stringify(keys);
    let index = this._indexes.get(indexId);
    if (!index) {
      index = new Index(this._entries, keys);
      this._indexes.set(indexId, index);
    }
    return index;
  }
}

type IndexMap<E> = Map<unknown, IndexMap<unknown> | E[]>;

class Index<E> {
  private _keys: (keyof E)[];
  private _lastKey: (keyof E);
  _data: IndexMap<E> = new Map();

  constructor(entries: E[], keys: (keyof E)[]) {
    this._keys = keys;
    this._lastKey = this._keys.pop()!;
    this._data = new Map();
    for (const entry of entries) {
      let data = this._data;
      for (const key of this._keys) {
        const value = entry[key] as any;
        let map = data.get(value);
        if (!map) {
          map = new Map();
          data.set(value, map);
        }
        data = map as any;
      }
      const lastValue = entry[this._lastKey];
      let array = data.get(lastValue as any);
      if (!array) {
        array = [];
        data.set(lastValue as any, array);
      }
      (array as any).push(entry);
    }
  }

  iterateBuckets(data: IndexMap<E>, currentSelector: SMapSelector<E>, currentIndex: number, callback: (s: RequiredKeys<E, keyof E>, e: E[]) => void) {
    if (currentIndex === this._keys.length) {
      for (const value of data.keys()) {
        const elements = data.get(value) as E[];
        callback({ ...currentSelector, [this._lastKey]: value } as RequiredKeys<E, keyof E>, elements);
      }
      return;
    }
    for (const value of data.keys())
      this.iterateBuckets(data.get(value) as IndexMap<E>, { ...currentSelector, [this._keys[currentIndex]]: value }, currentIndex + 1, callback);
  }

  getAll(selector: SMapSelector<E>): E[] {
    let data = this._data;
    for (const key of this._keys) {
      data = data.get(selector[key]) as IndexMap<E>;
      if (!data)
        return [];
    }
    return data.get(selector[this._lastKey]) as E[] ?? [];
  }
}
