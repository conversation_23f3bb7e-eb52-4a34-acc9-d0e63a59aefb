import { spawnSync, SpawnSyncOptionsWithStringEncoding } from 'child_process';
import fs from 'fs';
import os from 'os';
import { FlakinessReport } from '../../report/sdk/src/reportutils.js';

function shell(command: string, args?: string[], options?: SpawnSyncOptionsWithStringEncoding) {
  try {
    const result = spawnSync(command, args, { encoding: 'utf-8', ...options });
    if (result.status !== 0) {
      return undefined;
    }
    return (result.stdout as string).trim();
  } catch (e) {
    console.error(e);
    return undefined;
  }
}

function readLinuxOSRelease() {
  const osReleaseText = fs.readFileSync('/etc/os-release', 'utf-8');
  return new Map(osReleaseText.toLowerCase().split('\n').filter(line => line.includes('=')).map(line => {
    line = line.trim();
    let [key, value] = line.split('=');
    if (value.startsWith('"') && value.endsWith('"'))
      value = value.substring(1, value.length - 1);
    return [key, value];
  }));
}

type OSInfo = { name?: string, arch?: string, version?: string };
function osLinuxInfo(): OSInfo {
  const arch = shell(`uname`, [`-m`]);
  const osReleaseMap = readLinuxOSRelease();
  const name = osReleaseMap.get('name') ?? shell(`uname`);
  const version = osReleaseMap.get('version_id');
  return { name, arch, version };
}

function osDarwinInfo(): OSInfo {
  const name = 'macos';
  const arch = shell(`uname`, [`-m`]);
  const version = shell(`sw_vers`, [`-productVersion`]);
  return { name, arch, version };
}

function osWinInfo(): OSInfo {
  const name = 'win';
  const arch = process.arch;
  const version = os.release();
  return { name, arch, version };
}

function getOSInfo(): OSInfo {
  if (process.platform === 'darwin')
    return osDarwinInfo();
  if (process.platform === 'win32')
    return osWinInfo();
  return osLinuxInfo();
}

function extractEnvConfiguration() {
  const ENV_PREFIX = 'FK_ENV_';
  return Object.fromEntries(Object
    .entries(process.env)
    .filter(([key]) => key.toUpperCase().startsWith(ENV_PREFIX.toUpperCase()))
    .map(([key, value]) => [key.substring(ENV_PREFIX.length).toLowerCase(), (value ?? '').trim().toLowerCase()])
  );
}

export function createEnvironment(options: {
  name: string,
  userSuppliedData?: Record<string, string>,
  opaqueData?: any,
}): FlakinessReport.Environment {
  const osInfo = getOSInfo();
  return {
    name: options.name,
    systemData: {
      osArch: osInfo.arch,
      osName: osInfo.name,
      osVersion: osInfo.version,
    },
    userSuppliedData: {
      ...extractEnvConfiguration(),
      ...options.userSuppliedData ?? {},
    },
    opaqueData: options.opaqueData,
  }
}
