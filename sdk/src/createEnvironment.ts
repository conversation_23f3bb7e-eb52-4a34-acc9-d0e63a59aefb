import { FlakinessReport } from '@flakiness/flakiness-report';
import fs from 'fs';
import os from 'os';
import { shell } from './_internalUtils.js';

function readLinuxOSRelease() {
  const osReleaseText = fs.readFileSync('/etc/os-release', 'utf-8');
  return new Map(osReleaseText.toLowerCase().split('\n').filter(line => line.includes('=')).map(line => {
    line = line.trim();
    let [key, value] = line.split('=');
    if (value.startsWith('"') && value.endsWith('"'))
      value = value.substring(1, value.length - 1);
    return [key, value];
  }));
}

type OSInfo = { name?: string, arch?: string, version?: string };
function osLinuxInfo(): OSInfo {
  const arch = shell(`uname`, [`-m`]);
  const osReleaseMap = readLinuxOSRelease();
  const name = osReleaseMap.get('name') ?? shell(`uname`);
  const version = osReleaseMap.get('version_id');
  return { name, arch, version };
}

function osDarwinInfo(): OSInfo {
  const name = 'macos';
  const arch = shell(`uname`, [`-m`]);
  const version = shell(`sw_vers`, [`-productVersion`]);
  return { name, arch, version };
}

function osWinInfo(): OSInfo {
  const name = 'win';
  const arch = process.arch;
  const version = os.release();
  return { name, arch, version };
}

function getOSInfo(): OSInfo {
  if (process.platform === 'darwin')
    return osDarwinInfo();
  if (process.platform === 'win32')
    return osWinInfo();
  return osLinuxInfo();
}

function extractEnvConfiguration() {
  const ENV_PREFIX = 'FK_ENV_';
  return Object.fromEntries(Object
    .entries(process.env)
    .filter(([key]) => key.toUpperCase().startsWith(ENV_PREFIX.toUpperCase()))
    .map(([key, value]) => [key.substring(ENV_PREFIX.length).toLowerCase(), (value ?? '').trim().toLowerCase()])
  );
}

export function createEnvironment(options: {
  name: string,
  userSuppliedData?: Record<string, string>,
  opaqueData?: any,
}): FlakinessReport.Environment {
  const osInfo = getOSInfo();
  return {
    name: options.name,
    systemData: {
      osArch: osInfo.arch,
      osName: osInfo.name,
      osVersion: osInfo.version,
    },
    userSuppliedData: {
      ...extractEnvConfiguration(),
      ...options.userSuppliedData ?? {},
    },
    opaqueData: options.opaqueData,
  }
}
