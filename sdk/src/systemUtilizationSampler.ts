import { spawnSync } from 'child_process';
import os from 'os';
import { FlakinessReport } from "../../report/sdk/src/reportutils.js";

type SystemUtilizationSample = {
  timestamp: FlakinessReport.UnixTimestampMS,
  idleTicks: number,
  totalTicks: number,
  freeBytes: number,
};

/**
 * On MacOS, the os.freemem() gives a very crude number; use the following
 * trick to have an output similar to the one that HTOP yields.
 */
function getAvailableMemMacOS() {
  const lines = spawnSync('vm_stat', { encoding: 'utf8' }).stdout.trim().split('\n');
  const pageSize = parseInt(lines[0].match(/page size of (\d+) bytes/)![1], 10);
  if (isNaN(pageSize)) {
    console.warn('[flakiness.io] Error detecting macos page size');
    return 0;
  }

  let totalFree = 0;
  for (const line of lines) {
    if (/Pages (free|inactive|speculative):/.test(line)) {
      const match = line.match(/\d+/);
      if (match)
        totalFree +=  parseInt(match[0], 10);
    }
  }
  return totalFree * pageSize;
}

function getSystemUtilization(): SystemUtilizationSample {
  let idleTicks = 0;
  let totalTicks = 0;
  for (const cpu of os.cpus()) {
    totalTicks += cpu.times.user + cpu.times.nice + cpu.times.sys + cpu.times.irq + cpu.times.idle;
    idleTicks += cpu.times.idle;
  }
  return {
    idleTicks,
    totalTicks,
    timestamp: Date.now() as FlakinessReport.UnixTimestampMS,
    freeBytes: os.platform() === 'darwin' ? getAvailableMemMacOS() : os.freemem(),
  };
}

function toFKUtilization(sample: SystemUtilizationSample, previous: SystemUtilizationSample): FlakinessReport.SystemUtilizationSample {
  const idleTicks = sample.idleTicks - previous.idleTicks;
  const totalTicks = sample.totalTicks - previous.totalTicks;
  const cpuUtilization = Math.floor((1 - idleTicks / totalTicks) * 10000) / 100;
  const memoryUtilization = Math.floor((1 - sample.freeBytes / os.totalmem()) * 10000) / 100;
  return {
    cpuUtilization,
    memoryUtilization: memoryUtilization,
    dts: (sample.timestamp - previous.timestamp) as FlakinessReport.DurationMS,
  }
}

export class SystemUtilizationSampler {
  public readonly result: FlakinessReport.SystemUtilization;

  private _lastSample = getSystemUtilization();
  private _timer: NodeJS.Timeout;

  constructor() {
    this.result = {
      samples: [],
      startTimestamp: this._lastSample.timestamp,
      totalMemoryBytes: os.totalmem(),
    };
    // We collect the very first sample pretty fast; all other will be slower.
    this._timer = setTimeout(this._addSample.bind(this), 50);
  }

  private _addSample() {
    const sample = getSystemUtilization();
    this.result.samples.push(toFKUtilization(sample, this._lastSample));
    this._lastSample = sample;
    this._timer = setTimeout(this._addSample.bind(this), 1000);
  }

  dispose() {
    clearTimeout(this._timer);
  }
}
