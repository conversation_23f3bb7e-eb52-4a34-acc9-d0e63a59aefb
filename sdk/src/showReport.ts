import chalk from 'chalk';
import open from "open";
import path from 'path';
import { FlakinessProjectConfig } from "./flakinessProjectConfig.js";
import { LocalReportServer } from './localReportServer.js';

export async function showReport(reportFolder: string) {
  const reportPath = path.join(reportFolder, 'report.json');

  const config = await FlakinessProjectConfig.load();
  const projectPublicId = config.projectPublicId();

  const reportViewerEndpoint = config.reportViewerEndpoint();
  const server = await LocalReportServer.create({
    endpoint: reportViewerEndpoint,
    port: 9373,
    reportPath,
    attachmentsFolder: reportFolder,
  });

  const url = new URL(reportViewerEndpoint);
  url.searchParams.set('port', String(server.port()));
  url.searchParams.set('token', server.authToken());
  if (projectPublicId)
    url.searchParams.set('ppid', projectPublicId);

  console.log(chalk.cyan(`
  Serving Flakiness report at ${(url.toString())}
  Press Ctrl+C to quit.`))
  await open(url.toString());
  await new Promise(() => {});
}