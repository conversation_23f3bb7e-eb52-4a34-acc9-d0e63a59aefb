import chalk from 'chalk';
import open from "open";
import { randomUUIDBase62 } from './_internalUtils.js';
import { FlakinessProjectConfig } from "./flakinessProjectConfig.js";
import { StaticServer } from './staticServer.js';

export async function showReport(reportFolder: string) {
  const config = await FlakinessProjectConfig.load();
  const projectPublicId = config.projectPublicId();

  const reportViewerEndpoint = config.reportViewerUrl();

  const token = randomUUIDBase62();
  const server = new StaticServer(token, reportFolder, reportViewerEndpoint);
  await server.start(9373, '127.0.0.1');

  const url = new URL(reportViewerEndpoint);
  url.searchParams.set('port', String(server.port()));
  url.searchParams.set('token', token);
  if (projectPublicId)
    url.searchParams.set('ppid', projectPublicId);

  console.log(chalk.cyan(`
  Serving Flakiness report at ${(url.toString())}
  Press Ctrl+C to quit.`))
  await open(url.toString());
  await new Promise(() => {});
}