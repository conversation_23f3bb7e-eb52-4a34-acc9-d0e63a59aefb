import { FlakinessReport } from '@flakiness/flakiness-report';
import assert from 'assert';
import { exec } from 'child_process';
import debug from 'debug';
import { posix as posixPath, win32 as win32Path } from 'path';
import { promisify } from 'util';
import { Brand, shell } from './_internalUtils.js';

const log = debug('fk:git');

const execAsync = promisify(exec);

export type GitCommit = {
  commitId: FlakinessReport.CommitId,
  timestamp: FlakinessReport.UnixTimestampMS,
  message: string,
  author?: string,
  parents: FlakinessReport.CommitId[],
}

type PosixAbsolutePath = Brand<string, 'PosixPath'>;

/**
 * Different environments might yield different paths.
 * - Win32: D:\foo\bar.txt
 * - ALMOST_POSIX: D:/foo/bar.txt (this is how many folks on the internet end up converting Win32 paths to Posix paths, including <PERSON>wright.)
 * - Posix: /d/foo/bar.txt
 * Goal is to normalize them all to POSIX.
 * @param aPath a relative or absolute path.
 * @returns
 */
const IS_WIN32_PATH = new RegExp('^[a-zA-Z]:\\\\', 'i');
const IS_ALMOST_POSIX_PATH = new RegExp('^[a-zA-Z]:/', 'i');

function toPosixAbsolutePath(absolutePath: string): PosixAbsolutePath {
  if (IS_WIN32_PATH.test(absolutePath)) {
    // convert Win32 path to ALMOST_POSIX path
    absolutePath = absolutePath.split(win32Path.sep).join(posixPath.sep);
  }
  if (IS_ALMOST_POSIX_PATH.test(absolutePath))
    return ('/' + absolutePath[0] + absolutePath.substring(2)) as PosixAbsolutePath;
  return absolutePath as PosixAbsolutePath;
}

function toNativeAbsolutePath(posix: PosixAbsolutePath): string {
  // On non-win32 systems, posix path is already native
  if (process.platform !== 'win32')
    return posix;

  // Convert POSIX path (/d/foo/bar.txt) to Win32 format (D:\foo\bar.txt)
  assert(posix.startsWith('/'), 'The path must be absolute');
  const m = posix.match(/^\/([a-zA-Z])(\/.*)?$/);
  assert(m, `Invalid POSIX path: ${posix}`)

  const drive = m[1];
  const rest = (m[2] ?? '').split(posixPath.sep).join(win32Path.sep);
  return drive.toUpperCase() + ':' + rest;
}

/**
 * Utilities for working with git repositories and converting between git-relative paths
 * and absolute native paths. Essential for creating Flakiness Reports where all paths
 * must be relative to the git root.
 */
export class GitWorktree {
  /**
   * Creates a GitWorktree instance from any path inside a git repository.
   */
  static create(somePathInsideGitRepo: string) {
    const root = shell(`git`, ['rev-parse', '--show-toplevel'], {
      cwd: somePathInsideGitRepo,
      encoding: 'utf-8',
    });
    assert(root, `FAILED: git rev-parse --show-toplevel HEAD @ ${somePathInsideGitRepo}`);
    return new GitWorktree(root);
  }

  private _posixGitRoot: PosixAbsolutePath;

  constructor(private _gitRoot: string) {
    this._posixGitRoot = toPosixAbsolutePath(this._gitRoot);
  }

  /** Returns the native absolute path of the git root. */
  rootPath(): string {
    return this._gitRoot;
  }

  /** Returns the commit ID of the current HEAD. */
  headCommitId(): FlakinessReport.CommitId {
    const sha = shell(`git`, ['rev-parse', 'HEAD'], {
      cwd: this._gitRoot,
      encoding: 'utf-8',
    });
    assert(sha, `FAILED: git rev-parse HEAD @ ${this._gitRoot}`);
    return sha.trim() as FlakinessReport.CommitId;
  }

  /**
   * Converts a native absolute path to a git-relative POSIX path.
   * Use with absolutePath() for bidirectional path conversion.
   */
  gitPath(absolutePath: string): FlakinessReport.GitFilePath {
    return posixPath.relative(this._posixGitRoot, toPosixAbsolutePath(absolutePath)) as FlakinessReport.GitFilePath;
  }

  /**
   * Converts a git-relative POSIX path to a native absolute path.
   * Use with gitPath() for bidirectional path conversion.
   */
  absolutePath(relativePath: FlakinessReport.GitFilePath): string {
    return toNativeAbsolutePath(posixPath.join(this._posixGitRoot, relativePath) as PosixAbsolutePath);
  }

  /**
   * Lists recent commits from the repository.
   * Note: CI environments often have shallow checkouts with limited history.
   */
  async listCommits(count: number): Promise<GitCommit[]> {
    return await listCommits(this._gitRoot, 'HEAD', count);
  }
}

async function listCommits(gitRoot: string, head: string, count: number): Promise<GitCommit[]> {
  const FIELD_SEPARATOR = '|~|';
  const RECORD_SEPARATOR = '\0';

  // Git log format: hash, timestamp, author, subject, parents
  const prettyFormat = [
    '%H',  // Full commit hash
    '%ct', // Commit timestamp (Unix seconds)
    '%an', // Author name
    '%s',  // Subject line
    '%P'   // Parent hashes (space-separated)
  ].join(FIELD_SEPARATOR);

  const command = `git log ${head} -n ${count} --pretty=format:"${prettyFormat}" -z`;

  try {
    const { stdout } = await execAsync(command, { cwd: gitRoot });

    if (!stdout) {
      return [];
    }

    return stdout
      .trim()
      .split(RECORD_SEPARATOR)
      .filter(record => record)
      .map(record => {
        const [commitId, timestampStr, author, message, parentsStr] = record.split(FIELD_SEPARATOR);
        const parents = parentsStr ? parentsStr.split(' ').filter(p => p) : [];

        return {
          commitId: commitId as FlakinessReport.CommitId,
          timestamp: parseInt(timestampStr, 10) * 1000 as FlakinessReport.UnixTimestampMS,
          author,
          message,
          parents: parents as FlakinessReport.CommitId[],
          walkIndex: 0,
        };
      });
  } catch (error) {
    log(`Failed to list commits for repository at ${gitRoot}:`, error);
    return [];
  }
}