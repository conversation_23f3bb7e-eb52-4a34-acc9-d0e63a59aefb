import { FlakinessReport } from '@flakiness/flakiness-report';
import assert from 'assert';
import { exec } from 'child_process';
import debug from 'debug';
import { posix as posixPath, win32 as win32Path } from 'path';
import { promisify } from 'util';
import { Brand, shell } from './_internalUtils.js';

const log = debug('fk:git');

const execAsync = promisify(exec);

export type GitCommit = {
  commitId: FlakinessReport.CommitId,
  timestamp: FlakinessReport.UnixTimestampMS,
  message: string,
  author?: string,
  parents: FlakinessReport.CommitId[],
}

type PosixAbsolutePath = Brand<string, 'PosixPath'>;

/**
 * Different environments might yield different paths.
 * - Win32: D:\foo\bar.txt
 * - ALMOST_POSIX: D:/foo/bar.txt (this is how many folks on the internet end up converting Win32 paths to Posix paths, including <PERSON>wright.)
 * - Posix: /d/foo/bar.txt
 * Goal is to normalize them all to POSIX.
 * @param aPath a relative or absolute path.
 * @returns
 */
const IS_WIN32_PATH = new RegExp('^[a-zA-Z]:\\\\', 'i');
const IS_ALMOST_POSIX_PATH = new RegExp('^[a-zA-Z]:/', 'i');

function toPosixAbsolutePath(absolutePath: string): PosixAbsolutePath {
  if (IS_WIN32_PATH.test(absolutePath)) {
    // convert Win32 path to ALMOST_POSIX path
    absolutePath = absolutePath.split(win32Path.sep).join(posixPath.sep);
  }
  if (IS_ALMOST_POSIX_PATH.test(absolutePath))
    return ('/' + absolutePath[0] + absolutePath.substring(2)) as PosixAbsolutePath;
  return absolutePath as PosixAbsolutePath;
}

function toNativeAbsolutePath(posix: PosixAbsolutePath): string {
  // On non-win32 systems, posix path is native as well.
  if (process.platform !== 'win32')
    return posix;

  // Posix path for win32 system looks like /d/foo/bar.txt
  assert(posix.startsWith('/'), 'The path must be absolute');
  const m = posix.match(/^\/([a-zA-Z])(\/.*)?$/);
  assert(m, `Invalid POSIX path: ${posix}`)

  const drive = m[1];
  const rest = (m[2] ?? '').split(posixPath.sep).join(win32Path.sep);
  return drive.toUpperCase() + ':' + rest;
}

/**
 * GitWorktree provides a set of utilities to work with git repository,
 * and a mapping between posix paths, relative to the git root,
 * and absolute native paths.
 * 
 * This class is crucial when creating Flakiness Reports, since all paths
 * in the Flakiness Report are relative to the git root.
 */
export class GitWorktree {
  /**
   * Initialize a git worktree with some path inside the git repository.
   * @param somePathInsideGitRepo 
   * @returns 
   */
  static create(somePathInsideGitRepo: string) {
    const root = shell(`git`, ['rev-parse', '--show-toplevel'], {
      cwd: somePathInsideGitRepo,
      encoding: 'utf-8',
    });
    assert(root, `FAILED: git rev-parse --show-toplevel HEAD @ ${somePathInsideGitRepo}`);
    return new GitWorktree(root);
  }

  private _posixGitRoot: PosixAbsolutePath;

  constructor(private _gitRoot: string) {
    this._posixGitRoot = toPosixAbsolutePath(this._gitRoot);
  }

  /**
   * @returns Native absolute path of the git root.
   */
  rootPath(): string {
    return this._gitRoot;
  }

  /**
   * 
   * @returns CommitId of the checked out HEAD
   */
  headCommitId(): FlakinessReport.CommitId {
    const sha = shell(`git`, ['rev-parse', 'HEAD'], {
      cwd: this._gitRoot,
      encoding: 'utf-8',
    });
    assert(sha, `FAILED: git rev-parse HEAD @ ${this._gitRoot}`);
    return sha.trim() as FlakinessReport.CommitId;
  }

  /**
   * This method, together with {absolutePath}, provide a pair of
   * transformations from posit path, relative to the git root, and absolute
   * native paths.
   * @param absolutePath native absolute path
   * @returns posix path, relative to the git root
   */
  gitPath(absolutePath: string): FlakinessReport.GitFilePath {
    return posixPath.relative(this._posixGitRoot, toPosixAbsolutePath(absolutePath)) as FlakinessReport.GitFilePath;
  }

  /**
   * This method, together with {absolutePath}, provide a pair of
   * transformations from posit path, relative to the git root, and absolute
   * native paths.
   * @param relativePath posix path, relative to the git root
   * @returns native absolute path
   */
  absolutePath(relativePath: FlakinessReport.GitFilePath): string {
    return toNativeAbsolutePath(posixPath.join(this._posixGitRoot, relativePath) as PosixAbsolutePath);
  }

  async listCommits(count: number): Promise<GitCommit[]> {
    return await listCommits(this._gitRoot, 'HEAD', count);
  }
}

async function listCommits(gitRoot: string, head: string, count: number): Promise<GitCommit[]> {
  // Define separators for robust parsing of the git log output
  const FIELD_SEPARATOR = '|~|';
  const RECORD_SEPARATOR = '\0'; // Use the null character as a safe record separator

  // Define the custom format for the git log output.
  // This specifies which pieces of information we want for each commit.
  const prettyFormat = [
    '%H',  // %H:  Full commit hash
    '%ct', // %at: Commit date as a Unix timestamp (seconds since epoch)
    '%an', // %an: Author name
    '%s',  // %s:  Subject (the first line of the commit message)
    '%P'   // %P:  Parent hashes (space-separated)
  ].join(FIELD_SEPARATOR);

  // Construct the git log command. The `-z` flag separates commits with a null character,
  // which prevents issues with commit messages that contain newlines.
  const command = `git log ${head} -n ${count} --pretty=format:"${prettyFormat}" -z`;

  try {
    // Execute the command in the specified git repository directory
    const { stdout } = await execAsync(command, { cwd: gitRoot });

    // If there's no output, return an empty array
    if (!stdout) {
      return [];
    }

    // Parse the raw string output from the git command
    return stdout
      .trim() // Remove any leading/trailing whitespace
      .split(RECORD_SEPARATOR) // Split the output into individual commit records
      .filter(record => record) // Filter out any empty strings that may result from splitting
      .map(record => {
        // For each record, split it into its constituent fields
        const [commitId, timestampStr, author, message, parentsStr] = record.split(FIELD_SEPARATOR);

        // Parse parent commit IDs (space-separated)
        const parents = parentsStr ? parentsStr.split(' ').filter(p => p) : [];

        // Create and return a Commit object
        return {
          commitId: commitId as FlakinessReport.CommitId,
          timestamp: parseInt(timestampStr, 10) * 1000 as FlakinessReport.UnixTimestampMS,
          author,
          message,
          parents: parents as FlakinessReport.CommitId[],
          walkIndex: 0,
        };
      });
  } catch (error) {
    log(`Failed to list commits for repository at ${gitRoot}:`, error);
    return [];
  }
}