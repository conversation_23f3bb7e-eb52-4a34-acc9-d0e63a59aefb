import { FlakinessReport } from '@flakiness/flakiness-report';
import assert from 'assert';
import fs from 'fs';
import { URL } from 'url';
import { compressTextAsync, httpUtils, retryWithBackoff, sha1File, sha1Text } from './_internalUtils.js';

type ReportUploaderOptions = {
  flakinessEndpoint: string;
  flakinessAccessToken: string;
}

export type DataAttachment = {
  type: 'buffer',
  id: FlakinessReport.AttachmentId;
  contentType: string,
  body: Buffer;
}

export type FileAttachment = {
  type: 'file',
  id: FlakinessReport.AttachmentId;
  contentType: string,
  path: string;
}

export type Attachment = FileAttachment | DataAttachment;

export async function createFileAttachment(contentType: string, filePath: string): Promise<FileAttachment> {
  return {
    type: 'file',
    contentType,
    id: await sha1File(filePath) as FlakinessReport.AttachmentId,
    path: filePath,
  };
}

export async function createDataAttachment(contentType: string, data: Buffer): Promise<DataAttachment> {
  return {
    type: 'buffer',
    contentType,
    id: sha1Text(data) as FlakinessReport.AttachmentId,
    body: data,
  };
}

type UploadResult = 
  | { status: 'success'; reportUrl: string }
  | { status: 'skipped'; reason: string }
  | { status: 'failed'; error: string };

interface Logger {
  log(message: string): void;
  warn(message: string): void;
  error(message: string): void;
}

export async function uploadReport(
  report: FlakinessReport.Report, 
  attachments: (DataAttachment|FileAttachment)[], 
  options?: {
    flakinessEndpoint?: string;
    flakinessAccessToken?: string;
    logger?: Logger;
    /**
     * If true, the method will throw an error on failure.
     * Default: false (Safe mode)
     */
    throwOnFailure?: boolean;
  }
): Promise<UploadResult> {
  const flakinessAccessToken = options?.flakinessAccessToken ?? process.env['FLAKINESS_ACCESS_TOKEN'];
  const flakinessEndpoint = options?.flakinessEndpoint ?? process.env['FLAKINESS_ENDPOINT'] ?? 'https://flakiness.io';

  const logger = options?.logger ?? console;

  if (!flakinessAccessToken) {
    const reason = 'No FLAKINESS_ACCESS_TOKEN found';
    if (process.env.CI)
      logger.warn(`[flakiness.io] ⚠ Skipping upload: ${reason}`);
    return { status: 'skipped', reason }; 
  }

  try {
    const upload = new ReportUpload(report, attachments, { flakinessAccessToken, flakinessEndpoint });
    const uploadResult = await upload.upload();
    if (!uploadResult.success) {
      const errorMessage = uploadResult.message || 'Unknown upload error';
      logger.error(`[flakiness.io] ✕ Failed to upload: ${errorMessage}`);
      if (options?.throwOnFailure)
        throw new Error(`Flakiness upload failed: ${errorMessage}`);
      return { status: 'failed', error: errorMessage };
    }
    logger.log(`[flakiness.io] ✓ Uploaded to ${uploadResult.reportUrl}`);
    return { status: 'success', reportUrl: uploadResult.reportUrl! };
  } catch (e: any) {
    // --- Scenario D: Unexpected Crash (FAIL) ---
    const errorMessage = e.message || String(e);
    logger.error(`[flakiness.io] ✕ Unexpected error during upload: ${errorMessage}`);
    if (options?.throwOnFailure)
      throw e;
    return { status: 'failed', error: errorMessage };
  }
}

const HTTP_BACKOFF = [100, 500, 1000, 1000, 1000, 1000];

class ReportUpload {
  private _report: FlakinessReport.Report;
  private _attachments: Attachment[];
  private _options: ReportUploaderOptions;

  constructor(report: FlakinessReport.Report, attachments: Attachment[], options: ReportUploaderOptions) {
    this._options = options;
    this._report = report;
    this._attachments = attachments;
  }

  private async _api<OUTPUT>(pathname: string, token: string, body?: any): Promise<{ result?: OUTPUT, error?: string }> {
    const url = new URL(this._options.flakinessEndpoint);
    url.pathname = pathname;
    return await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: body ? JSON.stringify(body) : undefined,
    }).then(async response => !response.ok ? {
      result: undefined,
      error: response.status + ' ' + url.href + ' ' + await response.text(),
    } : {
      result: await response.json() as OUTPUT,
      error: undefined,
    }).catch(error => ({
      result: undefined,
      error,
    }));
  }

  async upload(): Promise<{ success: false, message?: string } | { success: true, reportUrl: string }> {
    const response = await this._api<{ uploadToken: string, presignedReportUrl: string, webUrl: string, }>('/api/upload/start', this._options.flakinessAccessToken);
    if (response?.error || !response.result)
      return { success: false, message: response.error};
    const webUrl = new URL(response.result.webUrl, this._options.flakinessEndpoint).toString();

    const attachmentsPresignedUrls = await this._api<{ attachmentId: string, presignedUrl: string }[]>('/api/upload/attachments', response.result.uploadToken, {
      attachmentIds: this._attachments.map(a => a.id),
    });
    if (attachmentsPresignedUrls?.error || !attachmentsPresignedUrls.result)
      return { success: false, message: attachmentsPresignedUrls.error};

    const attachments = new Map(attachmentsPresignedUrls.result.map(a => [a.attachmentId, a.presignedUrl]));
    await Promise.all([
      this._uploadReport(JSON.stringify(this._report), response.result.presignedReportUrl),
      ...this._attachments.map(attachment => {
        const uploadURL = attachments.get(attachment.id);
        if (!uploadURL)
          throw new Error('Internal error: missing upload URL for attachment!');
        return this._uploadAttachment(attachment, uploadURL);
      }),
    ]);
    await this._api<{ webUrl: string }>('/api/upload/finish', response.result.uploadToken);
    return { success: true, reportUrl: webUrl };
  }

  private async _uploadReport(data: string, uploadUrl: string) {
    const compressed = await compressTextAsync(data);
    const headers = {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(compressed) + '',
      'Content-Encoding': 'br',
    };
    await retryWithBackoff(async () => {
      const { request, responseDataPromise } = httpUtils.createRequest({
        url: uploadUrl,
        headers,
        method: 'put'
      });
      request.write(compressed);
      request.end();
      await responseDataPromise;
    }, HTTP_BACKOFF);
  }

  private async _uploadAttachment(attachment: Attachment, uploadUrl: string) {
    const mimeType = attachment.contentType.toLocaleLowerCase().trim();
    const compressable = mimeType.startsWith('text/')
      || mimeType.endsWith('+json')
      || mimeType.endsWith('+text')
      || mimeType.endsWith('+xml')
    ;
    // Stream file only if there's attachment path and we should NOT compress it.
    if (!compressable && attachment.type === 'file') {
      await retryWithBackoff(async () => {
        const { request, responseDataPromise } = httpUtils.createRequest({
          url: uploadUrl,
          headers: {
            'Content-Type': attachment.contentType,
            'Content-Length': (await fs.promises.stat(attachment.path)).size + '',
          },
          method: 'put'
        });
        fs.createReadStream(attachment.path)
          .pipe(request);
        await responseDataPromise;
      }, HTTP_BACKOFF);
      return;
    }
    let buffer = attachment.type === 'buffer' ? attachment.body : await fs.promises.readFile(attachment.path);
    assert(buffer);

    const encoding = compressable ? 'br' : undefined;

    if (compressable)
      buffer = await compressTextAsync(buffer);

    const headers = {
      'Content-Type': attachment.contentType,
      'Content-Length': Buffer.byteLength(buffer) + '',
      'Content-Encoding': encoding,
    };

    await retryWithBackoff(async () => {
      const { request, responseDataPromise } = httpUtils.createRequest({
        url: uploadUrl,
        headers,
        method: 'put'
      });
      request.write(buffer);
      request.end();
      await responseDataPromise;
    }, HTTP_BACKOFF);
  }
}