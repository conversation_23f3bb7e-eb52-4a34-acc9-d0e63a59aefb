import { FlakinessReport } from '@flakiness/flakiness-report';
import fs from 'fs';
import path from 'path';
import { ExternalAttachment } from './uploadReport.js';
// Re-exports
export { FlakinessReport } from '@flakiness/flakiness-report';

export async function writeReport(report: FlakinessReport.Report, attachments: ExternalAttachment[], outputFolder: string): Promise<ExternalAttachment[]> {
  // Write report and its attachments to outputFolder.
  const reportPath = path.join(outputFolder, 'report.json');
  const attachmentsFolder = path.join(outputFolder, 'attachments');
  await fs.promises.rm(outputFolder, { recursive: true, force: true });
  await fs.promises.mkdir(outputFolder, { recursive: true });
  await fs.promises.writeFile(reportPath, JSON.stringify(report), 'utf-8');

  if (attachments.length)
    await fs.promises.mkdir(attachmentsFolder);

  const movedAttachments: ExternalAttachment[] = [];
  for (const attachment of attachments) {
    const attachmentPath = path.join(attachmentsFolder, attachment.id);
    if (attachment.path)
      await fs.promises.cp(attachment.path, attachmentPath);
    else if (attachment.body)
      await fs.promises.writeFile(attachmentPath, attachment.body);
    movedAttachments.push({
      contentType: attachment.contentType,
      id: attachment.id,
      path: attachmentPath,
    });
  }
  return movedAttachments;
}