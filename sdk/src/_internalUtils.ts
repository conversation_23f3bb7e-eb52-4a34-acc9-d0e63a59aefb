import { spawnSync, SpawnSyncOptionsWithStringEncoding } from 'child_process';
import crypto from 'crypto';
import fs from 'fs';
import http from 'http';
import https from 'https';
import util from 'util';
import zlib from 'zlib';

const asyncBrotliCompress = util.promisify(zlib.brotliCompress);
export async function compressTextAsync(text: string|Buffer) {
  return asyncBrotliCompress(text, {
    chunkSize: 32 * 1024,
    params: {
      [zlib.constants.BROTLI_PARAM_QUALITY]: 6,
      [zlib.constants.BROTLI_PARAM_MODE]: zlib.constants.BROTLI_MODE_TEXT,
    }
  });
}

export type Brand<T, Brand extends string> = T & {
  readonly [B in Brand as `__${B}_brand`]: never;
};

const FLAKINESS_DBG = !!process.env.FLAKINESS_DBG;
export function errorText(error: Error) {
  return FLAKINESS_DBG ? error.stack : error.message;
}

export async function retryWithBackoff<T>(job: () => Promise<T>, backoff: number[] = []): Promise<T> {
  for (const timeout of backoff) {
    try {
      return await job();
    } catch (e: any) {
      if (e instanceof AggregateError)
        console.error(`[flakiness.io err]`, errorText(e.errors[0]));
      else if (e instanceof Error)
        console.error(`[flakiness.io err]`, errorText(e));
      else
        console.error(`[flakiness.io err]`, e);
      await new Promise(x => setTimeout(x, timeout));
    }
  }
  return await job();
}

export namespace httpUtils {
  export function createRequest({ url, method = 'get', headers = {} }: {
    url: string,
    method?: 'get' | 'post' | 'put',
    headers?: Record<string, string|undefined>,
  }) {
    let resolve: (data: Buffer) => void;
    let reject: (e: Error) => void;
    const responseDataPromise = new Promise<Buffer>((a, b) => {
      resolve = a;
      reject = b;
    });

    const protocol = url.startsWith('https') ? https : http;
    headers = Object.fromEntries(Object.entries(headers).filter(([key, value]) => value !== undefined));
    const request = protocol.request(url, { method, headers }, (res: http.IncomingMessage) => {
      const chunks: Buffer[] = [];
      res.on('data', (chunk: Buffer) => chunks.push(chunk));
      res.on('end', () => {
        if (res.statusCode && res.statusCode >= 200 && res.statusCode < 300)
          resolve(Buffer.concat(chunks));
        else
          reject(new Error(`Request to ${url} failed with ${res.statusCode}`));
      });
      res.on('error', error => reject(error));
    });
    request.on('error', reject!);
    return { request, responseDataPromise };
  }

  export async function getBuffer(url: string, backoff?: number[]): Promise<Buffer> {
    return await retryWithBackoff(async () => {
      const { request, responseDataPromise } = createRequest({ url });
      request.end();
      return await responseDataPromise;  
    }, backoff);
  }

  export async function getText(url: string, backoff?: number[]): Promise<string> {
    const buffer = await getBuffer(url, backoff);
    return buffer.toString('utf-8');
  }

  export async function getJSON(url: string) {
    return JSON.parse(await getText(url));
  }

  export async function postText(url: string, text: string, backoff?: number[]): Promise<Buffer> {
    const headers = {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(text) + ''
    };
    return await retryWithBackoff(async () => {
      const { request, responseDataPromise } = createRequest({ url, headers, method: 'post' });
      request.write(text);
      request.end();
      return await responseDataPromise;
    }, backoff);
  }

  export async function postJSON(url: string, json: any, backoff?: number[]): Promise<any> {
    const buffer = await postText(url, JSON.stringify(json), backoff);
    return JSON.parse(buffer.toString('utf-8'));
  }
}


export function shell(command: string, args?: string[], options?: SpawnSyncOptionsWithStringEncoding) {
  try {
    const result = spawnSync(command, args, { encoding: 'utf-8', ...options });
    if (result.status !== 0) {
      return undefined;
    }
    return (result.stdout as string).trim();
  } catch (e) {
    console.error(e);
    return undefined;
  }
}

export function sha1Text(data: crypto.BinaryLike) {
  const hash = crypto.createHash('sha1');
  hash.update(data);
  return hash.digest('hex');
}

export function sha1File(filePath: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const hash = crypto.createHash('sha1');
    const stream = fs.createReadStream(filePath);
    stream.on('data', (chunk) => { hash.update(chunk); });

    stream.on('end', () => {
      resolve(hash.digest('hex'));
    });

    stream.on('error', (err) => {
      reject(err);
    });
  });
}

export function randomUUIDBase62(): string {
  const BASE62_CHARSET = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
  let num = BigInt('0x' + crypto.randomUUID().replace(/-/g, ''));
  if (num === 0n)
    return BASE62_CHARSET[0];

  const chars = [];
  while (num > 0n) {
    const remainder = Number(num % 62n);
    num /= 62n;
    chars.push(BASE62_CHARSET[remainder]);
  }
  
  return chars.reverse().join('');
}