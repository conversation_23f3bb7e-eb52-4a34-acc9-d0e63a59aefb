export namespace CIUtils {
  export function runUrl(): string | undefined {
    // - Github actions and Azure require special handling.
    // - GitLab utilizes CI_JOB_URL
    // - <PERSON><PERSON>, <PERSON>, and possibly others use BUILD_URL
    return githubActions() ?? azure() ?? process.env.CI_JOB_URL ?? process.env.BUILD_URL;
  }
}

function githubActions(): string | undefined {
  const serverUrl = process.env.GITHUB_SERVER_URL || 'https://github.com';
  const repo = process.env.GITHUB_REPOSITORY;
  const runId = process.env.GITHUB_RUN_ID;

  if (!repo || !runId) return undefined;

  try {
    const url = new URL(`${serverUrl}/${repo}/actions/runs/${runId}`);
    const attempt = process.env.GITHUB_RUN_ATTEMPT;
    if (attempt) url.searchParams.set('attempt', attempt);
    url.searchParams.set('check_suite_focus', 'true');
    return url.toString();
  } catch (error) {
    return undefined;
  }
}

function azure(): string | undefined {
  const collectionUri = process.env.SYSTEM_TEAMFOUNDATIONCOLLECTIONURI;
  const project = process.env.SYSTEM_TEAMPROJECT;
  const buildId = process.env.BUILD_BUILDID;

  if (!collectionUri || !project || !buildId)
    return undefined;

  try {
    const baseUrl = collectionUri.endsWith('/') ? collectionUri : `${collectionUri}/`;
    const url = new URL(`${baseUrl}${project}/_build/results`);
    
    url.searchParams.set('buildId', buildId);
    return url.toString();
  } catch (error) {
    return undefined;
  }
}
