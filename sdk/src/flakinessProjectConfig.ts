import fs from 'fs';
import path from 'path';
import { computeGitRoot } from './git.js';

function createConfigPath(dir: string) {
  return path.join(dir, '.flakiness', 'config.json');
}

let gConfigPath: string|undefined;

function ensureConfigPath(): string {
  if (!gConfigPath)
    gConfigPath = computeConfigPath();
  return gConfigPath;
}

function computeConfigPath() {
  // 1. Iterate the directory structure from process.cwd(), looking for the `.flakiness` folder.
  // Pick it if it exists.
  for (let p = process.cwd(); p !== path.resolve(p, '..'); p = path.resolve(p, '..')) {
    const configPath = createConfigPath(p);
    if (fs.existsSync(configPath))
      return configPath;
  }
  // 2. Since no existing config is found, try to find git root and place config there.
  try {
    const gitRoot = computeGitRoot(process.cwd());
    return createConfigPath(gitRoot);
  } catch (e) {
    // the computeGitRoot will fail if we're not inside git.
    // In this case, put config in the process.cwd.
    return createConfigPath(process.cwd());
  }
}

type JSONConfig = {
  projectPublicId?: string;
  customReportViewerEndpoint?: string,
}

export class FlakinessProjectConfig {
  static async load(): Promise<FlakinessProjectConfig> {
    const configPath = ensureConfigPath();
    const data = await fs.promises.readFile(configPath, 'utf-8').catch(e => undefined);
    const json: JSONConfig = data ? JSON.parse(data) as JSONConfig : {};
    return new FlakinessProjectConfig(configPath, json);
  }

  static createEmpty() {
    return new FlakinessProjectConfig(ensureConfigPath(), {});
  }

  constructor(
    private _configPath: string,
    private _config: JSONConfig) {
  }

  path() {
    return this._configPath;
  }

  projectPublicId() {
    return this._config.projectPublicId;
  }

  reportViewerEndpoint() {
    return this._config.customReportViewerEndpoint ?? 'https://report.flakiness.io';
  }

  setProjectPublicId(projectId: string|undefined) {
    this._config.projectPublicId = projectId;
  }

  async save() {
    await fs.promises.mkdir(path.dirname(this._configPath), { recursive: true });
    await fs.promises.writeFile(this._configPath, JSON.stringify(this._config, null, 2));
  }
}
