import { FlakinessReport } from "@flakiness/flakiness-report";

export function visitTests(report: FlakinessReport.Report, testVisitor: (test: FlakinessReport.Test, parentSuites: FlakinessReport.Suite[]) => void) {
  function visitSuite(suite: FlakinessReport.Suite, parents: FlakinessReport.Suite[]) {
    parents.push(suite);
    for (const test of suite.tests ?? [])
      testVisitor(test, parents);
    for (const childSuite of suite.suites ?? [])
      visitSuite(childSuite, parents);
    parents.pop();
  }
  for (const test of report.tests ?? [])
    testVisitor(test, []);
  for (const suite of report.suites)
    visitSuite(suite, []);
}
