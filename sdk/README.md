# Flakiness SDK

The package consists of:
- A CLI to interact with flakiness.io service
- A set of reporters for popular test runners to generate & upload Flakiness
  report.

## Getting Started

To start using reporter with Playwright Test:

1. Install this package:
    ```bash
    npm i @flakiness/sdk@latest
    ```
2. Add flakiness.io  to the `playwright.config.ts` file:
    ```ts
    import { defineConfig } from '@playwright/test';

    export default defineConfig({
      reporter: [
        ['list'],
        ['@flakiness/sdk/playwright-test', {
          endpoint: 'https://flakiness.io', // custom endpoint
          token: '...', // Flakiness access token
          collectBrowserVersion: true, // collect browser versions
        }]
      ],
    });
    ```
