name: Build cr.flakiness.io/app container

on:
  workflow_dispatch:
  push:
    tags:
      - 'v*'  # This will match tags like v1.0.0, v2.1.0, etc.

env:
  OP_SERVICE_ACCOUNT_TOKEN: ${{ secrets.OP_PROD_CI_USE_WITH_EXTREME_CAUTION }}

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    steps:
      - name: Install Docker
        uses: docker/setup-buildx-action@v2

      # We use regclient to push images to cr.flakiness.io
      - name: Install regctl
        run: |
          curl -LO https://github.com/regclient/regclient/releases/latest/download/regctl-linux-amd64
          chmod +x regctl-linux-amd64
          sudo mv regctl-linux-amd64 /usr/local/bin/regctl
          regctl version

      - name: Install 1Password CLI
        uses: 1password/install-cli-action@v1

      - name: Check 1Password CLI User
        run: op user get --me

      - name: Login to Registry
        run: |
          op read op://ci/docker/password | regctl registry login --user "$(op read op://ci/docker/username)" --pass-stdin cr.flakiness.io

      - uses: actions/checkout@v4

      - name: Build & Publish Docker Image
        run: ./docker/publish_docker.sh

