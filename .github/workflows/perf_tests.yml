name: Perf Tests

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

env:
  # Force terminal colors. @see https://www.npmjs.com/package/colors
  FORCE_COLOR: 1
  FLAKINESS_ACCESS_TOKEN: ${{ secrets.FLAKINESS_ACCESS_TOKEN }}
  OP_SERVICE_ACCOUNT_TOKEN: ${{ secrets.OP_PROD_CI_USE_WITH_EXTREME_CAUTION }}

jobs:
  perf_tests:
    runs-on: gha.flakiness.io

    steps:
      - name: Install 1Password CLI
        uses: 1password/install-cli-action@v1

      - name: Check 1Password CLI User
        run: op user get --me

      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 24

      # Perf tests use data from prod,
      # so they require prod access.
      # TODO: ideally, we give readonly access to the bucket/db.
      - run: ./config/config prod && cp .env.prod .env.prodlocal
      - run: npm ci
      - run: npx kubik ./experimental/build.mts -j 10
      - run: node --enable-source-maps ./experimental/lib/perf_nodejs_iterators.js
      - run: node --enable-source-maps ./experimental/lib/perf_xxhash.js
      - run: node --enable-source-maps ./experimental/lib/perf_ranges.js
      - run: node --enable-source-maps ./experimental/lib/perf_sequence.js
      - run: node --enable-source-maps ./experimental/lib/perf_histogram.js
      - run: npm run perf -- --project playwright
      - run: npm run perf -- --project kotlin


