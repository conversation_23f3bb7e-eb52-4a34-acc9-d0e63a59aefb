# Enable debug logging for all flakiness subsystems
DEBUG='fk:*'

# Build-time env
FLAKINESS_LICENSE_PUBLIC_KEY="op://test/license-generation/public key"

# Server license
FLAKINESS_LICENSE="op://test/server/FLAKINESS_LICENSE"

# Testing environment
# Github App
GITHUB_APP_ID="op://test/github_app/app_id"
GITHUB_APP_PRIVATE_KEY="op://test/github_app/private key"
GITHUB_APP_CLIENT_ID="op://test/github_app/client_id"
GITHUB_APP_CLIENT_SECRET="op://test/github_app/client_secret"
GITHUB_APP_CALLBACK_URL="op://test/github_app/callback_url"
GITHUB_APP_PUBLIC_URL="op://test/github_app/public_url"

# Github Test User
GITHUB_TEST_USER_LOGIN="op://test/github_user/login"
GITHUB_TEST_USER_NAME="op://test/github_user/name"
GITHUB_TEST_USER_EMAIL="op://test/github_user/email"
GITHUB_TEST_USER_PASSWORD="op://test/github_user/password"
GITHUB_TEST_USER_PAT="op://test/github_user/pat"
GITHUB_TEST_USER_GITHUB_ID="op://test/github_user/github_id"
GITHUB_TEST_USER_AVATAR_URL="op://test/github_user/avatar_url"

# Stripe App
STRIPE_API_KEY="op://test/stripe/STRIPE_API_KEY"

STRIPE_PRODUCT_ID_SEATS="op://test/stripe/STRIPE_PRODUCT_ID_SEATS"
STRIPE_PRODUCT_ID_STORAGE="op://test/stripe/STRIPE_PRODUCT_ID_STORAGE"
STRIPE_PRODUCT_ID_TEST_RUNS="op://test/stripe/STRIPE_PRODUCT_ID_TEST_RUNS"

STRIPE_WEBHOOK_SECRET="op://test/stripe/STRIPE_WEBHOOK_SECRET"
STRIPE_CALLBACK_URL="http://localhost:3000/"
STRIPE_METER_STORAGE="flakiness.io_storage_usage"
STRIPE_METER_TEST_RUNS="flakiness.io_test_runs"

# The "JSON_REPORTS_PER_COMMIT_URL" is a URL pointing to a file (.json, .json.gz, .json.br) that contains the following object:
# ```ts
# {
#   commitId: string,
#   url: string,
# }[]
# ```
# 
# The commits are in reverse-chronological order; for each commit, 
# there's a URL (.json, .json.gz, .json.br) that points to a file that contains all the Playwright JSON reports for a given commit:
# ```ts
# import { JSONReport } from '@playwright/test/reporter';
#
# (JSONReport & {
#   metadata: {
#     gitRoot: string,
#     uuid: string,
#     osName: string,
#     arch: string,
#     osVersion: string,
#     runURL?: string,
#     commitTimestamp: string,
#     commitSHA: string,
#   }
# })[]
# ```
TESTDATA_JSON_REPORTS_PER_COMMIT_URL="op://test/artifacts/JSON_REPORTS_PER_COMMIT_URL"

