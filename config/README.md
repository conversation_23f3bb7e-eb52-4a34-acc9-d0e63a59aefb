# Configuring environments

This folder contains a `config` script and a bunch of template environments `env.*.example` for
`flakiness.io`. The environment names are usually self-descriptive; if not,
head over to the comments inside the `*

The `config` script uses 1Password CLI to pull references secrets and provision top-level env file. It is usually enough to provision the environment once.

Example (running from the root folder):

```bash
# Configure developer environment with Stripe test-mode billing
./config/config dev+billing
npm run dev+billing
```

## Interesting Environments

- `dev` - a fully local environment, with local database and storage.
- `dev+billing` - a fully local environment with a test stripe integration. NOTE: stripe integration is in Stripe Sandbox, so no money are actually transfered. Use `4242 4242 4242 4242` to test payments.
- `prod` - production environment. Connects to prod infrastructure, runs on port 80.
- `prodlocal` - run server locally, but connect to prod infrastructure. This is launchable with `npm run prod` command. **NOTE:** the `npm run prod` does NOT launch the job processing services; it only SERVES data.
