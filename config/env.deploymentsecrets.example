# These are the secrets required for deployment automation

# Configuring telemetry credentials
TELEMETRY_USER="op://Prod/deploymentsecrets/TELEMETRY_USER"
TELEMETRY_PASSWORD="op://Prod/deploymentsecrets/TELEMETRY_PASSWORD"

# Cloudflare load balancing & cache automation
CF_ACCOUNT_ID="op://Prod/deploymentsecrets/CF_ACCOUNT_ID"
CF_ZONE_ID="op://Prod/deploymentsecrets/CF_ZONE_ID"
CF_API_TOKEN="op://Prod/deploymentsecrets/CF_API_TOKEN"

# Terraform state
TF_S3_ACCESS_KEY_ID="op://Prod/deploymentsecrets/TF_S3_ACCESS_KEY_ID"
TF_S3_SECRET_ACCESS_KEY="op://Prod/deploymentsecrets/TF_S3_SECRET_ACCESS_KEY"
TF_S3_ENDPOINT="op://Prod/deploymentsecrets/TF_S3_ENDPOINT"
TF_S3_REGION="op://Prod/deploymentsecrets/TF_S3_REGION"

# S3 bucket for CLI deployment
CLI_S3_ACCESS_KEY_ID="op://Prod/deploymentsecrets/CLI_S3_ACCESS_KEY_ID"
CLI_S3_SECRET_ACCESS_KEY="op://Prod/deploymentsecrets/CLI_S3_SECRET_ACCESS_KEY"
CLI_S3_ENDPOINT="op://Prod/deploymentsecrets/CLI_S3_ENDPOINT"
