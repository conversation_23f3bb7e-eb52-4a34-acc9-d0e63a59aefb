#!/usr/bin/env bash
set -e
set +x

trap "cd $(pwd -P)" EXIT
cd "$(dirname "$0")"

if [[ ($1 == '--help') || ($1 == '-h') || ($1 == '') ]]; then
  echo "usage: $(basename $0) prod|test|dev|prodlocal"
  echo
  echo "Populate environments for server using 1Password storage"
  echo
  echo "Supported environments:"
  echo "  dev               env to run dev server on localhost:3000 without billing"
  echo "  dev+billing       env to run dev server on localhost:3000 with billing"
  echo "  test              env to run tests"
  echo "  prod              env to run prod"
  echo "  prodlocal         env to run server on localhost:3000 against prod S3 & DB"
  echo "  deploymentsecrets env to run automation scripts that drives deployment"
  exit 0
fi

FILE=""
if [[ $1 == "prod" ]]; then
  FILE="env.prod"
elif [[ $1 == "dev" ]]; then
  FILE="env.dev"
elif [[ $1 == "dev+billing" ]]; then
  FILE="env.dev+billing"
elif [[ $1 == "test" ]]; then
  FILE="env.test"
elif [[ $1 == "prodlocal" ]]; then
  FILE="env.prodlocal"
elif [[ $1 == "deploymentsecrets" ]]; then
  FILE="env.deploymentsecrets"
else
  echo "Unknown environment; must be one of 'prod', 'test', 'dev', 'dev+billing', 'prodlocal' or 'deploymentsecrets'"
  exit 1
fi

cat "${FILE}.example" | op inject > "../.$FILE"

