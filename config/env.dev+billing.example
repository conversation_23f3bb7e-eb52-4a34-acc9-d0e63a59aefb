# Enable debug logging for all flakiness subsystems
DEBUG='fk:*'

# Build-time env
FLAKINESS_LICENSE_PUBLIC_KEY="op://test/license-generation/public key"

# Server
FLAKINESS_WEB_PATH="./web/dist"
FLAKINESS_JWT_SECRET='some jwt secret'
PORT=3000
SUPERUSERS=746130 # as<PERSON><PERSON><PERSON><PERSON>'s github id.
DISABLE_ORG_CREATION=1
FLAKINESS_LICENSE="op://test/server/FLAKINESS_LICENSE"

# MinIO configuration
S3_ENDPOINT="http://localhost:9000"
S3_ACCESS_KEY_ID="minio"
S3_SECRET_ACCESS_KEY="miniopass"
S3_REGION="wnam"
S3_BUCKET_NAME=flakiness-data-2

# PostgreSQL configuration
PGHOST="localhost"
PGPORT=5430
PGUSER="user"
PGPASSWORD="password"
PGDATABASE="postgres"
DB_ENCRYPTION_KEY="fkdb_eb9f0082ee140e784270fa4c89d78cd7b198d2953ef5ad17a8a6d2081c94f568"

# Github App
GITHUB_APP_ID="op://test/github_app/app_id"
GITHUB_APP_PRIVATE_KEY="op://test/github_app/private key"
GITHUB_APP_CLIENT_ID="op://test/github_app/client_id"
GITHUB_APP_CLIENT_SECRET="op://test/github_app/client_secret"
GITHUB_APP_CALLBACK_URL="op://test/github_app/callback_url"
GITHUB_APP_PUBLIC_URL="op://test/github_app/public_url"

# Stripe App
STRIPE_API_KEY="op://test/stripe/STRIPE_API_KEY"

STRIPE_PRODUCT_ID_SEATS="op://test/stripe/STRIPE_PRODUCT_ID_SEATS"
STRIPE_PRODUCT_ID_STORAGE="op://test/stripe/STRIPE_PRODUCT_ID_STORAGE"
STRIPE_PRODUCT_ID_TEST_RUNS="op://test/stripe/STRIPE_PRODUCT_ID_TEST_RUNS"

STRIPE_WEBHOOK_SECRET="op://test/stripe/STRIPE_WEBHOOK_SECRET"
STRIPE_CALLBACK_URL="http://localhost:3000/"
STRIPE_METER_STORAGE="op://Test/stripe/STRIPE_METER_STORAGE"
STRIPE_METER_TEST_RUNS="op://Test/stripe/STRIPE_METER_TEST_RUNS"
