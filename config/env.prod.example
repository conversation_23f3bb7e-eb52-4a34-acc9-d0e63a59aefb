# This is a production environment.

# Enable debug logging for all flakiness subsystems
DEBUG='fk:*'

# Build-time env
NODE_ENV="production"
FLAKINESS_LICENSE_PUBLIC_KEY="op://prod/license-generation/public key"

# Server
PORT="3000"
SUPERUSERS="op://prod/server/SUPERUSERS"
DISABLE_ORG_CREATION="op://prod/server/DISABLE_ORG_CREATION"
FLAKINESS_JWT_SECRET="op://prod/server/FLAKINESS_JWT_SECRET"
FLAKINESS_LICENSE="op://prod/server/FLAKINESS_LICENSE"

# S3
S3_ENDPOINT="op://prod/s3/S3_ENDPOINT"
S3_ACCESS_KEY_ID="op://prod/s3/S3_ACCESS_KEY_ID"
S3_SECRET_ACCESS_KEY="op://prod/s3/S3_SECRET_ACCESS_KEY"
S3_REGION="op://prod/s3/S3_REGION"
S3_BUCKET_NAME="op://prod/s3/S3_BUCKET_NAME"

# Enable telemetry
# TELEMETRY_PORT is configured in systemd units for builders.
TELEMETRY=1

# Database
PGHOST="op://prod/db/HOST"
PGPORT="op://prod/db/PORT"
PGUSER="op://prod/db/USER"
PGPASSWORD="op://prod/db/PASSWORD"
PGDATABASE="op://prod/db/DATABASE"
DB_ENCRYPTION_KEY="op://prod/db/ENCRYPTION_KEY"

# Backoffice database
BACKOFFICE_PGHOST="op://backoffice/db/HOST"
BACKOFFICE_PGPORT="op://backoffice/db/PORT"
BACKOFFICE_PGUSER="op://backoffice/db/USER"
BACKOFFICE_PGPASSWORD="op://backoffice/db/PASSWORD"
BACKOFFICE_PGDATABASE="op://backoffice/db/DATABASE"

# Github App
GITHUB_APP_ID="op://prod/github_app/app_id"
GITHUB_APP_PRIVATE_KEY="op://prod/github_app/private key"
GITHUB_APP_CLIENT_ID="op://prod/github_app/client_id"
GITHUB_APP_CLIENT_SECRET="op://prod/github_app/client_secret"
GITHUB_APP_CALLBACK_URL="op://prod/github_app/callback_url"
GITHUB_APP_PUBLIC_URL="op://prod/github_app/public_url"

# Stripe App
STRIPE_CALLBACK_URL="op://prod/stripe/STRIPE_CALLBACK_URL"
STRIPE_API_KEY="op://prod/stripe/STRIPE_API_KEY"
STRIPE_PRODUCT_ID_SEATS="op://prod/stripe/STRIPE_PRODUCT_ID_SEATS"
STRIPE_PRODUCT_ID_STORAGE="op://prod/stripe/STRIPE_PRODUCT_ID_STORAGE"
STRIPE_PRODUCT_ID_TEST_RUNS="op://prod/stripe/STRIPE_PRODUCT_ID_TEST_RUNS"
STRIPE_WEBHOOK_SECRET="op://prod/stripe/STRIPE_WEBHOOK_SECRET"
STRIPE_METER_STORAGE="op://prod/stripe/STRIPE_METER_STORAGE"
STRIPE_METER_TEST_RUNS="op://prod/stripe/STRIPE_METER_TEST_RUNS"
