DEBUG='fk:*' # Enable debug logging for all flakiness subsystems
DEBUG_COLORS=1 # Enable colors

# Build-time env
FLAKINESS_LICENSE_PUBLIC_KEY="op://test/license-generation/public key"

# Server
FLAKINESS_WEB_PATH="./web/dist"
FLAKINESS_JWT_SECRET='some jwt secret'
PORT=3000
SUPERUSERS=746130 # as<PERSON><PERSON><PERSON><PERSON>'s github id.
DISABLE_ORG_CREATION=1
FLAKINESS_LICENSE="op://test/server/FLAKINESS_LICENSE"

# MinIO configuration
S3_ENDPOINT="http://localhost:9000"
S3_ACCESS_KEY_ID="minio"
S3_SECRET_ACCESS_KEY="miniopass"
S3_REGION="wnam"
S3_BUCKET_NAME=flakiness-data-2

# Telemetry configuration
TELEMETRY=1

# PostgreSQL configuration
PGHOST="localhost"
PGPORT=5430
PGUSER="user"
PGPASSWORD="password"
PGDATABASE="postgres"
DB_ENCRYPTION_KEY="fkdb_eb9f0082ee140e784270fa4c89d78cd7b198d2953ef5ad17a8a6d2081c94f568"

# Backoffice database - use the same as the main one.
BACKOFFICE_PGHOST="localhost"
BACKOFFICE_PGPORT="5430"
BACKOFFICE_PGUSER="user"
BACKOFFICE_PGPASSWORD="password"
BACKOFFICE_PGDATABASE="postgres"

# Github App
GITHUB_APP_ID="op://test/github_app/app_id"
GITHUB_APP_PRIVATE_KEY="op://test/github_app/private key"
GITHUB_APP_CLIENT_ID="op://test/github_app/client_id"
GITHUB_APP_CLIENT_SECRET="op://test/github_app/client_secret"
GITHUB_APP_CALLBACK_URL="op://test/github_app/callback_url"
GITHUB_APP_PUBLIC_URL="op://test/github_app/public_url"

# The "JSON_REPORTS_PER_COMMIT_URL" is a URL pointing to a file (.json, .json.gz, .json.br) that contains the following object:
# ```ts
# {
#   commitId: string,
#   url: string,
# }[]
# ```
# 
# The commits are in reverse-chronological order; for each commit, 
# there's a URL (.json, .json.gz, .json.br) that points to a file that contains all the Playwright JSON reports for a given commit:
# ```ts
# import { JSONReport } from '@playwright/test/reporter';
#
# (JSONReport & {
#   metadata: {
#     gitRoot: string,
#     uuid: string,
#     osName: string,
#     arch: string,
#     osVersion: string,
#     runURL?: string,
#     commitTimestamp: string,
#     commitSHA: string,
#   }
# })[]
# ```
TESTDATA_JSON_REPORTS_PER_COMMIT_URL="op://test/artifacts/JSON_REPORTS_PER_COMMIT_URL"

