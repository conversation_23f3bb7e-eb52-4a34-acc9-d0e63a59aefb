# Enable debug logging for all flakiness subsystems
DEBUG='fk:*'
DEBUG_COLORS=1 # Enable colors

# Build-time env
FLAKINESS_LICENSE_PUBLIC_KEY="op://prod/license-generation/public key"

HDD_CACHE_ROOTDIR="/tmp/flakiness-s3-cache"
HDD_CACHE_STATS_MB=6000

# This env works against prod DB and S3 storage,
# while running the compute locally on localhost:3000 port.
SUPERUSERS=746130 # as<PERSON><PERSON><PERSON><PERSON>'s github id.
DISABLE_ORG_CREATION=1
FLAKINESS_WEB_PATH="./web/dist"
FLAKINESS_JWT_SECRET="op://prod/server/FLAKINESS_JWT_SECRET"
PORT=3000
FLAKINESS_LICENSE="op://prod/server/FLAKINESS_LICENSE"

S3_ENDPOINT="op://prod/s3/S3_ENDPOINT"
S3_ACCESS_KEY_ID="op://prod/s3/S3_ACCESS_KEY_ID"
S3_SECRET_ACCESS_KEY="op://prod/s3/S3_SECRET_ACCESS_KEY"
S3_REGION="op://prod/s3/S3_REGION"
S3_BUCKET_NAME="op://prod/s3/S3_BUCKET_NAME"

PGHOST="op://prod/db/HOST"
PGPORT="op://prod/db/PORT"
PGUSER="op://prod/db/USER"
PGPASSWORD="op://prod/db/PASSWORD"
PGDATABASE="op://prod/db/DATABASE"
DB_ENCRYPTION_KEY="op://prod/db/ENCRYPTION_KEY"

# Backoffice database
BACKOFFICE_PGHOST="op://backoffice/db/HOST"
BACKOFFICE_PGPORT="op://backoffice/db/PORT"
BACKOFFICE_PGUSER="op://backoffice/db/USER"
BACKOFFICE_PGPASSWORD="op://backoffice/db/PASSWORD"
BACKOFFICE_PGDATABASE="op://backoffice/db/DATABASE"

# Github App
GITHUB_APP_ID="op://test/github_app/app_id"
GITHUB_APP_PRIVATE_KEY="op://test/github_app/private key"
GITHUB_APP_CLIENT_ID="op://test/github_app/client_id"
GITHUB_APP_CLIENT_SECRET="op://test/github_app/client_secret"
GITHUB_APP_CALLBACK_URL="op://test/github_app/callback_url"
GITHUB_APP_PUBLIC_URL="op://test/github_app/public_url"

# Stripe App
STRIPE_CALLBACK_URL="op://prod/stripe/STRIPE_CALLBACK_URL"
STRIPE_API_KEY="op://prod/stripe/STRIPE_API_KEY"
STRIPE_PRODUCT_ID_SEATS="op://prod/stripe/STRIPE_PRODUCT_ID_SEATS"
STRIPE_PRODUCT_ID_STORAGE="op://prod/stripe/STRIPE_PRODUCT_ID_STORAGE"
STRIPE_PRODUCT_ID_TEST_RUNS="op://prod/stripe/STRIPE_PRODUCT_ID_TEST_RUNS"
STRIPE_WEBHOOK_SECRET="op://prod/stripe/STRIPE_WEBHOOK_SECRET"
STRIPE_METER_STORAGE="op://prod/stripe/STRIPE_METER_STORAGE"
STRIPE_METER_TEST_RUNS="op://prod/stripe/STRIPE_METER_TEST_RUNS"
