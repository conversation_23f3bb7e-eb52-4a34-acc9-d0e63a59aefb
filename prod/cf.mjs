/**
 * This script enables/disables servers in Cloudflare's load balancer.
 */
import assert from 'assert';
import dns from 'dns';

const ACCOUNT_ID = process.env.CF_ACCOUNT_ID;
const ZONE_ID = process.env.CF_ZONE_ID;
const TOKEN = process.env.CF_API_TOKEN;

assert(ACCOUNT_ID, 'CF_ACCOUNT_ID env variable is missing');
assert(ZONE_ID, 'CF_ZONE_ID env variable is missing');
assert(TOKEN, 'CF_API_TOKEN env variable is missing');

const SERVER = process.argv[2];
assert(SERVER, 'first argument must be a server hostname');
assert(await dns.promises.resolve(SERVER), 'Server hostname must resolve');

const STATUS_TEXT = (process.argv[3] ?? '').toLowerCase();
let STATUS = false;
if (['on', '1', 'true', 'enable'].includes(STATUS_TEXT))
  STATUS = true;
else if (['off', '0', 'false', 'disable'].includes(STATUS_TEXT))
  STATUS = false;
else
  assert(false, 'second argument must be on/off/0/1/true/false/enable/disable');

const loadBalancers = await cfapi('GET', `https://api.cloudflare.com/client/v4/zones/${ZONE_ID}/load_balancers`);

const pools = await Promise.all(loadBalancers.map(lb => {
  return collectPoolIds(lb).map(poolId => cfapi('GET', `https://api.cloudflare.com/client/v4/accounts/${ACCOUNT_ID}/load_balancers/pools/${poolId}`));
}).flat());

const pool = pools.find(pool => pool.origins.some(origin => origin.address === SERVER));
if (!pool) {
  console.log('Failed to find pool with a given server');
  process.exit(1);
}

const origins = pool.origins.map(origin => ({
  ...origin,
  enabled: origin.address === SERVER ? STATUS : origin.enabled,
}));
await cfapi('PATCH', `https://api.cloudflare.com/client/v4/accounts/${ACCOUNT_ID}/load_balancers/pools/${pool.id}`, { origins });

// ----------------------------------

function collectPoolIds(lb) {
  return [...new Set([
    lb.default_pools ?? [],
    lb.fallback_pool ? [lb.fallback_pool] : [],
    lb.region_pools ? Object.values(lb.region_pools).flat() : [],
    lb.country_pools ? Object.values(lb.country_pools).flat() : [],
    lb.pop_pools ? Object.values(lb.pop_pools).flat() : [],
  ].flat())];
}

async function cfapi(method, url, body) {
  const response = await fetch(url, {
    method,
    headers: {
      'Authorization': `Bearer ${TOKEN}`,
      'Content-Type': 'application/json',
    },
    body: body ? JSON.stringify(body) : undefined,
  });

  if (!response.ok) {
    const errorBody = await response.json();
    throw new Error(`API request to ${url} failed with status ${response.status}: ${JSON.stringify(errorBody)}`);
  }

  const data = await response.json();
  if (!data.success) {
      throw new Error(`Cloudflare API reported an error: ${JSON.stringify(data.errors)}`);
  }
  return data.result;
}

