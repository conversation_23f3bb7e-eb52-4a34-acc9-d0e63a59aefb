#!/usr/bin/env bash
set -euo pipefail

cleanup() {
  rm -f ./prodsshkey
}

trap "cd $(pwd -P); cleanup" EXIT
cd "$(dirname "$0")"

if ! command -v terraform > /dev/null; then
  echo "Please install terraform"
  exit 1
fi

if [[ ! -f ../../.env.deploymentsecrets ]]; then
  echo "Deployment secrets not found!"
  echo "Run 'config/config deploymentsecrets'"
  exit 1
fi
source ../../.env.deploymentsecrets
CLOUDFLARE_API_TOKEN=$CF_API_TOKEN \
AWS_ENDPOINT_URL_S3=$TF_S3_ENDPOINT \
AWS_DEFAULT_REGION=$TF_S3_REGION \
AWS_ACCESS_KEY_ID=$TF_S3_ACCESS_KEY_ID \
AWS_SECRET_ACCESS_KEY=$TF_S3_SECRET_ACCESS_KEY \
terraform "$@"
