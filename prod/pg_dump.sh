#!/usr/bin/env bash
set -e
set +x

trap "cd $(pwd -P)" EXIT
cd "$(dirname "$0")"

if ! command -v pgcli > /dev/null; then
  echo "ERROR: requires installed PGCLI"
  exit 1
fi

if [[ -f ../.env.prod ]]; then
  source ../.env.prod
elif [[ -f ../.env.prodlocal ]]; then
  source ../.env.prodlocal
else
  echo "No prod or prodlocal env found."
  echo "Please run //config/config prod"
  exit 1
fi

PGHOST="${PGHOST}" \
PGUSER="${PGUSER}" \
PGPORT="${PGPORT}" \
PGDATABASE="${PGDATABASE}" \
PGPASSWORD="${PGPASSWORD}" \
pg_dump -Fc --schema=public -f "./prod.dump"
