# Prod

As of Oct, 2024, the production deployment uses the following:
- Cloudflare R2 as S3-storage
- Supabase as Postgres
- Hetzner API server in US
- Hetzner API server in Germany
- Hetzner Builder server in US
- Backoffice Hetzner server in Germany (cheaper)

Flakiness.io service is distributed as a docker container.

Our deployment runs multiple processes of Flakiness.io service, each running in
its own mode and configured using the systemd service files. The
processes are:
- an API "serving" processes which only "serves" requests, and doesn't do any compute.
- a "builder" processes which do not serve anything and are only concerned with
  processing job queues.

As of Apr, 2025, the deployment is fully automated via github actions.
It should never be run manually.

## Scripts

- [`pgcli.sh`](./pgcli.sh): use PGCLI to connect to  the production postgres.
  However:
    * make sure to configure `prod` or `prodlocal` environments for it to work.
    * do not run **ANY** destroying operations against prod database.

