#!/usr/bin/env bash
set -e
set +x

trap "cd $(pwd -P)" EXIT
cd "$(dirname "$0")"

if ! command -v pgcli > /dev/null; then
  echo "ERROR: requires installed PGCLI"
  exit 1
fi

if [[ -f ../.env.prodlocal ]]; then
  source ../.env.prodlocal
else
  echo "No prod or prodlocal env found."
  echo "Please run //config/config prod"
  exit 1
fi

PGHOST="${BACKOFFICE_PGHOST}" \
PGUSER="${BACKOFFICE_PGUSER}" \
PGPORT="${BACKOFFICE_PGPORT}" \
PGDATABASE="${BACKOFFICE_PGDATABASE}" \
PGPASSWORD="${BACKOFFICE_PGPASSWORD}" \
pgcli
