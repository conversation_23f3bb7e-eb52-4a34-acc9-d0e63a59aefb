#!/usr/bin/env bash
set -euo pipefail

cleanup() {
  rm -rf ./prodsshkey
}

trap "cd $(pwd -P); cleanup" EXIT
cd "$(dirname "$0")"

if ! command -v op > /dev/null; then
  echo "ERROR: please install & authenticate 1Password CLI to fetch secrets"
  exit 1
fi

if [[ $# -ne 1 ]]; then
  echo "Usage: $0 <APP_VERSION>"
  exit 1
fi

source ./assets/utilities.sh

APP_VERSION="${1}"

# Check if APP_VERSION matches semver pattern (x.y.z with optional pre-release and build metadata)
# This regex handles: 1.2.3, 1.2.3-alpha, 1.2.3-alpha.1, 1.2.3+build, 1.2.3-alpha+build, etc.
semver_regex='^v[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9]+(\.[a-zA-Z0-9]+)*)?(\+[a-zA-Z0-9]+(\.[a-zA-Z0-9]+)*)?$'
if [[ "$APP_VERSION" == "latest" || "$APP_VERSION" =~ $semver_regex ]]; then
  log "Deploying APP_VERSION=${APP_VERSION}"
else
  log "Malformed APP_VERSION: ${APP_VERSION}"
  exit 15
fi

# Read configurations to run deployments.
if [[ ! -e "./prodsshkey" ]]; then
  log "Fetching SSH key..."
  op read "op://prod/ssh/private key"?ssh-format=openssh > ./prodsshkey
  chmod 400 ./prodsshkey
fi

if [[ ! -e "../.env.prod" ]]; then
  log "Fetching production secrets..."
  ../config/config prod
fi

if [[ ! -e "../.env.deploymentsecrets" ]]; then
  log "Fetching deployment secrets..."
  ../config/config deploymentsecrets
  # Put .env.telemetry into assets: these will be synced to remote and cleaned
  # afterwards.
  cat ../.env.deploymentsecrets | grep 'TELEMETRY_' > ./assets/.env.telemetry
fi

migrate_db_to_latest() {
  log "Migrating database to latest"

  FLAKINESS_LICENSE=$(
    source ../.env.prod
    echo "$FLAKINESS_LICENSE"
  )

  pull_container "${FLAKINESS_LICENSE}" "${APP_VERSION}"
  # Migrate database to latest
  docker run \
    --rm \
    --name flakiness \
    --user www-data \
    --network=host \
    -v "$(pwd)/../.env.prod:/etc/flakiness/env" \
    --env NODE_ENV=production \
    cr.flakiness.io/app:"${APP_VERSION}" \
    node --env-file=/etc/flakiness/env --enable-source-maps \
    ./server/lib/cli.js migrate-postgres-to-latest
}

remote_copy_prod_env() {
  local REMOTE="$1"
  log "Copying prod env to ${REMOTE}"

  ssh -i ./prodsshkey "${REMOTE}" 'rm -rf /etc/flakiness; mkdir -p /etc/flakiness/'
  scp -q -i ./prodsshkey ../.env.prod "${REMOTE}":/etc/flakiness/env
}

remote_rsync_assets() {
  local REMOTE="$1"
  log "Syncing assets to ${REMOTE}"
  rsync --quiet -av --delete -e "ssh -i ./prodsshkey" ./assets/ "${REMOTE}":/tmp/assets
}

remote_cleanup_assets() {
  local REMOTE="$1"
  log "Removing assets from ${REMOTE}"
  ssh -i ./prodsshkey "${REMOTE}" 'rm -rf /tmp/assets'
}

disable_host_in_load_balancer() {
  log "Disabling $1 in Cloudflare load balancer"
  node --env-file=../.env.deploymentsecrets cf.mjs "api.$1" disable
}

enable_host_in_load_balancer() {
  log "Enabling $1 in Cloudflare load balancer"
  node --env-file=../.env.deploymentsecrets cf.mjs "api.$1" enable
}


deploy() {
  local TYPE="$1"
  local HOST_NAME="$2"
  local USER="$3"

  log "Starting \""${TYPE}"\" deployment on ${HOST_NAME}"

  # Add key.
  if [[ -f "$HOME/.ssh/known_hosts" ]]; then
    ssh-keygen -R "${HOST_NAME}" > /dev/null
  else
    mkdir -p ~/.ssh
  fi
  ssh-keyscan -H "${HOST_NAME}" >> ~/.ssh/known_hosts

  remote_rsync_assets "${USER}@${HOST_NAME}"
  if [[ $TYPE == "api" ]]; then
    # API deployment requires prod env + disabling/enabling machines from cloudflare.
    remote_copy_prod_env "${USER}@${HOST_NAME}"
    disable_host_in_load_balancer "${HOST_NAME}"
    ssh -i ./prodsshkey "${USER}@${HOST_NAME}" "/tmp/assets/configure.sh ${TYPE} ${HOST_NAME} ${APP_VERSION}"
    enable_host_in_load_balancer "${HOST_NAME}"
  elif [[ $TYPE == "builder" || $TYPE == "reuploader" ]]; then
    # These environments require prod environment as well
    remote_copy_prod_env "${USER}@${HOST_NAME}"
    ssh -i ./prodsshkey "${USER}@${HOST_NAME}" "/tmp/assets/configure.sh ${TYPE} ${HOST_NAME} ${APP_VERSION}"
  else
    # Everything other doesn't require production environment. It does get access to telemetry
    # in assets.
    ssh -i ./prodsshkey "${USER}@${HOST_NAME}" "/tmp/assets/configure.sh ${TYPE} ${HOST_NAME} ${APP_VERSION}"
  fi
  remote_cleanup_assets "${USER}@${HOST_NAME}"
  log "Finished \""${TYPE}"\" deployment on ${HOST_NAME}"
}

purge_cf_caches() {
  log "Purging Cloudflare caches"
  source ../.env.deploymentsecrets
  curl -X POST "https://api.cloudflare.com/client/v4/zones/${CF_ZONE_ID}/purge_cache" \
    -H "Authorization: Bearer ${CF_API_TOKEN}" \
    -H "Content-Type: application/json" \
    --data "{\"purge_everything\":true}"
}

migrate_db_to_latest

deploy api usa1.flakiness.io root
deploy api ger1.flakiness.io root
purge_cf_caches

deploy builder usa2.flakiness.io root
deploy builder usa3.flakiness.io root

deploy backoffice backoffice.flakiness.io root
deploy reuploader backoffice.flakiness.io root
