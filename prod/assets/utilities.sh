#!/bin/bash

SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )
ENV_TELEMETRY="${SCRIPT_DIR}/.env.telemetry"
NODE_MAJOR=24

log() {
  local grey='\033[90m'
  local white='\033[97m'
  local reset='\033[0m'
  local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
  local hostname=$(hostname -f)

  printf "${grey}%s${reset} [${white}%s${reset}] %s\n" "$timestamp" "$hostname" "$1"
}

assert_flakiness_env() {
  if [[ ! -f /etc/flakiness/env ]]; then
    echo "ERROR: env file must be present at /etc/flakiness/env"
    exit 1
  fi

  # Change ownership of /etc/flakiness/env to harden security.
  chown www-data:www-data /etc/flakiness/env
  chmod 0400 /etc/flakiness/env
}

assert_telemetry_env() {
  if [[ ! -f "${ENV_TELEMETRY}" ]]; then
    echo "ERROR: env file must be present at ${ENV_TELEMETRY}"
    exit 1
  fi
}

ensure_curl() {
  if ! command -v curl > /dev/null; then
    log "Installing curl"
    apt-get update && apt-get install -y curl
  fi
}

ensure_caddy() {
  # Install caddy if needed.
  if ! command -v caddy > /dev/null; then
    log "Installing caddy"
    curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/gpg.key' | gpg --dearmor --batch --yes -o /usr/share/keyrings/caddy-stable-archive-keyring.gpg
    curl -1sLf 'https://dl.cloudsmith.io/public/caddy/stable/debian.deb.txt' | tee /etc/apt/sources.list.d/caddy-stable.list
    apt-get update
    apt-get install -y caddy
  fi
}

ensure_prometheus_and_grafana() {
  # Add Prometheus repository if not already added
  if [[ ! -f /etc/apt/sources.list.d/grafana.list ]]; then
    log "Adding prometheus APT repository"
    curl -fsSL https://packages.grafana.com/gpg.key | gpg --dearmor -o /usr/share/keyrings/grafana.gpg
    echo "deb [signed-by=/usr/share/keyrings/grafana.gpg] https://packages.grafana.com/oss/deb stable main" | tee /etc/apt/sources.list.d/grafana.list
    apt-get update
  fi

  # Install Prometheus
  if ! command -v prometheus > /dev/null; then
    log "Installing prometheus"
    apt-get install -y prometheus
  fi

  # Install Grafana
  if ! systemctl is-active grafana-server >/dev/null 2>&1; then
    log "Installing grafana"
    apt-get install -y grafana
  fi

  systemctl enable --now prometheus
  systemctl enable --now grafana-server
}

# Ensure Docker is installed as well.
ensure_docker() {
  if ! command -v docker > /dev/null; then
    # Technically this is not the safest, but oh well.
    log "Installing docker"
    curl -fsSL https://get.docker.com | sh
  fi
}

ensure_prom_node_exporter() {
  if systemctl is-enabled prometheus-node-exporter; then
    return;
  fi
  log "Installing prometheus-node-exporter"
  apt-get update
  apt-get install -y prometheus-node-exporter
  systemctl enable --now prometheus-node-exporter
}

pull_container() {
  local FLAKINESS_LICENSE="$1"
  local APP_VERSION="$2"
  log "Pulling image cr.flakiness.io:${APP_VERSION}"
  # Login to docker and pull container
  echo "${FLAKINESS_LICENSE}" | docker login -u degulabs --password-stdin cr.flakiness.io
  docker pull cr.flakiness.io/app:"${APP_VERSION}"
  docker tag cr.flakiness.io/app:"${APP_VERSION}" cr.flakiness.io/app:last-deploy
}

# Extract static files folder from the container. We will serve them
# directly by caddy file server.
extract_frontend_from_container() {
  local FLAKINESS_WEB_PATH=/var/www/flakiness/web
  local FLAKINESS_LANDING_PATH=/var/www/flakiness/landing

  docker rm -f temp-container # cleanup
  docker create --name temp-container cr.flakiness.io/app:last-deploy
  
  log "Extracting frontend from image to ${FLAKINESS_WEB_PATH}"
  rm -rf "${FLAKINESS_WEB_PATH}/dist"
  mkdir -p "${FLAKINESS_WEB_PATH}"
  docker cp temp-container:"${FLAKINESS_WEB_PATH}/dist" "${FLAKINESS_WEB_PATH}"

  log "Extracting landing from image to ${FLAKINESS_LANDING_PATH}"
  rm -rf "${FLAKINESS_LANDING_PATH}/dist"
  mkdir -p "${FLAKINESS_LANDING_PATH}"
  docker cp temp-container:"${FLAKINESS_LANDING_PATH}/dist" "${FLAKINESS_LANDING_PATH}"

  docker rm temp-container
}

wait_for_service() {
  local url="${1:-localhost:3000}"
  local timeout="${2:-30}"
  local counter=0

  while [ $counter -lt $timeout ]; do
    if curl -s --fail "$url" >/dev/null 2>&1; then
      log "✓ Service at $url is up!"
      return 0
    fi
    counter=$((counter + 1))
    log "Waiting for service $url to come up..."
    sleep 1
  done

  log "✗ Timeout: Service at $url did not respond within $timeout seconds"
  return 1
}

total_ram_mb() {
  local TOTAL_RAM_KB=$(grep MemTotal /proc/meminfo | awk '{print $2}')
  echo $(( TOTAL_RAM_KB / 1024 ))
}

systemd_config() {
  local name="${1}"
  log "configuring $(basename $name)"
  MAX_NODE_HEAP_SIZE_MB="$2" envsubst '$MAX_NODE_HEAP_SIZE_MB' < $name > "/etc/systemd/system/$(basename $name)"
  systemctl daemon-reload
}

caddy_config() {
  local name="$1"
  local HOST_NAME="$2"
  log "Configuring Caddy"

  source "${ENV_TELEMETRY}"

  HOST_NAME="${HOST_NAME}" \
  TELEMETRY_USER="${TELEMETRY_USER}" \
  TELEMETRY_PWD_BCRYPT=$(caddy hash-password --plaintext "${TELEMETRY_PASSWORD}") \
  envsubst '$HOST_NAME,$TELEMETRY_USER,$TELEMETRY_PWD_BCRYPT' < $name > /etc/caddy/Caddyfile
  caddy validate --config /etc/caddy/Caddyfile

  if systemctl is-active --quiet caddy; then
    systemctl reload caddy
  else
    systemctl start caddy
  fi
}

prom_config() {
  local name="$1"
  log "Configuring Prometheus"

  source "${ENV_TELEMETRY}"

  TELEMETRY_USER="${TELEMETRY_USER}" \
  TELEMETRY_PASSWORD="${TELEMETRY_PASSWORD}" \
  envsubst '$TELEMETRY_USER,$TELEMETRY_PASSWORD' < $name > /etc/prometheus/prometheus.yml

  systemctl reload prometheus
}

ensure_nodejs() {
  if command -v node > /dev/null; then
    local DETECTED_NODE=$(node -v | sed 's/^v//' | cut -d. -f1)
    if (( "${DETECTED_NODE}" < "${NODE_MAJOR}" )); then
      apt-get purge -y nodejs npm
      apt-get autoremove -y
    fi
  fi
  if ! command -v node > /dev/null; then
    log "Installing nodejs"
    curl -fsSL "https://deb.nodesource.com/setup_${NODE_MAJOR}.x" -o nodesource_setup.sh
    bash nodesource_setup.sh
    apt-get install -y nodejs
    rm nodesource_setup.sh
  fi
}

ensure_git() {
  if ! command -v git > /dev/null; then
    log "Installing git"
    apt-get update
    apt-get install -y git
  fi
}
