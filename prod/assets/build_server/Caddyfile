{
  metrics {
    per_host
  }
}

b.${HOST_NAME} {
  basic_auth /metrics {
    ${TELEMETRY_USER} "${TELEMETRY_PWD_BCRYPT}"
  }
  reverse_proxy localhost:3000
}

m.${HOST_NAME} {
  handle_path /metrics/http {
    basic_auth {
      ${TELEMETRY_USER} "${TELEMETRY_PWD_BCRYPT}"
    }
    metrics
  }

  handle_path /metrics/machine {
    basic_auth {
      ${TELEMETRY_USER} "${TELEMETRY_PWD_BCRYPT}"
    }
    rewrite * /metrics
    reverse_proxy localhost:9100
  }
}
