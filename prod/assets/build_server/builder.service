[Unit]
Description=Flakiness Builder
After=network.target

[Service]
User=root
Group=root
ExecStart=/usr/bin/docker run \
    --rm \
    --name builder \
    --user www-data \
    --network=host \
    -v /etc/flakiness/env:/etc/flakiness/env \
    --env NODE_ENV=production \
    --env TELEMETRY_PORT=3000 \
    cr.flakiness.io/app:last-deploy \
    node \
      --max-old-space-size=$MAX_NODE_HEAP_SIZE_MB \
      --env-file=/etc/flakiness/env \
      --enable-source-maps \
      ./server/lib/units/builder_process.js
Restart=always
ExecStop=/usr/bin/docker stop builder
ExecStopPost=/usr/bin/docker rm builder
EnvironmentFile=/etc/flakiness/env
StandardOutput=journal
StandardError=journal
SyslogIdentifier=builder

[Install]
WantedBy=multi-user.target

