global:
  scrape_interval: 10s
  evaluation_interval: 1m

scrape_configs:
  - job_name: api
    metrics_path: /metrics
    scheme: https
    basic_auth:
      username: $TELEMETRY_USER
      password: $TELEMETRY_PASSWORD
    static_configs:
      - targets:
        - api.usa1.flakiness.io
        - api.ger1.flakiness.io
  - job_name: builders
    metrics_path: /metrics
    scheme: https
    basic_auth:
      username: $TELEMETRY_USER
      password: $TELEMETRY_PASSWORD
    static_configs:
      - targets:
        - b.usa2.flakiness.io
        - b.usa3.flakiness.io
  - job_name: machines
    metrics_path: /metrics/machine
    scheme: https
    basic_auth:
      username: $TELEMETRY_USER
      password: $TELEMETRY_PASSWORD
    static_configs:
      - targets:
        - m.usa1.flakiness.io
        - m.usa2.flakiness.io
        - m.usa3.flakiness.io
        - m.ger1.flakiness.io
  - job_name: http
    metrics_path: /metrics/http
    scheme: https
    basic_auth:
      username: $TELEMETRY_USER
      password: $TELEMETRY_PASSWORD
    static_configs:
      - targets:
        - m.usa1.flakiness.io
        - m.ger1.flakiness.io
