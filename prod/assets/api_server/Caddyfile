{
  metrics {
    per_host
  }

  servers {
    timeouts {
      # <PERSON>f<PERSON><PERSON>'s idle timeout is 15 minutes. We should have something large
      # to avoid 502.
      idle 16m
    }
  }
}

api.${HOST_NAME} {
  handle /metrics {
    basic_auth {
      ${TELEMETRY_USER} "${TELEMETRY_PWD_BCRYPT}"
    }
    reverse_proxy localhost:3000 {
      transport http {
        # Rule of thumb: proxy keepalive should be < that upstream server keepalive.
        keepalive 50s
        keepalive_interval 25s
      }
    }
  }

  handle {
    @paths {
      path /api/*
      path /stripewebhook
      path /login/*
      path /logout
    }
    encode
    header >Server-Timing "caddy;dur={http.request.duration_ms}, {http.response.header.Server-Timing}"
    reverse_proxy @paths localhost:3000 {
      transport http {
        # Rule of thumb: proxy keepalive should be < that upstream server keepalive.
        keepalive 50s
        keepalive_interval 25s
      }
    }
  }

  @docs_route path /docs /docs/*
  handle @docs_route {
    root * /var/www/flakiness/web/dist/docs
    encode

    # Strip /docs prefix manually
    uri strip_prefix /docs

    # By default, aggressively validate caches for everything    .
    # But if it is an image/css/js, let it cache! <PERSON><PERSON> uses content
    # hashes in filenames anyway
    @yesCaching     path *.css *.js *.png *.svg *.jpg *.woff2
    @notCaching not path *.css *.js *.png *.svg *.jpg *.woff2
    header @yesCaching Cache-Control "max-age=31536000"
    header @notCaching Cache-Control "no-cache"

    file_server
  }

  @legal_page path /legal /legal/*
  handle @legal_page {
    root * /var/www/flakiness/legal
    encode

    # Strip /legal prefix manually
    uri strip_prefix /legal

    # By default, aggressively validate caches for everything    .
    # But if it is an image/css/js, let it cache! Astro uses content
    # hashes in filenames anyway
    @yesCaching     path *.css *.js *.png *.svg *.jpg *.woff2
    @notCaching not path *.css *.js *.png *.svg *.jpg *.woff2
    header @yesCaching Cache-Control "max-age=31536000"
    header @notCaching Cache-Control "no-cache"

    file_server
  }

  @landing_page path /landing /landing/*
  handle @landing_page {
    root * /var/www/flakiness/landing/dist
    encode

    # Strip /landing prefix manually
    uri strip_prefix /landing

    # By default, aggressively validate caches for everything    .
    # But if it is an image/css/js, let it cache! Astro uses content
    # hashes in filenames anyway
    @yesCaching     path *.css *.js *.png *.svg *.jpg *.woff2
    @notCaching not path *.css *.js *.png *.svg *.jpg *.woff2
    header @yesCaching Cache-Control "max-age=31536000"
    header @notCaching Cache-Control "no-cache"

    file_server
  }

  # Serve landing page if there's no session cookie.
  # In case of report.flakiness.io, do NOT serve landing page: the report
  # viewer does NOT require anybody to be logged in.
  @no_session_root {
    path /
    not header_regexp has_session Cookie "session_token="
    not header X-Forwarded-Host report.flakiness.io
  }

  handle @no_session_root {
    root * /var/www/flakiness/landing/dist
    header Cache-Control "no-cache"

    # Caddy uses both last-modified and etags; since we serve
    # different index.html for with/without session, we MUST 
    # convince browser to use etags instead of last-modified.
    header {
      defer
      -Last-Modified
    }
    route {
      try_files {path} /index.html
    }
    file_server
  }

  handle {
    root * /var/www/flakiness/web/dist
    encode

    # By default, aggressively validate caches for everything
    # However, aggressively cache only static assets, like fonts
    # and images.
    @yesCaching     path *.png *.jpg *.jpeg *.svg *.woff2
    @notCaching not path *.png *.jpg *.jpeg *.svg *.woff2
    header @yesCaching Cache-Control "max-age=31536000"
    header @notCaching Cache-Control "no-cache"

    # Caddy uses both last-modified and etags; since we serve
    # different index.html for with/without session, we MUST 
    # convince browser to use etags instead of last-modified.
    header {
      defer
      -Last-Modified
    }

    # Define a matcher for the report.flakiness.io
    # We add this header explicitly via the Traffic Rule, because the
    # original host header is used to prompt Caddy to create TLS certificates.
    @report header X-Forwarded-Host report.flakiness.io

    # If it is the report host, serve viewer.html
    handle @report {
      try_files {path} /viewer.html
    }

    # For everything else, serve index.html
    handle {
      try_files {path} /index.html
    }

    file_server
  }
}

m.${HOST_NAME} {
  handle_path /metrics/http {
    basic_auth {
      ${TELEMETRY_USER} "${TELEMETRY_PWD_BCRYPT}"
    }
    metrics
  }

  handle_path /metrics/machine {
    basic_auth {
      ${TELEMETRY_USER} "${TELEMETRY_PWD_BCRYPT}"
    }
    rewrite * /metrics
    reverse_proxy localhost:9100
  }
}
