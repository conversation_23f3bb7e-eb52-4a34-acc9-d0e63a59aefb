[Unit]
Description=Flakiness Web Service
After=network.target

[Service]
User=root
Group=root
CacheDirectory=flakiness
CacheDirectoryMode=0777
ExecStartPre=-/usr/bin/docker rm -f flakiness
ExecStart=/usr/bin/docker run \
    --rm \
    --name flakiness \
    --user www-data \
    --network=host \
    -v /etc/flakiness/env:/etc/flakiness/env:ro \
    -v /var/cache/flakiness:/data/cache \
    --env NODE_ENV=production \
    --env HDD_CACHE_ROOTDIR="/data/cache" \
    --env HDD_CACHE_STATS_MB=20480 \
    cr.flakiness.io/app:last-deploy \
    node \
      --max-old-space-size=$MAX_NODE_HEAP_SIZE_MB \
      --env-file=/etc/flakiness/env \
      --enable-source-maps \
      ./server/lib/units/server_process.js
Restart=always
ExecStop=/usr/bin/docker stop flakiness
ExecStopPost=/usr/bin/docker rm flakiness
EnvironmentFile=/etc/flakiness/env
StandardOutput=journal
StandardError=journal
SyslogIdentifier=flakiness

[Install]
WantedBy=multi-user.target
