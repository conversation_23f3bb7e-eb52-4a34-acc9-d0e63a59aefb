#!/usr/bin/env bash
set -e
set +x

if [[ ($1 == '--help') || ($1 == '-h') || ($1 == '') ]]; then
  echo "usage: $(basename $0) {--arm,--x86} flakiness:localbuild"
  echo
  echo "Build docker image and tag it with a name"
  echo "Once image is built, you can run it with"
  echo ""
  echo "  docker run --rm -it flakiness:localbuild /bin/bash"
  echo ""
  echo "NOTE: this requires local build to be complete!"
  echo ""
  exit 0
fi

trap "cd $(pwd -P)" EXIT
cd "$(dirname "$0")"
SCRIPT_PATH=$(pwd)

PLATFORM=""
if [[ "$1" == "--arm" || "$1" == "--arm64" ]]; then
  PLATFORM="linux/arm64";
elif [[ "$1" == "--x86" || "$1" == "--amd64" ]]; then
  PLATFORM="linux/amd64"
else
  echo "ERROR: unknown platform specifier - $1. Only --arm/--arm64 or --x86/--amd64 is supported"
  exit 1
fi

TAGNAME="$2"
if [[ -z "$TAGNAME" ]]; then
  TAGNAME="flakiness:localbuild"
  echo "Using default name - ${TAGNAME}"
fi

../config/config prod

export FLAKINESS_LICENSE_PUBLIC_KEY=$(op read "op://prod/license-generation/public key")
docker build \
  --no-cache \
  --build-arg FLAKINESS_LICENSE_PUBLIC_KEY="${FLAKINESS_LICENSE_PUBLIC_KEY}" \
  --platform "${PLATFORM}" \
  -t "${TAGNAME}" \
  -f "./app.dockerfile" ..

