import { FlakinessReport, ReportUtils } from '@flakiness/sdk';
import { spawnSync, SpawnSyncOptionsWithStringEncoding } from 'child_process';
import crypto from 'crypto';
import fs from 'fs';
import http from 'http';
import https from 'https';
import path from 'path';

export async function existsAsync(aPath: string) {
  return fs.promises.stat(aPath).then(() => true).catch(e => false);
}

export function extractEnvConfiguration() {
  const ENV_PREFIX = 'FK_ENV_';
  return Object.fromEntries(Object
    .entries(process.env)
    .filter(([key]) => key.toUpperCase().startsWith(ENV_PREFIX.toUpperCase()))
    .map(([key, value]) => [key.substring(ENV_PREFIX.length).toLowerCase(), (value ?? '').trim().toLowerCase()])
  );
}

export function sha1File(filePath: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const hash = crypto.createHash('sha1');
    const stream = fs.createReadStream(filePath);

    stream.on('data', (chunk) => {
      hash.update(chunk);
    });

    stream.on('end', () => {
      resolve(hash.digest('hex'));
    });

    stream.on('error', (err) => {
      reject(err);
    });
  });
}

const FLAKINESS_DBG = !!process.env.FLAKINESS_DBG;
export function errorText(error: Error) {
  return FLAKINESS_DBG ? error.stack : error.message;
}

export function sha1Buffer(data: string|Buffer): string {
  const hash = crypto.createHash('sha1');
  hash.update(data);
  return hash.digest('hex');
}

export async function retryWithBackoff<T>(job: () => Promise<T>, backoff: number[] = []): Promise<T> {
  for (const timeout of backoff) {
    try {
      return await job();
    } catch (e: any) {
      if (e instanceof AggregateError)
        console.error(`[flakiness.io err]`, errorText(e.errors[0]));
      else if (e instanceof Error)
        console.error(`[flakiness.io err]`, errorText(e));
      else
        console.error(`[flakiness.io err]`, e);
      await new Promise(x => setTimeout(x, timeout));
    }
  }
  return await job();
}

export namespace httpUtils {
  export function createRequest({ url, method = 'get', headers = {} }: {
    url: string,
    method?: 'get' | 'post' | 'put',
    headers?: Record<string, string|undefined>,
  }) {
    let resolve: (data: Buffer) => void;
    let reject: (e: Error) => void;
    const responseDataPromise = new Promise<Buffer>((a, b) => {
      resolve = a;
      reject = b;
    });

    const protocol = url.startsWith('https') ? https : http;
    headers = Object.fromEntries(Object.entries(headers).filter(([key, value]) => value !== undefined));
    const request = protocol.request(url, { method, headers }, (res: http.IncomingMessage) => {
      const chunks: Buffer[] = [];
      res.on('data', (chunk: Buffer) => chunks.push(chunk));
      res.on('end', () => {
        if (res.statusCode && res.statusCode >= 200 && res.statusCode < 300)
          resolve(Buffer.concat(chunks));
        else
          reject(new Error(`Request to ${url} failed with ${res.statusCode}`));
      });
      res.on('error', error => reject(error));
    });
    request.on('error', reject!);
    return { request, responseDataPromise };
  }

  export async function getBuffer(url: string, backoff?: number[]): Promise<Buffer> {
    return await retryWithBackoff(async () => {
      const { request, responseDataPromise } = createRequest({ url });
      request.end();
      return await responseDataPromise;  
    }, backoff);
  }

  export async function getText(url: string, backoff?: number[]): Promise<string> {
    const buffer = await getBuffer(url, backoff);
    return buffer.toString('utf-8');
  }

  export async function getJSON(url: string) {
    return JSON.parse(await getText(url));
  }

  export async function postText(url: string, text: string, backoff?: number[]): Promise<Buffer> {
    const headers = {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(text) + ''
    };
    return await retryWithBackoff(async () => {
      const { request, responseDataPromise } = createRequest({ url, headers, method: 'post' });
      request.write(text);
      request.end();
      return await responseDataPromise;
    }, backoff);
  }

  export async function postJSON(url: string, json: any, backoff?: number[]): Promise<any> {
    const buffer = await postText(url, JSON.stringify(json), backoff);
    return JSON.parse(buffer.toString('utf-8'));
  }
}

const ansiRegex = new RegExp('[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:[a-zA-Z\\d]*(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)|(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))', 'g');
export function stripAnsi(str: string): string {
  return str.replace(ansiRegex, '');
}

export function shell(command: string, args?: string[], options?: SpawnSyncOptionsWithStringEncoding) {
  try {
    const result = spawnSync(command, args, { encoding: 'utf-8', ...options });
    if (result.status !== 0) {
      return undefined;
    }
    return (result.stdout as string).trim();
  } catch (e) {
    console.error(e);
    return undefined;
  }
}

export function parseStringDate(dateString: string): FlakinessReport.UnixTimestampMS {
  return +(new Date(dateString)) as FlakinessReport.UnixTimestampMS;
}

export async function resolveAttachmentPaths(report: FlakinessReport.Report, attachmentsDir: string) {
  const attachmentFiles = await listFilesRecursively(attachmentsDir);
  const filenameToPath = new Map(attachmentFiles.map(file => [path.basename(file), file]));
  const attachmentIdToPath = new Map<FlakinessReport.AttachmentId, ReportUtils.Attachment>();
  const missingAttachments = new Set<FlakinessReport.AttachmentId>();
  ReportUtils.visitTests(report, (test) => {
    for (const attempt of test.attempts) {
      for (const attachment of attempt.attachments ?? []) {
        const attachmentPath = filenameToPath.get(attachment.id);
        if (!attachmentPath) {
          missingAttachments.add(attachment.id);
        } else {
          attachmentIdToPath.set(attachment.id, {
            contentType: attachment.contentType,
            id: attachment.id,
            path: attachmentPath,
            type: 'file',
          });
        }
      }
    }
  });
  return { attachmentIdToPath, missingAttachments: Array.from(missingAttachments) };
}

async function listFilesRecursively(dir: string, result: string[] = []): Promise<string[]> {
  const entries = await fs.promises.readdir(dir, { withFileTypes: true });
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    if (entry.isDirectory())
      await listFilesRecursively(fullPath, result);
    else
      result.push(fullPath);
  }
  return result;
}
