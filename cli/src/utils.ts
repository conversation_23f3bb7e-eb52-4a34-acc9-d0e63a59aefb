import { ExternalAttachment } from '@flakiness/sdk';
import assert from 'assert';
import { spawnSync, SpawnSyncOptionsWithStringEncoding } from 'child_process';
import crypto from 'crypto';
import fs from 'fs';
import http from 'http';
import https from 'https';
import os from 'os';
import path, { posix as posixPath, win32 as win32Path } from 'path';
import { FlakinessReport, ReportUtils } from '../../report/sdk/src/reportutils.js';

export async function existsAsync(aPath: string) {
  return fs.promises.stat(aPath).then(() => true).catch(e => false);
}

export function extractEnvConfiguration() {
  const ENV_PREFIX = 'FK_ENV_';
  return Object.fromEntries(Object
    .entries(process.env)
    .filter(([key]) => key.toUpperCase().startsWith(ENV_PREFIX.toUpperCase()))
    .map(([key, value]) => [key.substring(ENV_PREFIX.length).toLowerCase(), (value ?? '').trim().toLowerCase()])
  );
}

export function sha1File(filePath: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const hash = crypto.createHash('sha1');
    const stream = fs.createReadStream(filePath);

    stream.on('data', (chunk) => {
      hash.update(chunk);
    });

    stream.on('end', () => {
      resolve(hash.digest('hex'));
    });

    stream.on('error', (err) => {
      reject(err);
    });
  });
}

const FLAKINESS_DBG = !!process.env.FLAKINESS_DBG;
export function errorText(error: Error) {
  return FLAKINESS_DBG ? error.stack : error.message;
}

export function sha1Buffer(data: string|Buffer): string {
  const hash = crypto.createHash('sha1');
  hash.update(data);
  return hash.digest('hex');
}

export async function retryWithBackoff<T>(job: () => Promise<T>, backoff: number[] = []): Promise<T> {
  for (const timeout of backoff) {
    try {
      return await job();
    } catch (e: any) {
      if (e instanceof AggregateError)
        console.error(`[flakiness.io err]`, errorText(e.errors[0]));
      else if (e instanceof Error)
        console.error(`[flakiness.io err]`, errorText(e));
      else
        console.error(`[flakiness.io err]`, e);
      await new Promise(x => setTimeout(x, timeout));
    }
  }
  return await job();
}

export namespace httpUtils {
  export function createRequest({ url, method = 'get', headers = {} }: {
    url: string,
    method?: 'get' | 'post' | 'put',
    headers?: Record<string, string|undefined>,
  }) {
    let resolve: (data: Buffer) => void;
    let reject: (e: Error) => void;
    const responseDataPromise = new Promise<Buffer>((a, b) => {
      resolve = a;
      reject = b;
    });

    const protocol = url.startsWith('https') ? https : http;
    headers = Object.fromEntries(Object.entries(headers).filter(([key, value]) => value !== undefined));
    const request = protocol.request(url, { method, headers }, (res: http.IncomingMessage) => {
      const chunks: Buffer[] = [];
      res.on('data', (chunk: Buffer) => chunks.push(chunk));
      res.on('end', () => {
        if (res.statusCode && res.statusCode >= 200 && res.statusCode < 300)
          resolve(Buffer.concat(chunks));
        else
          reject(new Error(`Request to ${url} failed with ${res.statusCode}`));
      });
      res.on('error', error => reject(error));
    });
    request.on('error', reject!);
    return { request, responseDataPromise };
  }

  export async function getBuffer(url: string, backoff?: number[]): Promise<Buffer> {
    return await retryWithBackoff(async () => {
      const { request, responseDataPromise } = createRequest({ url });
      request.end();
      return await responseDataPromise;  
    }, backoff);
  }

  export async function getText(url: string, backoff?: number[]): Promise<string> {
    const buffer = await getBuffer(url, backoff);
    return buffer.toString('utf-8');
  }

  export async function getJSON(url: string) {
    return JSON.parse(await getText(url));
  }

  export async function postText(url: string, text: string, backoff?: number[]): Promise<Buffer> {
    const headers = {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(text) + ''
    };
    return await retryWithBackoff(async () => {
      const { request, responseDataPromise } = createRequest({ url, headers, method: 'post' });
      request.write(text);
      request.end();
      return await responseDataPromise;
    }, backoff);
  }

  export async function postJSON(url: string, json: any, backoff?: number[]): Promise<any> {
    const buffer = await postText(url, JSON.stringify(json), backoff);
    return JSON.parse(buffer.toString('utf-8'));
  }
}

const ansiRegex = new RegExp('[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:[a-zA-Z\\d]*(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)|(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))', 'g');
export function stripAnsi(str: string): string {
  return str.replace(ansiRegex, '');
}

export async function saveReportAndAttachments(report: FlakinessReport.Report, attachments: ExternalAttachment[], outputFolder: string): Promise<ExternalAttachment[]> {
  // Write report and its attachments to outputFolder.
  const reportPath = path.join(outputFolder, 'report.json');
  const attachmentsFolder = path.join(outputFolder, 'attachments');
  await fs.promises.rm(outputFolder, { recursive: true, force: true });
  await fs.promises.mkdir(outputFolder, { recursive: true });
  await fs.promises.writeFile(reportPath, JSON.stringify(report), 'utf-8');

  if (attachments.length)
    await fs.promises.mkdir(attachmentsFolder);

  const movedAttachments: ExternalAttachment[] = [];
  for (const attachment of attachments) {
    const attachmentPath = path.join(attachmentsFolder, attachment.id);
    if (attachment.path)
      await fs.promises.cp(attachment.path, attachmentPath);
    else if (attachment.body)
      await fs.promises.writeFile(attachmentPath, attachment.body);
    movedAttachments.push({
      contentType: attachment.contentType,
      id: attachment.id,
      path: attachmentPath,
    });
  }
  return movedAttachments;
}

export function shell(command: string, args?: string[], options?: SpawnSyncOptionsWithStringEncoding) {
  try {
    const result = spawnSync(command, args, { encoding: 'utf-8', ...options });
    if (result.status !== 0) {
      return undefined;
    }
    return (result.stdout as string).trim();
  } catch (e) {
    console.error(e);
    return undefined;
  }
}

function readLinuxOSRelease() {
  const osReleaseText = fs.readFileSync('/etc/os-release', 'utf-8');
  return new Map(osReleaseText.toLowerCase().split('\n').filter(line => line.includes('=')).map(line => {
    line = line.trim();
    let [key, value] = line.split('=');
    if (value.startsWith('"') && value.endsWith('"'))
      value = value.substring(1, value.length - 1);
    return [key, value];
  }));
}

type OSInfo = { name?: string, arch?: string, version?: string };
function osLinuxInfo(): OSInfo {
  const arch = shell(`uname`, [`-m`]);
  const osReleaseMap = readLinuxOSRelease();
  const name = osReleaseMap.get('name') ?? shell(`uname`);
  const version = osReleaseMap.get('version_id');
  return { name, arch, version };
}

function osDarwinInfo(): OSInfo {
  const name = 'macos';
  const arch = shell(`uname`, [`-m`]);
  const version = shell(`sw_vers`, [`-productVersion`]);
  return { name, arch, version };
}

function osWinInfo(): OSInfo {
  const name = 'win';
  const arch = process.arch;
  const version = os.release();
  return { name, arch, version };
}

export function getOSInfo(): OSInfo {
  if (process.platform === 'darwin')
    return osDarwinInfo();
  if (process.platform === 'win32')
    return osWinInfo();
  return osLinuxInfo();
}

// This function tries to search for well-known env variables to figure out run URL.
export function inferRunUrl() {
  if (process.env.GITHUB_REPOSITORY && process.env.GITHUB_RUN_ID)
    return `https://github.com/${process.env.GITHUB_REPOSITORY}/actions/runs/${process.env.GITHUB_RUN_ID}`;
  return undefined;
}

export function parseStringDate(dateString: string): FlakinessReport.UnixTimestampMS {
  return +(new Date(dateString)) as FlakinessReport.UnixTimestampMS;
}

export function gitCommitInfo(gitRepo: string): FlakinessReport.CommitId {
  const sha = shell(`git`, ['rev-parse', 'HEAD'], {
    cwd: gitRepo,
    encoding: 'utf-8',
  });
  assert(sha, `FAILED: git rev-parse HEAD @ ${gitRepo}`);
  return sha.trim() as FlakinessReport.CommitId;
}

export async function resolveAttachmentPaths(report: FlakinessReport.Report, attachmentsDir: string) {
  const attachmentFiles = await listFilesRecursively(attachmentsDir);
  const filenameToPath = new Map(attachmentFiles.map(file => [path.basename(file), file]));
  const attachmentIdToPath = new Map<FlakinessReport.AttachmentId, {
    contentType: string,
    id: FlakinessReport.AttachmentId,
    path: string,
  }>();
  const missingAttachments = new Set<FlakinessReport.AttachmentId>();
  ReportUtils.visitTests(report, (test) => {
    for (const attempt of test.attempts) {
      for (const attachment of attempt.attachments ?? []) {
        const attachmentPath = filenameToPath.get(attachment.id);
        if (!attachmentPath) {
          missingAttachments.add(attachment.id);
        } else {
          attachmentIdToPath.set(attachment.id, {
            contentType: attachment.contentType,
            id: attachment.id,
            path: attachmentPath,
          });
        }
      }
    }
  });
  return { attachmentIdToPath, missingAttachments: Array.from(missingAttachments) };
}

async function listFilesRecursively(dir: string, result: string[] = []): Promise<string[]> {
  const entries = await fs.promises.readdir(dir, { withFileTypes: true });
  for (const entry of entries) {
    const fullPath = path.join(dir, entry.name);
    if (entry.isDirectory())
      await listFilesRecursively(fullPath, result);
    else
      result.push(fullPath);
  }
  return result;
}

export function computeGitRoot(somePathInsideGitRepo: string): NormalizedPath {
  const root = shell(`git`, ['rev-parse', '--show-toplevel'], {
    cwd: somePathInsideGitRepo,
    encoding: 'utf-8',
  });
  assert(root, `FAILED: git rev-parse --show-toplevel HEAD @ ${somePathInsideGitRepo}`);
  return normalizePath(root);
}

/**
 * Brands a type by intersecting it with a type with a brand property based on
 * the provided brand string.
 */
type Brand<T, Brand extends string> = T & {
  readonly [B in Brand as `__${B}_brand`]: never;
};


const IS_WIN32_PATH = new RegExp('^[a-zA-Z]:\\\\', 'i');
const IS_ALMOST_POSIX_PATH = new RegExp('^[a-zA-Z]:/', 'i');

export type NormalizedPath = Brand<string, 'NormalizedPath'>;

export function normalizePath(aPath: string): NormalizedPath {
  // Different environments might yield different paths.
  // - Win32: D:\foo\bar.txt
  // - ALMOST_POSIX: D:/foo/bar.txt (this is how many folks on the internet end up converting Win32 paths to Posix paths, including Playwright.)
  // - Posix: /d/foo/bar.txt
  // Goal is to normalize them all to POSIX.
  if (IS_WIN32_PATH.test(aPath)) {
    // convert Win32 path to ALMOST_POSIX path
    aPath = aPath.split(win32Path.sep).join(posixPath.sep);
  }
  if (IS_ALMOST_POSIX_PATH.test(aPath))
    return ('/' + aPath[0] + aPath.substring(2)) as NormalizedPath;
  return aPath as NormalizedPath;
}

export function getCallerLocation(gitRoot: NormalizedPath, offset: number = 0): FlakinessReport.Location|undefined {
  const err = new Error();
  const stack = err.stack?.split("\n");

  // stack[0] is "Error"
  // stack[1] is this function itself
  // stack[2] is the caller
  const caller = stack?.[2 + offset]?.trim();

  // Example line: "at Object.<anonymous> (/path/to/file.js:10:5)"
  const match = caller?.match(/\((.*):(\d+):(\d+)\)$/);
  if (!match) return undefined;

  const [, filePath, line, column] = match;
  return {
    file: gitFilePath(gitRoot, normalizePath(filePath)),
    line: Number(line) as FlakinessReport.Number1Based,
    column: Number(column) as FlakinessReport.Number1Based,
  };
}

export function parseStackLocations() {
  const err = new Error();
  // stack[0] is "Error"
  // stack[1] is this function itself
  // stack[2] is the caller
  const stack = err.stack?.split('\n').slice(2);
  if (!stack)
    return [];

  const result = [];
  for (const caller of stack) {
    const match = caller.trim().match(/\((.*):(\d+):(\d+)\)$/);
    if (!match)
      continue;
    const [, file, line, column] = match;
    result.push({ file, line, column });
  }
  return result;
}

export function gitFilePath(gitRoot: NormalizedPath, absolutePath: NormalizedPath): FlakinessReport.GitFilePath {
  return posixPath.relative(gitRoot, absolutePath) as FlakinessReport.GitFilePath;
}

export function parseDurationMS(value: number) {
  if (isNaN(value))
    throw new Error('Duration cannot be NaN');

  if (value < 0)
    throw new Error(`Duration cannot be less than 0, found ${value}`);
  return (value|0) as FlakinessReport.DurationMS;
}


type GenericProject = {
  name: string,
  metadata: { [key: string]: any },
}

export function createEnvironment(options: {
  name: string,
  userSuppliedData?: Record<string, string>,
}): FlakinessReport.Environment {
  const osInfo = getOSInfo();
  return {
    name: options.name,
    systemData: {
      osArch: osInfo.arch,
      osName: osInfo.name,
      osVersion: osInfo.version,
    },
    userSuppliedData: {
      ...extractEnvConfiguration(),
      ...options.userSuppliedData ?? {},
    },
  }
}

export function createEnvironments<T extends GenericProject>(projects: T[]): Map<T, FlakinessReport.Environment> {
  const envConfiguration = extractEnvConfiguration();
  const osInfo = getOSInfo();
  // Each environment must have a unique name in this report so that we differentiate between them.
  let uniqueNames = new Set<string>();
  const result = new Map<T, FlakinessReport.Environment>();
  for (const project of projects) {
    let defaultName = project.name;
    if (!defaultName.trim())
      defaultName = 'anonymous';

    let name = defaultName;
    for (let i = 2; uniqueNames.has(name); ++i)
      name = `${defaultName}-${i}`;
    uniqueNames.add(defaultName);

    result.set(project, {
      name,
      systemData: {
        osArch: osInfo.arch,
        osName: osInfo.name,
        osVersion: osInfo.version,
      },
      userSuppliedData: {
        ...envConfiguration,
        ...project.metadata,
      },
      opaqueData: {
        project,
      },
    });
  }
  return result;
}
