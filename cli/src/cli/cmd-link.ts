import { FlakinessProjectConfig } from '../flakinessProjectConfig.js';
import { FlakinessSession } from '../flakinessSession.js';

export async function cmdLink(session: FlakinessSession, slug: string) {
  const [orgSlug, projectSlug] = slug.split('/');
  const project = await session.api.project.findProject.GET({
    orgSlug,
    projectSlug
  });
  if (!project) {
    console.log(`Failed to find project ${slug}`);
    process.exit(1);
  }

  const config = FlakinessProjectConfig.createEmpty();
  config.setProjectPublicId(project.projectPublicId);
  await config.save();
  console.log(`✓ Linked to ${session.endpoint()}/${project.org.orgSlug}/${project.projectSlug}`);
}