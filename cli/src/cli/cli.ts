#!/usr/bin/env node
import { Ranges } from '@flakiness/server/common/ranges.js';
import { TypedHTTP } from '@flakiness/shared/common/typedHttp.js';
import assert from 'assert';
import { Command, Option } from 'commander';
import fs from 'fs';
import ora from 'ora';
import path from 'path';
import PACKAGE_JSON from '../../../package.json' with { type: "json" };
import { FlakinessProjectConfig } from '../flakinessProjectConfig.js';
import { FlakinessSession } from '../flakinessSession.js';
import { errorText } from '../utils.js';
import { cmdConvert } from './cmd-convert.js';
import { cmdDownload } from './cmd-download.js';
import { cmdLink } from './cmd-link.js';
import { cmdLogin, DEFAULT_FLAKINESS_ENDPOINT } from './cmd-login.js';
import { cmdLogout } from './cmd-logout.js';
import { cmdShowReport } from './cmd-show-report.js';
import { cmdStatus } from './cmd-status.js';
import { cmdUnlink } from './cmd-unlink.js';
import { cmdUploadPlaywrightJson } from './cmd-upload-playwright-json.js';
import { cmdUpload } from './cmd-upload.js';
import { cmdWhoami } from './cmd-whoami.js';

const session = await FlakinessSession.load();

// Add the upload command
type OptAccessToken = { accessToken?: string };
const optAccessToken = new Option('-t, --access-token <token>', 'A read-write flakiness.io access token')
  .env('FLAKINESS_ACCESS_TOKEN');

type OptEndpoint = { endpoint: string };
const optEndpoint = new Option('-e, --endpoint <url>', 'An endpoint where the service is deployed')
  .default(session?.endpoint() ?? DEFAULT_FLAKINESS_ENDPOINT)
  .env('FLAKINESS_ENDPOINT');

type OptAttachmentsDir = { attachmentsDir: string };
const optAttachmentsDir = new Option('--attachments-dir <dir>', 'Directory containing attachments to upload. Defaults to the report directory')

async function runCommand(callback: () => Promise<void>) {
  try {
    await callback();
  } catch (e) {
    if (!(e instanceof Error))
      throw e;
    console.error(errorText(e));
    process.exit(1);
  }
}

const program = new Command()
  .name('flakiness')
  .description('Flakiness CLI tool')
  .version(PACKAGE_JSON.version); // Using version from package.json

async function ensureAccessToken<T extends OptAccessToken>(options: T): Promise<T & Required<OptAccessToken>> {
  let accessToken = options.accessToken;
  if (!accessToken) {
    const config = await FlakinessProjectConfig.load();
    const projectPublicId = config.projectPublicId();
    if (session && projectPublicId) {
      try {
        accessToken = (await session.api.project.getProject.GET({ projectPublicId: projectPublicId as any })).readWriteAccessToken;
      } catch (e) {
        // HTTP404 means the project got removed; handle this gracefully.
        if ((e instanceof TypedHTTP.HttpError) && e.status === 404) {
          // noop; the accesstoken is undefined.
        } else {
          throw e;
        }
      }
    }
  }
  assert(accessToken, `Please either pass FLAKINESS_ACCESS_TOKEN or run login + link`);
  return {
    ...options,
    accessToken,
  };
}

program.command('upload-playwright-json', { hidden: true })
  .description('Upload Playwright Test JSON report to the flakiness.io service')
  .argument('<relative-path-to-json>', 'Path to the Playwright JSON report file')
  .addOption(optAccessToken)
  .addOption(optEndpoint)
  .action(async (relativePath, options: OptAccessToken & OptEndpoint) => runCommand(async () => {
    await cmdUploadPlaywrightJson(relativePath, await ensureAccessToken(options));
  }));

program.command('login')
  .description('Login to the Flakiness.io service')
  .addOption(optEndpoint)
  .action(async (options: OptEndpoint) => runCommand(async () => {
    await cmdLogin(options.endpoint);
  }));

program.command('logout')
  .description('Logout from current session')
  .action(async () => runCommand(async () => {
    await cmdLogout();
  }));

program.command('whoami')
  .description('Show current logged in user information')
  .action(async () => runCommand(async () => {
    await cmdWhoami();
  }));

program.command('link')
  .description('Link repository to the flakiness project')
  .addOption(optEndpoint)
  .argument('flakiness.io/org/project', 'A URL of the Flakiness.io project')
  .action(async (slugOrUrl: string, options: OptEndpoint) => runCommand(async () => {
    let slug = slugOrUrl;
    let endpoint = options.endpoint;
    if (slugOrUrl.startsWith('http://') || slugOrUrl.startsWith('https://')) {
      const url = URL.parse(slugOrUrl);
      if (!url) {
        console.error(`Invalid URL: ${slugOrUrl}`);
        process.exit(1);
      }
      slug = url.pathname.substring(1);
      endpoint = url.origin;
    } else if (slugOrUrl.startsWith('flakiness.io/')) {
      endpoint = 'https://flakiness.io';
      slug = slugOrUrl.substring('flakiness.io/'.length);
    }
    let session = await FlakinessSession.load();
    // Load the current session.
    // If the session does not exist, or authorized in a different deployment,
    // or cannot get logged in user, then re-authorize.
    if (!session || session.endpoint() !== endpoint || (await session.api.user.whoami.GET().catch(e => undefined) === undefined))
      session = await cmdLogin(endpoint);
    await cmdLink(session, slug);
  }));

program.command('unlink')
  .description('Unlink repository from the flakiness project')
  .action(async () => runCommand(async () => {
    await cmdUnlink();
  }));

program.command('status')
  .description('Status repository from the flakiness project')
  .action(async () => runCommand(async () => {
    await cmdStatus();
  }));

type OptRunId = { runId?: number };
const optRunId = new Option('--run-id <runId>', 'RunId flakiness.io access token')
  .argParser((value) => {
    const parsed = parseInt(value, 10);
    if (isNaN(parsed) || parsed < 1) {
      throw new Error('runId must be a number >= 1');
    }
    return parsed;
  });

type OptSince = { since?: Date };
const optSince = new Option('--since <date>', 'Start date for filtering')
  .argParser((value) => {
    const parsed = new Date(value);
    if (isNaN(parsed.getTime())) {
      throw new Error('Invalid date format');
    }
    return parsed;
  });

type OptParallel = { parallel?: number };
const optParallel = new Option('-j, --parallel <date>', 'Parallel jobs to run')
  .argParser((value) => {
    const parsed = parseInt(value, 10);
    if (isNaN(parsed) || parsed < 1) {
      throw new Error('parallel must be a number >= 1');
    }
    return parsed;
  });

program.command('download')
  .description('Download run')
  .addOption(optSince)
  .addOption(optRunId)
  .addOption(optParallel)
  .action(async (options: OptRunId & OptSince & OptParallel) => runCommand(async () => {
    const config = await FlakinessProjectConfig.load();
    const session = await FlakinessSession.loadOrDie();
    const projectPublicId = config.projectPublicId();
    if (!projectPublicId)
      throw new Error(`Please link to flakiness project with 'npx flakiness link'`);
    const project = await session.api.project.getProject.GET({ projectPublicId: projectPublicId as any }).catch(e => undefined);
    if (!project)
      throw new Error(`Failed to fetch linked project; please re-link with 'npx flakiness link'`);
    let runIds: Ranges.Ranges<number> = [] as number[] as Ranges.Ranges<number>;
    if (options.runId) {
      runIds = [options.runId, options.runId] as Ranges.Ranges<number>;
    } else if (options.since) {
      runIds = await session.api.project.listRuns.GET({ 
        orgSlug: project.org.orgSlug,
        projectSlug: project.projectSlug,
        sinceTimestampMs: +options.since,
      });
      console.log(`Found ${Ranges.cardinality(runIds)} reports uploaded since ${options.since}`);
    }

    const alreadyExisting = fs.readdirSync(process.cwd());
    const downloadedRuns = alreadyExisting.filter(entry => entry.startsWith('fkrun-')).map(run => parseInt(run.substring(`fkrun-`.length), 10)).filter(runId => !isNaN(runId));
    console.log(`Found ${downloadedRuns.length} locally downloaded reports`);
    const toBeDownloaded = Ranges.subtract(runIds, Ranges.fromList(downloadedRuns));
    console.log(`Downloading ${Ranges.cardinality(toBeDownloaded)} reports`);

    const it = Ranges.iterate(toBeDownloaded);
    const downloaders: Promise<void>[] = [];
    const spinner = ora('Downloading reports:').start();
    const total = Ranges.cardinality(toBeDownloaded);
    let downloaded = 0;
    for (let i = 0; i < (options.parallel ?? 1); ++i) {
      downloaders.push((async () => {
        for (let result = it.next(); !result.done; result = it.next()) {
          const runId = result.value;
          await cmdDownload(session, project, result.value, `fkrun-${runId}`);
          ++downloaded;
          spinner.text = `Downloaded ${Math.floor(downloaded / total * 100)}% [${downloaded}/${total}] reports`;
        }
      })());
    }
    await Promise.all(downloaders);
    spinner.stop();
  }));

program.command('upload')
  .description('Upload Flakiness report to the flakiness.io service')
  .argument('<relative-paths...>', 'Paths to the Flakiness report files')
  .addOption(optAccessToken)
  .addOption(optEndpoint)
  .addOption(optAttachmentsDir)
  .action(async (relativePaths, options: OptAccessToken & OptEndpoint & OptAttachmentsDir) => {
    await runCommand(async () => {
      await cmdUpload(relativePaths, await ensureAccessToken(options));
    });
  });

program.command('show')
  .description('Show flakiness report')
  .argument('[relative-path]', 'Path to the Flakiness report file or folder that contains `report.json`. (default: flakiness-report)')
  .action(async (arg: string) => runCommand(async () => {
    const dir = path.resolve(arg ?? 'flakiness-report');
    await cmdShowReport(dir);
  }));


program.command('convert-junit')
  .description('Convert JUnit XML report(s) to Flakiness report format')
  .argument('<junit-root-dir-path>', 'Path to JUnit XML file or directory containing XML files')
  .option('--env-name <name>', 'Environment name for the report', 'junit')
  .option('--commit-id <id>', 'Git commit ID (auto-detected if not provided)')
  .option('--output-dir <dir>', 'Output directory for the report', 'flakiness-report')
  .action(async (junitPath, options: { envName: string, commitId?: string, outputDir: string }) => {
    await runCommand(async () => {
      await cmdConvert(junitPath, options);
    });
  });

// Parse arguments and execute the matching command
await program.parseAsync();
