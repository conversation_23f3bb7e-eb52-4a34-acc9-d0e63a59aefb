---
layout: ../layouts/LegalLayout.astro
title: Terms of Service
description: The rules for using Flakiness.io
date: Dec 18, 2025
---

## 1\. Introduction

These Terms of Service (“Terms”) govern your access to and use of the Flakiness.io platform (the “Service”), which includes:

1. The cloud-based analytics dashboard provided by Degu Labs (the “Cloud Service”), and

2. The downloadable agent, Docker container, SDK, and CLI tools (the “Self-Hosted Components”).

The Service is operated by Degu Labs, Inc., a Delaware corporation, located at:

Degu Labs, Inc.  
440 N Barranca Ave, \#4556  
Covina, CA 91723

(“Degu Labs”, “we”, or “us”).

By accessing or using the Service, you agree to be bound by these Terms.

## 2\. Account Registration

You must provide accurate information when creating an account and are responsible for all activities conducted under your credentials.

## 3\. Hybrid Service Model

### 3.1 Cloud Service

The Cloud Service provides hosted dashboards, analytics, and processing of Test Artifacts.

### 3.2 Self-Hosted Components (EULA Applies)

The Self-Hosted Components consist of locally deployed agents, Docker containers, SDKs, libraries, and CLI tools.  
Use of these components is governed by the End User License Agreement in Section 12\.

## 4\. Customer Data & Test Artifacts

### 4.1 Definitions

“**Test Artifacts**” include videos, screenshots, network logs, console logs, Playwright traces, and similar debugging outputs.

### 4.2 Customer Responsibilities

Customer acknowledges that Test Artifacts may unintentionally contain personal or sensitive information. Customer is solely responsible for:

* Minimizing/anonymizing sensitive data

* Ensuring uploaded data complies with law

* Preventing ingestion of prohibited data

* Securing access credentials and environments

### 4.3 Prohibited Data Categories

Customer must not upload or process:

1. HIPAA-regulated Protected Health Information (PHI)

2. PCI-DSS payment card data

3. Children’s personal data under age 13 (COPPA)

4. Classified or export-controlled government data

5. Any data Customer is not legally permitted to provide

Degu Labs may delete prohibited data.

### 4.4 Storage & Retention

Retention periods for Test Artifacts are defined by the Customer’s Subscription Plan or Order Form.

* Videos/traces/logs/screenshots: retained according to the plan

* Metadata and aggregated analytics: retained indefinitely unless restricted by law

## 5\. Fees, Billing & Refunds

Customer agrees to pay fees associated with the Subscription Plan.

### 5.1 Billing

Charges may be based on usage, seats, consumption, or enterprise agreements.

### 5.2 No Refunds

Except where required by law:

* Fees are non-refundable

* No refunds apply to partial months, unused time, or downgrades

## 6\. Acceptable Use

Customer agrees not to:

* Upload Prohibited Data (Section 4.3)

* Interfere with Service operations

* Bypass or disable license/usage controls

* Use the Service for competitive benchmarking

* Reverse engineer or misuse the Self-Hosted Components

* Violate export control laws

Degu Labs may suspend or terminate accounts for violations.

## 7\. Security

Degu Labs implements commercially reasonable security controls.

Customer is responsible for securing:

* Systems hosting Self-Hosted Components

* Network/firewall configurations

* Local authentication and access controls

## 8\. No SLA for Self-Serve Plans

There is no SLA for non-enterprise/self-serve plans.

SLAs apply only where expressly included in an Enterprise Order Form.

## 9\. Intellectual Property

Degu Labs retains all rights to:

* Software

* Documentation

* Cloud Service

* Analytics models

* Aggregated and anonymized data

No implied rights are granted.

## 10\. Confidentiality

Test Artifacts constitute Customer Confidential Information.  
Degu Labs may access such data only for:

* Operating the Service

* Troubleshooting performance issues

* Security or legal compliance

* Removing prohibited content

## 11\. Warranties & Disclaimers

**THE SERVICE IS PROVIDED “AS IS” WITHOUT WARRANTY OF ANY KIND.**

Degu Labs does not warrant:

* Error-free operation

* Uptime (except as provided in an SLA)

* Compatibility with Customer systems

* Accuracy of analytics

## 12\. End User License Agreement (EULA) Self-Hosted Components

### 12.1 License Grant

A revocable, non-exclusive, non-transferable license to deploy Self-Hosted Components for internal business purposes.

### 12.2 Restrictions

Customer may not:

* Reverse engineer

* Modify or create derivative works

* Resell or distribute the software

* Circumvent license controls

* Access or use for competitive analysis

### 12.3 Updates

Updates may be required for continued operation.

### 12.4 Customer Responsibility

Customer bears full responsibility for:

* Deployment

* System security

* Configuration

* Environment maintenance

## 13\. Limitation of Liability

To the extent allowed by law:

* Degu Labs’ total liability is limited to fees paid in the twelve (12) months preceding a claim

* Degu Labs is not liable for prohibited data uploaded by Customer

* No indirect, consequential, or punitive damages

## 14\. Arbitration Agreement & Class Action Waiver

### 14.1 Binding Arbitration

Disputes are resolved by binding arbitration administered by JAMS under its Streamlined Arbitration Rules.

### 14.2 No Class Actions

Claims may only be brought individually.

### 14.3 Governing Law

These Terms are governed by the laws of Delaware.

## 15\. Contact

For legal or privacy inquiries:

<EMAIL>  
<EMAIL>

Degu Labs, Inc.  
440 N Barranca Ave, \#4556  
Covina, CA 91723

