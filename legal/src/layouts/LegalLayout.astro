---
// src/layouts/LegalLayout.astro
const { frontmatter } = Astro.props;
---

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{frontmatter.title}</title>
    <meta name="description" content="Legal documentation">
</head>
<body>
    <main>
        <article>
            <h1>{frontmatter.title}</h1>
            <div class="last-updated">Effective Date: {frontmatter.date}</div>
            <slot />
        </article>
    </main>
</body>
</html>

<style is:global>
    /* 1. Reset & Variables */
    :root {
        --bg-color: #ffffff;
        --text-main: #1f2937; /* Gray-900 */
        --text-muted: #6b7280; /* Gray-500 */
        --border-color: #e5e7eb;
        --link-color: #2563eb;
        
        --font-sans: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        --max-width: 68ch; /* Optimal reading width */
        --spacing-unit: 1.5rem;
    }

    /* 2. Dark Mode Auto-Support */
    @media (prefers-color-scheme: dark) {
        :root {
            --bg-color: #111827; /* Gray-900 */
            --text-main: #f3f4f6; /* Gray-100 */
            --text-muted: #9ca3af; /* Gray-400 */
            --border-color: #374151;
            --link-color: #60a5fa;
        }
    }

    /* 3. Base Styles */
    html {
        font-family: var(--font-sans);
        background-color: var(--bg-color);
        color: var(--text-main);
        line-height: 1.6;
        -webkit-font-smoothing: antialiased;
    }

    body {
        margin: 0;
        padding: 0;
        display: flex;
        justify-content: center;
    }

    main {
        width: 100%;
        max-width: var(--max-width);
        padding: 4rem 1.5rem;
    }

    /* 4. Professional Typography (The "Prose" Look) */
    h1, h2, h3, h4 {
        color: var(--text-main);
        font-weight: 700;
        margin-top: 2.5em;
        margin-bottom: 0.8em;
        line-height: 1.3;
    }

    h1 { font-size: 2.25rem; letter-spacing: -0.025em; margin-top: 0; }
    h2 { font-size: 1.5rem; letter-spacing: -0.015em; border-bottom: 1px solid var(--border-color); padding-bottom: 0.3em; }
    h3 { font-size: 1.25rem; }

    p, ul, ol { margin-bottom: 1.5em; }
    
    li { margin-bottom: 0.5em; }
    
    a {
        color: var(--link-color);
        text-decoration: underline;
        text-underline-offset: 3px;
    }
    
    a:hover { text-decoration: none; }

    strong { font-weight: 600; color: var(--text-main); }
    
    code {
        background: var(--border-color);
        padding: 0.2em 0.4em;
        border-radius: 4px;
        font-size: 0.9em;
    }

    /* 5. Utility */
    .last-updated {
        color: var(--text-muted);
        font-size: 0.875rem;
        margin-bottom: 3rem;
        font-family: ui-monospace, SFMono-Regular, Menlo, monospace;
    }
</style>
