// @ts-check
import starlight from '@astrojs/starlight';
import { defineConfig } from 'astro/config';
import starlightLinksValidator from 'starlight-links-validator';

// https://astro.build/config
export default defineConfig({
  base: '/docs',
  integrations: [
    starlight({
      title: 'Flakiness.io',
      routeMiddleware: './src/routeMiddleware.ts',
      logo: {
        src: '/public/favicon.svg',
      },
      social: [
        { icon: 'email', label: 'e-mail', href: 'mailto:<EMAIL>' },
      ],
      sidebar: [
        {
          label: 'Getting Started',
          items: [
            { 
              label: 'What is Flakiness.io?',
              link: '/',
            },
            {
              label: 'Installation',
              link: '/installation'
            },
          ]
        },
        {
          label: 'CLI Tool',
          link: '/cli'
        },
        {
          label: 'Integrations',
          autogenerate: { directory: 'integrations' },
        },
        {
          label: 'Concepts',
          autogenerate: { directory: 'concepts' },
        },
        {
          label: 'On-Premise',
          autogenerate: { directory: 'on-premise' },
        },
      ],
      plugins: [
        starlightLinksValidator({
          exclude: ['/new-organization', '/login/github']
        })
      ],
    }),
  ],
});
