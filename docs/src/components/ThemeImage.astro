---
import type { ImageMetadata } from 'astro';
import { Image } from 'astro:assets';

interface Props {
  light: ImageMetadata;
  dark: ImageMetadata;
  alt: string;
  // Allow passing arbitrary props like width, height, or styles
  [key: string]: any;
}

const { light, dark, alt, class: className, ...props } = Astro.props;
---

<Image 
  src={light}
  alt={alt}
  class:list={['dark:sl-hidden', className]}
  {...props}
/>

<Image 
  src={dark} 
  alt={alt} 
  class:list={['light:sl-hidden', className]}
  {...props}
/>
