---
title: Overview
sidebar:
  order: 1
---
import { Aside } from '@astrojs/starlight/components';

Flakiness.io is a cloud-native application that can be deployed on any infrastructure. This guide walks you through deploying flakiness.io in your own environment.

:::tip
When deployed on-premises, flakiness.io stores all information within your infrastructure.
:::

## Prerequisites

Flakiness.io is distributed as a **Docker container** available from the Flakiness container registry.
Currently, flakiness.io integrates with GitHub for repository access and user authentication.

You'll need:

- A Flakiness.io License Key
- An S3-compatible storage service
- A PostgreSQL v16+ database
- A GitHub application for user authentication
