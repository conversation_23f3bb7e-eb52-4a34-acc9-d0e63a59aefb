---
title: Environments
sidebar:
  order: 2
---

import ThemeImage from '../../../components/ThemeImage.astro';

Let's say that we run tests on both Linux and Windows machines.
Then some of our runs will be coming from Windows machines, and some from Linux:

import CommitsDark from '../../../assets/timelines-commits-dark.svg';
import CommitsLight from '../../../assets/timelines-commits-light.svg';

<ThemeImage 
  light={CommitsLight}
  dark={CommitsDark}
  alt="Commits with runs in different environments"
/>

This information will be captures in [Flakiness Reports](/docs/concepts/flakiness-report): each report
contains description of an environment that executed tests.

**Environment** is a simple list of key-value pairs.
These can be generic properties, such as OS name and version:

```
os_version: 22.04
os_name: ubuntu
os_arch: x86_64
```

But also could be organization-specific properties:

```
endpoint: staging
db_provider: mysql
```

Different Flakiness Report generators collect different environment metadata.
Please refer to the generator documentation for details.