---
title: Flakiness Query Language (FQL)
sidebar:
  order: 5
---

**F**lakiness **Q**uery **L**anguage (FQL) is a simple yet powerful language for filtering tests in Flakiness.io.

A few examples:
```fql
# Find all flaky tests in the e2e folder
s:flaked f:e2e

# Find slow tests that are not smoke tests
duration>5s -#smoke

# Find tests with a specific error
$timeout f:login

# Find tests in a specific file that take longer than 2 seconds
f:login.spec.ts duration>2s

# Find all failed tests with "network" in the error message
s:failed $network
```

:::tip
Multiple filters are combined with `AND` logic by default.
:::

## Filters 

FQL supports the following filters:

| Filter Type    | Examples | Description                    |
|---------------|---------|--------------------------------|
| Test filters  | `click`  | Tests that have `click` in either the test name, suite name, or file name |
| Status filters  | `s:flaked` | Tests that flaked |
| File filters | `f:click`, `file:click` | Tests that have `click` in their file name |
| Line filters | `:12` | Tests that are at line #12 |
| Error filters | `$undefined` | Tests that have `undefined` in the error text |
| Annotation filters | `@skip` | Tests that have `skip` as either the annotation type or inside the annotation text |
| Tags | `#smoke` | Tests that have `smoke` in their tags |
| Duration filters | `d<1s`, `duration>100ms` | Tests with matching duration |

## Exclusion

Any filter can be prefixed with `-` to exclude matches.

```fql
-click           # exclude tests that have "click"
-<12             # exclude tests that are located before line #12 of some file
-#smoke          # exclude tests with #smoke tag
-s:flaked        # exclude flaked tests
```

## Combining multiple filters

Filters can be combined in any way:

```fql
click foo            # Tests with "click" and "foo" 
-#smoke click f:e2e  # Tests from e2e folder, not smoke, with "click" in name
```

## Whitespaces and special characters

If the searched word has a special character or space, the filter token should be
surrounded with quotes. Either single (`'`) or double (`"`) quotes work:

```fql
@'foo bar'      # search for "foo bar" in annotations
f:'some path'   # search for "some path" in file paths
```

When using quotes, you can escape special characters using backslashes:

```fql
'can\'t touch this'     # Single quotes with escaped apostrophe
"say \"hello\""         # Double quotes with escaped quotes
"path\\to\\file"        # Escaped backslashes
```

The escaping rules follow JSON conventions:
- Use `\'` to include a single quote inside single-quoted strings
- Use `\"` to include a double quote inside double-quoted strings  
- Use `\\` to include a literal backslash

## Number filters

Line number and duration filters are "number" filters, so mathematical comparison
operators can be used:

```fql
duration<1s        # All tests with duration less than 1 second
f:foo.spec.ts >20  # All tests in `foo.spec.ts` after line 20
```

## Duration filter

The duration filter accepts various suffixes to specify duration.
If there's no suffix, the number is treated as milliseconds. Fractional values
are also accepted.

- nanoseconds: `ns`, `nanos`, `nanosecond`, `nanoseconds`
- milliseconds: `ms`, `millis`, `millisecond`, `milliseconds`
- seconds: `s`, `sec`, `second`, `seconds`
- minutes: `m`, `min`, `mins`, `minute`, `minutes`
- hours: `h`, `hour`, `hours`

```fql
d>100ns         # Nanoseconds
d>100           # Milliseconds (default)
d>1.2s          # Fractional values are also supported
```
