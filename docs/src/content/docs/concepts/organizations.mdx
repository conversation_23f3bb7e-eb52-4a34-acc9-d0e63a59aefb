---
title: Access Control
sidebar:
  order: 10
---

Flakiness.io uses a role-based access control system to manage permissions across different levels of the application. This guide explains how organizations work and how to manage access rights effectively.

## Core Concepts

### Key Entities

| Entity | Description |
|--------|-------------|
| Users | Individuals who authenticate via GitHub |
| Organizations | Groups of users with shared permissions |
| Projects | Test data containers associated with organizations |
| Deployment | A flakiness.io instance (cloud or self-hosted) |

### Role Hierarchy

Access control in flakiness.io operates at three levels:

1. Deployment-level roles
2. Organization roles
3. Project roles

## Role Types & Permissions

### Deployment Roles

:::note
In [flakiness.io](https://flakiness.io), deployment roles are restricted to Degu Labs, Inc. employees. These roles are primarily relevant for self-hosted deployments.
:::

**SuperAdmin**
- Full deployment access
- Can create/modify/delete organizations
- Can assign Organization Admins
- Has access to deployment health statistics
- Can access deployment backoffice

### Organization Roles

**Org Admin**
- Full organization control:
  - Manage organization settings
  - Add/remove users
  - Assign member roles
  - Create/delete projects
  - Automatically receives Editor role for all projects

**Org Member**
- Basic organization access:
  - Automatically receives Viewer access to all projects
  - Can view organization details

### Project Roles

**Editor**
- Full project control:
  - Modify project visibility
  - Manage contributors
  - Add/delete test results
  - Access read-write project tokens
  - Delete project

**Viewer**
- Read-only access:
  - View test results
  - Access test history
  - View project statistics

## Project Visibility

Projects can be set to one of two visibility levels:

| Visibility | Access |
|------------|--------|
| Public | Anyone (including non-logged users) gets Viewer access |
| Private | Only organization members can access; others receive 404 |

## License Considerations

Important notes about organization membership:

- External users can be given project roles
- All users (internal and external) consume organization license seats
- Project roles do not override organization-level restrictions

## Best Practices

1. Start with a clear organization structure
2. Use project visibility strategically
3. Regularly audit user access
4. Assign minimum necessary permissions
5. Consider license implications when adding external users
