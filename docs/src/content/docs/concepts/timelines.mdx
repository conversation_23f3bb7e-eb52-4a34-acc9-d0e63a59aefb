---
title: Timelines
sidebar:
  order: 2
---

import ThemeImage from '../../../components/ThemeImage.astro';

import CommitsDark from '../../../assets/timelines-commits-dark.svg';
import CommitsLight from '../../../assets/timelines-commits-light.svg';

Let’s say we run tests on both Linux and Windows machines:

<ThemeImage 
  light={CommitsLight}
  dark={CommitsDark}
  alt="Commits with runs in different environments"
/>

In this case, we're likely interested in exploring test histories for Windows and Linux independently.

**Timeline** is a slice of all runs, filtered by environments.
Flakiness.io automatically creates a **separate timeline** foreach unique [execution environment](/docs/concepts/environments).

For the example above, we would end up with 2 timelines by default: one for Windows and one for Linux.

import TimelinesMultipleDark from '../../../assets/timelines-multiple-dark.svg';
import TimelinesMultipleLight from '../../../assets/timelines-multiple-light.svg';

<ThemeImage 
  light={TimelinesMultipleLight}
  dark={TimelinesMultipleDark}
  alt="Multiple Timelines"
/>

The website UI allows defining custom timelines. For example, we can create a single
timeline that contains all runs:

import TimelinesSingleDark from '../../../assets/timelines-single-dark.svg';
import TimelinesSingleLight from '../../../assets/timelines-single-light.svg';

<ThemeImage 
  light={TimelinesSingleLight}
  dark={TimelinesSingleDark}
  alt="Single Timeline"
/>


## Example

Let's take a look at https://flakiness.io/flakiness/demo-timelines

This project has a single end-to-end test that is executed across 3 different browsers using Playwright Test's
["projects" feature](https://playwright.dev/docs/test-projects).

We also configure the Flakiness.io Test reporter to automatically collect browser versions and record them as
an environment property using the `collectBrowserVersions: true` option.

Looking at the test history, we can clearly see that once the browsers get updated
(as a result of a Playwright Test version bump), the test starts failing on a new version of Chromium.

If you open the Timeline Editor and merge environments across different browser versions (by removing the `browser` key from environments),
you will end up with only 3 timelines: one for each browser type:

https://flakiness.io/flakiness/demo-timelines?split=%7B%22splitByDefault%22%3Atrue%2C%22filters%22%3A%7B%7D%2C%22inverse%22%3A%7B%22user%22%3A%5B%22browser%22%5D%7D%7D

