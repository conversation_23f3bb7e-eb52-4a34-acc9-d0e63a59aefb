---
title: Custom Integrations
sidebar:
  order: 10
---

import { Card, CardGrid, Steps } from '@astrojs/starlight/components';

If Flakiness.io doesn't have a first-party integration for your testing framework, you can still integrate with our platform using several approaches.

## Integration Options

<CardGrid>
  <Card title="File an Issue" icon="github">
    File an issue on our feedback repository to request official support for your testing framework.
    
    [Request New Integration →](https://github.com/flakiness/flakiness-feedback)
  </Card>
  
  <Card title="JUnit XML" icon="seti:xml">
    Many testing frameworks can output JUnit XML format, which Flakiness.io supports natively.
    
    [JUnit Integration →](/docs/integrations/junit)
  </Card>
  
  <Card title="Custom Reporter" icon="puzzle">
    Build your own reporter using the Flakiness Report specification and upload via CLI.
    
    [Learn More ↓](#custom-reporter)
  </Card>
</CardGrid>

## Custom Reporter

If your testing framework doesn't support JUnit XML output, you can create a custom reporter that generates [Flakiness Reports](/docs/concepts/flakiness-report).

### Getting Started

<Steps>

1. **Generate Report JSON**

    Review the [Flakiness Report specification](https://github.com/flakiness/flakiness-report) to understand the required JSON structure.
    Create a reporter plugin for your testing framework that outputs a `report.json` file following the specification.

   `report.json` looks like this:
   
    ```json
    {
      "category": "bash",
      "commitId": "a1b2c3d4e5f6789012345678901234567890abcd",
      "environments": [{ "name": "ubuntu" }],
      "tests": [
        {
          "title": "My shell test",
          "location": {
            "file": "tests/shell.sh",
            "line": 3,
            "column": 1
          },
          "attempts": [
            {
              "environmentIdx": 0,
              "expectedStatus": "passed",
              "status": "passed",
              "startTimestamp": 1703001600000,
              "duration": 1500,
              "attachments": [{
                "name": "screenshot.png",
                "contentType": "image/png",
                "id": "5d41402abc4b2a76b9719d911017c592"
              }]
            }
          ]
        }
      ],
      "startTimestamp": 1703001600000,
      "duration": 2000
    }
    ```

2. **Include Attachments (Optional)**
   
   Place any test artifacts (screenshots, logs, videos) in the same directory as `report.json`:

   ```text
    flakiness-report/
    ├── report.json
    └── attachments/
        └── 5d41402abc4b2a76b9719d911017c592
   ```
   
4. **Upload to Flakiness.io**
   
   Use the [Flakiness CLI](/docs/cli) to upload your generated report:
   
   ```bash
   flakiness upload ./flakiness-report
   ```

</Steps>

### Example Implementations

Looking for inspiration? Check out our existing integrations:

- [Pytest Plugin](https://github.com/flakiness/pytest-flakiness)

:::tip[Need Help?]
File an issue on [GitHub](https://github.com/flakiness/flakiness-feedback) if you need assistance building your custom integration.
:::
