---
title: Report Uploading
sidebar:
  order: 1
---

import { Steps, TabItem, Tabs } from '@astrojs/starlight/components';

The Upload API utilizes [presigned URLs](https://docs.aws.amazon.com/AmazonS3/latest/userguide/ShareObjectPreSignedURL.html) to securely upload test reports and attachments to Flakiness.io. Clients request presigned URLs from the service, upload data directly to storage, and then notify Flakiness.io when the upload is complete.

The upload process consists of three sequential steps:

1. **Start Upload** - Initialize an upload session and receive an `uploadToken` to control the report upload.
2. **Upload Report & Attachments** - Upload the compressed report and any test artifacts using presigned URLs
3. **Finish Upload** - Complete the upload process and mark the report for processing

## Working with Presigned URLs

Here are the essential rules for working with Flakiness.io presigned URLs:

- **HTTP Method**: Always use `PUT` method for uploads
- **No Authentication**: Presigned URLs contain embedded authentication - do not add additional authorization headers

### Report Upload Requirements

When uploading the main report file:

- **Compression**: Reports **must** be compressed with Brotli compression
- **Content-Type**: Set to `application/json`
- **Content-Encoding**: Set to `br` (Brotli)
- **Quality**: Use `--quality=9` for optimal compression

```bash
# Compress the report
brotli --quality=9 --output=report.json.br report.json

# Upload with required headers
curl -X PUT "$PRESIGNED_REPORT_URL" \
  -H "Content-Type: application/json" \
  -H "Content-Encoding: br" \
  --data-binary @report.json.br
```

### Attachment Upload Guidelines

For test attachments (screenshots, logs, videos):

- **Compression**: Optional but recommended for text-based files
- **Supported Formats**: `deflate`, `br` (Brotli), or `gzip`
- **Binary Files**: Images and videos typically don't benefit from compression
- **Content Headers**: Only add compression headers if the file is actually compressed

```bash
# Upload uncompressed attachment
curl -X PUT "$PRESIGNED_ATTACHMENT_URL" \
  --data-binary @attachments/5d41402

# Upload compressed attachment
gzip -c ./attachments/5d41402 > ./attachments/5d41402.gz
curl -X PUT "$PRESIGNED_ATTACHMENT_URL" \
  -H "Content-Encoding: gzip" \
  --data-binary @attachments/5d41402.gz
```

## Quick Start Example

Consider uploading a test report with a single attachment to Flakiness.io:

```text
flakiness-report/
├── report.json
└── attachments/
    └── 5d41402
```

<Steps>

1. **Start Upload Session**

   Initialize the upload process to receive your upload token and presigned URLs:

   ```bash
   curl -X POST "https://flakiness.io/api/upload/start" \
     -H "Authorization: Bearer $FLAKINESS_ACCESS_TOKEN" \
     -H "Content-Type: application/json"
   ```

   Response:
   ```json
   {
     "uploadToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
     "webUrl": "/f/ff/run/9",
     "presignedReportUrl": "https://s3.flakiness.test/flakiness-data/reports/..."
   }
   ```

   The `webUrl` provides a path to track upload and processing status on the Flakiness.io platform.

2. **Compress and Upload Report**

   Reports must be compressed with Brotli for optimal efficiency:

   ```bash
   brotli --quality=9 --output=report.json.br report.json
   ```

   Upload the compressed report using the presigned URL:

   ```bash
   curl -X PUT "$REPORT_PRESIGNED_URL" \
     -H "Content-Type: application/json" \
     -H "Content-Encoding: br" \
     --data-binary @report.json.br
   ```

   :::note[Report Compression]
   Please note that report **must be** compressed with brotli. Report presigned url expects `Content-Encoding: br` header to be present.
   :::


3. **Request Attachment URLs**

   If your report includes attachments, request presigned URLs for each attachment:

   ```bash
   curl -X POST "https://flakiness.io/api/upload/attachments" \
     -H "Authorization: Bearer $UPLOAD_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"attachmentIds":["5d41402"]}'
   ```

   Response:
   ```json
   [{
     "attachmentId": "5d41402",
     "presignedUrl": "https://s3.flakiness.test/flakiness-data/reports/..."
   }]
   ```

4. **Upload Attachments**

   Upload each attachment using its corresponding presigned URL:

   ```bash
   curl -X PUT "$ATTACHMENT_PRESIGNED_URL" \
     --data-binary @attachments/5d41402
   ```

   :::tip[Attachments Compression]
   Attachments can be compressed using standard browser-supported formats (`deflate`, `br`, or `gzip`) to save space. Only compress attachments that benefit from compression.
   :::

5. **Finalize Upload**

   Complete the upload process to mark the report for processing:

   ```bash
   curl -X POST "https://flakiness.io/api/upload/finish" \
     -H "Authorization: Bearer $UPLOAD_TOKEN"
   ```

</Steps>

## API Methods

### Start Upload

<Tabs>
  <TabItem label="Overview">
    | Method | Endpoint | Authorization | Content-Type |
    |--------|----------|---------------|--------------|
    | `POST` | `/api/upload/start` | `Bearer {ACCESS_TOKEN}` | `application/json` |
  </TabItem>

  <TabItem label="cURL Example">
    ```bash
    curl -X POST "https://flakiness.io/api/upload/start" \
      -H "Authorization: Bearer $FLAKINESS_ACCESS_TOKEN" \
      -H "Content-Type: application/json"
    ```
  </TabItem>

  <TabItem label="JSON Response">
    ```json
    {
      "uploadToken": "eyJhbGciOiJIUz...",
      "webUrl": "/org/project/run/9",
      "presignedReportUrl": "https://s3.flakiness.test/flakiness-data/repor..."
    }
    ```
  </TabItem>
</Tabs>

Initializes a new upload session and returns an upload token along with a presigned URL for the report upload. The upload token is used to authenticate subsequent API calls within this upload session.

**Response Fields:**
- `uploadToken` - JWT token for authenticating subsequent upload operations. The token will auto-expire
  in 24 hours.
- `webUrl` - Relative path to track upload progress on the platform
- `presignedReportUrl` - Pre-signed S3 URL for uploading the compressed report


### Request Attachment URLs

<Tabs>
  <TabItem label="Overview">
    | Method | Endpoint | Authorization | Content-Type |
    |--------|----------|---------------|--------------|
    | `POST` | `/api/upload/attachments` | `Bearer {UPLOAD_TOKEN}` | `application/json` |
  </TabItem>

  <TabItem label="cURL Example">
    ```bash
    curl -X POST "https://flakiness.io/api/upload/attachments" \
      -H "Authorization: Bearer $UPLOAD_TOKEN" \
      -H "Content-Type: application/json" \
      -d '{"attachmentIds":["5d41402","abc123"]}'
    ```
  </TabItem>

  <TabItem label="JSON Response">
    ```json
    [{
      "attachmentId": "5d41402",
      "presignedUrl": "https://s3.flakiness.test/flakiness-data/re..."
    }, {
      "attachmentId": "abc123",
      "presignedUrl": "https://s3.flakiness.test/flakiness-data/re..."
    }]
    ```
  </TabItem>
</Tabs>

Generates presigned URLs for uploading test attachments. Each attachment ID from your report will receive a corresponding presigned URL for direct upload to storage.

**Request Body:**
- `attachmentIds` - Array of attachment identifiers referenced in your test report.
    * Maximum of **1000 attachment** ids is supported.
    * Each attachment Id must not exceed **256 characters** length.

**Response:**
- Array of objects containing `attachmentId` and corresponding `presignedUrl`

### Finish Upload

<Tabs>
  <TabItem label="Overview">
    | Method | Endpoint | Authorization | Content-Type |
    |--------|----------|---------------|--------------|
    | `POST` | `/api/upload/finish` | `Bearer {UPLOAD_TOKEN}` | N/A |
  </TabItem>

  <TabItem label="cURL Example">
    ```bash
    curl -X POST "https://flakiness.io/api/upload/finish" \
      -H "Authorization: Bearer $UPLOAD_TOKEN"
    ```
  </TabItem>

</Tabs>

Completes the upload session and marks the report for processing. This endpoint should be called after successfully uploading the report and all attachments to their respective presigned URLs.
