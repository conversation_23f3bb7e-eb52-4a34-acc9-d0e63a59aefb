---
title: Installation
sidebar:
  order: 1.5

---

import { <PERSON><PERSON><PERSON><PERSON>, LinkCard, Steps, TabItem, Tabs } from '@astrojs/starlight/components';


<Steps>
1. **Sign in to Flakiness.io**
   
   :::tip[Note about *Act on your behalf*]
   [Flakiness.io Github App](https://github.com/apps/flakiness-io) only asks for read-only permissions.

   There is a long thread disscussing this weird "Act on your behalf" wording [here](https://github.community/t/why-does-this-forum-need-permission-to-act-on-my-behalf/120453/7) on GitHub's own commuity forum.
   :::

   <LinkButton href="/login/github" icon="github">Sign in</LinkButton>

1. **Create an Organization**
   
   *Organizations* are groups of users with shared permissions that contain your projects. Think of them as your company, team, or personal workspace where you manage access control and billing.
   
   Start with creating a new organization for your team or personal use:
   
   <LinkButton href="/new-organization" icon="right-arrow">Create Organization</LinkButton>

1. **Create a Project**
  
   *Projects* are containers for your test data, associated with a specific code repository. Each project stores test results, analytics, and history for one repository.

   Within your organization, create a project linked to your GitHub repository. You’ll need to grant access via the [Flakiness.io GitHub App](https://github.com/apps/flakiness-io), which provides secure, scoped access to your repositories.

1. **Configure Your Testing Framework**
   
   Set up your testing framework to send reports to Flakiness.io:
   
   <LinkCard title="Playwright" href="/docs/integrations/playwright/" />
   <LinkCard title="JUnit" href="/docs/integrations/junit/" />
   <LinkCard title="Pytest" href="/docs/integrations/pytest/" />

1. **Install Flakiness CLI Tool**

   *Flakiness CLI* handles viewing, downloading & uploading Flakiness Reports. It can
   be installed on any system:

   <Tabs>
   <TabItem label="Mac & Linux">

      ```bash
      curl -LsSf https://cli.flakiness.io/install.sh | sh
      ```
   </TabItem>
   
   <TabItem label="Windows">

      ```powershell
      pwsh -c "irm https://cli.flakiness.io/install.ps1 | iex"
      ```
   </TabItem>

   <TabItem label="NPM">

      ```bash
      npm i @flakiness/sdk@latest
      ```
   </TabItem>
   </Tabs>

   Learn more about the [Flakiness CLI Tool](/docs/cli/).
</Steps>
