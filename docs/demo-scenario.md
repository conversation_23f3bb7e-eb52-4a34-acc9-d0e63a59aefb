# Introducing Flakiness.io

This demo is geared towards the auditory that uses Playwright test to run end-to-end tests.

## 1. Getting Started

Hi folks,

I'd like to introduce you today to a new project I've been working on during
last 2 years. It's called Flakiness.io, and rather than talking about it, I'll
be showing it to you.

Let's get started with a toy project: this is a set of e2e-tests, written using Playwright Test
in typescript – the best test runner for web :) 

Let's run them to make sure they all pass:

(Typing `npx playwright test` in terminal. Tests pass)

Great, all tests pass. And if some of them were to fail, then we'll be shown a nice
Playwright's HTML report, like this one

(Typing `npx playwright show-report` in terminal. A Playwright HTML report is getting shown).

Ok, everyone's loving Playwright HTML report, this is the experience many of you have
today. One thing that would be nice though is is to have history for these tests.

And this is exactly what Flakiness.io is about.

Let me start with installing a Flakiness reporter - this is just a Playwright Test reporter.

(Running `npm i -D @flakiness/playwright-test`, editing `playwright.config.ts` and
adding the `@flakiness/playwright-test` as a reporter)

Ok, this is it - let's see what it gives us. Let me run the tests one more time:

(Running `npx playwright test` one more time. The tests pring a command to open flakiness report)

Just like Playwright's HTML report, Flakiness report is now generated, and can be nicely viewed in a browser:

(Running `flakiness show` which shows Flakiness Report)

If you've been using Playwright HTML report, you might find this UI familiar. Let me quickly
walk you over the UI here.

Here at the top, we have a timeline of all your tests and their retries as they were run by workers.
Our tests were executed in a fully-parallel mode, so I can actually see their execution right away. This timeline
also has system health information - like CPU and memory load. It's nice to have them visible since they
might often explain some end-to-end test flakiness or slowdowns.

Down there, we have a list of tests, with their statuses, and more. Each test could be clicked, and with this it
reveals a detailed information about the test run: attached artifacts, errors, terminal output, steps, and so on.

The filter field at the top of the report lets us quickly filter tests however we like, for example:

- just typing in `foo` will search for the `foo` in the test name, suite name or file path
- or we can actually exclude all tests with "foo": `-foo`. The exclusion can be prepended to any
  filter.
- you can also filter by file, with the `f:misc`
- you can also filter by line numbers, just `:120` will give me all tests on line 120. (comes handy when copy-pasting
  some test locations)
- and of course, you can filter by status: `s:passed`. Or just clicking on the big status charts.

Now let's get some history for the tests.
We'll start with creating a new project on flakiness.io website:

(
  Navigates to flakiness.io. The website doesn't have any session
  initiated, showing a beautiful "landing page" with a big "Login with GitHub"
  button. Logging in and clicking "new project" in the "DemoOrg" organization
)

Flakiness.io projects link to the upstream project source code, fetching code history.
We need to pick a nice name for the project - I usually keep it the same as the upstream
github repo - and here we go.

The new project gives us the `FLAKINESS_ACCESS_TOKEN`. This is a secret, and providing it to the reporter
will upload this report to the server.

(
  runs `FLAKINESS_ACCESS_TOKEN=<token> npx playwright test`.
  The `report uploaded as https://flakiness.io/demoorg/demoproject/runs/1` is printed to the CLI.
)

Nice, now the report is uploaded, and we can click the link to see it:

(
  clicks the link. The report opens.
)

Great! What does it give us?

First of all, let's explore the 
