# Flakiness.io Contributor guide

## High-level overview

Flakiness.io is a service that collects test reports and generates reports
based on the data.

This repository is a monorepo that contains all components required to
build & run flakiness.io locally. Most of the code is written in Typescript,
built with [kubik](https://github.com/flakiness/kubik) and shipped in a form of
NPM packages and docker image.

The docker image is published on our very-own
[cr.flakiness.io](https://github.com/flakiness/cr.flakiness.io) image registry. 

## Repository Structure

Flakiness.io consists of the following main components:
- `//config` - a bunch of helper files to manage `.env` files.
- `//database` - a database-related code
- `//devenv` - provisioning and launching developer environment locally
- `//docker` - scripts to build a docker image
- `//docs` - documentation website, powered by Astro starlight
- `//e2e` - end-2-end tests for the service
- `//experimental` - a folder with a bunch of code that compiles against all
  other packages.
- `//prod` - scripts to access prod environment
- `//report` - the `@flakiness/report` package. This package has the type for
  the FlakinessReport json, along with `@playwright/test` reporter that
  generates this report and utilities to upload report to the server.
- `//shared` - a set of utilities shared between `//web`, `//server` and
  `//report`. In theory we should compile them in rather than publish as a
  separate package, but oh well.
- `//web` - front-end code.

There are also various `*.mts` (ecmascript module typescript) files around -
these are usually Kubik tasks.

We also try to provide high-level context using the `README.md` files around the repository.

## Running Flakiness.io locally

First and foremost, make sure that:

- You have docker installed
- You have NPM and Node.js available locally, as well as git :)
- You have access to the 1Password `test` vault. If not, please reach out to
  @aslushnikov to get your token.

To run repository:

1. Install all dependencies:
    ```bash
    npm i
    ```
1. Provision the `//.env.dev` file:
    ```bash
    # This uses 1Password CLI to pull secrets from the "test" vault
    ./config/config dev
    ```
3. Start developer environment:
    ```bash
    npm run dev
    ```

The last command will occupy your terminal window - this is the "developer environment". Each pane there is a Kubik task output. Tasks are configured to re-run
once some files change, so this auto-rebuilds server code and front-end code as the changes start.

Your local `flakiness.io` is now running on `http://localhost:3000`.

## Releasing a new version

Releases are mostly automated to avoid human mistakes.

To release:

1. If database schema was changed, check if migrations will run successfully against production database:
    ```bash
    ./database/lib/cli.js` test-prod-migration
    ```
1. Bump a new version of workspace:

    ```bash
    # bump version, add commit and tag the commit
    npm run minor
    # Pushing tag will kick the pipeline to build the docker image
    git push --tags upstream main
    ```
1. In github actions, navigate to [Deployment Workflow](https://github.com/flakiness/flakiness/actions/workflows/deploy.yml) and kick it off with the `vX.X.X` version.

