---
import "@fontsource/inter";
import "@fontsource/inter/500.css";
import "@fontsource/inter/600.css";
import "@fontsource/inter/700.css";
import "@fontsource/inter/800.css";
import "@fontsource/inter/900.css";

export interface Props {
  title: string;
}

const { title } = Astro.props;
---

<!doctype html>
<html lang="en" class="bg-bgDark2">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width" />
    <link rel="icon" type="image/svg+xml" href="/landing/favicon.svg" />
    <meta name="generator" content={Astro.generator} />
    <meta
      name="description"
      content="Flakiness.io - Git-Aware Test Analytics"
    />
    <title>{title}</title>
  </head>
  <body class="bg-bgDark2">
    <main aria-label="Main content">
      <slot />
    </main>
    <style is:global>
      html {
        font-family: Inter;
        background-color: #26272b;
        overflow-x: hidden;
        scroll-behavior: smooth !important;
      }
    </style>
  </body>
</html>
