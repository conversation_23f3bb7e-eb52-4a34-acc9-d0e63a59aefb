import { motion } from "framer-motion";

import { CheckArrowIcon } from "../assets/icons/CheckArrowIcon";

import feature1 from "../assets/images/calendar.png";
import feature2 from "../assets/images/commits.png";

export const Features1 = () => {
  return (
    <section
      className="w-full bg-bgDark2 pt-24 -mt-8  mb-8 sm:-mt-8 sm:mb-24 xl:-mt-8 2xl:mt-0 md:pt-[16vw] lg:pt-32"
      id="features"
    >
      <motion.div
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <div className="flex flex-wrap items-center 2xl:w-[1450px] xl:w-[1300px] w-11/12 mx-auto md:pl-4 xl:pr-16 xl:pl-16">
          <div className="w-full lg:w-1/2 mb-12 lg:mb-0">
            <div className="mx-auto lg:mx-auto w-11/12 sm:w-4/5 md:w-3/4 lg:w-unset">
              <span className="block-subtitle">ROOT CAUSE ANALYSIS</span>
              <h2 className="mt-6 mb-8 text-4xl lg:text-5xl block-big-title">
                Isolate the signal from the noise
              </h2>
              <p className="mb-10 text-secondaryText leading-loose">
                Because we bind every report to a specific commit,
                we automatically detect a genuine failure and flakiness.
                Know exactly what broke, and who broke it.
              </p>
              <ul className="mb-6 text-primaryText">
                <li className="mb-4 flex">
                  <CheckArrowIcon />
                  <div className="flex flex-col">
                    <strong>Regression Detection</strong>
                    <span className="text-secondaryText">See exactly which commit introduced the failure.</span>
                  </div>
                </li>
                <li className="mb-4 flex">
                  <CheckArrowIcon />
                  <div className="flex flex-col">
                    <strong>Intra-revision Flakiness</strong>
                    <span className="text-secondaryText">Detect tests that flip outcomes within the same commit.</span>
                  </div>
                </li>
                <li className="mb-4 flex">
                  <CheckArrowIcon />
                  <div className="flex flex-col">
                    <strong>Speedboard Trends</strong>
                    <span className="text-secondaryText">See which tests and when started running much longer.</span>
                  </div>
                </li>
              </ul>
            </div>
          </div>
          
          <div className="w-11/12 sm:w-3/4 mx-auto lg:w-1/2 flex flex-wrap lg:-mx-4 sm:pr-8 justify-center">
            <div className="mb-8 lg:mb-0 w-full px-2 lg:pl-16 flex flex-col justify-center md:pl-8">
              <div className="mb-4 py-3 md:pl-3 md:pr-20 lg:pr-12 rounded">
                <img
                  src={feature1.src}
                  alt="Feature image 5"
                  className="rounded-xl  main-border-gray"
                />
              </div>
              <div className="py-3 md:pl-20 lg:pl-12 md:pr-2 rounded ">
                <img
                  src={feature2.src}
                  alt="Feature image 6"
                  className="rounded-xl  main-border-gray"
                />
              </div>
            </div>
          </div>

        </div>
      </motion.div>
    </section>
  );
};
