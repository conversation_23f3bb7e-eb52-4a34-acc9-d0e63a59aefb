import { motion } from "framer-motion";
import { useState } from "react";

import { CheckArrowIcon } from "../assets/icons/CheckArrowIcon";

export const Pricing = () => {
  const [isMonthly, setIsMonthly] = useState(true);

  const handleChange = () => {
    setIsMonthly(!isMonthly);
  };

  return (
    <section className="w-screen flex justify-center bg-bgDark2 relative">
      <div className="absolute -top-16" id="pricing" />
      <div className="pb-20 pt-12 bg-bgDark2  2xl:w-[1150px] lg:w-[1050px]  md:w-4/5 ">
        <motion.div
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="container mx-auto px-4">
            <div className="max-w-2xl mx-auto text-center mb-16">
              <span className="block-subtitle">Find Your Perfect Fit</span>
              <h2 className="mt-6 mb-6 text-4xl lg:text-5xl font-bold font-heading text-primaryText">
                Choose your best plan
              </h2>
              <p className="mb-6 text-secondaryText">
                Free to sign up, no credit card required.
              </p>
              <label className="mx-auto bg-bgDark3 relative flex justify-between items-center group text-xl w-44 h-12 rounded-lg pr-36 pl-1 cursor-pointer">
                <input
                  type="checkbox"
                  className="peer appearance-none"
                  checked={!isMonthly}
                  onChange={handleChange}
                />
                <span className="h-8 w-[5.5rem] flex items-center pr-2 bg-bgDark3 after:rounded-lg duration-300 ease-in-out  after:w-[30rem] after:h-10  after:bg-primaryColor   after:shadow-md after:duration-300 peer-checked:after:translate-x-[5.5rem] cursor-pointer"></span>
                <div className="flex absolute text-primaryText text-sm font-bold">
                  <div
                    className={
                      isMonthly ? "mr-9 ml-3" : "mr-9 ml-3 text-gray-400"
                    }
                  >
                    Monthly
                  </div>
                  <div className={isMonthly ? "text-gray-400" : ""}>Yearly</div>
                </div>
              </label>
            </div>
            <div className="flex flex-wrap flex-col lg:flex-row -mx-4 mt-20 items-center">
              <div className="w-[350px] sm:w-[380px] lg:w-1/3 px-4 mb-8 lg:mb-0">
                <div className="p-8 bg-bgDark3 rounded-3xl">
                  <h3 className="mb-2 text-xl font-bold font-heading text-primaryText text-left">
                    Team
                  </h3>
                  <div className="flex justify-start items-end">
                    <div className="text-4xl sm:text-5xl font-bold text-primaryText text-left mt-4 mr-2">
                      {isMonthly ? "$75" : "$68.75"}
                    </div>
                    <div className="text-gray-500">
                      / month
                    </div>
                  </div>
                  <div className="text-gray-500 mt-4">{isMonthly ? 'billed monthly' : '$875 billed annually'}</div>
                  <ul className="mb-14 text-primaryText mt-6">
                    <li className="mb-4 flex">
                      <CheckArrowIcon />
                      <span>All Features</span>
                    </li>
                    <li className="mb-4 flex">
                      <CheckArrowIcon />
                      <span>Unlimited Test Runs</span>
                    </li>
                    <li className="mb-4 flex">
                      <CheckArrowIcon />
                      <span><strong>5</strong> seats</span>
                    </li>
                    <li className="mb-4 flex">
                      <CheckArrowIcon />
                      <div><strong>10GB</strong> Included Storage</div>
                    </li>
                    <li className="mb-4 flex">
                      <CheckArrowIcon />
                      <span><strong>90 Days</strong> Data Retention</span>
                    </li>
                    <li className="mb-4 flex">
                      <CheckArrowIcon />
                      <span>Standard Support</span>
                    </li>
                  </ul>
                  <a
                    href={isMonthly ? 
                      '/new-organization?plan_id=08cd66b8-c271-424e-8d1a-b44f58e4a5ae' :
                      '/new-organization?plan_id=0257ed6a-aeda-4872-8ae1-2f36dde2ab12'
                    }
                    className="inline-block text-center py-2 px-4 w-full rounded-xl rounded-t-xl contained-button font-bold leading-loose mt-16"
                    aria-label="Start for free"
                  >
                    Start for free
                  </a>
                </div>
              </div>
              <div className="w-[350px] sm:w-[380px] lg:w-1/3 px-4 mb-8 lg:mb-0">
                <div className="px-8 py-8 bg-bgDark3 rounded-3xl">
                  <h3 className="mb-2 text-xl font-bold font-heading text-primaryText text-left">
                    Business
                  </h3>
                  <div className="flex justify-start items-end">
                    <div className="text-4xl sm:text-5xl font-bold text-primaryText text-left mt-4 mr-2">
                      {isMonthly ? "$300" : "$275"}
                    </div>
                    <div className="text-gray-500">
                      / month
                    </div>
                  </div>
                  <div className="text-gray-500 mt-4">{isMonthly ? 'billed monthly' : '$3300 billed annually'}</div>
                  <ul className="mb-14 text-primaryText mt-6">
                    <li className="mb-4 flex">
                      <CheckArrowIcon />
                      <span>All Features</span>
                    </li>
                    <li className="mb-4 flex">
                      <CheckArrowIcon />
                      <span>Unlimited Test Runs</span>
                    </li>
                    <li className="mb-4 flex">
                      <CheckArrowIcon />
                      <span><strong>30</strong> seats</span>
                    </li>
                    <li className="mb-4 flex">
                      <CheckArrowIcon />
                      <div><strong>50GB</strong> Included Storage</div>
                    </li>
                    <li className="mb-4 flex">
                      <CheckArrowIcon />
                      <span><strong>365 Days</strong> Data Retention</span>
                    </li>
                    <li className="mb-4 flex">
                      <CheckArrowIcon />
                      <span>Standard Support</span>
                    </li>
                  </ul>
                  <a
                    href={isMonthly ? 
                      '/new-organization?plan_id=74d84029-e737-466b-8c11-1ec043c996b3' :
                      '/new-organization?plan_id=b50f6436-7e73-4cd0-81ce-8177eaff86a7'
                    }
                    className="inline-block text-center py-2 px-4 w-full contained-button leading-loose transition duration-200 mt-16"
                    aria-label="Start for free"
                  >
                    Start for free
                  </a>
                </div>
              </div>
              <div className="w-[350px] sm:w-[380px] lg:w-1/3 px-4 mb-8 lg:mb-0">
                <div className="p-8 bg-bgDark3 rounded-3xl">
                  <h3 className="mb-2 text-xl font-bold font-heading text-primaryText text-left">
                    Enterprise
                  </h3>
                  <div className="flex justify-start items-end">
                    <div className="text-4xl sm:text-5xl font-bold text-primaryText text-left mt-4 mr-2">
                      Custom
                    </div>
                  </div>
                  <div className="text-gray-500 mt-4">billed annually</div>
                  <ul className="mb-14 text-primaryText mt-6">
                    <li className="mb-4 flex">
                      <CheckArrowIcon />
                      <span>All Features</span>
                    </li>
                    <li className="mb-4 flex">
                      <CheckArrowIcon />
                      <span>Unlimited Test Runs</span>
                    </li>
                    <li className="mb-4 flex">
                      <CheckArrowIcon />
                      <span><strong>Unlimited</strong> seats</span>
                    </li>
                    <li className="mb-4 flex">
                      <CheckArrowIcon />
                      <div><strong>Custom</strong> Included Storage</div>
                    </li>
                    <li className="mb-4 flex">
                      <CheckArrowIcon />
                      <span><strong>Custom</strong> Data Retention</span>
                    </li>
                    <li className="mb-4 flex">
                      <CheckArrowIcon />
                      <span><strong>Priority Support</strong></span>
                    </li>
                    <li className="mb-4 flex">
                      <CheckArrowIcon />
                      <span><strong>On-Premise</strong> or Cloud Deployment</span>
                    </li>
                  </ul>
                  <a
                    href="mailto:<EMAIL>"
                    className="inline-block text-center py-2 px-4 w-full rounded-xl rounded-t-xl contained-button font-bold leading-loose mt-16"
                    aria-label="Start for free"
                  >
                    <EMAIL>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};
