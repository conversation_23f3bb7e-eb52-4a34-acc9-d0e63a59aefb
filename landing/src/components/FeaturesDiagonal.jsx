import { motion } from "framer-motion";

import { CheckArrowIcon } from "../assets/icons/CheckArrowIcon";
import customreport from "../assets/images/customreport.png";
import onprem from "../assets/images/onprem.png";

export const FeaturesDiagonal = () => {
  return (
    <section className="lg:mb-16 w-full flex flex-col justify-center items-center bg-bgDark1">
      <div className="shape-divider-bottom-1665696614">
        <svg
          data-name="Layer 1"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 1200 120"
          preserveAspectRatio="none"
          className="bg-bgDark2  fill-bgDark2"
        >
          <path
            d="M1200 120L0 16.48 0 0 1200 0 1200 120z"
            className="bg-bgDark1  fill-bgDark1"
          ></path>
        </svg>
      </div>
      <motion.div
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5, delay: 0.2 }}
      >
        <div className=" 2xl:w-[1150px] xl:w-[1050px]  md:w-4/5 flex justify-center bg-bgDark1 pt-12 lg:pt-24 pb-8 lg:pb-20 mx-auto lg:flex-row flex-col">
          <div className="w-3/4 lg:w-1/2 flex flex-col lg:mx-unset mx-auto">
            <span className="block-subtitle">Enterprise Ready</span>
            <h2 className="mt-10 mb-8 text-4xl lg:text-5xl block-big-title">
              Scales with your needs
            </h2>
            <p className="mb-10 text-secondaryText leading-loose">
              Whether you are scaling your infrastructure or your integration requirements,
              Flakiness.io adapts to your environment – not the other way around.
            </p>
            <ul className="mb-6 text-primaryText">
              <li className="mb-4 flex">
                <CheckArrowIcon />
                <div className="flex flex-col">
                  <strong>Deploy On-Premise</strong>
                  <span className="text-secondaryText">Host the entire platform on your own infrastructure for maximum security and compliance.</span>
                </div>
              </li>
              <li className="mb-4 flex">
                <CheckArrowIcon />
                <div className="flex flex-col">
                  <strong>Open Report Standard</strong>
                  <span className="text-secondaryText">Easily build custom reporters and integrations using our fully open-source report specification.</span>
                </div>
              </li>
            </ul>
            <a
              href="/docs/on-premise/overview/"
              className="w-[210px] h-12 contained-button mr-10 "
              aria-label="Get started"
            >
              Deploy On-Premise
            </a>
          </div>

          <div className="w-3/4 mt-16 mx-auto lg:w-1/2 flex flex-wrap lg:-mt-16 sm:pr-8 lg:pt-10 justify-center lg:pl-4 xl:px-8">
            <div className="lg:w-1/2 w-full">
              <div className="mb-4 lg:-mr-10 py-3 pl-3 pr-2 rounded">
                <img
                  src={onprem.src}
                  alt="Feature image 1"
                  className="rounded-xl  main-border-gray mx-auto sm:mx-unset"
                  aria-label="Feature image 1"
                />
              </div>
              <div className="py-3 lg:-ml-10 pl-10 pl-3 pr-2 rounded ">
                <img
                  src={customreport.src}
                  alt="Feature image 2"
                  className="rounded-xl  main-border-gray mx-auto sm:mx-unset"
                  aria-label="Feature image 2"
                />
              </div>
            </div>
          </div>
        </div>
      </motion.div>
      <div className="shape-divider-top-1665696661 w-full">
        <svg
          data-name="Layer 1"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 1200 120"
          preserveAspectRatio="none"
          className="bg-bgDark2 fill-bgDark2"
        >
          <path
            d="M1200 120L0 16.48 0 0 1200 0 1200 120z"
            className="bg-bgDark1 fill-bgDark1"
          ></path>
        </svg>
      </div>
    </section>
  );
};
