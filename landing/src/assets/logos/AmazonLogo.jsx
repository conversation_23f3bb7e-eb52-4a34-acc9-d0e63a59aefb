export const AmazonLogo = () => (
  <svg
    width="107"
    height="32"
    viewBox="0 0 107 32"
    className="fill-[rgb(174,178,183)] "
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M65.9419 25.0257C59.7881 29.5759 50.837 32.0001 43.1168 32.0001C32.3009 32.0001 22.6039 28.0094 15.2193 21.3707C14.6599 20.8485 15.1447 20.1399 15.8534 20.5501C23.7974 25.1749 33.6063 27.9348 43.7508 27.9348C50.576 27.9348 58.1098 26.5176 65.0468 23.5711C66.0911 23.1609 66.9489 24.2798 65.9419 25.0257Z"
    
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M68.5153 22.0793C67.732 21.0723 63.2938 21.5944 61.3171 21.8555C60.7204 21.9301 60.6085 21.4079 61.1679 21.035C64.7111 18.5361 70.4919 19.2821 71.1633 20.1026C71.8346 20.9231 70.9768 26.7413 67.6574 29.5012C67.1353 29.9114 66.6504 29.6877 66.8742 29.1282C67.6201 27.2634 69.2985 23.1235 68.5153 22.0793Z"
    
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M61.429 3.50582V1.08158C61.429 0.708622 61.6901 0.484844 62.0257 0.484844H72.8416C73.1773 0.484844 73.4756 0.745918 73.4756 1.08158V3.13286C73.4756 3.46853 73.1773 3.91608 72.6551 4.662L67.0607 12.6434C69.1493 12.6061 71.3498 12.9044 73.2146 13.9487C73.6248 14.1725 73.7367 14.5455 73.774 14.8811V17.4545C73.774 17.8275 73.401 18.2378 72.9908 18.014C69.6714 16.2611 65.2332 16.0746 61.5782 18.0513C61.2052 18.2378 60.795 17.8648 60.795 17.4918V15.0303C60.795 14.6573 60.795 13.986 61.2052 13.3893L67.6948 4.10256H62.063C61.7274 4.10256 61.429 3.84149 61.429 3.50582ZM22.0071 18.5361H18.7251C18.4267 18.4988 18.1656 18.275 18.1283 17.9767V1.11888C18.1283 0.783217 18.4267 0.522142 18.7624 0.522142H21.8206C22.1563 0.522142 22.3801 0.783214 22.4174 1.08158V3.28205H22.492C23.2752 1.15617 24.8043 0.149182 26.8183 0.149182C28.8696 0.149182 30.1749 1.15617 31.07 3.28205C31.8533 1.15617 33.6808 0.149182 35.6202 0.149182C37.0001 0.149182 38.492 0.708622 39.4244 2.01398C40.4686 3.43123 40.2449 5.48252 40.2449 7.31002V17.9767C40.2449 18.3123 39.9465 18.5734 39.6108 18.5734H36.3661C36.0304 18.5361 35.7693 18.275 35.7693 17.9767V9.02563C35.7693 8.31701 35.8439 6.5268 35.6948 5.85547C35.4337 4.73659 34.7251 4.40093 33.7554 4.40093C32.9721 4.40093 32.1143 4.92308 31.7787 5.78089C31.443 6.63869 31.4803 8.05594 31.4803 9.02563V17.9767C31.4803 18.3123 31.1819 18.5734 30.8463 18.5734H27.5642C27.2285 18.5361 26.9675 18.275 26.9675 17.9767V9.02563C26.9675 7.16083 27.2658 4.36363 24.9535 4.36363C22.6038 4.36363 22.6784 7.04894 22.6784 9.02563V17.9767C22.6411 18.275 22.3801 18.5361 22.0071 18.5361ZM82.7996 0.149182C87.6854 0.149182 90.3335 4.32634 90.3335 9.65967C90.3335 14.8065 87.4244 18.9091 82.7996 18.9091C78.0258 18.9091 75.415 14.7319 75.415 9.51049C75.3777 4.25175 78.0258 0.149182 82.7996 0.149182ZM82.7996 3.61771C80.3754 3.61771 80.2262 6.93706 80.2262 8.98835C80.2262 11.0396 80.1889 15.4406 82.7624 15.4406C85.2985 15.4406 85.4477 11.8974 85.4477 9.73427C85.4477 8.31702 85.3731 6.6014 84.9628 5.25874C84.5899 4.06527 83.8439 3.61771 82.7996 3.61771ZM96.6365 18.5361H93.3544C93.0188 18.4988 92.7577 18.2378 92.7577 17.9394V1.04428C92.795 0.745916 93.0561 0.484844 93.3917 0.484844H96.45C96.7484 0.484844 96.9721 0.708627 97.0467 0.969699V3.54312H97.1213C98.0537 1.23077 99.3218 0.149182 101.597 0.149182C103.051 0.149182 104.506 0.671322 105.438 2.12587C106.296 3.46852 106.296 5.74358 106.296 7.38461V18.014C106.259 18.3123 105.998 18.5361 105.662 18.5361H102.38C102.082 18.4988 101.821 18.2751 101.783 18.014V8.83915C101.783 6.97435 102.007 4.28904 99.732 4.28904C98.9488 4.28904 98.2029 4.81119 97.8299 5.6317C97.3824 6.67599 97.3078 7.68298 97.3078 8.83915V17.9394C97.2705 18.2751 96.9721 18.5361 96.6365 18.5361ZM52.8136 10.4802C52.8136 11.7483 52.8509 12.8298 52.2169 13.986C51.6948 14.9184 50.8742 15.4779 49.9418 15.4779C48.6738 15.4779 47.9279 14.5082 47.9279 13.0909C47.9279 10.2937 50.4267 9.77155 52.8136 9.77155V10.4802ZM56.133 18.4988C55.9092 18.6853 55.6108 18.7226 55.3498 18.5734C54.2682 17.6783 54.0444 17.2308 53.4477 16.373C51.6575 18.2005 50.3521 18.7599 48.0397 18.7599C45.2798 18.7599 43.1167 17.0443 43.1167 13.6504C43.1167 10.965 44.5712 9.17482 46.6225 8.27971C48.4127 7.4965 50.9115 7.34732 52.8136 7.12354V6.71329C52.8136 5.93007 52.8882 4.99767 52.4034 4.32634C51.9931 3.72961 51.2472 3.46853 50.5759 3.46853C49.3078 3.46853 48.1889 4.10257 47.9279 5.44522C47.8533 5.74359 47.6668 6.04195 47.3684 6.04195L44.1982 5.70629C43.9372 5.6317 43.6388 5.44522 43.7134 5.03496C44.4593 1.15618 47.9278 0 51.0607 0C52.6644 0 54.753 0.410254 56.0211 1.64102C57.6248 3.13286 57.4756 5.14685 57.4756 7.31002V12.4196C57.4756 13.9487 58.1097 14.62 58.7064 15.4779C58.9302 15.7762 58.9675 16.1492 58.7064 16.3357C57.9978 16.8951 56.8043 17.9394 56.133 18.4988ZM9.73671 10.4802C9.73671 11.7483 9.774 12.8298 9.13997 13.986C8.61783 14.9184 7.79731 15.4779 6.86491 15.4779C5.59685 15.4779 4.85093 14.5082 4.85093 13.0909C4.85093 10.2937 7.34976 9.77155 9.73671 9.77155V10.4802ZM13.0188 18.4988C12.795 18.6853 12.4966 18.7226 12.2355 18.5734C11.154 17.6783 10.9302 17.2308 10.3334 16.373C8.54323 18.2005 7.23787 18.7599 4.92552 18.7599C2.16561 18.7599 0.00244141 17.0443 0.00244141 13.6504C0.00244141 10.965 1.45698 9.17482 3.50827 8.27971C5.29848 7.4965 7.79731 7.34732 9.69941 7.12354V6.71329C9.69941 5.93007 9.774 4.99767 9.28915 4.32634C8.8789 3.72961 8.13298 3.46853 7.46165 3.46853C6.19358 3.46853 5.0747 4.10257 4.81363 5.44522C4.73904 5.74359 4.55256 6.04195 4.25419 6.04195L1.08402 5.70629C0.822952 5.6317 0.524588 5.44522 0.59918 5.03496C1.3451 1.15618 4.81363 0 7.9465 0C9.55023 0 11.6388 0.410254 12.9069 1.64102C14.5106 3.13286 14.3614 5.14685 14.3614 7.31002V12.4196C14.3614 13.9487 14.9955 14.62 15.5922 15.4779C15.816 15.7762 15.8533 16.1492 15.5922 16.3357C14.8836 16.8951 13.6901 17.9394 13.0188 18.4988Z"
   
    />
  </svg>
);
