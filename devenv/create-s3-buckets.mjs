import { S3Client, PutObjectCommand, ListObjectsV2Command, CreateBucketCommand, ListBucketsCommand, GetObjectCommand } from '@aws-sdk/client-s3';

const s3 = new S3Client({
  endpoint: process.env.S3_ENDPOINT,
  region: process.env.S3_REGION,
  credentials: {
    accessKeyId: process.env.S3_ACCESS_KEY_ID,
    secretAccessKey: process.env.S3_SECRET_ACCESS_KEY,
  },
  forcePathStyle: true,
});

await s3.send(new CreateBucketCommand({
  Bucket: 'flakiness-data-2',
}));
