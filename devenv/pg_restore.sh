#!/usr/bin/env bash
set -e
set +x

trap "cd $(pwd -P)" EXIT
cd "$(dirname "$0")"

if ! command -v pg_restore >/dev/null; then
  echo "ERROR: requires installed pg_restore"
  exit 1
fi
if [[ -f ../.env.dev ]]; then
  source ../.env.dev
else
  echo "No dev env configured "
  echo "Please run //config/config dev"
  exit 1
fi

PGPASSWORD=$PGPASSWORD pg_restore -h "${PGHOST}" -U "${PGUSER}" -d "${PGDATABASE}" -p "${PGPORT}" "$@"
