global:
    scrape_interval: 2s
    evaluation_interval: 2s

scrape_configs:
  - job_name: minio-job
    metrics_path: /minio/v2/metrics/cluster
    scheme: http
    static_configs:
      - targets: ['minio:9000']

  - job_name: server
    metrics_path: /metrics
    scheme: http
    basic_auth:
      username: tuser
      password: tpass
    static_configs:
      - targets: ['host.docker.internal:3001']

  - job_name: builder
    metrics_path: /metrics
    scheme: http
    basic_auth:
      username: tuser
      password: tpass
    static_configs:
      - targets: ['host.docker.internal:3002']
