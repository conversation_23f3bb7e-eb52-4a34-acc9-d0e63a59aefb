#!/bin/bash
set -e
set +x

trap "cd $(pwd -P)" EXIT
cd "$(dirname "$0")"

docker compose --progress quiet --env-file ../.env.dev -f ./docker-compose.yml down -v
docker compose --progress quiet --env-file ../.env.dev -f ./docker-compose.yml up -d --wait
source ../.env.dev

ENCRYPTION_KEY=$(node ../database/lib/cli.js gen-encryption-key)
# Migrate postgres instance.
PGUSER=$PGUSER \
PGPASSWORD=$PGPASSWORD \
PGDATABASE=$PGDATABASE \
PGHOST=$PGHOST \
PGPORT=$PGPORT \
node ../database/lib/cli.js migrate_postgres -y --encryption-key "${ENCRYPTION_KEY}"


S3_ENDPOINT="${S3_ENDPOINT}" \
S3_ACCESS_KEY_ID="${S3_ACCESS_KEY_ID}" \
S3_SECRET_ACCESS_KEY="${S3_SECRET_ACCESS_KEY}" \
S3_REGION="${S3_REGION}" \
node create-s3-buckets.mjs

