version: '3.8'

services:
  postgres:
    image: postgres:latest
    environment:
      POSTGRES_USER: "${PGUSER}"
      POSTGRES_PASSWORD: "${PGPASSWORD}"
      POSTGRES_DB: "${PGDATABASE}"
    ports:
      - "${PGPORT}:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready --username ${PGUSER} --dbname ${PGDATABASE}"]
      interval: 1s
      timeout: 1s
      retries: 5

  prometheus:
    image: prom/prometheus
    command: --config.file=/etc/prometheus/prometheus.yml
    ports:
      - "9090:9090"
    volumes:
      - type: bind
        source: ./prometheus.yml
        target: /etc/prometheus/prometheus.yml
    restart: unless-stopped
    depends_on:
      - minio

  grafana:
    image: grafana/grafana
    container_name: grafana
    ports:
      - 4000:3000
    restart: unless-stopped
    environment:
      - GF_SECURITY_ALLOW_EMBEDDING=true
      - GF_AUTH_ANONYMOUS_ENABLED=true
      - GF_AUTH_ANONYMOUS_ORG_ROLE=Editor
    volumes:
      - type: bind
        source: ./grafana/provisioning
        target: /etc/grafana/provisioning
      - type: bind
        source: ./grafana/dashboards
        target: /etc/dashboards

  minio:
    image: minio/minio:latest
    environment:
      MINIO_ROOT_USER: "${S3_ACCESS_KEY_ID}"
      MINIO_ROOT_PASSWORD: "${S3_SECRET_ACCESS_KEY}"
      MINIO_PROMETHEUS_AUTH_TYPE: "public"
      MINIO_PROMETHEUS_URL: "http://prometheus:9090"
    command: server /data --console-address ":9001"
    ports:
      - "9000:9000"
      - "9001:9001"
    restart: unless-stopped

