{"__inputs": [], "__requires": [{"id": "grafana", "name": "<PERSON><PERSON>", "type": "grafana", "version": "7.4.3"}, {"id": "graph", "name": "Graph", "type": "panel", "version": ""}, {"id": "prometheus", "name": "Prometheus", "type": "datasource", "version": "1.0.0"}], "annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}, {"datasource": "$datasource", "enable": true, "expr": "process_start_time_seconds{job=~\"$job\", instance=~\"$instance\"} * 1000", "hide": false, "iconColor": "#B877D9", "name": "Node.js Start", "showIn": 0, "textFormat": "{{instance}}", "titleFormat": "Node.js Start", "useValueForTime": true}]}, "description": "A quickstart to setup the Prometheus Node.js Exporter with preconfigured dashboards, alerting rules, and recording rules.", "editable": true, "gnetId": 14058, "graphTooltip": 0, "id": 1, "iteration": 1608497517213, "links": [], "panels": [{"datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 1, "title": "Overview", "type": "row"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "$datasource", "description": "The version of Node.js.", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 2, "w": 3, "x": 0, "y": 1}, "id": 2, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "pluginVersion": "6.6.2", "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false, "ymax": null, "ymin": null}, "tableColumn": "version", "targets": [{"expr": "nodejs_version_info", "format": "table", "instant": true, "legendFormat": "", "refId": "A"}], "thresholds": "", "timeFrom": null, "timeShift": null, "title": "Node.js Version", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [{"op": "=", "text": "N/A", "value": "null"}], "valueName": "first"}, {"cacheTimeout": null, "colorBackground": false, "colorValue": false, "colors": ["#299c46", "rgba(237, 129, 40, 0.89)", "#d44a3a"], "datasource": "$datasource", "description": "The number of times Node.js restarted.", "fieldConfig": {"defaults": {"custom": {}}, "overrides": []}, "format": "none", "gauge": {"maxValue": 100, "minValue": 0, "show": false, "thresholdLabels": false, "thresholdMarkers": true}, "gridPos": {"h": 2, "w": 3, "x": 3, "y": 1}, "id": 3, "interval": null, "links": [], "mappingType": 1, "mappingTypes": [{"name": "value to text", "value": 1}, {"name": "range to text", "value": 2}], "maxDataPoints": 100, "nullPointMode": "connected", "nullText": null, "pluginVersion": "6.6.2", "postfix": "", "postfixFontSize": "50%", "prefix": "", "prefixFontSize": "50%", "rangeMaps": [{"from": "null", "text": "N/A", "to": "null"}], "sparkline": {"fillColor": "rgba(31, 118, 189, 0.18)", "full": false, "lineColor": "rgb(31, 120, 193)", "show": false, "ymax": null, "ymin": null}, "tableColumn": "", "targets": [{"expr": "sum(changes(process_start_time_seconds{job=~\"$job\", instance=~\"$instance\", hostname=~\"$hostname\"}[$__range]))", "instant": false, "legendFormat": "Node.js", "refId": "A"}], "thresholds": "", "timeFrom": null, "timeShift": null, "title": "Node.js Rest<PERSON>s", "type": "singlestat", "valueFontSize": "80%", "valueMaps": [], "valueName": "current"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 3}, "id": 4, "panels": [], "title": "Process", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "CPU usage.", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 8, "x": 0, "y": 4}, "hiddenSeries": false, "id": 5, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.0-beta2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "irate(process_cpu_user_seconds_total{job=~\"$job\", instance=~\"$instance\"}[1m]) * 100", "interval": "", "legendFormat": "User CPU - {{instance}}", "refId": "A"}, {"expr": "irate(process_cpu_system_seconds_total{job=~\"$job\", instance=~\"$instance\"}[1m]) * 100", "interval": "", "legendFormat": "System CPU - {{instance}}", "refId": "B"}, {"expr": "irate(process_cpu_seconds_total{job=~\"$job\", instance=~\"$instance\"}[1m]) * 100", "interval": "", "legendFormat": "Total CPU - {{instance}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "CPU time spent.", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 8, "x": 8, "y": 4}, "hiddenSeries": false, "id": 6, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.0-beta2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(process_cpu_seconds_total{job=~\"$job\", instance=~\"$instance\"}[$interval])", "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU Time Spent", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Memory usage.", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 8, "x": 16, "y": 4}, "hiddenSeries": false, "id": 7, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideEmpty": false, "hideZero": false, "max": true, "min": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.0-beta2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "process_resident_memory_bytes{job=~\"$job\", instance=~\"$instance\"}", "interval": "", "legendFormat": "Process Memory - {{instance}}", "refId": "A"}, {"expr": "nodejs_heap_size_total_bytes{job=~\"$job\", instance=~\"$instance\"}", "interval": "", "legendFormat": "Heap Total - {{instance}}", "refId": "B"}, {"expr": "nodejs_heap_size_used_bytes{job=~\"$job\", instance=~\"$instance\"}", "interval": "", "legendFormat": "Heap Used - {{instance}}", "refId": "C"}, {"expr": "nodejs_external_memory_bytes{job=~\"$job\", instance=~\"$instance\"}", "interval": "", "legendFormat": "External Memory - {{instance}}", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Number of active handle and active requests.", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 8, "x": 0, "y": 13}, "hiddenSeries": false, "id": 8, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.0-beta2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "nodejs_active_handles_total{job=~\"$job\", instance=~\"$instance\"}", "interval": "", "legendFormat": "Active Handler - {{instance}}", "refId": "A"}, {"expr": "nodejs_active_requests_total{job=~\"$job\", instance=~\"$instance\"}", "interval": "", "legendFormat": "Active Request - {{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Active Handlers and Requests", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Latency of the event loop.", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 8, "x": 8, "y": 13}, "hiddenSeries": false, "id": 9, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.0-beta2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "nodejs_eventloop_lag_seconds{job=~\"$job\", instance=~\"$instance\"}", "hide": false, "instant": false, "interval": "", "legendFormat": "Last - {{instance}}", "refId": "A"}, {"expr": "nodejs_eventloop_lag_p99_seconds{job=~\"$job\", instance=~\"$instance\"}", "interval": "", "legendFormat": "P99 - {{instance}}", "refId": "B"}, {"expr": "nodejs_eventloop_lag_p50_seconds{job=~\"$job\", instance=~\"$instance\"}", "interval": "", "legendFormat": "P50 - {{instance}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Event Loop Latency", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 22}, "id": 10, "panels": [], "title": "Garbage Collector", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Rate of garbage collection duration.", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 23}, "hiddenSeries": false, "id": 11, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.0-beta2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(nodejs_gc_duration_seconds_sum{job=~\"$job\", instance=~\"$instance\"}[$interval])", "interval": "", "legendFormat": "{{kind}} - {{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "GC Duration Rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "s", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Duration of garbage collection.", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 23}, "hiddenSeries": false, "id": 12, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.0-beta2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "nodejs_gc_duration_seconds_sum{job=~\"$job\", instance=~\"$instance\"}", "interval": "", "legendFormat": "{{kind}} - {{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "GC Duration", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "s", "label": "", "logBase": 2, "max": null, "min": null, "show": true}, {"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Usage of heap memory.", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 23}, "hiddenSeries": false, "id": 13, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.0-beta2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "nodejs_heap_size_total_bytes{job=~\"$job\", instance=~\"$instance\"}", "interval": "", "legendFormat": "Total - {{instance}}", "refId": "A"}, {"expr": "nodejs_heap_size_used_bytes{job=~\"$job\", instance=~\"$instance\"}", "interval": "", "legendFormat": "Used - {{instance}}", "refId": "B"}, {"expr": "process_resident_memory_bytes{job=~\"$job\", instance=~\"$instance\"}", "interval": "", "legendFormat": "Resident - {{instance}}", "refId": "C"}, {"expr": "nodejs_external_memory_bytes{job=~\"$job\", instance=~\"$instance\"}", "interval": "", "legendFormat": "External - {{instance}}", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Heap Memory Usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Rate of garbage collection.", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 31}, "hiddenSeries": false, "id": 14, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.0-beta2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "rate(nodejs_gc_duration_seconds_count{job=~\"$job\", instance=~\"$instance\"}[$interval])", "interval": "", "legendFormat": "{{instance}} - {{kind}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "GC Rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "short", "label": "", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Count of garbage collection.", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 8, "y": 31}, "hiddenSeries": false, "id": 15, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.0-beta2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "nodejs_gc_duration_seconds_count{job=~\"$job\", instance=~\"$instance\"}", "interval": "", "legendFormat": "{{kind}} - {{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "GC Count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "short", "label": "", "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Usage of heap space.", "fieldConfig": {"defaults": {"custom": {}, "links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 8, "x": 16, "y": 31}, "hiddenSeries": false, "id": 16, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.3.0-beta2", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "nodejs_heap_space_size_used_bytes{instance=~\"$instance\"}", "interval": "", "legendFormat": "{{space}} - {{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Heap Space Used", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": false, "schemaVersion": 26, "style": "dark", "tags": ["node.js", "nodejs"], "templating": {"list": [{"current": {"selected": false, "text": "prometheus", "value": "prometheus"}, "error": null, "hide": 0, "includeAll": false, "label": "Datasource", "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "$datasource", "definition": "label_values(nodejs_eventloop_lag_seconds, job)", "error": null, "hide": 0, "includeAll": true, "label": "Job", "multi": true, "name": "job", "options": [], "query": "label_values(nodejs_eventloop_lag_seconds, job)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": "$datasource", "definition": "label_values(nodejs_eventloop_lag_seconds, instance)", "error": null, "hide": 0, "includeAll": true, "label": "Instance", "multi": true, "name": "instance", "options": [], "query": "label_values(nodejs_eventloop_lag_seconds, instance)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"auto": false, "auto_count": 30, "auto_min": "10s", "current": {"selected": false, "text": "1m", "value": "1m"}, "error": null, "hide": 0, "label": "Interval", "name": "interval", "options": [{"selected": true, "text": "1m", "value": "1m"}, {"selected": false, "text": "10m", "value": "10m"}, {"selected": false, "text": "30m", "value": "30m"}, {"selected": false, "text": "1h", "value": "1h"}, {"selected": false, "text": "6h", "value": "6h"}, {"selected": false, "text": "12h", "value": "12h"}, {"selected": false, "text": "1d", "value": "1d"}, {"selected": false, "text": "7d", "value": "7d"}, {"selected": false, "text": "14d", "value": "14d"}, {"selected": false, "text": "30d", "value": "30d"}], "query": "1m,10m,30m,1h,6h,12h,1d,7d,14d,30d", "refresh": 2, "skipUrlSync": false, "type": "interval"}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Node.js Metrics", "uid": "yX2d7k1Gk", "version": 1}