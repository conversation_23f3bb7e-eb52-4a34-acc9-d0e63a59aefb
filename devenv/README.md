# Developer Environment

Developer Environment is the local replica of a cloud infrastructre
required to run the `Flakiness.io` service.

In other words, Developer Environment provides:
- locally-running Postgres server
- locally-running Minio server that implements the S3-compatible storage, accessible on `http://localhost:9000`
- locally-running Prometheus
- locally-runniing Grafana instance, accessible on `http://localhost:4000`

> NOTE: as of Apr, 2025, the metrics reporting endpoint is removed
  since it was unsecured.

Developer environment starts all these services using `docker compose`, and
brings them down when not needed.

The most important script is probably `pgcli.sh`: this uses PGCLI to connect to
the locally running Postgres SQL.

