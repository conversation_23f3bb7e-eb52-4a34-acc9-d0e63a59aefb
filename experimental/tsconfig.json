{"include": ["src"], "exclude": ["lib"], "compilerOptions": {"allowSyntheticDefaultImports": true, "experimentalDecorators": true, "composite": true, "emitDeclarationOnly": true, "allowJs": true, "checkJs": true, "strict": true, "target": "ESNext", "isolatedModules": true, "resolveJsonModule": true, "moduleResolution": "NodeNext", "esModuleInterop": true, "module": "NodeNext", "lib": ["esnext", "DOM", "DOM.Iterable"], "types": ["node"], "incremental": true, "tsBuildInfoFile": "./.tsconfig.tsbuildinfo", "outDir": "./types"}, "references": [{"path": "../server"}, {"path": "../shared"}, {"path": "../report"}]}