import assert from 'assert';
import * as mitata from 'mitata';
import { MitataReporter } from "./mitataReporter.js";
import { generateSortedList } from './rangeUtils.js';

const m = new MitataReporter({
  name: `node.js iteration`,
  configPath: import.meta.filename,
});

const list = generateSortedList({
  seed: 1989,
  count: 10000000,
  density: 0.12,
});

const sum = list.reduce((acc, x) => acc + x, 0);

m.bench(`for-loop`, function*(state: mitata.k_state) {
  yield () => {
    for (let i = 0; i < list.length; ++i)
      m.do_not_optimize(list[i]);
  }
});

m.bench(`for-of loop`, function*(state: mitata.k_state) {
  yield () => {
    for (const x of list)
      m.do_not_optimize(x);
  }
});

m.bench(`soft iterator`, function*(state: mitata.k_state) {
  function createIterator(pos: number): IterableIterator<number> {
    return {
      next() {
        if (pos >= list.length)
          return { done: true, value: undefined };
        return { done: false, value: list[pos++] };
      },
    
      [Symbol.iterator](){ return this },
    };
  }
  assert([...createIterator(0)].reduce((acc, x) => acc + x, 0) === sum);

  yield () => {
    for (const x of list)
      m.do_not_optimize(x);
  }
});

m.bench(`native iterator`, function*(state: mitata.k_state) {
  // Generate
  const g = function*() {
    for (const x of list)
      yield x;
  };
  assert([...g()].reduce((acc, x) => acc + x, 0) === sum);

  yield () => {
    for (const x of g())
      m.do_not_optimize(x);
  }
});

m.bench(`allocating 2MB of memory`, function() {
  m.do_not_optimize(new Int32Array(1024 * 1024 * 2));
});

await m.cli(process.argv);
