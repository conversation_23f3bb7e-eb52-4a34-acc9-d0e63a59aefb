
export class Teamcity {
  constructor(private _base: string) {

  }
   
  async *listBuilds(projectId: string, commitId: string): AsyncGenerator<TeamCityBuild[]> {
    let start = 0;
    const count = 100;
    while (true) {
      const url = `${this._base}/guestAuth/app/rest/builds?locator=` +
                `start:${start},count:${count},revision:${commitId},project:(id:${projectId})`;
      const res = await fetch(url, {
        headers: {
          'Accept': 'application/json',
        },
      });
      const builds = (await res.json()).build;
      if (!builds.length)
        break;
      yield builds;
      if (builds.length < count)
        break;
      start += count;
    }
  }

  async fetchBuildInfo(buildId: number): Promise<TeamCityBuildInfo> {
    const url = `${this._base}/guestAuth/app/rest/builds/id:${buildId}`;
    const res = await fetch(url, {
      headers: { 'Accept': 'application/json', },
    });
    return await res.json();
  }

  async fetchArtifact(buildId: number, artifactPath: string): Promise<Buffer> {
    const zipUrl = `${this._base}/guestAuth/app/rest/builds/id:${buildId}/artifacts/content/${artifactPath}`;
    const res = await fetch(zipUrl);
    if (!res.ok)
      throw new Error(`Failed to fetch ${artifactPath} from build=${buildId}: ${res.status}`);
    return Buffer.from(await res.arrayBuffer());
  }
}

export type TeamCityBuild = {
  id: number;
  buildTypeId: string;
  number: string;
  status: 'SUCCESS' | 'FAILURE' | 'ERROR' | 'UNKNOWN';
  state: 'queued' | 'running' | 'finished';
  composite: boolean;
  branchName: string;
  defaultBranch: boolean;
  href: string;
  webUrl: string;
  finishOnAgentDate: string; // e.g. '20250725T222747+0000'
  customized: boolean;
}

// Generated by ChatGPT
export type TeamCityBuildInfo = {
  id: number;
  buildTypeId: string;
  number: string;
  status: 'SUCCESS' | 'FAILURE' | 'ERROR' | 'UNKNOWN';
  state: 'queued' | 'running' | 'finished';
  branchName: string;
  defaultBranch: boolean;
  href: string;
  webUrl: string;
  statusText: string;

  buildType: {
    id: string;
    name: string;
    projectName: string;
    projectId: string;
    href: string;
    webUrl: string;
  };

  queuedDate: string;
  startDate: string;
  finishDate: string;

  triggered: {
    type: string;
    date: string;
    build: TeamCityBuild;
    buildType: {
      id: string;
      name: string;
      projectName: string;
      projectId: string;
      href: string;
      webUrl: string;
    };
  };

  lastChanges: {
    change: any[]; // or define if needed
    count: number;
  };

  changes: {
    href: string;
  };

  revisions: {
    count: number;
    revision: any[]; // define if needed
  };

  versionedSettingsRevision: {
    version: string;
    vcsBranchName: string;
    'vcs-root-instance': {
      id: string;
      'vcs-root-id': string;
      name: string;
      href: string;
    };
  };

  agent: {
    name: string;
    typeId: number;
  };

  testOccurrences: {
    count: number;
    passed?: number;
    href: string;
  };

  artifacts: {
    href: string;
  };

  relatedIssues: {
    href: string;
  };

  statistics: {
    href: string;
  };

  'snapshot-dependencies': {
    count: number;
    build: any[]; // or TeamCityBuild[] if you want to reuse
  };

  'artifact-dependencies': {
    count: number;
    build: any[];
  };

  vcsLabels: string[];

  finishOnAgentDate: string;
  customized: boolean;

  customization: {
    changes: Record<string, unknown>;
  };
}
