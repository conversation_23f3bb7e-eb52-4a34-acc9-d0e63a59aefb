import { FlakinessReport } from "@flakiness/report";
import { Stats } from "@flakiness/server/common/stats/stats.js";
import { StatsBuilder } from "@flakiness/server/common/stats/statsBuilder.js";
import { TestIndex } from "@flakiness/server/common/stats/testIndex.js";
import { Services } from "@flakiness/server/node/services.js";
import assert from "assert";
import { program } from "commander";
import fs from 'fs';
import { glob } from 'glob';
import path from 'path';
import { ProjectPublicId } from "../../database/src/database.js";

program
  .command('stats')
  .command('build')
  .argument('<reports>', 'Path to folder with reports')
  .option('--count <count>', 'Number of reports', parseInt)
  .action(async (aPath: string, options) => {
    const jsonbrFiles = await glob('**/*.json.br', { absolute: true, cwd: aPath });
    jsonbrFiles.sort((a, b) => a < b ? -1 : a > b ? 1 : 0);
    const testIndex = new TestIndex();
    const statsBuilder = StatsBuilder.create(testIndex);
    for (let runId = 0, N = Math.min(jsonbrFiles.length, options.count ?? Infinity); runId < N; ++runId) {
      const report = JSON.parse(fs.readFileSync(jsonbrFiles[runId], 'utf-8')) as FlakinessReport.Report;
      testIndex.addReport(report);
      statsBuilder.addRun(runId as Stats.RunId, report);
      console.log(runId);
    }
    const statsPath = path.resolve(`./stats.json`);
    const testsPath = path.resolve(`./tests.json`);
    await Promise.all([
      fs.promises.writeFile(statsPath, JSON.stringify(statsBuilder.jsonStats())),
      fs.promises.writeFile(testsPath, JSON.stringify(testIndex.serialize())),
    ]);
    console.log(`Tests: ${testsPath}`);
    console.log(`Stats: ${statsPath}`);
  })

const shardCommand = program
  .command('shard')
  .description('Shard management commands');

function die(message: string): never {
  console.error(message);
  process.exit(1);  
}

async function listShards(services: Services, projectPublicId: ProjectPublicId, head?: string, count?: number) {
  const index = await services.uploadWorker.reportIndex(projectPublicId);
  console.log(`head: ${head}`)
  if (!head)
    return index.allShards();
  const repo = await services.gitWorker.getRepo(projectPublicId);
  assert(repo);
  const commits = repo.iterator(head).collect({ maxCount: count });
  return Array.from(index.splitIntoShards(commits).keys());
}

shardCommand
  .command('ls')
  .description('List shard IDs for a project')
  .argument('<project>', 'Project in format orgSlug/projectSlug')
  .option('--head <head>', 'Head commit')
  .option('--count <count>', 'Number of commits', parseInt)
  .action(async (orgSlugProjSlug: string, options) => {
    const [orgSlug, projectSlug] = orgSlugProjSlug.split('/');
    if (!orgSlug || !projectSlug)
      die('Project must be in format orgSlug/projectSlug');

    const config = await Services.configFromEnvOrDie();
    const services = await Services.initializeOrDie(config);

    const org = await services.db.orgs.getBySlug(orgSlug);
    const project = org ? await services.db.projects.getBySlug(org.org_id, projectSlug) : undefined;
    if (!project)
      die(`Failed to find project ${orgSlug}/${projectSlug}`);
    const shards = await listShards(services, project.project_public_id, options.head, options.count);
    for (const shard of shards)
      console.log(shard.shardId);
    await services.dispose();
  });

shardCommand
  .command('download')
  .description('Download shard files by their IDs')
  .argument('<project>', 'Project in format orgSlug/projectSlug')
  .argument('<shardIds...>', 'Shard IDs to download. If empty, then download everything')
  .action(async (orgSlugProjSlug, shardIds, options) => {
    try {
      const [orgSlug, projectSlug] = orgSlugProjSlug.split('/');
      if (!orgSlug || !projectSlug) {
        throw new Error('Project must be in format orgSlug/projectSlug');
      }

      const config = await Services.configFromEnvOrDie();
      const services = await Services.initializeOrDie(config);

      const org = await services.db.orgs.getBySlug(orgSlug);
      const project = org ? await services.db.projects.getBySlug(org.org_id, projectSlug) : undefined;
      if (!project)
        throw new Error(`Failed to find project ${orgSlug}/${projectSlug}`);

      for (const shardId of shardIds) {
        const file = await services.s3objects.stats.getBuffer({
          projectPublicId: project.project_public_id,
          shardId: shardId,
        });
        if (file) {
          const fileName = `shard-${shardId}.json.br`;
          await fs.promises.writeFile(fileName, file.buffer);
          console.log(`Saved as ${fileName}`);
        } else {
          console.log(`shard ${shardId} not found`);
        }
      }
      await services.dispose();
    } catch (error: any) {
      console.error('Error:', error.message);
      process.exit(1);
    }
  });


program.parse();
