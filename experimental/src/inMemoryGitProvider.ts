import { HeapSet } from "@flakiness/server/common/heapSet.js";
import { Git } from "@flakiness/server/node/git.js";
import { compressTextSync } from "@flakiness/shared/node/compression.js";
import { exec } from 'child_process';
import * as fs from 'fs/promises';
import { promisify } from 'util';

const execAsync = promisify(exec);

async function readAllCommits(dataFolder: string, options: { since?: Date }, signal?: AbortSignal): Promise<Map<string, Git.Commit>> {
  const FIELD_SEPARATOR = '|~|';
  const prettyFormat = [
    '%H',  // Full commit hash
    '%ct', // Commit date as Unix timestamp
    '%an', // Author name
    '%s',  // Subject
    '%P'   // Parent hashes
  ].join(FIELD_SEPARATOR);

  const args = [
    'log',
    '--all',
    `--pretty="format:${prettyFormat}"`,
    '-z'
  ];

  if (options.since) {
    args.push(`--since=${options.since.toISOString()}`);
  }

  const { stdout } = await execAsync(`git ${args.join(' ')}`, { 
    cwd: dataFolder,
    signal,
    maxBuffer: 1024 * 1024 * 250 // 250MB buffer
  });

  const commitMap = new Map<string, Git.Commit>();
  const lines = stdout.toString().split('\0');
  for (const line of lines) {
    if (!line) continue;

    const [commitId, timestampStr, author, message, parentsStr] = line.split(FIELD_SEPARATOR);
    const parents = parentsStr ? parentsStr.split(' ').filter(p => p) : [];
    
    commitMap.set(commitId, {
      commitId: commitId as any,
      timestamp: parseInt(timestampStr, 10) * 1000 as any,
      author,
      message,
      parents: parents as any,
      walkIndex: 0,
    });
  }
  return commitMap;
}

async function readAllRefs(dataFolder: string, signal?: AbortSignal): Promise<{ name: string, sha: string, type: 'tag'|'branch' }[]> {
  // We look for remote branches (refs/remotes) and tags (refs/tags)
  // We filter out local HEADs later
  const args = [
    'for-each-ref',
    '--format="%(refname) %(objectname)"',
    'refs/remotes',
    'refs/tags'
  ];

  const { stdout } = await execAsync(`git ${args.join(' ')}`, { 
    cwd: dataFolder, 
    signal,
    maxBuffer: 10 * 1024 * 1024 // 10MB buffer for repos with many tags
  });

  return stdout
    .trim()
    .split('\n')
    .filter(line => line && !line.includes('/HEAD')) // Ignore HEAD pointers
    .map(line => {
      const [ref, sha] = line.trim().split(' ');
      
      // Parse Tags
      if (ref.startsWith('refs/tags/')) {
        return {
          name: ref.replace('refs/tags/', ''),
          sha,
          type: 'tag' as const
        };
      }

      // Parse Remote Branches
      // Matches user's previous logic: removes 'refs/remotes/origin/' -> 'main'
      if (ref.startsWith('refs/remotes/')) {
          const name = ref
            .replace('refs/remotes/', '')
            .replace(/^origin\//, ''); // Remove 'origin/' prefix if present
          
          return {
            name,
            sha,
            type: 'branch' as const
          };
      }

      return null;
    })
    .filter((item): item is NonNullable<typeof item> => item !== null);
}

async function ensureRepository(dataFolder: string, repoUrl: string, signal?: AbortSignal) {
    try {
      await fs.access(dataFolder);
      console.log('Fetching repository');
      await execAsync('git fetch --all', { cwd: dataFolder, signal });
    } catch (e) {
      await fs.mkdir(dataFolder, { recursive: true });
      console.log('Checking out repository...');
      await execAsync(`git clone --filter=tree:0 --no-checkout ${repoUrl} ${dataFolder}`, { signal });
    }
}

export class InMemoryGitProvider implements Git.Provider {
  static async createFromFS(dataFolder: string, defaultBranch: string, onListCommits?: () => void,) {
    const [allRefs, allCommits] = await Promise.all([
      readAllRefs(dataFolder),
      readAllCommits(dataFolder, {}),
    ]);
    return new InMemoryGitProvider(allCommits, allRefs, defaultBranch, onListCommits);
  }

  constructor(
    private _allCommits: Map<string, Git.Commit>,
    private _allRefs: { name: string; sha: string; type: 'tag' | 'branch' }[],
    private _defaultBranch: string,
    private _onListCommits?: () => void,
  ) {}

  async *listPullRequests(options: { sort: "created" | "updated"; since?: Date; }, signal: AbortSignal | undefined): AsyncGenerator<Git.PullRequest[]> {    
  }

  async defaultBranch(signal: AbortSignal | undefined): Promise<string> {
    return this._defaultBranch;
  }

  async *listBranches(signal: AbortSignal | undefined): AsyncGenerator<{ name: string; sha: string }[]> {
    // Filter the pre-calculated refs for branches only
    const branches = this._allRefs
      .filter((ref) => ref.type === 'branch')
      .map((ref) => ({
        name: ref.name,
        sha: ref.sha,
      }));

    // Yield as a single batch since it is already in memory
    yield branches;
  }

  async *listCommits(
    options: { sha: string; since?: Date },
    signal: AbortSignal | undefined
  ): AsyncGenerator<Git.Commit[]> {
    const ref = this._allRefs.find(ref => ref.name === options.sha);
    const startCommit = this._allCommits.get(ref?.sha ?? options.sha);
    if (!startCommit) return;

    const frontier = HeapSet.createMax<Git.Commit>([[startCommit, startCommit.timestamp]]);
    const visited = new Set<string>([startCommit.commitId]);

    const sinceTimestamp = options.since ? options.since.getTime() : 0;
    const BATCH_SIZE = 100;
    let currentBatch: Git.Commit[] = [];

    // Process until we run out of parents to walk
    while (frontier.size > 0) {
      signal?.throwIfAborted();
      const current = frontier.pop()!;
      if (current.timestamp < sinceTimestamp)
        continue;

      // 4. Add to batch
      currentBatch.push(current);

      // 5. Add parents to queue
      for (const parentSha of current.parents) {
        if (visited.has(parentSha))
          continue;
        const parent = this._allCommits.get(parentSha);
        // Only add if we actually have the parent in our map
        if (parent) {
          visited.add(parentSha);
          frontier.add(parent, parent.timestamp);
        }
      }

      // 6. Yield batch if full
      if (currentBatch.length >= BATCH_SIZE) {
        this._onListCommits?.call(null);
        yield currentBatch;
        currentBatch = [];
        // Yield to event loop briefly to ensure we don't block the main thread
        // during heavy processing of large in-memory graphs
        await new Promise((resolve) => process.nextTick(resolve));
      }
    }
    // Yield any remaining commits
    if (currentBatch.length > 0) {
      this._onListCommits?.call(null);
      yield currentBatch;
    }
  }
}

const defaultBranch = 'main';
const data = '/tmp/webkit';
// await ensureRepository(data, 'https://github.com/webkit/webkit');

// const defaultBranch = 'master';
// const data = '/tmp/kotlin';
// await ensureRepository(data, 'https://github.com/jetbrains/kotlin');

const historyCutoff = +new Date('Jan 1, 2025');
let counter = 0;
const provider = await InMemoryGitProvider.createFromFS(data, defaultBranch, () => ++counter);
const rawCommits: Git.Commit[] = [];
for await (const commits of provider.listCommits({ sha: defaultBranch}, undefined)) {
  rawCommits.push(...commits);
}
console.log(`raw commits:`, rawCommits.filter(c => c.timestamp >= historyCutoff).length);
await fs.writeFile('/tmp/raw', rawCommits.filter(c => c.timestamp >= historyCutoff).map(c => c.commitId).join('\n'));

let git = Git.Repository.createEmpty(historyCutoff);
console.time('fetching');
await git.fetch(provider, []);
counter = 0;
await git.fetch(provider, []);
console.log('second fetch requested commits:', counter);
counter = 0;
await git.fetch(provider, []);
console.log('third fetch requested commits:', counter);

console.timeEnd('fetching');
console.time('collecting');
const commits = git.iterator(defaultBranch).collect({});
console.timeEnd('collecting');
console.log(`collected commits:`, commits.length);

const json = git.serialize();
const compressed = compressTextSync(JSON.stringify(json));
console.log(`size:`, compressed.length);
git = Git.Repository.deserialize(git.serialize(), historyCutoff)!;


