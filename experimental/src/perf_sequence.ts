import { Sequence } from "@flakiness/server/common/sequence.js";
import * as mitata from 'mitata';
import { MitataReporter } from "./mitataReporter.js";
import { generateSortedList } from "./rangeUtils.js";

const m = new MitataReporter({
  name: `sequence`,
  configPath: import.meta.filename,
});

m.bench(`Sequence.fromList`, function*(state: mitata.k_state) {
  const list = generateSortedList({
    seed: 4812,
    count: 10000000,
    density: 0.33,
  });
  yield () => {
    for (const e of Sequence.fromList(list).seek(0))
      m.do_not_optimize(e);
  }
});

m.bench(`Sequence.chain`, function*(state: mitata.k_state) {
  const list = generateSortedList({
    seed: 4812,
    count: 10000000,
    density: 0.33,
  });

  // This yields the same sequence as the Sequence.fromList test above.
  const lists: number[][] = [];
  for (let i = 0; i < list.length; i += 5000)
    lists.push(list.slice(i, i + 5000));
  yield () => {
    const seqs = lists.map(r => Sequence.fromList(r));
    for (const e of Sequence.chain(seqs).seek(0))
      m.do_not_optimize(e);
  }
});

m.bench(`Sequence.merge`, function*(state: mitata.k_state) {
  const lists = new Array(1000).fill(0).map((_, idx) => generateSortedList({
    seed: idx,
    count: 1000,
    density: 0.12,
  }));
  yield () => {
    const seqs = lists.map(r => Sequence.fromList(r));
    for (const e of Sequence.merge(seqs, (a, b) => a - b).seek(0))
      m.do_not_optimize(e);
  }
});

await m.run({ v8profile: false });
