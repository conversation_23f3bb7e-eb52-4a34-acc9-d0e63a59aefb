import { xxHash, xxHashBigint } from '@flakiness/shared/common/utils.js';
import { MitataReporter } from "./mitataReporter.js";
import { createSeededRandom } from './rangeUtils.js';

const m = new MitataReporter({
  name: `xxHash`,
  configPath: import.meta.filename,
});

const rng = createSeededRandom(23);
const list: string[] = [];
for (let i = 0; i < 1000000; ++i)
  list.push(String(rng()));

m.bench(`xxHash.str x ${list.length}`, function*() {
  yield () => {
    for (let i = 0; i < list.length; ++i)
      m.do_not_optimize(xxHash(list[i]));
  }
});

m.bench(`xxHash.bigint x ${list.length}`, function*() {
  yield () => {
    for (let i = 0; i < list.length; ++i)
      m.do_not_optimize(xxHashBigint(list[i]));
  }
});

m.bench(`map.set(str) x ${list.length}`, function*() {
  yield () => {
    const map = new Map<string, string>();
    for (let i = 0; i < list.length; ++i)
      map.set(xxHash(list[i]), list[i]);
    m.do_not_optimize(map);
  }
});

m.bench(`map.set(bigint) x ${list.length}`, function*() {
  yield () => {
    const map = new Map<bigint, string>();
    for (let i = 0; i < list.length; ++i)
      map.set(xxHashBigint(list[i]), list[i]);
    m.do_not_optimize(map);
  }
});

m.bench(`map.get(str) x ${list.length}`, function*() {
  const map = new Map<string, string>();
  for (let i = 0; i < list.length; ++i)
    map.set(xxHash(list[i]), list[i]);

  yield () => {
    for (let i = 0; i < list.length; ++i)
      m.do_not_optimize(map.get(xxHash(list[i])));
  }
});

m.bench(`map.get(bigint) x ${list.length}`, function*() {
  const map = new Map<bigint, string>();
  for (let i = 0; i < list.length; ++i)
    map.set(xxHashBigint(list[i]), list[i]);

  yield () => {
    for (let i = 0; i < list.length; ++i)
      m.do_not_optimize(map.get(xxHashBigint(list[i])));
  }
});

await m.cli(process.argv);
