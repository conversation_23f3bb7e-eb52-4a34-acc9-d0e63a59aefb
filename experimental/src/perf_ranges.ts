import { Ranges } from "@flakiness/server/common/ranges.js";
import { humanNumber as h, MitataReporter, ratio2percent as p } from "./mitataReporter.js";
import { computeStats, generateHistogram, generateRanges, generateSortedList } from "./rangeUtils.js";

const m = new MitataReporter({
  name: `ranges`,
  configPath: import.meta.filename,
});

const R = {
  ranges_36p: new Array(10).fill(0).map((_, seed) =>
    generateRanges({ seed, avgGap: 10, avgInterval: 100, count: 100000, })
  ),
  // 100 disjoint ranges.
  ranges_disjoint: generateHistogram({
    seed: 1823,
    buckets: generateSortedList({ count: 100, seed: 431, density: 0.12, }),
    elements: generateSortedList({ count: 1000000, seed: 431, density: 0.88, }),
    maxRelativeError: 0,
    minRadius: 0,
  }).map(b => b[1]),
};

const PAIRS = {
  overlap_83p: {
    r1: generateRanges({
      seed: 12345,
      count: 3500000,
      avgInterval: 1000,
      avgGap: 100,
    }),
    r2: generateRanges({
      seed: 101,
      count: 3500000,
      avgInterval: 1000,
      avgGap: 100,
    }),
  },

  overlap_9p: {
    r1: generateRanges({
      seed: 12345,
      count: 3500000,
      avgInterval: 1000,
      avgGap: 100,
    }),
    r2: generateRanges({
      seed: 101,
      count: 3500000,
      avgInterval: 100,
      avgGap: 1000,
    }),
  },

  few_ranges_cover_all: {
    r1: generateRanges({
      seed: 1,
      count: 10,
      avgInterval: 5000000,
      avgGap: 5,
    }),
    r2: generateRanges({
      seed: 4321,
      count: 5000000,
      avgInterval: 5,
      avgGap: 3,
    }),
  },

  few_tiny_ranges: {
    r1: generateRanges({
      seed: 234,
      count: 5000000,
      avgInterval: 10,
      avgGap: 5,
    }),
    r2: generateRanges({
      seed: 434,
      count: 10,
      avgInterval: 5,
      avgGap: 5000000,
    }),
  }
}

const r = Ranges.fromSortedList(generateSortedList({
  seed: 100,
  count: 5000000,
  density: 0.25,
}));


m.bench(`Ranges.sequence(${h(Ranges.cardinality(r))})`, function*() {
  yield () => {
    let sum = 0;
    for (const e of Ranges.sequence(r).seek(0))
      sum += e;
    m.do_not_optimize(sum);
  }
});

for (const ranges of Object.values(R)) {
  const stats = computeStats(ranges);

  m.bench(`unionAll(${p(stats.overlapRatio)}, ${h(stats.count)})=${h(stats.union.count)}`, function() {
    m.do_not_optimize(Ranges.unionAll(ranges));
  });

  m.bench(`unionAll_2(${p(stats.overlapRatio)}, ${h(stats.count)})=${h(stats.union.count)}`, function() {
    m.do_not_optimize(Ranges.unionAll_2(ranges));
  });

  m.bench(`intersectAll(${p(stats.overlapRatio)}, ${h(stats.count)})=${h(stats.intersection.count)}`, function() {
    m.do_not_optimize(Ranges.intersectAll(ranges));
  });
}

for (const pair of Object.values(PAIRS)) {
  const { r1, r2 } = pair;
  const stats = computeStats([r1, r2]);

  m.bench(`union(${p(stats.overlapRatio)},${h(r1.length/2)},${h(r2.length/2)})=${h(stats.union.count)}`, function() {
    m.do_not_optimize(Ranges.union(r1, r2));
  });

  m.bench(`intersect(${p(stats.overlapRatio)},${h(r1.length/2)},${h(r2.length/2)})=${h(stats.intersection.count)}`, function() {
    m.do_not_optimize(Ranges.intersect(r1, r2));
  });
}

await m.cli(process.argv);
