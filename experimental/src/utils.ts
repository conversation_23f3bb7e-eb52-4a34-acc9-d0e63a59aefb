import { FlakinessReport } from '@flakiness/report';
import { PlaywrightJSONReport } from '@flakiness/sdk/playwright';
import { normalizePath } from '@flakiness/sdk/utils';
import { ComputationCache } from '@flakiness/server/common/computationCache.js';
import { Stats } from '@flakiness/server/common/stats/stats.js';
import { StatsAnalyzer } from '@flakiness/server/common/stats/statsAnalyzer.js';
import { StatsBuilder } from '@flakiness/server/common/stats/statsBuilder.js';
import { TestIndex } from '@flakiness/server/common/stats/testIndex.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { Git } from '@flakiness/server/node/git.js';
import { ReportIndex } from '@flakiness/server/node/reportIndex.js';
import { S3 } from '@flakiness/server/node/s3.js';
import { S3Stats } from '@flakiness/server/node/s3layout.js';
import { S3Objects } from '@flakiness/server/node/s3object.js';
import { xxHash } from '@flakiness/shared/common/utils.js';
import { brotliDecompressSync, compressTextSync } from '@flakiness/shared/node/compression.js';
import { JSONReport } from '@playwright/test/reporter';
import assert from 'assert';
import chalk from 'chalk';
import fs from 'fs';
import path from 'path';
import { Readable } from 'stream';
import { finished } from 'stream/promises';
import { ReadableStream } from 'stream/web';
import unzipper from 'unzipper';
import { gunzipSync } from 'zlib';
import { ProjectPublicId } from '../../database/src/database.js';

const TEST_ARTIFACTS = path.join(import.meta.dirname, '..', '.flakiness-perf-artifacts');

let hasPrintedMessage = false;

export const DAY_MS = 1000 * 60 * 60 * 24;

export function utcFloor(date: Date): Date {
  return new Date(Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 0, 0, 0, 0));
}

export function utcCeil(date: Date): Date {
  return new Date(Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), 0, 0, 0, 0) + DAY_MS);
}

export function wireUTCDays(options: {
  since: Date,
  until: Date,
  backfill?: number,
}): WireTypes.Day[] {
  const fromDate = utcFloor(options.since);
  const toDate = utcCeil(options.until);
  const result: WireTypes.Day[] = [];
  for (let ts = +fromDate - (options.backfill ?? 0) * DAY_MS; ts < +toDate; ts += DAY_MS) {
    result.push({
      sinceTimestamp: ts as FlakinessReport.UnixTimestampMS,
      untilTimestamp: ts + DAY_MS as FlakinessReport.UnixTimestampMS,
    });
  }
  return result.reverse();
}

export class CachedS3Storage {
  private _cacheStatsJSONData = new ComputationCache<S3Stats.Id, Stats.JSONData>({
    size: Infinity,
    etag: s3id => S3Stats.path(s3id),
    compute: async (s3id, key, signal) => {
      const json = await cacheJSON(`stats-${s3id.projectPublicId}-${s3id.shardId}`, () => this.getS3Objects().then(s3objects => s3objects.stats.get(s3id)));
      assert(json);
      return json.stats;
    }
  });

  private _cacheStatsAnalyzers = new ComputationCache<S3Stats.Id, StatsAnalyzer>({
    size: Infinity,
    etag: s3id => S3Stats.path(s3id),
    compute: async (s3id, key, signal) => {
      const json = await this._cacheStatsJSONData.get(s3id);
      return new StatsAnalyzer(json ?? Stats.createEmptyStats());
    }
  });

  private _cacheReportIndexes = new ComputationCache<ProjectPublicId, ReportIndex.ReportIndex>({
    size: Infinity,
    etag: k => k,
    compute: async (ppid, cacheKey, signal) => {
      const s3objects = await this.getS3Objects();
      const metadata = await s3objects.reportIndex.getMetadata({ projectPublicId: ppid });
      assert(metadata);
      const json = await cacheJSON(`report-index-${ppid}-${metadata.etag}`, () => s3objects.reportIndex.get({ projectPublicId: ppid }));
      return new ReportIndex.ReportIndex(ppid, json);
    },
  });

  private _cacheRepositories = new ComputationCache<ProjectPublicId, Git.Repository>({
    size: Infinity,
    etag: ppid => ppid,
    compute: async (ppid, key, signal) => {
      const s3objects = await this.getS3Objects();
      const metadata = await s3objects.repositories.getMetadata({ projectPublicId: ppid });
      assert(metadata);
      const json = await cacheJSON(`repo-${ppid}-${metadata.etag}`, () => s3objects.repositories.get({ projectPublicId: ppid }));
      return Git.Repository.deserialize(json, 0) ?? Git.Repository.createEmpty(0);
    }
  });

  private _s3objectsPromise: Promise<S3Objects>|undefined;

  async getS3Objects() {
    if (!this._s3objectsPromise) {
      this._s3objectsPromise = Promise.resolve().then(async () => {
        const config = await S3.configFromEnvOrDie()
        const s3data = await S3.initialize(config);
        return new S3Objects(s3data);
      })
    }
    return this._s3objectsPromise;
  }

  async getReportIndex(ppid: ProjectPublicId): Promise<ReportIndex.ReportIndex> {
    return await this._cacheReportIndexes.get(ppid) ?? new ReportIndex.ReportIndex(ppid, undefined);
  }

  async getRepository(ppid: ProjectPublicId): Promise<Git.Repository> {
    return await this._cacheRepositories.get(ppid) ?? Git.Repository.createEmpty(0);
  }

  async statsJSONData(s3id: S3Stats.Id): Promise<Stats.JSONData|undefined> {
    return await this._cacheStatsJSONData.get(s3id);
  }

  async getStatsAnalyzer(s3id: S3Stats.Id): Promise<StatsAnalyzer> {
    return await this._cacheStatsAnalyzers.get(s3id) ?? new StatsAnalyzer(Stats.createEmptyStats());
  }
}

export async function extractXMLFiles(zipDirectory: Buffer): Promise<{ path: string, content: string}[]> {
  // 2. Use unzipper to open the buffer
  const xmlFiles = [];
  const directory = await unzipper.Open.buffer(zipDirectory);

  // 3. Loop through the file entries and extract XML content
  for (const file of directory.files) {
    const fileName = file.path;
    if (fileName.endsWith('.xml')) {
      const contentBuffer = await file.buffer(); // Get content for this file
      xmlFiles.push({
        path: fileName,
        content: contentBuffer.toString('utf-8'),
      });
    }
  }
  return xmlFiles;
}

export async function cacheJSON<T extends {}>(name: string, fetcher: () => Promise<T|undefined>): Promise<T|undefined> {
  if (!hasPrintedMessage) {
    hasPrintedMessage = true;
    console.log(chalk.gray(`Using ${path.relative(process.cwd(), TEST_ARTIFACTS)} to store test artifacts.`));
  }
  const filepath = path.join(TEST_ARTIFACTS, `__downloaded__`, name);
  if (fs.existsSync(filepath)) {
    const buffer = await fs.promises.readFile(filepath);
    const text = brotliDecompressSync(buffer).toString('utf-8');
    return JSON.parse(text) as T;
  }
  await fs.promises.mkdir(path.dirname(filepath), { recursive: true }).catch(e => {});
  console.log(`Downloading from S3: ${name}`);
  const data = await fetcher();
  console.log(`Downloaded: ${name}`);
  assert(data);
  const text = JSON.stringify(data);
  const buffer = compressTextSync(text);
  await fs.promises.writeFile(filepath, buffer);
  return data;
}

export async function downloadArtifactIsMissing(url: string) {
  if (!hasPrintedMessage) {
    hasPrintedMessage = true;
    console.log(chalk.gray(`Using ${path.relative(process.cwd(), TEST_ARTIFACTS)} to store test artifacts.`));
  }
  const parsed = new URL(url);
  const filepath = path.join(TEST_ARTIFACTS, `__downloaded__`, parsed.pathname);
  if (fs.existsSync(filepath))
    return filepath;
  await fs.promises.mkdir(path.dirname(filepath), { recursive: true }).catch(e => {});
  const response = await fetch(url);
  if (!response.ok)
    throw new Error(`Failed to fetch: ${response.status} ${response.statusText}`);
  const body = response.body as ReadableStream;
  if (!body)
    throw new Error(`response has no body`);
  await fs.promises.mkdir(path.dirname(filepath), { recursive: true });
  const fileStream = fs.createWriteStream(filepath);
  await finished(Readable.fromWeb(body).pipe(fileStream));
  return filepath;
}

export function artifactPath(name: string) {
  return path.join(TEST_ARTIFACTS, name);
}

const IS_ALMOST_POSIX_PATH = new RegExp("^[a-zA-Z]:/", "gi");

function findGitRoot(gitRoot: string) {
  const aPath = gitRoot.startsWith('/') || IS_ALMOST_POSIX_PATH.test(gitRoot) ? path.posix : path.win32;

  while (aPath.basename(gitRoot) !== 'tests')
    gitRoot = aPath.dirname(gitRoot);
  return aPath.dirname(gitRoot);
}

export type EnhancedJSONReport = JSONReport & {
  metadata: {
    gitRoot: string,
    uuid: string,
    osName: string,
    arch: string,
    osVersion: string,
    runURL?: string,
    commitTimestamp: string,
    commitSHA: string,
  }
};

export async function parseMicrosoftPlaywrightReport(pwReport: EnhancedJSONReport): Promise<FlakinessReport.Report> {
  const result = await PlaywrightJSONReport.parse(
    {
      arch: pwReport.metadata.arch,
      runURL: pwReport.metadata.runURL,
      commitId: pwReport.metadata.commitSHA,
      gitRoot: normalizePath(findGitRoot(pwReport.config.rootDir)),
      osName: pwReport.metadata.osName,
      osVersion: pwReport.metadata.osVersion,
    }, pwReport, {
      extractAttachments: false,
    }
  );
  return result.report;
}

export async function loadJSON(filepath: string) {
  let bytes = await fs.promises.readFile(filepath);
  if (filepath.endsWith('.gz'))
    bytes = gunzipSync(bytes);
  else if (filepath.endsWith('.br'))
    bytes = brotliDecompressSync(bytes);
  return JSON.parse(bytes.toString('utf-8'));
}

export async function ensurePlaywrightJSONStats(commits: {
  commitId: string,
  url: string,
}[]): Promise<{
  tests: Stats.JSONTests,
  stats: Stats.JSONData,
  commitIds: Stats.CommitId[],
}> {
  let runId = 0 as Stats.RunId;
  const commitIds = commits.map(commit => commit.commitId as Stats.CommitId);
  const statsPath = artifactPath(`stats-${xxHash(commitIds)}.json`);
  const testsPath = artifactPath(`tests-${xxHash(commitIds)}.json`);
  try {
    const [tests, stats] = await Promise.all([
      fs.promises.readFile(testsPath, 'utf-8').then(text => JSON.parse(text) as Stats.JSONTests),
      fs.promises.readFile(statsPath, 'utf-8').then(text => JSON.parse(text) as Stats.JSONData),
    ]);
    assert(stats.version === Stats.STATS_VERSION);
    return { tests, stats, commitIds };
  } catch (e) {
    // if versions mismatch or if files are missing, then re-build everything.
    console.log(`stats and project info are missing - rebuilding for ${commitIds.length} commits`);
    const testIndex = new TestIndex();
    const statsBuilder = StatsBuilder.create(testIndex);

    for (let i = 0; i < commits.length; ++i) {
      const commitId = commits[i].commitId;
      const url = commits[i].url;

      console.log(`commit ${i + 1}/${commitIds.length} ${commitId.substring(0, 7)}`);
      console.time(`- downloaded`);
      const filepath = await downloadArtifactIsMissing(url);
      console.timeEnd(`- downloaded`);
      console.time(`- parsed`);
      const pwReports = await loadJSON(filepath).catch(e => {
        console.error(`error while loading json: ${filepath}`);
        console.error(e);
        return [];
      }) as EnhancedJSONReport[];
      console.timeEnd(`- parsed`);
      console.time(`- processed ${pwReports.length} reports`);
      for (const pwReport of pwReports) {
        const fkReport = await parseMicrosoftPlaywrightReport(pwReport);
        testIndex.addReport(fkReport);
        statsBuilder.addRun(++runId as Stats.RunId, fkReport);
      }
      console.timeEnd(`- processed ${pwReports.length} reports`);
    }

    const stats = statsBuilder.jsonStats();
    const tests = testIndex.serialize();
    await Promise.all([
      fs.promises.writeFile(statsPath, JSON.stringify(stats)),
      fs.promises.writeFile(testsPath, JSON.stringify(tests)),
    ]);
    return { tests, stats, commitIds };
  }
}
