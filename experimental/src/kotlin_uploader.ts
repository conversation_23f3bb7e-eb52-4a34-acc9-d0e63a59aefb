#! /usr/bin/env node

import { FlakinessReport } from '@flakiness/report';
import { ReportUploader } from '@flakiness/sdk';
import { Services } from '@flakiness/server/node/services.js';
import { measure } from '@flakiness/shared/common/utils.js';
import assert from 'assert';
import { parseJUnit } from 'flakiness/junit';
import { Kysely, PostgresDialect } from 'kysely';
import ms from 'ms';
import pg from 'pg';
import { Teamcity } from './teamcity.js';
import { extractXMLFiles } from './utils.js';

const teamcity = new Teamcity('https://teamcity.jetbrains.com');
const PROJECT_IDS = ['Kotlin_KotlinPublic_Tests_Linux'];

// Database connection configuration
const backofficeDB = new Kysely<any>({
  dialect: new PostgresDialect({
    pool: new pg.Pool({
      host: process.env.BACKOFFICE_PGHOST,
      port: parseInt(process.env.BACKOFFICE_PGPORT || '5432', 10),
      database: process.env.BACKOFFICE_PGDATABASE,
      user: process.env.BACKOFFICE_PGUSER,
      password: process.env.BACKOFFICE_PGPASSWORD,
    }),
  }),
});

// Ensure table exists with primary key
await backofficeDB.schema
  .createTable('uploaded_report_ids')
  .ifNotExists()
  .addColumn('report_id', 'varchar(36)', col => 
    col.notNull().primaryKey()
  )
  .addColumn('added_timestamp_ms', 'bigint', col => 
    col.notNull()
  )
  .execute();

// Drop all reports that were uploaded more than 1 year ago.
await backofficeDB
  .deleteFrom('uploaded_report_ids')
  .where('added_timestamp_ms', '<', Date.now() - ms('365 days'))
  .execute();

// Fetch all report IDs from the last 30 days
const uploadedReportIds = new Set((await backofficeDB
  .selectFrom('uploaded_report_ids')
  .select('report_id')
  .where('added_timestamp_ms', '>', Date.now() - ms('30 days'))
  .execute()).map(x => x.report_id));

const config = await Services.configFromEnvOrDie();
const services = await Services.initializeOrDie(config);

function teamcityDate(raw: string) {
  return new Date(raw.replace(
    /^(\d{4})(\d{2})(\d{2})T(\d{2})(\d{2})(\d{2})([+-]\d{2})(\d{2})$/,
    '$1-$2-$3T$4:$5:$6$7:$8'
  ));
}

const org = await services.db.orgs.getBySlug('flakiness');
const project = org ? await services.db.projects.getBySlug(org.org_id, 'kotlin') : undefined;
if (!project)
  throw new Error('failed to find flakiness/kotlin project!');

const repo = await services.gitWorker.getRepo(project.project_public_id);
assert(repo);

// Fetch latest commits.
const provider = await services.gitWorker.getProvider(project);
await repo.fetch(provider, [], AbortSignal.timeout(ms('2 minutes'))).catch(e => void e);
const commits = repo.iterator(repo.defaultBranch().commit.commitId).collect({
  sinceTimestamp: (Date.now() - ms('7 days')) as FlakinessReport.UnixTimestampMS,
});

await services.dispose();

const uploader = new ReportUploader({
  flakinessAccessToken: project.flakiness_access_token,
  flakinessEndpoint: process.env.GITHUB_APP_CALLBACK_URL!,
});

for (const commit of commits) {
  const commitId = commit.commitId;
  try {
    const m = measure();
    console.log(`Start processing commit: ${commitId.substring(0, 8)}`);
    for (const projectId of PROJECT_IDS) {
      for await (let builds of teamcity.listBuilds(projectId, commitId)) {
        console.log(`loaded ${builds.length} builds`);
        for (const build of builds) {
          if (build.state !== 'finished')
            continue;
          if (uploadedReportIds.has(build.id))
            continue;
          const zip = await teamcity.fetchArtifact(build.id, 'internal/test_results.zip').catch(e => undefined);
          if (!zip) {
            console.log(`failed to find test results for build = ${build.id}`);
            continue;
          }
          const [xmlFiles, buildInfo] = await Promise.all([
            extractXMLFiles(zip),
            teamcity.fetchBuildInfo(build.id),
          ]);
          const start = teamcityDate(buildInfo.startDate);
          const end = teamcityDate(buildInfo.finishDate);
          const { report } = await parseJUnit(xmlFiles.map(file => file.content), {
            commitId,
            defaultEnv: { name: buildInfo.buildType.name },
            runStartTimestamp: +start as FlakinessReport.UnixTimestampMS,
            runDuration: (end.getTime() - start.getTime()) as FlakinessReport.DurationMS,
            runUrl: buildInfo.webUrl,
            ignoreAttachments: true,
          });
          const result = await uploader.createUpload(report, []).upload({ syncCompression: true });
          if (!result.success) {
            console.error(`- upload failed: ${result.message ?? '<unknown>'}`);
          } else {
            await backofficeDB
              .insertInto('uploaded_report_ids')
              .values([{
                report_id: build.id,
                added_timestamp_ms: Date.now(),
              }])
              .execute();
            console.log(`upload successful`);
          }
        }
      }
    }
  } catch (e: any) {
    console.log('Error when processing ' + commitId + ' ' + e.message);
    console.log(e.stack);
  }
}
await backofficeDB.destroy();
