import { Ranges } from "@flakiness/server/common/ranges.js";
import { Histogram } from "@flakiness/server/common/stats/histogram.js";
import { assert } from "console";

export function createSeededRandom(seed: number) {
  return function() {
    // A simple LCG (Linear Congruential Generator)
    seed = (seed * 1664525 + 1013904223) | 0;
    return (seed & 0x7FFFFFFF) / 0x80000000;
  };
}

export interface RangeStats {
  /** The total number of intervals. */
  count: number;
  /** The average interval length. */
  avgInterval: number;
  /** The average gap between consecutive intervals. */
  avgGap: number;
}

export function generateSortedList(options: { seed: number, count: number, density: number }) {
  const random = createSeededRandom(options.seed);
  const result: number[] = [];
  // This uses a bi of math to balance the accumulated density.
  // density = (count / size)
  // size = count / density;
  // avgGapSize = size / count = 1 / density
  // Let's compute density progressively.
  // if we generated X elements, and their density is Y.
  // and we also have TargetDensity.
  // THEN, we should compensate.

  // Basically: we need to generate N elements with density D. We already generated N1, with density D1.
  // density1 = (count1 / size1)
  // density2 = (count2 / size2)
  // (count1 + count2) / (size1 + size2) = D
  // size1 + size2 = (count1 + count2) / D
  // size2 = (count1 + count2) / D - size1 = count / D - size1
  // density2 = count2 / size2
  let last = 0;
  for (let i = 0; i < options.count; ++i) {
    const size2 = (options.count / options.density) - last;
    const density = (options.count - i) / size2;
    const avgGapLength = 1 / density;
    last += Math.floor((avgGapLength - 1) * random() * 2) + 1;
    result.push(last);
  }
  return result;
}

export function generateRanges(options: RangeStats & { seed: number }): Ranges.Ranges<number> {
  const { seed, count: intervalCount, avgInterval: avgIntervalLength, avgGap: avgGapLength } = options;
  const random = createSeededRandom(seed);
  const ranges: Ranges.Ranges<number> = [] as number[] as Ranges.Ranges<number>;

  let currentPos = 0;

  for (let i = 0; i < intervalCount; i++) {
    // 1. Determine the gap before the next interval
    // We use a random factor to vary the gap around the average.
    const gap = Math.floor(avgGapLength * random() * 2);
    const start = currentPos + gap;

    // 2. Determine the length of the new interval
    const length = Math.ceil(avgIntervalLength * random() * 2);
    // Ensure length is at least 1
    const end = start + Math.max(0, length - 1);

    ranges.push(start, end);

    // 4. Update the current position for the next iteration
    currentPos = end + 2;
  }

  assert(validateRanges(ranges));
  return ranges;
}

export function generateHistogram(options: {
  seed: number,
  elements: number[],
  buckets: number[],
  maxRelativeError: number,
  minRadius: number,
}): Histogram.Histogram {
  const random = createSeededRandom(options.seed);
  const scores = new Map<number, number>();
  for (const e of options.elements) {
    scores.set(e, options.buckets[Math.floor(options.buckets.length * random())]);
  }
  return Histogram.compute(scores, {
    maxRelativeError: options.maxRelativeError,
    minRadius: options.minRadius,
  });
}

export function validateRanges(a: Ranges.Ranges<number>) {
  if (a.length % 2 !== 0)
    return false;
  for (let i = 0; i < a.length; i += 2) {
    if (a[i] > a[i + 1])
      return false;
  }
  for (let i = 0; i < a.length - 2; i += 2) {
    if (a[i + 1] + 1 >= a[i + 2])
      return false;
  }
  return true;
}

export function computeRangeStats(a: Ranges.Ranges<number>): RangeStats {
  if (!a.length) {
    return {
      count: 0,
      avgInterval: 0,
      avgGap: 0,
    };
  }
  const size = Ranges.cardinality(a);
  const count = a.length >> 1;
  return {
    count: count,
    avgInterval: size / count,
    avgGap: (a[a.length - 1] - size) / count,
  };
}

export function computeStats(ranges: Ranges.Ranges<number>[]) {
  const intersect = Ranges.intersectAll(ranges);
  const union = Ranges.unionAll(ranges);
  return {
    count: ranges.reduce((acc, r) => acc + (r.length >>> 1), 0),
    overlapRatio: Ranges.cardinality(intersect) / Ranges.cardinality(union),
    union: computeRangeStats(union),
    intersection: computeRangeStats(intersect),
  };
}