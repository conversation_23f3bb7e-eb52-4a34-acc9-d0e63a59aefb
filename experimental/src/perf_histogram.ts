import { Ranges } from "@flakiness/server/common/ranges.js";
import { ExpandedHistogram, Histogram } from "@flakiness/server/common/stats/histogram.js";
import { MitataReporter, ratio2percent as p } from "./mitataReporter.js";
import { computeStats, generateHistogram, generateSortedList } from "./rangeUtils.js";

const m = new MitataReporter({
  name: `histogram`,
  configPath: import.meta.filename,
});

const PAIRS = {
  overlap_85p: {
    h1: generateHistogram({
      seed: 1823,
      buckets: generateSortedList({ count: 100, seed: 431, density: 0.12, }),
      elements: generateSortedList({ count: 400000, seed: 431, density: 0.88, }),
      maxRelativeError: 0,
      minRadius: 0,
    }),
    h2: generateHistogram({
      seed: 183,
      buckets: generateSortedList({ count: 100, seed: 4231, density: 0.12, }),
      elements: generateSortedList({ count: 400000, seed: 4343, density: 0.88, }),
      maxRelativeError: 0,
      minRadius: 0,
    }),
  },
  overlap_1p_many_buckets: {
    h1: generateHistogram({
      seed: 1823,
      buckets: generateSortedList({ count: 100, seed: 222, density: 0.12, }),
      elements: generateSortedList({ count: 400000, seed: 111, density: 0.88, }),
      maxRelativeError: 0,
      minRadius: 0,
    }),

    h2: generateHistogram({
      seed: 183,
      buckets: generateSortedList({ count: 100, seed: 333333, density: 0.12, }),
      elements: generateSortedList({ count: 4000, seed: 111, density: 0.88, }),
      maxRelativeError: 0,
      minRadius: 0,
    }),
  },
  overlap_1p_few_buckets: {
    h1: generateHistogram({
      seed: 1823,
      buckets: generateSortedList({ count: 100, seed: 222, density: 0.12, }),
      elements: generateSortedList({ count: 400000, seed: 111, density: 0.88, }),
      maxRelativeError: 0,
      minRadius: 0,
    }),
    h2: generateHistogram({
      seed: 183,
      buckets: generateSortedList({ count: 10, seed: 333333, density: 0.12, }),
      elements: generateSortedList({ count: 4000, seed: 111, density: 0.88, }),
      maxRelativeError: 0,
      minRadius: 0,
    }),
  },
  overlap_1p_1_bucket: {
    h1: generateHistogram({
      seed: 1823,
      buckets: generateSortedList({ count: 100, seed: 222, density: 0.12, }),
      elements: generateSortedList({ count: 400000, seed: 111, density: 0.88, }),
      maxRelativeError: 0,
      minRadius: 0,
    }),
    h2: generateHistogram({
      seed: 183,
      buckets: generateSortedList({ count: 1, seed: 333333, density: 0.12, }),
      elements: generateSortedList({ count: 400, seed: 111, density: 0.88, }),
      maxRelativeError: 0,
      minRadius: 0,
    }),
  }
}

for (const { h1, h2} of Object.values(PAIRS)) {
  const stats = computeStats([Histogram.elements(h1), Histogram.elements(h2)]);
  const h3 = Histogram.sum(h1, h2);
  m.bench(`sum(${p(stats.overlapRatio)}, ${h1.length}, ${h2.length})=${h3.length}`, function() {
    m.do_not_optimize(ExpandedHistogram.sumAll([h1, h2]));
  });
}

const histograms = new Array(14).fill(0).map((_, idx) => generateHistogram({
  seed: idx,
  buckets: generateSortedList({ count: 100, seed: idx, density: 0.12, }),
  elements: generateSortedList({ count: 200000, seed: idx, density: 0.88, }),
  maxRelativeError: 0,
  minRadius: 0,
}));

m.bench(`sum 14 histograms into expanded`, function*() {
  yield () => {
    m.do_not_optimize(ExpandedHistogram.sumAll(histograms));
  }
});

m.bench(`expand 14 histograms`, function*() {
  yield () => {
    for (const h of histograms)
      m.do_not_optimize(ExpandedHistogram.sumAll([h]));
  }
});

m.bench(`sum 14 expanded histograms`, function*() {
  const qs = histograms.map(h => ExpandedHistogram.sumAll([h]));
  yield () => {
    const offset = Math.min(...qs.map(e => e.offset));
    const max = Math.max(...qs.map(e => e.buffer.length + e.offset));
    const result = new Int32Array(max - offset);
    for (let i = 0; i < qs.length; ++i) {
      const h = qs[i];
      for (let j = 0; j < h.buffer.length; ++j)
        result[j + h.offset - offset] += h.buffer[j];
    }
    m.do_not_optimize(result);
  }
});

m.bench(`Histogram.get`, function*() {
  const h = generateHistogram({
    seed: 1823,
    buckets: generateSortedList({ count: 100, seed: 431, density: 0.12, }),
    elements: generateSortedList({ count: 200000, seed: 431, density: 0.88, }),
    maxRelativeError: 0,
    minRadius: 0,
  });

  const all = Ranges.toSortedList(Ranges.unionAll(h.map(bucket => bucket[1])));

  yield () => {
    for (let i = 0; i < all.length; ++i)
      m.do_not_optimize(Histogram.get(h, i));
  }
});

await m.run({});
