import assert from 'assert';
import fs from 'fs';
import { Session } from 'inspector';

export class V8CPUProfiler {
  private _session?: Session;

  private _post(method: string): Promise<any> {
    // Enable the Profiler domain
    return new Promise<any>((resolve, reject) => {
      this._session?.post(method, (err, value) => {
        if (err)
          reject(err);
        else
          resolve(value);
      });
    });
  }

  async start() {
    this._session = new Session();
    this._session.connect();

    await this._post('Profiler.enable');
    await this._post('Profiler.start');
    console.log(`Started CPU profiling`);
  }

  async stop(outputPath = './cpu-profile.json') {
    assert(this._session, 'Profiler not started');

    const result = await this._post('Profiler.stop');
    await this._post('Profiler.disable');

    // Disconnect the session
    this._session.disconnect();
    this._session = undefined;

    // Save profile to file
    fs.writeFileSync(outputPath, JSON.stringify(result.profile, null, 2));
    console.log(`CPU profile saved to: ${outputPath}`);
    console.log('You can load this file in Chrome DevTools > Performance tab');
  }
}