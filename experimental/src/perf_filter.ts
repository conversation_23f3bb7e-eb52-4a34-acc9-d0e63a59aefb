/**
 * Regenerate new results with the following command:
 *   npm run perf -- --md > experimental/perf_filter_results.md
 */
import { FlakinessReport } from '@flakiness/report';
import { SharedCacheStore } from '@flakiness/server/common/caches/cache.js';
import { LRUPolicy } from '@flakiness/server/common/caches/lruPolicy.js';
import { Query } from '@flakiness/server/common/fql/query.js';
import { CommitAnalyzer } from '@flakiness/server/common/stats/commitAnalyzer.js';
import { HistoryAnalyzer } from '@flakiness/server/common/stats/historyAnalyzer.js';
import { SpanAnalyzer } from '@flakiness/server/common/stats/spanAnalyzer.js';
import { Stats } from '@flakiness/server/common/stats/stats.js';
import { StatsAnalyzer } from '@flakiness/server/common/stats/statsAnalyzer.js';
import { TestOutcomes } from '@flakiness/server/common/stats/testOutcomes.js';
import { TimelineTestsReport } from '@flakiness/server/common/stats/timelineTestsReport.js';
import { TimelineSplit } from '@flakiness/server/common/timeline/timelineSplit.js';
import { WireTypes } from '@flakiness/server/common/wireTypes.js';
import { QueryService } from '@flakiness/server/node/queryService.js';
import { ServerTiming } from '@flakiness/server/node/serverTiming.js';
import assert from 'assert';
import chalk from 'chalk';
import { program } from 'commander';
import type * as mitata from 'mitata';
import { Temporal } from 'temporal-polyfill';
import { ProjectPublicId } from '../../database/src/database.js';
import { MitataReporter } from './mitataReporter.js';
import { CachedS3Storage, DAY_MS, utcFloor, wireUTCDays as wireUTCDaysReversed } from './utils.js';

const PROJECTS = {
  playwright: {
    ppid: '49e5534f-0b71-45d6-a275-5f1a350c874b' as ProjectPublicId,
    head: '18f147a8327bce3713d36e15ba7a7b32eb20e990' as FlakinessReport.CommitId,
    queries: ['', 'page-click', '$expect'],
  },
  kotlin: {
    ppid: 'e1510908-4385-491e-acb9-c245f4765621' as ProjectPublicId,
    head: 'a072b973fcd63f59d1ac2b0231710969ca5c234f' as FlakinessReport.CommitId,
    queries: [''],
  },
}

program
  .name('perf_filter')
  .description('Performance benchmarks for filter operations')
  .option('--profile', 'Record V8 CPU Profile')
  .option('-p, --project <projectName>', 'either Kotlin or Playwright to use as a test data. Defaults to Playwright')
  .option('-g, --grep <filter>', 'optional filter regex for benchmark names')
  .parse();

const options = program.opts();

const filter = options.grep ? new RegExp(options.grep, 'i') : undefined;
if (filter) {
  console.log(chalk.yellow(`Running tests matching ${filter}`));
}

const projectName = !options.project || !options.project.toLowerCase().startsWith('k') ? 'playwright' : 'kotlin';
const project = PROJECTS[projectName];
console.log(chalk.yellow(`Using ${chalk.bold(projectName)} test data`));

const cachedS3Storage = new CachedS3Storage();

async function loadProjectData(options: {
  head: string,
  ppid: ProjectPublicId,
}) {
  const [
    reportIndex,
    repo,
  ] = await Promise.all([
    cachedS3Storage.getReportIndex(options.ppid),
    cachedS3Storage.getRepository(options.ppid),
  ]);

  const head = repo.commit(options.head);
  assert(head);
  const days120Reversed = wireUTCDaysReversed({
    since: new Date(head.timestamp),
    until: new Date(head.timestamp),
    backfill: 120,
  });
  const it = repo.iterator(options.head);
  const dayCommits = days120Reversed.map(day => it.collect(day));
  const dayAnalyzers = await Promise.all(dayCommits.map(async commits => {
    return await Promise.all(commits.map(async commit => {
      const s3id = reportIndex.getShard(commit.commitId);
      if (!s3id)
        return CommitAnalyzer.createEmpty(commit.commitId);
      const statsAnalyzer = await cachedS3Storage.getStatsAnalyzer(s3id);
      return statsAnalyzer.getCommitAnalyzer(commit.commitId);
    }));
  }));

  const shardId = reportIndex.getShard(head.commitId);
  assert(shardId);
  const jsonStats = await cachedS3Storage.statsJSONData(shardId);
  assert(jsonStats);

  const day7CommitOptions: WireTypes.ListCommitOptions = {
    head: head.commitId,
    sinceTimestamp: +utcFloor(new Date(head.timestamp)) - DAY_MS * 7 as FlakinessReport.UnixTimestampMS,
  };
  const commits = repo.iterator(head.commitId).collect(day7CommitOptions);

  const shards = reportIndex.splitIntoShards(commits);
  const analyzers = (await Promise.all(shards.map(async (s3id, commits) => {
    const statsAnalyzer = await cachedS3Storage.getStatsAnalyzer(s3id);
    return [...commits].map(commit => statsAnalyzer.getCommitAnalyzer(commit.commitId));
  }))).flat();
  const envs = CommitAnalyzer.runEnvironments(analyzers);
  return {
    ppid: options.ppid,
    repo,
    days120Reversed: days120Reversed,
    reportIndex,
    head,
    commits,
    dayAnalyzers,
    jsonStats,
    day7CommitOptions: day7CommitOptions,
    split: TimelineSplit.DEFAULT,
    envs,
    timelines: TimelineSplit.DEFAULT.timelines(envs),
  };
}

const data = await loadProjectData(project);
console.log(chalk.grey(`Loaded ${data.commits.length} commits, ${data.envs.length} envs and ${data.timelines.length} timelines`));

const commitIds = data.jsonStats.commits.map(c => c.commitId);

const m = new MitataReporter({
  name: `tests`,
  configPath: import.meta.filename,
  envOptions: {
    projectName,
    splitType: 'large',
  },
})

m.group(`StatsAnalyzer`, '', () => {
  m.bench(`init`, function* (state: mitata.k_state) {
    yield () => {
      const stats = new StatsAnalyzer(data.jsonStats);
      m.do_not_optimize(stats);
    }
  });
});

m.group(`CommitAnalyzer`, `${commitIds.length} commits x ${data.timelines.length} timelines`, () => {
  m.bench(`init ${commitIds.length} commits`, function* (state: mitata.k_state) {
    yield () => {
      const stats = new StatsAnalyzer(data.jsonStats);
      const commits = commitIds.map(commitId => stats.getCommitAnalyzer(commitId));
      m.do_not_optimize(commits);
    }
  });
  m.bench(`.runEnvironments`, function* (state: mitata.k_state) {
    const stats = new StatsAnalyzer(data.jsonStats);
    const commits = commitIds.map(commitId => stats.getCommitAnalyzer(commitId));
    yield () => {
      const result = CommitAnalyzer.runEnvironments(commits);
      m.do_not_optimize(result);
    }
  });
  m.bench(`.runEnvironmentsForTest`, function* (state: mitata.k_state) {
    const stats = new StatsAnalyzer(data.jsonStats);
    const commits = commitIds.map(commitId => stats.getCommitAnalyzer(commitId));
    yield () => {
      const result = CommitAnalyzer.runEnvironmentsForTest(commits, 0 as Stats.TestIndex);
      m.do_not_optimize(result);
    }
  });
  m.bench(`.timelineTestReport`, function* (state: mitata.k_state) {
    const stats = new StatsAnalyzer(data.jsonStats);
    const commits = commitIds.map(commitId => stats.getCommitAnalyzer(commitId));
    const span = new SpanAnalyzer(commits);
    yield () => {
      const result = data.timelines.map(timeline => TimelineTestsReport.create(timeline, span, TestOutcomes.EMPTY_TESTS));
      m.do_not_optimize(result);
    }
  });
});

m.group('TimelineTestReport', `${commitIds.length} commits x ${data.timelines.length} timelines`, () => {
  m.bench(`.filter($q)`, function* (state: mitata.k_state) {
    const stats = new StatsAnalyzer(data.jsonStats);
    const commits = commitIds.map(commitId => stats.getCommitAnalyzer(commitId));
    const span = new SpanAnalyzer(commits);
    const reports = data.timelines.map(timeline => TimelineTestsReport.create(timeline, span, TestOutcomes.EMPTY_TESTS));
    const filter = data.reportIndex.testIndex().createFilterContext(Query.parse(state.get('q')));
    yield () => {
      m.do_not_optimize(reports.map(r => r.applyFilter(filter)));
    }
  }).args({
    'q': project.queries
  });
})

m.group(`HistoryAnalyzer`, `${data.dayAnalyzers.length} days x ${data.timelines.length} timelines`, () => {
  m.bench(`.dayStats`, function* () {
    const historyAnalyzer = new HistoryAnalyzer(data.dayAnalyzers.map(d => new SpanAnalyzer(d)), 5);
    yield () => {
      const result = data.timelines.map(timeline => {
        return data.dayAnalyzers.map((day, dayIdx) => historyAnalyzer.dayStats(timeline, dayIdx, false));
      });
      m.do_not_optimize(result);
    }
  });
  m.bench(`.commitStats`, function* () {
    const historyAnalyzer = new HistoryAnalyzer(data.dayAnalyzers.map(d => new SpanAnalyzer(d)), 5);
    yield () => {
      const result = data.timelines.map(timeline => {
        return data.commits.map(commit => {
          return historyAnalyzer.commitStats(timeline, commit.commitId, false);
        })
      });
      m.do_not_optimize(result);
    }
  });
});

m.group(`QueryService`, ``, () => {
  const queryService = new QueryService(
    new SharedCacheStore({ policy: new LRUPolicy(Infinity) }),
    new ServerTiming(),
    ppid => cachedS3Storage.getReportIndex(ppid),
    s3id => cachedS3Storage.getStatsAnalyzer(s3id),
    ppid => cachedS3Storage.getRepository(ppid),
  );

  m.bench(`testsReport`, function* (state: mitata.k_state) {
    yield async () => {
      const queryService = new QueryService(
        new SharedCacheStore({ policy: new LRUPolicy(Infinity) }),
        new ServerTiming(),
        ppid => cachedS3Storage.getReportIndex(ppid),
        s3id => cachedS3Storage.getStatsAnalyzer(s3id),
        ppid => cachedS3Storage.getRepository(ppid),
      );
      const report = await queryService.testsReport(data.ppid, {
        commitOptions: data.day7CommitOptions,
        timeZoneId: 'UTC',
        regressionWindowDays: 30,
        timelineSplit: data.split.serialize(),
        dailyReportBreakdown: true,
        historyBuckets: 0,
        acceptableFlakinessRate: 0.015,
      });
      m.do_not_optimize(report);
      m.do_not_optimize(report.tests.page({
        number: 50,
        size: 20,
      }, 'name', 'desc'));
    }
  });
  m.bench(`reportCounters`, function* (state: mitata.k_state) {
    yield async () => {
      const counters = await queryService.reportCounters(data.ppid, {
        commitOptions: data.day7CommitOptions,
        timeZoneId: 'UTC',
        regressionWindowDays: 30,
        timelineSplit: data.split.serialize(),
      });
      m.do_not_optimize(counters);
    }
  });
  m.bench(`commitsReport`, function* (state: mitata.k_state) {
    const timelineSplit = data.split.serialize();
    yield async () => {
      m.do_not_optimize(await queryService.commitsReport(data.ppid, {
        commitOptions: data.day7CommitOptions,
        timeZoneId: 'UTC',
        regressionWindowDays: 30,
        timelineSplit,  
      }));
    }
  });
  m.bench(`runEnvironments`, function* (state: mitata.k_state) {
    yield async () => {
      const envs = await queryService.runEnvironments(data.ppid, data.day7CommitOptions);
      m.do_not_optimize(envs);
    }
  });
  m.bench(`dailyOutcomes`, function* (state: mitata.k_state) {
    const headDay = Temporal.Instant.fromEpochMilliseconds(data.head.timestamp).toZonedDateTimeISO('UTC').toPlainDate().add({ days: 1 });
    const sinceDay = headDay.subtract({ days: 90 });
    yield async () => {
      const result = await queryService.dailyOutcomes(data.ppid, {
        timeZoneId: 'UTC',
        head: data.head.commitId,
        acceptableFlakinessRatio: 1.5,
        regressionWindowDays: 30,
        sinceDay: sinceDay,
        untilDay: headDay,
        timelineSplit: data.split.serialize(),
      });
      m.do_not_optimize(result);
    }
  });
});

await m.run({
  grep: filter,
  v8profile: options.profile,
});
