# Database

This package implements flakiness.io database: access, migrations, API.

The API is available as an `@flakiness/database` import:

```ts
import { Database } from '@flakiness/database';
```

There's also a CLI that, once compiled, is accessible like this:

```bash
//database/lib/cli.ts --help
```

### Adding migrations

To add a migration:

1. Add a new file to the `/src/migrations/...`. Make sure to specify
   a proper number to specify migration order.
2. Implement migration
3. Regenerate Typescript types:

    ```bash
    //database/lib/cli.ts gen
    ```

4. Once all ready, you can migrate production database; note though that
   prod servers have to be stopped before running the migration.

    ```bash
    //prod/dbmigrate.sh
    ```

