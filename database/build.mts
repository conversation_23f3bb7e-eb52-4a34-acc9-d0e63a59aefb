#!/usr/bin/env npx kubik

import path from 'path';
import esbuild from 'esbuild';
import fs from 'fs';
import { Task } from 'kubik';
import { glob } from 'glob';

const { __dirname, $ } = Task.init(import.meta, {
  name: 'database-build',
  watch: [ './src', './package.json', 'tsconfig.json', './tests' ],
  deps: [ '../shared/build.mts' ],
});

const outDir = path.join(__dirname, 'lib');
const srcDir = path.join(__dirname, 'src');
const typesDir = path.join(__dirname, 'types');
await fs.promises.rm(outDir, { recursive: true, force: true });
await fs.promises.rm(typesDir, { recursive: true, force: true });

// 1. build types generator
const result = esbuild.buildSync({
  color: true,
  entryPoints: [
    path.join(srcDir, '**/*.ts'),
  ],
  packages: 'external',
  outdir: outDir,
  format: 'esm',
  platform: 'node',
  target: ['node22'],
  sourcemap: true,
  bundle: false,
  minify: false,
});
if (result.errors.length)
  process.exit(1);

// 3. lint
await $`tsc --pretty -p .`;
