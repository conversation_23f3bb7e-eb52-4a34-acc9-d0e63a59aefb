{"include": ["src", "tests"], "exclude": ["lib"], "compilerOptions": {"allowSyntheticDefaultImports": true, "experimentalDecorators": true, "composite": true, "declarationMap": true, "declaration": true, "allowJs": true, "checkJs": true, "strict": true, "target": "ESNext", "isolatedModules": true, "moduleResolution": "nodenext", "esModuleInterop": true, "module": "nodenext", "lib": ["esnext", "dom"], "outDir": "./types"}, "references": [{"path": "../shared"}]}