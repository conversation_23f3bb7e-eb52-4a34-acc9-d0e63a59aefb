import { defineConfig } from '@playwright/test';
import path from 'path';
import url from 'url';
import type { FixtureOptions } from './tests/fixtures.js';

const __filename = url.fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

export default defineConfig<FixtureOptions>({
  fullyParallel: true,
  reporter: [
    ['list'],
    ['@flakiness/sdk/playwright-test'],
  ],
  retries: 2,
  projects: [{
    name: 'postgres',
    testDir: './tests',
    use: {
    }
  }]
});
