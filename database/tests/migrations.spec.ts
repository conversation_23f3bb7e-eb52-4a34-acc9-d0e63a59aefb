import { expect } from '@playwright/test';
import { Kysely } from 'kysely';
import { DB as DB_002 } from '../src/generated/002_queues_types.js';
import { DB as DB_003 } from '../src/generated/003_github_access_tokens_types.js';
import { DB as DB_004 } from '../src/generated/004_organizations_types.js';
import { DB as DB_005 } from '../src/generated/005_project_slug_types.js';
import { migrationTest } from './fixtures.js';

migrationTest('001_initial', async ({ rollToMigration }) => {
  const { db, migration } = await rollToMigration('001_initial');
  await migration.migration.up(db);
  expect((await db.introspection.getTables()).length).toBe(4);
  await migration.migration.down?.call(null, db);
  expect((await db.introspection.getTables()).length).toBe(0);
});

migrationTest('002_queues', async ({ rollToMigration }) => {
  const { db, migration } = await rollToMigration('002_queues');
  await migration.migration.up(db);
  expect((await db.introspection.getTables()).length).toBe(7);
  await migration.migration.down?.call(null, db);
  expect((await db.introspection.getTables()).length).toBe(4);
});

migrationTest('003_github_access_token', async ({ rollToMigration }) => {
  const { db, migration } = await rollToMigration<DB_002>('003_github_access_tokens');
  expect(await db
    .selectFrom('personal_access_tokens')
    .select('value')
    .where('project_id', '=', 'project-1')
    .executeTakeFirst()
  ).toEqual({
    value: 'pat-foobarbaz',
  });
  await migration.migration.up(db);
  expect(await (db as any as Kysely<DB_003>)
    .selectFrom('personal_access_tokens')
    .select('pat_encoded')
    .where('project_id', '=', 'project-1')
    .executeTakeFirst()
  ).not.toEqual({
    pat_encoded: 'pat-foobarbaz',
  });
  await migration.migration.down!(db);
  expect(await db
    .selectFrom('personal_access_tokens')
    .select('value')
    .where('project_id', '=', 'project-1')
    .executeTakeFirst()
  ).toEqual({
    value: 'pat-foobarbaz',
  });
});


migrationTest('004_organizations', async ({ rollToMigration }) => {
  const { db, migration } = await rollToMigration<DB_004>('004_organizations');
  await migration.migration.up(db);
  expect((await db.introspection.getTables()).length).toBe(11);
  // Check that there are organizations for users with users as owners
  expect((await db
    .selectFrom('organizations')
    .select(['org_name', 'org_slug', 'owner_id'])
    .execute()).sort((a, b) => a.owner_id < b.owner_id ? -1 : 1)
  ).toEqual([{
    org_name: 'john',
    org_slug: 'john',
    owner_id: 'user-1',
  }, {
    org_name: 'jane',
    org_slug: 'jane',
    owner_id: 'user-2',
  }]);
  // Check that project now belongs to proper organization
  expect((await db
    .selectFrom('projects')
    .innerJoin('organizations', 'organizations.org_id', 'projects.org_id')
    .select(['projects.project_name', 'org_name'])
    .where('projects.project_id', '=', 'project-1')
    .executeTakeFirst())
  ).toEqual({
    project_name: 'flakiness',
    org_name: 'john',
  });

  // Rollback.
  await migration.migration.down?.call(null, db);

  // Make sure the project now belongs back to user 1
  expect((await (db as unknown as Kysely<DB_003>)
    .selectFrom('projects')
    .select(['projects.owner_id'])
    .where('projects.project_id', '=', 'project-1')
    .executeTakeFirst())
  ).toEqual({
    owner_id: 'user-1',
  });
  expect((await db.introspection.getTables()).length).toBe(9);
});

migrationTest('005_project_slug', async ({ rollToMigration }) => {
  const { db, migration } = await rollToMigration<DB_005>('005_project_slug');
  await migration.migration.up(db);
  // Check that project now has a slug
  expect((await db
    .selectFrom('projects')
    .select(['project_name', 'project_slug'])
    .execute()).sort((a, b) => a.project_name < b.project_name ? -1 : 1)
  ).toEqual([{
    project_name: 'flakiness',
    project_slug: 'flakiness',
  }]);

  // Check that project now has a visibility
  expect(await db
    .selectFrom('projects')
    .select(['project_name', 'visibility'])
    .execute()
  ).toEqual([{
    project_name: 'flakiness',
    visibility: null,
  }]);

  await (migration.migration as any).mock(db);

  // Check that project now has a visibility
  expect(await db
    .selectFrom('projects')
    .select(['project_name', 'visibility'])
    .execute()
  ).toEqual([{
    project_name: 'flakiness',
    visibility: 777,
  }]);

  // Rollback.
  await migration.migration.down?.call(null, db);
});
