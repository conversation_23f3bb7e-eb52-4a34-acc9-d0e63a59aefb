import { test as baseTest } from '@playwright/test';
import assert from 'assert';
import { Kysely, MigrationInfo, NO_MIGRATIONS } from 'kysely';
import { Postgres } from 'podkeeper';
import { Database, DatabaseConfig } from '../src/database.js';
import { QueueWorker } from '../src/queue.js';

export type FixtureOptions = {
};

const TEST_ENCRYPTION_KEY = 'fkdb_696b00949e27d55714efe9f15336addf7b1a924ebc011a346868d788a00a4215';
process.env.DB_ENCRYPTION_KEY = TEST_ENCRYPTION_KEY;

export const dbTest = baseTest.extend<{
  db: Database,
  cleanupWorker: (worker: QueueWorker<any>) => void,
}, FixtureOptions & { service: Postgres }>({

  service: [async ({  }, use) => {
    const service = await Postgres.start();
    await use(service);
    await service.stop();
  }, { scope: 'worker' }],

  cleanupWorker: async ({ db }, use) => {
    const workers: QueueWorker<any>[] = [];
    await use((worker) => {
      workers.push(worker);
    });
    await Promise.all(workers.map(w => w.stop()));
  },

  db: async ({ service }, use) => {
    const dbConfig: DatabaseConfig = {
      ...service.connectOptions(),
      encryptionKey: TEST_ENCRYPTION_KEY,
    };
    const rollup = await Database.migrate(dbConfig, async migrator => migrator.migrateToLatest());
    if (rollup.error)
      throw rollup.error;
    const db = await Database.initialize(dbConfig);
    await use(db);
    await db.close();
    const rollback = await Database.migrate(dbConfig, async migrator => migrator.migrateTo(NO_MIGRATIONS));
    if (rollback.error)
      throw rollback.error;
  }
});

export const migrationTest = baseTest.extend<{ rollToMigration: <T>(name: string) => Promise<{ db: Kysely<T>, migration: MigrationInfo }> }, FixtureOptions>({

  rollToMigration: async ({ }, use) => {
    const service = await Postgres.start();

    const dbConfig: DatabaseConfig = {
      ...service.connectOptions(),
      encryptionKey: TEST_ENCRYPTION_KEY,
    };

    const { db, migrator } = Database.createKyselyForTest(dbConfig);

    await use(async <T>(name: string) => {
      const migrations = await migrator.getMigrations();
      const index = migrations.findIndex(m => m.name === name);
      assert(index !== -1, `cannot find migration "${name}"`);
      for (const { migration } of migrations.slice(0, index)) {
        await migration.up(db);
        await (migration as any).mock?.call(migration, db);
      }
      return { db: db as unknown as Kysely<T>, migration: migrations[index] };
    });
    const rollback = await migrator.migrateTo(NO_MIGRATIONS);
    await db.destroy();
    await service.stop();
    if (rollback.error)
      throw rollback.error;
  }
});


export const muTest = baseTest.extend<{ db: Kysely<any> }, FixtureOptions>({
  db: async ({  }, use) => {
    const service = await Postgres.start();

    const dbConfig: DatabaseConfig = {
      ...service.connectOptions(),
      encryptionKey: TEST_ENCRYPTION_KEY,
    };

    const { db, migrator } = Database.createKyselyForTest(dbConfig);

    await use(db);
    await db.destroy();
    await service.stop();
  }
});

