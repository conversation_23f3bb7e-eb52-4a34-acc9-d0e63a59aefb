import { expect, test } from '@playwright/test';
import { Kysely } from 'kysely';
import { mu } from '../src/migrations/mu.js';
import { muTest } from './fixtures.js';

async function listTables(db: Kysely<any>) {
  const rawTables = await db.introspection.getTables();
  return Object.fromEntries(rawTables.map(table => [table.name, Object.fromEntries(table.columns.map(column => [column.name, {
    null: column.isNullable,
    inc: column.isAutoIncrementing,
    type: column.dataType,
  }]))]));
}

async function listTableTypes(db: Kysely<any>) {
  const rawTables = await db.introspection.getTables();
  return Object.fromEntries(rawTables.map(table => [table.name, Object.fromEntries(table.columns.map(column => [column.name, column.dataType]))]));
}

test.describe('mu.edit', () => {
  test('add/remove tables', () => {
    expect(mu.edit({
      foo: {
        columns: { id1: { type: 'serial' } },
        indexes: {
          'id1': { columns: ['id1'] },
        }
      },
      bar: {
        columns: { id2: { type: 'text', is: ['not-null'] } },
      },
    }, {
      bar: undefined,
      baz: {
        columns: { id3: { type: 'text', is: ['not-null'] } },
      },
    })).toEqual({
      foo: {
        columns: { id1: { type: 'serial' } },
        indexes: {
          'id1': { columns: ['id1'] },
        }
      },
      baz: {
        columns: { id3: { type: 'text', is: ['not-null'] } },
      },
    });
  });

  test('add/remove index', () => {
    expect(mu.edit({
      foo: {
        columns: { id1: { type: 'serial' } },
        indexes: {
          'id1': { columns: ['id1'] },
          'id2': { columns: ['id2'] },
        }
      },
    }, {
      foo: {
        columns: {},
        indexes: {
          'id1': undefined,
          'id3': { columns: ['id3'] },
        }
      },
    })).toEqual({
      foo: {
        columns: { id1: { type: 'serial' } },
        indexes: {
          'id1': undefined,
          'id2': { columns: ['id2'] },
          'id3': { columns: ['id3'] },
        },
        uniques: {},
      },
    });
  });
})

muTest('should add/remove tables', async ({ db }) => {
  const schema: mu.Schema<any> = {
    people: {
      columns: {
        id: { type: 'serial', is: ['primary'] },
        name: { type: 'text', is: ['not-null'] },
      }
    }
  };
  await mu.transform(db, {}, schema);
  expect(await listTables(db)).toEqual({
    people: {
      id: {
        type: 'int4',
        null: false,
        inc: true, 
      },
      name: {
        type: 'text',
        null: false,
        inc: false,
      }
    }
  });
  await mu.transform(db, schema, {});
  expect(await listTables(db)).toEqual({});
});

muTest('should properly infer types of foreign keys', async ({ db }) => {
  const base: mu.Schema<any> = {
    people: {
      columns: {
        name: { type: 'varchar(10)' },
        id: { type: 'varchar(255)', is: ['primary'] },
      }
    },
    pet: {
      columns: {
        name: { type: 'varchar(10)' },
        id: { ref: 'people.id', onDelete: 'cascade' }
      },
    }
  };
  await mu.transform(db, {}, base);
  expect(await listTableTypes(db)).toEqual({
    people: { id: 'varchar', name: 'varchar' },
    pet: { id: 'varchar', name: 'varchar' }
  });

  await mu.transform(db, base, mu.edit(base, {
    people: {
      columns: {
        // change PK type from text to integer
        id: { type: 'integer', is: ['primary'] },
      }
    }
  }));
  expect(await listTableTypes(db)).toEqual({
    people: { id: expect.stringContaining('int'), name: 'varchar' },
    pet: { id: expect.stringContaining('int'), name: 'varchar' },
  });
});

muTest('should allow creating & filling non-nullable columns', async ({ db }) => {
  const base: mu.Schema<any> = {
    people: {
      columns: {
        id: { type: 'serial', is: ['primary'] },
      }
    }
  };

  await mu.transform(db, {}, base, async db => {
    await db.insertInto('people').values([{ id: 5 }]).execute();
  });

  // New schema adds a not-null column.
  const schema = mu.edit(base, {
    people: {
      columns: {
        name: { type: 'text', is: ['not-null'] },
      }
    }
  });

  // We have to fill in data for not-null to work/
  await mu.transform(db, base, schema, async db => {
    await db.updateTable('people').set({ name: 'foo' }).execute();
  });
  expect(await db.selectFrom('people').selectAll().execute()).toEqual([{
    name: 'foo', id: 5
  }]);

  // Now let's roll back and try doing migration without data - it should throw since
  // adding not-null column without filling it in is impossible.
  // BEWARE: MySQL's DDL does IMPLICIT COMMIT - so these cannot be rolled back.
  // As a result, we have to have this as a very last line in the test.
  await mu.transform(db, schema, base);
  let error;
  await mu.transform(db, base, schema).catch(e => error = e);
  expect(error).toBeTruthy();
});

muTest('should allow changing pk type to serial', async ({ db }) => {
  // First schema has TEXT PK
  const base: mu.Schema<any> = {
    people: {
      columns: {
        id: { type: 'varchar(255)', is: ['primary'] },
        name: { type: 'varchar(255)', is: ['not-null'] },
      },
    }
  };
  await mu.transform(db, {}, base);
  await db.insertInto('people').values([
    { id: 'person-1', name: 'foo' },
    { id: 'person-2', name: 'bar' },
  ]).execute();

  // This schema changes the PK from TEXT to SERIAL
  const schema = mu.edit(base, {
    people: {
      columns: {
        id: { type: 'serial', is: ['primary'] },
      },
    }
  });
  await mu.transform(db, base, schema);

  // Check that IDs are populated
  expect(await db.selectFrom('people').selectAll().execute()).toEqual([
    { name: 'foo', id: 1, },
    { name: 'bar', id: 2, },
  ]);

  // Check that adding a new person auto-increments id
  await db.insertInto('people').values([
    { name: 'baz' }
  ]).execute();
  expect(await db.selectFrom('people').selectAll().where('name', '=', 'baz').execute()).toEqual([
    { name: 'baz', id: 3 }
  ]);
});

muTest('integer + auto-inc', async ({ db }) => {
  // First schema has TEXT PK
  const base: mu.Schema<any> = {
    people: {
      columns: {
        id: { type: 'varchar(255)', is: ['primary'] },
        name: { type: 'varchar(255)', is: ['not-null'] },
      },
    }
  };
  await mu.transform(db, {}, base);
  await db.insertInto('people').values([
    { id: 'person-1', name: 'foo' },
    { id: 'person-2', name: 'bar' },
  ]).execute();

  // This schema changes the PK from TEXT to SERIAL
  const schema = mu.edit(base, {
    people: {
      columns: {
        id: { type: 'integer', is: ['primary', 'auto-inc'] },
      },
    }
  });
  await mu.transform(db, base, schema);

  // Check that IDs are populated
  expect(await db.selectFrom('people').selectAll().execute()).toEqual([
    { name: 'foo', id: 1, },
    { name: 'bar', id: 2, },
  ]);

  // Check that adding a new person auto-increments id
  await db.insertInto('people').values([
    { name: 'baz' }
  ]).execute();
  expect(await db.selectFrom('people').selectAll().where('name', '=', 'baz').execute()).toEqual([
    { name: 'baz', id: 3 }
  ]);
});