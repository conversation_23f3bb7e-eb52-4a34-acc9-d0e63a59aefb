import { ManualPromise } from '@flakiness/shared/common/manualPromise.js';
import { expect } from '@playwright/test';
import ms from 'ms';
import { setTimeout } from 'node:timers/promises';
import { QueueWorker } from '../src/queue.js';
import { dbTest } from './fixtures.js';

dbTest('should work', async ({ db, cleanupWorker }) => {
  const queue = await db.queues.createQueue<{ name: string }>('jobs');

  const mp = new ManualPromise<{ name: string }>();
  cleanupWorker(queue.createWorker('test worker', async ({ data }) => {
    mp.resolve(data);
  }, { pollIntervalMs: 100 }));
  const timeStart = Date.now();
  expect(await queue.send({ name: 'bar' })).toBe(true);
  await mp.promise;
  const timeEnd = Date.now();
  // This should be pretty fast.
  expect(timeEnd - timeStart).toBeLessThanOrEqual(500);
  expect(await mp.promise).toEqual({ name: 'bar' });
});

dbTest('should work with delayed jobs', async ({ db, cleanupWorker }) => {
  const queue = await db.queues.createQueue<{ name: string }>('jobs');

  const mp = new ManualPromise<{ name: string }>();
  cleanupWorker(queue.createWorker('test worker', async ({ data }) => {
    mp.resolve(data);
  }, { pollIntervalMs: 100 }));
  const timeStart = Date.now();
  expect(await queue.send({ name: 'bar' }, { delayMs: 1000 })).toBe(true);
  await mp.promise;
  const timeEnd = Date.now();

  expect(await mp.promise).toEqual({ name: 'bar' });
  expect(timeEnd - timeStart).toBeGreaterThanOrEqual(1000);
});

dbTest('should not fail when updating job delay for non-existing job', async ({ db }) => {
  const queue = await db.queues.createQueue<{ name: string }>('jobs');
  await queue.setJobDelay('job-1', 0);
});

dbTest('should update job delay', async ({ db, cleanupWorker }) => {
  const queue = await db.queues.createQueue<{ name: string }>('jobs');

  const mp = new ManualPromise<{ name: string }>();
  cleanupWorker(queue.createWorker('test worker', async ({ data }) => {
    mp.resolve(data);
  }, { pollIntervalMs: 100 }));
  const timeStart = Date.now();
  expect(await queue.send({ name: 'bar' }, { jobId: 'job-1', delayMs: 10000 })).toBe(true);
  await queue.setJobDelay('job-1', 0);
  await mp.promise;
  const timeEnd = Date.now();

  expect(await mp.promise).toEqual({ name: 'bar' });
  expect(timeEnd - timeStart).toBeLessThanOrEqual(500);
});

dbTest('multiple queues should not clash', async ({ db, cleanupWorker }) => {
  const catsQueue = await db.queues.createQueue<{ name: string }>('cats');
  const dogsQueue = await db.queues.createQueue<{ name: string }>('dogs');

  await catsQueue.send({ name: 'Mikki' });
  await dogsQueue.send({ name: 'Dera' });

  const cat = new ManualPromise<{ name: string }>();
  const dog = new ManualPromise<{ name: string }>();
  cleanupWorker(catsQueue.createWorker('test worker', async ({ data }) => { cat.resolve(data); }, { pollIntervalMs: 100 }));
  cleanupWorker(dogsQueue.createWorker('test worker', async ({ data }) => { dog.resolve(data); }, { pollIntervalMs: 100 }));

  expect(await cat.promise).toEqual({ name: 'Mikki' });
  expect(await dog.promise).toEqual({ name: 'Dera' });
});

dbTest('jobs with the same jobId should be processed sequentially', async ({ db, cleanupWorker }) => {
  const queue = await db.queues.createQueue<{ name: string }>('jobs');

  // Start 3 workers. One of them will pick up a job, and wait until the second job is added to the queue.
  const pauseWorkers = new ManualPromise<void>();
  const log: string[] = [];
  for (let i = 0; i < 2; ++i) {
    cleanupWorker(queue.createWorker('test worker', async (job, { signal }) => {
      await pauseWorkers.promise;
      log.push('started');
      await setTimeout(200, undefined, { signal });
      log.push('finished');
    }, { pollIntervalMs: 100 }));
  }

  // 1. Submit first job and wait for it to be picked up by worker.
  queue.send({ name: 'foo' }, { jobId: 'id-1 '});
  await new Promise(x => queue.testEvents.once('will-start-job', x));
  // 2. Submit second job. Now we have 2 jobs in queue, one in status active and one in pending.
  expect(await queue.send({ name: 'bar' }, { jobId: 'id-1 '})).toBe(true);
  // 3. Now we can resume workers and check their execution log.
  pauseWorkers.resolve();

  // 4. Wait for all jobs to be processed.
  await expect.poll(() => db.queues.listArchivedJobs(0, 50).then(({ count }) => count)).toEqual(2);

  expect(log).toEqual([
    'started',
    'finished',
    'started',
    'finished',
  ]);
});

dbTest('should retry failed jobs', async ({ db, cleanupWorker }) => {
  const MAX_RETRIES = 5;
  const queue = await db.queues.createQueue<{ name: string }>('jobs');

  let attempts = 0;
  cleanupWorker(queue.createWorker('test worker', async () => {
    ++attempts;
    throw new Error('i am failing!');
  }, { pollIntervalMs: 100, retries: MAX_RETRIES }));
  expect(await queue.send({ name: 'bar' })).toBe(true);
  await expect.poll(() => db.queues.listArchivedJobs(0,50).then(r => r.count)).toBe(1);
  expect(attempts).toBe(5 + 1);
  const { jobs } = await db.queues.listArchivedJobs(0, 50);
  expect(jobs.length).toBe(1);
  expect(jobs[0].execution_error).toContain('i am failing!');
  expect(jobs[0].execution_result).toEqual('failed');
});

dbTest('should process jobs if processor added after job submission', async ({ db, cleanupWorker }) => {
  const queue = await db.queues.createQueue<{ name: string }>('jobs');
  expect(await queue.send({ name: 'bar' })).toBe(true);

  const mp = new ManualPromise<{ name: string }>();
  cleanupWorker(queue.createWorker('test worker', async ({ data }) => {
    mp.resolve(data);
  }, { pollIntervalMs: 100 }));

  expect(await mp.promise).toEqual({ name: 'bar' });
});

dbTest('should fail to submit second job with the same ID if first is not processed', async ({ db }) => {
  const queue = await db.queues.createQueue<{ name: string }>('jobs');
  expect(await queue.send({ name: 'bar' }, { jobId: 'id-1' })).toBe(true);
  expect(await queue.send({ name: 'bar' }, { jobId: 'id-1' })).toBe(false);
});

dbTest('should submit second job after the first job with the same ID started execution', async ({ db, cleanupWorker }) => {
  const queue = await db.queues.createQueue<{ name: string }>('jobs');
  const started = new ManualPromise<void>();
  const done = new ManualPromise<void>();
  cleanupWorker(queue.createWorker('test worker', async () => {
    started.resolve();
    await done.promise;
  }, { pollIntervalMs: 100 }));

  expect(await queue.send({ name: 'bar' }, { jobId: 'id-1' })).toBe(true);
  await started.promise;
  expect(await queue.send({ name: 'bar' }, { jobId: 'id-1' })).toBe(true);
  await done.resolve();
});

dbTest('should submit & process jobs with different IDs', async ({ db, cleanupWorker }) => {
  const queue = await db.queues.createQueue<{ index: number }>('jobs');
  const jobs: { index: number, complete: ManualPromise<void> }[] = [];
  // Create 100 jobs
  for (let i = 0; i < 1000; ++i)
    jobs.push({ index: i, complete: new ManualPromise<void>() });

  // Submit all of them
  await queue.sendMany(jobs.map(job => ({
    data: { index: job.index },
    options: { jobId: `job-id-${job.index}` },
  })));

  // Process all of them.
  cleanupWorker(queue.createWorker('test worker', async ({ data }, signal) => {
    const job = jobs[data.index];
    await job.complete.resolve();
  }, { pollIntervalMs: 100 }));

  await Promise.all(jobs.map(job => job.complete.promise));
});

dbTest('make sure we submit multiple jobs if some of them have clashing ids', async ({ db, cleanupWorker }) => {
  const queue = await db.queues.createQueue<{ index: number }>('jobs');
  const jobs: { index: number, complete: ManualPromise<void> }[] = [];
  // Create 10 jobs jobs
  for (let i = 0; i < 10; ++i)
    jobs.push({ index: i, complete: new ManualPromise<void>() });

  // Submit all of them
  await queue.sendMany(jobs.map(job => ({
    data: { index: job.index },
    options: { jobId: `job-id-${job.index}` },
  })));

  // Create 10 more jobs
  for (let i = 10; i < 20; ++i)
    jobs.push({ index: i, complete: new ManualPromise<void>() });

  // Submit all of them again - this time, the first half will be already submitted.
  await queue.sendMany(jobs.map(job => ({
    data: { index: job.index },
    options: { jobId: `job-id-${job.index}` },
  })));

  // Process all of them.
  cleanupWorker(queue.createWorker('test worker', async ({ data }, signal) => {
    const job = jobs[data.index];
    await job.complete.resolve();
  }, { pollIntervalMs: 100 }));

  await Promise.all(jobs.map(job => job.complete.promise));
});

dbTest('Queue.hasJobsWithCategory', async ({ db, cleanupWorker }) => {
  const queue = await db.queues.createQueue<{ pet: string }>('jobs');
  expect(await queue.hasJobsWithCategory('dogs')).toBe(false);
  await queue.send({ pet: 'lucky' }, { category: 'dogs' });
  expect(await queue.hasJobsWithCategory('dogs')).toBe(true);

  const start = new ManualPromise<void>();
  const wait = new ManualPromise<void>();
  cleanupWorker(queue.createWorker('test worker', async (data) => {
    start.resolve();
    await wait.promise;
  }, { pollIntervalMs: 100 }));

  // Check that there are jobs in "dogs" category if the job is in active state
  await start.promise;
  expect(await queue.hasJobsWithCategory('dogs')).toBe(true);

  wait.resolve();
  await new Promise(x => queue.testEvents.once('did-finish-job', x));
  expect(await queue.hasJobsWithCategory('dogs')).toBe(false);
});

dbTest('same categories across different queues should not clash', async ({ db }) => {
  const qCats = await db.queues.createQueue<{ name: string }>('cats');
  const qDogs = await db.queues.createQueue<{ name: string }>('dogs');
  await qCats.send({ name: 'mikki' }, { category: 'male' });
  await qDogs.send({ name: 'dera' }, { category: 'female' });

  expect(await qCats.hasJobsWithCategory('male')).toBe(true);
  expect(await qCats.hasJobsWithCategory('female')).toBe(false);
  expect(await qDogs.hasJobsWithCategory('male')).toBe(false);
  expect(await qDogs.hasJobsWithCategory('female')).toBe(true);
});


dbTest.describe('concurrencyId', () => {
  dbTest('same concurrencyId should be processed serially', async ({ db, cleanupWorker }) => {
    const queue = await db.queues.createQueue<{ idx: number }>('jobs');
    
    const wait = new ManualPromise<void>();
    const log: string[] = [];
    for (let i = 0; i < 10; ++i) {
      cleanupWorker(queue.createWorker('test worker', async (jobInfo, { signal }) => {
        log.push(`started`);
        await wait.promise;
        await setTimeout(50, undefined, { signal });
        log.push(`finished`);
      }, {
        retries: 0,
        pollIntervalMs: 10,
        staleTimeoutMs: ms('1 hour'),
        bookkeepingIntervalMs: ms('1 hour'),
      }));
    }
  
    const JOBS_COUNT = 10;
    for (let i = 0; i < JOBS_COUNT; ++i) {
      await queue.send({ idx: i }, { concurrencyId: 'test' });
    }
  
    wait.resolve();
    await expect.poll(() => db.queues.listArchivedJobs(0, JOBS_COUNT).then(({ count }) => count)).toEqual(JOBS_COUNT);
    const expectedResult: string[] = Array(JOBS_COUNT).fill(0).map((_, idx) => [`started`, `finished`]).flat();
    expect(log).toEqual(expectedResult);
  });

  const concurrencyIds = [
    [undefined, undefined],
    [undefined, 'id-2'],
    ['id-1', 'id-2'],
  ]
  concurrencyIds.forEach(([concurrencyId1, concurrencyId2]) => {
    dbTest(`2 jobs with concurrencyIds = [${concurrencyId1}, ${concurrencyId2}] run in parallel`, async ({ db, cleanupWorker }) => {
      const queue = await db.queues.createQueue<{}>('jobs');
      
      const wait = new ManualPromise<void>();
  
      // 1. Create 2 workers.
      const log: string[] = [];
      const workerStarted: ManualPromise<void>[] = [];
      for (let i = 0; i < 2; ++i) {
        const started = new ManualPromise<void>();
        workerStarted.push(started);
        cleanupWorker(queue.createWorker('test worker', async (jobInfo, signal) => {
          started.resolve();
          await wait.promise;
          log.push(`started`);
          await Promise.resolve();
          log.push(`finished`);
        }, {
          retries: 0,
          pollIntervalMs: 10,
          staleTimeoutMs: ms('1 hour'),
          bookkeepingIntervalMs: ms('1 hour'),
        }));
      }
  
      // 2. Submit 2 jobs.
      await queue.send({}, { concurrencyId: concurrencyId1 });
      await queue.send({}, { concurrencyId: concurrencyId2 });
  
      // 3. Make sure both workers picked their jobs and waiting on continuation.
      await Promise.all(workerStarted.map(p => p.promise));
      // 4. Continue job processing.
      wait.resolve();
    });
  })
})

dbTest('bookkeeping stale jobs', async ({ db, cleanupWorker }) => {
  const queue = await db.queues.createQueue<{ name: string }>('jobs');

  // Lets create a malfunctioning job processor that just hangs indefinitely.
  // We will release it in the end of test.
  // The "heartbeat" will be beating every 1 hour - which is too slow, thus
  // the job will be marked as "stale".
  cleanupWorker(queue.createWorker('test worker', async (jobInfo, { signal }) => {
    // The promise will be aborted when the queue will be closing.
    await setTimeout(ms('10hours'), undefined, { signal });
  }, {
    retries: 1,
    pollIntervalMs: 100,
    heartbeatIntervalMs: ms('1 hour'),
    staleTimeoutMs: ms('1 sec'),
    bookkeepingIntervalMs: 1000,
  }));

  queue.send({ name: 'lucky' }, { category: 'dog' });

  // First, the first stalling worker should pick up the job.
  await new Promise(x => queue.testEvents.once('will-start-job', x));
  // The job should stall, and move to archive. Stalled jobs ARE NOT RETRIED.
  await new Promise(x => queue.testEvents.once('did-finish-job', x));

  // And with this, we should have no more running "dog" jobs.
  // We need to poll for this since the 'did-finish-job' event fires a
  // tad too early (from-inside bookkeeping transaction).
  await expect.poll(() => queue.hasJobsWithCategory('dog')).toBe(false);

  const archived = await db.queues.listArchivedJobs(0, 10);
  expect(archived.count).toBe(1);
  expect(archived.jobs.length).toBe(1);
  expect(archived.jobs[0].execution_result).toEqual('stalled');
});

dbTest('make sure heartbeat prevents a job from being marked as stalled', async ({ db, cleanupWorker }) => {
  const queue = await db.queues.createQueue<{ name: string }>('jobs');

  // Lets create a malfunctioning job processor that just hangs indefinitely.
  // We will release it in the end of test.
  // The "heartbeat" will be beating every 1 hour - which is too slow, thus
  // the job will be marked as "stale".
  cleanupWorker(queue.createWorker('test worker', async (jobInfo, { signal }) => {
    // The promise will be aborted when the queue will be closing.
    await setTimeout(ms('2 sec'), undefined, { signal });
  }, {
    retries: 0,
    pollIntervalMs: 100,
    heartbeatIntervalMs: 100,
    staleTimeoutMs: ms('1 sec'),
    bookkeepingIntervalMs: 100,
  }));

  await queue.send({ name: 'lucky' }, { category: 'dog' });

  await expect.poll(async () => (await db.queues.listArchivedJobs(0, 10)).count).toBe(1);
  const archived = await db.queues.listArchivedJobs(0, 10);
  expect(archived.jobs[0].execution_result).toEqual('passed');
});

dbTest('make sure absence of heartbeat marks a job as stalled', async ({ db, cleanupWorker }) => {
  const queue = await db.queues.createQueue<{ name: string }>('jobs');

  // Lets create a malfunctioning job processor that just hangs indefinitely.
  // We will release it in the end of test.
  // The "heartbeat" will be beating every 1 hour - which is too slow, thus
  // the job will be marked as "stale".
  cleanupWorker(queue.createWorker('test worker', async (jobInfo, { signal }) => {
    // The promise will be aborted when the queue will be closing.
    await setTimeout(ms('2 sec'), undefined, { signal });
  }, {
    retries: 0,
    pollIntervalMs: 100,
    heartbeatIntervalMs: ms('1 hour'),
    staleTimeoutMs: ms('1 sec'),
    bookkeepingIntervalMs: 100,
  }));

  await queue.send({ name: 'lucky' }, { category: 'dog' });
  await expect.poll(async () => (await db.queues.listArchivedJobs(0, 10)).count).toBe(1);
  const archived = await db.queues.listArchivedJobs(0, 10);
  expect(archived.jobs[0].execution_result).toEqual('stalled');
});

dbTest('should archive jobs', async ({ db, cleanupWorker }) => {
  const queue = await db.queues.createQueue<{ name: string }>('jobs');
  cleanupWorker(queue.createWorker('test worker', async () => {}, { pollIntervalMs: 100 }));
  queue.send({ name: 'foo' }, { category: 'dog' });
  await new Promise(x => queue.testEvents.once('did-finish-job', x));

  const archivedJobs = await db.queues.listArchivedJobs(0, 50);
  expect(archivedJobs.count).toBe(1);
  expect(archivedJobs.jobs[0]).toEqual(expect.objectContaining({
    category: 'dog',
    execution_result: QueueWorker.jobExecutionResult.passed,
  }));

  // Check that querying outside of existing results returns no results
  expect(await db.queues.listArchivedJobs(50, 50)).toEqual({
    count: 1,
    jobs: [],
  });
});

dbTest('bookkeeping should eventually cleanup archived jobs', async ({ db, cleanupWorker }) => {
  const queue = await db.queues.createQueue<{ name: string }>('jobs');
  cleanupWorker(queue.createWorker('test worker', async () => {}, {
    pollIntervalMs: 100,
    bookkeepingIntervalMs: 100,
    keepInArchiveMs: ms('1 second'),
  }));
  queue.send({ name: 'foo' }, { category: 'dog' });
  await new Promise(x => queue.testEvents.once('did-finish-job', x));

  // First, there should be some archived jobs.
  const archivedJobs = await db.queues.listArchivedJobs(0, 50);
  expect(archivedJobs.count).toBe(1);

  // But eventually, bookkeeping should clean them up.
  await expect.poll(() => db.queues.listArchivedJobs(0, 50).then(e => e.count)).toBe(0);
});

dbTest('regression test: should unstale & archive jobs when there is a pending one with the same id', async ({ db, cleanupWorker }) => {
  // Usecase 1:
  // 1. A job got started but worker died - so job is active
  // 2. A new job got added as "pending" with the same jobId
  // Conandrum:
  // - bookkeeper cannot move stale job to pending since there's a pending job already
  // - worker cannot move pending job to active since there's a stale active job
  // Solution:
  // - the stale job should be dropped, and a pending one can be updated to have retries and other stuff.
  const queue = await db.queues.createQueue<{ name: string }>('jobs');

  cleanupWorker(queue.createWorker('test worker', async (job, { signal }) => {
    await setTimeout(ms('1 hour'), undefined, { signal });
  }, {
    pollIntervalMs: 100,
    bookkeepingIntervalMs: 100,
    staleTimeoutMs: ms('1 sec'),
  }));

  queue.send({ name: 'foo' }, { jobId: 'id-1' });
  await new Promise(x => queue.testEvents.once('will-start-job', x));

  await queue.send({ name: 'bar' }, { jobId: 'id-1' });

  // Eventually, bookkeeping should clean up all jobs.
  await expect.poll(() => db.queues.listQueuedJobs(0, 50).then(e => e.count)).toBe(1);
});

dbTest('regression test: should schedule retries when there is a pending job already with the same id', async ({ db, cleanupWorker }) => {
  // Usecase:
  // 1. A job has started
  // 2. Meanwhile, another one with the same jobId got added as pending
  // 3. The first one failed.
  // Problem: A job retry cannot be added since there's a pending job already with the same jobId.
  // Solution: a pending job should be updated as if it is a retry.

  const queue = await db.queues.createQueue<{ name: string }>('jobs');

  const waitSecondJob = new ManualPromise<void>();
  cleanupWorker(queue.createWorker('test worker', async (job, signal) => {
    await waitSecondJob.promise;
    if (job.retry === 0)
      throw new Error('failing first iteration');
  }, {
    pollIntervalMs: 100,
  }));

  queue.send({ name: 'foo' }, { jobId: 'id-1' });
  await new Promise(x => queue.testEvents.once('will-start-job', x));
  await queue.send({ name: 'bar' }, { jobId: 'id-1' });
  waitSecondJob.resolve();

  // Eventually, bookkeeping should clean up all jobs.
  await expect.poll(() => db.queues.listQueuedJobs(0, 50).then(e => e.count)).toBe(0);
});

