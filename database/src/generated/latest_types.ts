/**
 * This file was generated by kysely-codegen.
 * Please do not edit it manually.
 */

import type { ColumnType } from "kysely";

export type Generated<T> = T extends ColumnType<infer S, infer I, infer U>
  ? ColumnType<S, I | undefined, U>
  : ColumnType<T, T | undefined, T>;

export type Json = JsonValue;

export type JsonArray = JsonValue[];

export type JsonObject = {
  [x: string]: JsonValue | undefined;
};

export type JsonPrimitive = boolean | number | string | null;

export type JsonValue = JsonArray | JsonObject | JsonPrimitive;

export interface ActiveJobs {
  category: string | null;
  concurrency_id: string | null;
  data: Json;
  execute_after_timestamp_seconds: number;
  execution_duration_seconds: number | null;
  execution_error: string | null;
  execution_heartbeat_timestamp_seconds: number;
  execution_result: string | null;
  execution_timestamp_seconds: number;
  id: number;
  job_id: string;
  queue_name: string;
  retry: number;
  submitted_timestamp_seconds: number;
  worker_name: string | null;
}

export interface ArchivedJobs {
  category: string | null;
  concurrency_id: string | null;
  data: Json;
  execute_after_timestamp_seconds: number;
  execution_duration_seconds: number | null;
  execution_error: string | null;
  execution_heartbeat_timestamp_seconds: number | null;
  execution_result: string;
  execution_timestamp_seconds: number;
  id: number;
  job_id: string;
  queue_name: string;
  retry: number;
  submitted_timestamp_seconds: number;
  worker_name: string | null;
}

export interface DeviceAuthRequests {
  approver_id: import('../database.js').UserId | null;
  client_id: import('../database.js').AppClientId;
  device_code: string;
  device_name: string;
  expiration_timestamp_seconds: number;
  request_id: Generated<import('../database.js').DeviceAuthRequestId>;
  user_code: string;
}

export interface GithubAppInstallations {
  installation_id_encoded: string;
  project_id: import('../database.js').ProjectId;
}

export interface OrganizationMembers {
  access_role: import('../database.js').OrgAccessRole;
  org_id: import('../database.js').OrgId;
  user_id: import('../database.js').UserId;
}

export interface Organizations {
  org_id: Generated<import('../database.js').OrgId>;
  org_name: string;
  org_public_id: import('../database.js').OrgPublicId;
  org_slug: string;
  owner_id: import('../database.js').UserId;
}

export interface PersonalAccessTokens {
  pat_encoded: string;
  project_id: import('../database.js').ProjectId;
}

export interface ProductPlans {
  max_data_retention_days: number;
  name: string;
  org_public_id: import('../database.js').OrgPublicId | null;
  plan_id: Generated<import('../database.js').ProductPlanId>;
  plan_public_id: import('../database.js').ProductPlanPublicId;
  seats: number | null;
  seats_price_id: string | null;
  storage_price_id: string | null;
  testruns_price_id: string | null;
  trial_days: number | null;
}

export interface ProjectCollaborators {
  access_role: import('../database.js').ProjectAccessRole;
  project_id: import('../database.js').ProjectId;
  user_id: import('../database.js').UserId;
}

export interface Projects {
  accepted_flakiness_ratio: number | null;
  default_timezone: string | null;
  flakiness_access_token: string;
  last_upload_timestamp_seconds: number | null;
  org_id: import('../database.js').OrgId;
  preferred_data_retention_days: number | null;
  project_id: Generated<import('../database.js').ProjectId>;
  project_name: string;
  project_public_id: import('../database.js').ProjectPublicId;
  project_slug: string;
  regression_window_days: number | null;
  report_time_is_upload_time: boolean | null;
  reports_count: Generated<number>;
  source_auth_type: import('../database.js').SourceAuthType;
  source_last_fetch_http_status: number | null;
  source_last_fetch_timestamp_seconds: number | null;
  source_owner_name: string;
  source_repo_name: string;
  visibility: import('../database.js').ProjectVisibility | null;
}

export interface QueuedJobs {
  category: string | null;
  concurrency_id: string | null;
  data: Json;
  execute_after_timestamp_seconds: number;
  execution_duration_seconds: number | null;
  execution_error: string | null;
  execution_heartbeat_timestamp_seconds: number | null;
  execution_result: string | null;
  execution_timestamp_seconds: number | null;
  id: Generated<number>;
  job_id: string;
  queue_name: string;
  retry: number;
  submitted_timestamp_seconds: number;
  worker_name: string | null;
}

export interface Users {
  github_id: number | null;
  user_avatar_url: string | null;
  user_id: Generated<import('../database.js').UserId>;
  user_login: string;
  user_name: string;
  user_public_id: import('../database.js').UserPublicId;
}

export interface UserSessions {
  client_id: import('../database.js').AppClientId;
  last_access_timestamp_seconds: number;
  session_id: Generated<import('../database.js').UserSessionId>;
  session_name: string | null;
  session_public_id: import('../database.js').UserSessionPublicId;
  token_sha256: string;
  user_id: import('../database.js').UserId;
}

export interface DB {
  active_jobs: ActiveJobs;
  archived_jobs: ArchivedJobs;
  device_auth_requests: DeviceAuthRequests;
  github_app_installations: GithubAppInstallations;
  organization_members: OrganizationMembers;
  organizations: Organizations;
  personal_access_tokens: PersonalAccessTokens;
  product_plans: ProductPlans;
  project_collaborators: ProjectCollaborators;
  projects: Projects;
  queued_jobs: QueuedJobs;
  user_sessions: UserSessions;
  users: Users;
}
