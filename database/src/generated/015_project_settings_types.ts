/**
 * This file was generated by kysely-codegen.
 * Please do not edit it manually.
 */

import type { ColumnType } from "kysely";

export type Generated<T> = T extends ColumnType<infer S, infer I, infer U>
  ? ColumnType<S, I | undefined, U>
  : ColumnType<T, T | undefined, T>;

export type Json = JsonValue;

export type JsonArray = JsonValue[];

export type JsonObject = {
  [x: string]: JsonValue | undefined;
};

export type JsonPrimitive = boolean | number | string | null;

export type JsonValue = JsonArray | JsonObject | JsonPrimitive;

export interface ActiveJobs {
  category: string | null;
  concurrency_id: string | null;
  data: Json;
  execute_after_timestamp_seconds: number;
  execution_duration_seconds: number | null;
  execution_error: string | null;
  execution_result: string | null;
  execution_timestamp_seconds: number;
  id: number;
  job_id: string;
  queue_name: string;
  retry: number;
  submitted_timestamp_seconds: number;
}

export interface ArchivedJobs {
  category: string | null;
  concurrency_id: string | null;
  data: Json;
  execute_after_timestamp_seconds: number;
  execution_duration_seconds: number | null;
  execution_error: string | null;
  execution_result: string;
  execution_timestamp_seconds: number;
  id: number;
  job_id: string;
  queue_name: string;
  retry: number;
  submitted_timestamp_seconds: number;
}

export interface GithubAppInstallations {
  installation_id_encoded: string;
  project_id: number;
}

export interface GithubOauthUserTokens {
  access_expiration_timestamp_seconds: number;
  access_token_encoded: string;
  refresh_expiration_timestamp_seconds: number;
  refresh_token_encoded: string;
  user_id: number;
}

export interface OrganizationMembers {
  access_role: number;
  org_id: number;
  user_id: number;
}

export interface Organizations {
  org_id: Generated<number>;
  org_name: string;
  org_public_id: string;
  org_slug: string;
  owner_id: number;
}

export interface PersonalAccessTokens {
  pat_encoded: string;
  project_id: number;
}

export interface ProductPlans {
  max_data_retention_days: number;
  name: string;
  org_public_id: string | null;
  plan_id: Generated<number>;
  plan_public_id: string;
  seats: number | null;
  seats_price_id: string | null;
  storage_price_id: string | null;
  testruns_price_id: string | null;
  trial_days: number | null;
}

export interface ProjectCollaborators {
  access_role: number;
  project_id: number;
  user_id: number;
}

export interface Projects {
  flakiness_access_token: string;
  org_id: number;
  preferred_data_retention_days: number | null;
  project_id: Generated<number>;
  project_name: string;
  project_public_id: string;
  project_slug: string;
  report_time_is_upload_time: boolean | null;
  reports_count: Generated<number>;
  source_auth_type: number;
  source_last_fetch_http_status: number | null;
  source_last_fetch_timestamp_seconds: number | null;
  source_owner_name: string;
  source_repo_name: string;
  visibility: number | null;
}

export interface QueuedJobs {
  category: string | null;
  concurrency_id: string | null;
  data: Json;
  execute_after_timestamp_seconds: number;
  execution_duration_seconds: number | null;
  execution_error: string | null;
  execution_result: string | null;
  execution_timestamp_seconds: number | null;
  id: Generated<number>;
  job_id: string;
  queue_name: string;
  retry: number;
  submitted_timestamp_seconds: number;
}

export interface Users {
  github_id: number | null;
  user_avatar_url: string | null;
  user_id: Generated<number>;
  user_login: string;
  user_name: string;
  user_public_id: string;
}

export interface DB {
  active_jobs: ActiveJobs;
  archived_jobs: ArchivedJobs;
  github_app_installations: GithubAppInstallations;
  github_oauth_user_tokens: GithubOauthUserTokens;
  organization_members: OrganizationMembers;
  organizations: Organizations;
  personal_access_tokens: PersonalAccessTokens;
  product_plans: ProductPlans;
  project_collaborators: ProjectCollaborators;
  projects: Projects;
  queued_jobs: QueuedJobs;
  users: Users;
}
