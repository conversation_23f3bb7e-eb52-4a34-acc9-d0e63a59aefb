/**
 * This file was generated by kysely-codegen.
 * Please do not edit it manually.
 */

import type { ColumnType } from "kysely";

export type Generated<T> = T extends ColumnType<infer S, infer I, infer U>
  ? ColumnType<S, I | undefined, U>
  : ColumnType<T, T | undefined, T>;

export interface PersonalAccessTokens {
  project_id: string;
  value: string;
}

export interface ProjectCollaborators {
  access_role: number;
  project_id: string;
  user_id: string;
}

export interface Projects {
  flakiness_access_token: string;
  general_access: number | null;
  owner_id: string;
  project_id: string;
  project_name: string;
  reports_count: Generated<number>;
  source_auth_type: number;
  source_last_fetch_http_status: number | null;
  source_last_fetch_timestamp_seconds: number | null;
  source_owner_name: string;
  source_repo_name: string;
}

export interface Users {
  github_id: number | null;
  user_avatar_url: string | null;
  user_id: string;
  user_login: string;
  user_name: string;
}

export interface DB {
  personal_access_tokens: PersonalAccessTokens;
  project_collaborators: ProjectCollaborators;
  projects: Projects;
  users: Users;
}
