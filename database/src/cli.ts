#!/usr/bin/env node
import confirm from '@inquirer/confirm';
import { Option, program } from "commander";
import dotenv from 'dotenv';
import fs from 'fs';
import { NO_MIGRATIONS } from 'kysely';
import path from 'path';
import { Postgres } from 'podkeeper';
import url from 'url';
import { aes } from './aes.js';
import { Database, DatabaseConfig } from "./database.js";
import { mu } from './migrations/mu.js';
import { generateTypes } from "./typesGenerator.js";

const __filename = url.fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

program
  .command('migrate_postgres')
  .description(`Migrate Postgres database.`)
  .addOption(new Option(`-y, --yes`, `skip prompts and migrate to latest`))
  .addOption(new Option(`--to-migration <name>`, `migrate to a given migration name`))
  .addOption(new Option(`-e, --encryption-key <key>`, `database encryption key`).env('DB_ENCRYPTION_KEY').makeOptionMandatory())
  .addOption(new Option(`-h, --host <string>`, `database host`).env('PGHOST').makeOptionMandatory())
  .addOption(new Option(`-p, --port <number>`, `database port`).env('PGPORT').argParser(parseInt).makeOptionMandatory())
  .addOption(new Option(`-d, --database <string>`, `database name`).env('PGDATABASE').makeOptionMandatory())
  .addOption(new Option(`-u, --user <string>`, `database user`).env('PGUSER').makeOptionMandatory())
  .addOption(new Option(`--password <string>`, `database password`).env('PGPASSWORD').makeOptionMandatory())
  .action(async (options: { yes?: boolean, encryptionKey: string, toMigration?: string, host: string, port: number, database: string, user: string, password: string }) => {
    const dbConfig: DatabaseConfig = {
      database: options.database,
      encryptionKey: options.encryptionKey,
      host: options.host,
      password: options.password,
      port: options.port,
      user: options.user,
    };
    await Database.migrate(dbConfig, async migrator => {
      const migrations = await migrator.getMigrations();
      if (!migrations.length) {
        console.error(`ERROR: no migrations found!`);
        process.exitCode = 1;
        return;
      }
      const lastAppliedMigrationIndex = migrations.findLastIndex(migration => migration.executedAt);

      const toMigration = options.toMigration;
      if (toMigration) {
        const targetIndex = migrations.findIndex(migration => migration.name.toLowerCase().includes(toMigration.toLowerCase()));
        if (targetIndex === -1) {
          console.error('Failed to find target migration; check that the name is correct!');
          process.exitCode = 1;
          return;
        }
        console.log(`- Current: ${lastAppliedMigrationIndex >= 0 ? migrations[lastAppliedMigrationIndex].name : 'N/A'}`);
        console.log(`-  Target: ${migrations[targetIndex].name}`);
        if (targetIndex === lastAppliedMigrationIndex) {
          console.log('Database is at the desired state; nothing to do.')
          return;
        }

        const op = targetIndex > lastAppliedMigrationIndex ? 'roll up' : 'roll back';
        const doMigrate = options.yes ?? await confirm({ message: ` Do you want to ${op} to ${migrations[targetIndex].name}?`, default: false });
        if (!doMigrate) {
          console.log(`No migrations applied.`);
          return;
        }
  
        const { error } = await migrator.migrateTo(migrations[targetIndex].name);
        if (error)
          console.error(error);
        else
          console.log('Successfully applied migrations.');
  
        return;
      }

      if (lastAppliedMigrationIndex !== -1) {
        console.log(`Last applied migration: ${migrations[lastAppliedMigrationIndex].name} applied on ${migrations[lastAppliedMigrationIndex].executedAt?.toISOString()}`);
      } else {
        console.log(`The database is pristine; no migrations have been applied to it.`);
      }

      const unappliedMigrations = migrations.filter(migration => !migration.executedAt);
      if (!unappliedMigrations.length) {
        console.log(`Database is up-to-date!`);
        return;
      }
  
      console.log(`Database is not up-todate; the following migrations have to be applied:`);
      for (const unappliedMigration of unappliedMigrations)
        console.log(`- ${unappliedMigration.name}`);
      
      const doMigrate = options.yes ?? await confirm({ message: `Apply ${unappliedMigrations.length} migrations?`, default: false });
      if (!doMigrate) {
        console.log(`No migrations applied.`);
        return;
      }
      const { error } = await migrator.migrateToLatest();
      if (error)
        console.error(error);
      else
        console.log('Successfully applied migrations');

    });
  });

program
  .command('gen')
  .description(`Generate types for the last migration.`)
  .addOption(new Option(`--all`, `regenerate types for all migrations.`))
  .action(async (options: { all?: boolean }) => {
    const outputDir = path.join(__dirname, '..', 'src', 'generated');
    console.log(`Using Postgres to drive migrations`);
    await generateTypes(outputDir, !!options.all);
    console.log(`Types written to ${outputDir}`);
  });

function die(message: string): never {
  console.error(message);
  process.exit(1);
}

program
  .command('mirror')
  .description(`Mirrors prod environment to local`)
  .action(async (options: { from: string, to: string }) => {
    // 1. load prod env
    const prodEnvPath = path.join(import.meta.dirname, '../../.env.prod');
    if (!fs.existsSync(prodEnvPath))
      die('Cannot load .env.prod - please run //config/config prod');
    const devEnvPath = path.join(import.meta.dirname, '../../.env.dev');
    if (!fs.existsSync(devEnvPath))
      die('Cannot load .env.prod - please run //config/config dev');
    const prodEnv = dotenv.parse(fs.readFileSync(prodEnvPath, 'utf-8'));
    const prodConfig: DatabaseConfig = {
      database: prodEnv.PGDATABASE,
      encryptionKey: prodEnv.DB_ENCRYPTION_KEY,
      host: prodEnv.PGHOST,
      password: prodEnv.PGPASSWORD,
      port: parseInt(prodEnv.PGPORT, 10),
      user: prodEnv.PGUSER,
    };
    const [prodMigration, schema, data] = await Database.migrate(prodConfig, async (migrator, db) => {
      const migrations = await migrator.getMigrations();
      const lastAppliedMigration = migrations.findLast(migration => migration.executedAt);
      if (!lastAppliedMigration)
        die('No migartion found');
      const schema = (lastAppliedMigration.migration as any).schema;
      const data = await mu.pull(db, schema as mu.Schema<any>);
      return [lastAppliedMigration, schema, data];
    });
    const devEnv = dotenv.parse(fs.readFileSync(devEnvPath, 'utf-8'));
    const devConfig: DatabaseConfig = {
      database: devEnv.PGDATABASE,
      encryptionKey: devEnv.DB_ENCRYPTION_KEY,
      host: devEnv.PGHOST,
      password: devEnv.PGPASSWORD,
      port: parseInt(devEnv.PGPORT, 10),
      user: devEnv.PGUSER,
    };
    if (devConfig.host !== 'localhost')
      die('ERROR: can mirror database only to the localhost instances (security reasons)');
    await Database.migrate(devConfig, async (migrator, db) => {
      await migrator.migrateTo(NO_MIGRATIONS);
      await migrator.migrateTo(prodMigration.name);
      await mu.pushmapped(db, schema, data, {});
    });
  });


  program
    .command('test-prod-migration')
    .description(`Tests that migrations can be applied to prod database`)
    .action(async (options: { from: string, to: string }) => {
      // 1. load prod env
      const prodEnvPath = path.join(import.meta.dirname, '../../.env.prod');
      if (!fs.existsSync(prodEnvPath))
        die('Cannot load .env.prod - please run //config/config prod');
      const prodEnv = dotenv.parse(fs.readFileSync(prodEnvPath, 'utf-8'));
      const prodConfig: DatabaseConfig = {
        database: prodEnv.PGDATABASE,
        encryptionKey: prodEnv.DB_ENCRYPTION_KEY,
        host: prodEnv.PGHOST,
        password: prodEnv.PGPASSWORD,
        port: parseInt(prodEnv.PGPORT, 10),
        user: prodEnv.PGUSER,
      };

      console.log('Pulling data from prod...');
      const [prodMigration, schema, data] = await Database.migrate(prodConfig, async (migrator, db) => {
        const migrations = await migrator.getMigrations();
        const lastAppliedMigration = migrations.findLast(migration => migration.executedAt);
        if (!lastAppliedMigration)
          die('No migartion found');
        const schema = (lastAppliedMigration.migration as any).schema;
        const data = await mu.pull(db, schema as mu.Schema<any>);
        return [lastAppliedMigration, schema, data];
      });
      console.log('Starting local postgres instance');
      const service = await Postgres.start();
      const devConfig: DatabaseConfig = {
        ...service.connectOptions(),
        encryptionKey: 'some key',
      };
      console.log('Migrating forward');
      const result = await Database.migrate(devConfig, async (migrator, db) => {
        await migrator.migrateTo(prodMigration.name);
        await mu.push(db, schema, data);
        return await migrator.migrateToLatest();
      });
      await service.stop();
      if (result.error)
        throw result.error;
      console.log('DONE, all good!');
    });

program
  .command('schema')
  .description(`Print last migration scheme`)
  .action(async () => {
    const allFiles = await fs.promises.readdir(path.join(import.meta.dirname, './migrations'));
    const migrations = allFiles.filter(file => /^\d\d\d.*\.js$/.test(file)).toSorted((a, b) => a < b ? -1 : 1);
    if (!migrations.length) {
      console.error('No migrations found!');
      process.exit(1);
    }
    const migration = await import(path.join(import.meta.dirname, 'migrations', migrations.at(-1)!));
    const schema = migration.schema as mu.Schema<any>;

    console.log(mu.print(schema));
  });


program
  .command('gen-encryption-key')
  .description(`Generate encryption key for the database.`)
  .action(async () => {
    console.log(aes.createKey());
  });


program.parse();

