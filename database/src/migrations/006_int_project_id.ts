import { Kysely } from 'kysely';


import { randomUUID } from 'crypto';
import * as baseTypes from '../generated/005_project_slug_types.js';
import * as newTypes from '../generated/006_int_project_id_types.js';
import { schema as base } from './005_project_slug.js';
import { mu } from './mu.js';

export const schema = mu.edit<baseTypes.DB, newTypes.DB>(base, {
  projects: {
    columns: {
      // So we introduce integer id to manage all inter-relationships
      project_id: { type: 'integer', is: ['primary', 'auto-inc'] },
      // However, we also need to have a public_id that we will use for i.e. S3 URLs.
      project_public_id: { type: mu.UUID_V4_TEXT_TYPE, is: ['not-null'] },
    },
    indexes: {
      project_public_id: { columns: ['project_public_id'] },
    },
  },
});

export async function up(db: Kysely<baseTypes.DB>): Promise<void> {
  await mu.migration(db, base, schema).run({
    projects: (e) => ({
      ...e,
      project_public_id: e.project_id,
      project_id: undefined,
    }),
    project_collaborators: (e, pushed, old) => ({
      ...e,
      project_id: pushed('projects', {
        org_id: old('projects', { project_id: e.project_id }).org_id,
        project_slug: old('projects', { project_id: e.project_id }).project_slug,
      }).project_id,
    }),
    github_app_installations: (e, pushed, old) => ({
      ...e,
      project_id: pushed('projects', {
        org_id: old('projects', { project_id: e.project_id }).org_id,
        project_slug: old('projects', { project_id: e.project_id }).project_slug,
      }).project_id,
    }),
    personal_access_tokens: (e, pushed, old) => ({
      ...e,
      project_id: pushed('projects', {
        org_id: old('projects', { project_id: e.project_id }).org_id,
        project_slug: old('projects', { project_id: e.project_id }).project_slug,
      }).project_id,
    }),
  });
}

export async function down(db: Kysely<newTypes.DB>): Promise<void> {
  await mu.migration(db, schema, base).run({
    projects: (e) => ({
      ...e,
      project_public_id: undefined,
      project_id: randomUUID(),
    }),
    project_collaborators: (e, pushed, old) => ({
      ...e,
      project_id: pushed('projects', {
        org_id: old('projects', { project_id: e.project_id }).org_id,
        project_slug: old('projects', { project_id: e.project_id }).project_slug,
      }).project_id,
    }),
    github_app_installations: (e, pushed, old) => ({
      ...e,
      project_id: pushed('projects', {
        org_id: old('projects', { project_id: e.project_id }).org_id,
        project_slug: old('projects', { project_id: e.project_id }).project_slug,
      }).project_id,
    }),
    personal_access_tokens: (e, pushed, old) => ({
      ...e,
      project_id: pushed('projects', {
        org_id: old('projects', { project_id: e.project_id }).org_id,
        project_slug: old('projects', { project_id: e.project_id }).project_slug,
      }).project_id,
    }),
  });
}
