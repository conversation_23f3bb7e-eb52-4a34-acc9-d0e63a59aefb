import { Kysely } from 'kysely';

import * as baseTypes from '../generated/019_drop_github_oauth_user_tokens_types.js';
import * as newTypes from '../generated/020_user_sessions_types.js';
import { schema as baseSchema } from './019_drop_github_oauth_user_tokens.js';

import { mu } from './mu.js';

export const schema = mu.edit<baseTypes.DB, newTypes.DB>(baseSchema, {
  user_sessions: {
    columns: {
      session_id: { type: 'integer', is: ['primary', 'auto-inc'] },
      client_id: { type: 'varchar(100)', is: ['not-null'] },
      session_name: { type: 'varchar(100)' },
      session_public_id: { type: mu.UUID_V4_TEXT_TYPE, is: ['not-null'] },
      token_sha256: { type: 'varchar(64)', is: ['not-null'], },
      user_id: {  ref: 'users.user_id', is: ['not-null'], onDelete: 'cascade', },
      last_access_timestamp_seconds: { type: 'integer', is: ['not-null'] },
    },
    indexes: {
      // Index by user_id to list all user sessions.
      'user_id': { columns: ['user_id'] },
    },
    uniques: {
      // This is to quickly retrieve session by token hash
      'token_sha256': { columns: ['token_sha256'] },
      // This is to quickly retrive sesison by its public id.
      'session_public_id': { columns: ['session_public_id'] },
    },
  }
});

export async function up(db: Kysely<baseTypes.DB>): Promise<void> {
  await mu.transform(db, baseSchema, schema);
}

export async function down(db: Kysely<newTypes.DB>): Promise<void> {
  await mu.transform(db, schema, baseSchema);
}
