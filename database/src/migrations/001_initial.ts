import { Kysely } from 'kysely';
import { Database } from '../database.js';
import { DB as DB_NEW } from '../generated/001_initial_types.js';
import { mu } from './mu.js';

export const schema: mu.Schema<DB_NEW> = {
  users: {
    columns: {
      user_id:    { type: mu.UUID_V4_TEXT_TYPE, is: ['primary'], },
      user_name:  { type: 'varchar(255)',       is: ['not-null'], },
      user_login: { type: 'varchar(255)',       is: ['not-null'], },
      github_id:  { type: 'integer' },
      user_avatar_url: { type: 'varchar(255)' },
    },
    uniques: {
      unique_github_id: { columns: ['github_id'], customName: 'users_github_id_key' },
      unique_login: { columns: ['user_login'], customName: 'users_user_login_key' },
    },
  },

  projects: {
    columns: {
      project_id:             { type: mu.UUID_V4_TEXT_TYPE, is: ['primary'] },
      project_name:           { type: 'varchar(255)',       is: ['not-null'] },
      flakiness_access_token: { type: 'varchar(255)',       is: ['not-null'] },
      reports_count:          { type: 'integer',            is: ['not-null'], default: 0 },
      owner_id:               { 
        ref: 'users.user_id', is: ['not-null'], onDelete: 'cascade', customName: 'fk__projects__owner_id',
      },
      source_auth_type:       { type: 'integer', is: ['not-null'] },
      source_owner_name:      { type: 'varchar(255)', is: ['not-null'] },
      source_repo_name:       { type: 'varchar(255)', is: ['not-null'] },
      source_last_fetch_timestamp_seconds:  { type: 'integer' },
      source_last_fetch_http_status:        { type: 'integer' },
      general_access:                       { type: `integer` },
    },

    indexes: {
      'flakiness_access_token': {
        customName: 'flakiness_projects_access_token_index',
        columns: ['flakiness_access_token'],
      }
    },

    uniques: {
      'owner_id + project_name': {
        customName: 'unique_projects_owner_id_project_name',
        columns: ['owner_id', 'project_name']
      },
    },
  },

  personal_access_tokens: {
    columns: {
      project_id: { ref: 'projects.project_id', is: ['primary'], onDelete: 'cascade', customName: 'fk__personal_access_tokens__project_id', },
      value: { type: 'varchar(255)', is: ['not-null'] },
    },
  },

  project_collaborators: {
    columns: {
      project_id: { ref: 'projects.project_id', is: ['not-null'], onDelete: 'cascade', customName: 'fk__project_collaborators__project_id' },
      user_id: { ref: 'users.user_id', is: ['not-null'], onDelete: 'cascade', customName: 'fk__project_collaborators__user_id' },
      access_role: { type: 'integer', is: ['not-null'], }
    },
    uniques: {
      'project_id + user_id': { columns: ['project_id', 'user_id'], customName: 'unique__project_collaborators' },
    },
  },
};

export async function up(db: Kysely<{}>): Promise<void> {
  await mu.transform(db, {}, schema);
}

export async function down(db: Kysely<DB_NEW>): Promise<void> {
  await mu.transform(db, schema, {});
}

export async function mock(db: Kysely<DB_NEW>): Promise<void> {
  await db.insertInto('users').values({
    user_id: 'user-1',
    user_login: 'john',
    user_name: 'doe',
    github_id: 123,
    user_avatar_url: 'https://example.com/john.png',
  }).execute();

  await db.insertInto('users').values({
    user_id: 'user-2',
    user_login: 'jane',
    user_name: 'doe',
    github_id: 456,
    user_avatar_url: 'https://example.com/jane.png',
  }).execute();

  await db.insertInto('projects').values({
    owner_id: 'user-1',
    project_id: 'project-1',
    flakiness_access_token: 'flakiness-access-token-1',
    project_name: 'flakiness',
    source_auth_type: Database.sourceAuthType.githubPat,
    source_owner_name: 'degulabs',
    source_repo_name: 'flakiness',
    general_access: Database.projectAccessRole.viewer,
    reports_count: 0,
    source_last_fetch_http_status: 200,
    source_last_fetch_timestamp_seconds: Math.floor(Date.now() / 1000),
  }).execute();

  await db.insertInto('personal_access_tokens').values({
    project_id: 'project-1',
    value: 'pat-foobarbaz',
  }).execute();

  await db.insertInto('project_collaborators').values({
    access_role: Database.projectAccessRole.editor,
    project_id: 'project-1',
    user_id: 'user-2',
  }).execute();
}
