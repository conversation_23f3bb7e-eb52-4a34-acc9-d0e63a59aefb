import { Kysely } from 'kysely';

import * as baseTypes from '../generated/010_billing_types.js';
import * as newTypes from '../generated/011_delayed_queue_types.js';
import { schema as base } from './010_billing.js';
import { mu } from './mu.js';

export const schema = mu.edit<baseTypes.DB, newTypes.DB>(base, {
  queued_jobs: {
    columns: {
      execute_after_timestamp_seconds: { type: 'integer', is: ['not-null'] },
    }
  },

  active_jobs: {
    columns: {
      execute_after_timestamp_seconds: { type: 'integer', is: ['not-null'] },
    },
  },

  archived_jobs: {
    columns: {
      execute_after_timestamp_seconds: { type: 'integer', is: ['not-null'] },
    },
  }
});

export async function up(db: Kysely<baseTypes.DB>): Promise<void> {
  await mu.migration(db, base, schema).run({
    active_jobs: job => ({
      ...job,
      execute_after_timestamp_seconds: 0,
    }),
    archived_jobs: (job) => ({
      ...job,
      execute_after_timestamp_seconds: 0,
    }),
    queued_jobs: (job) => ({
      ...job,
      execute_after_timestamp_seconds: 0,
    }),
  });
}

export async function down(db: Kysely<newTypes.DB>): Promise<void> {
  await mu.migration(db, schema, base).run({
    active_jobs: job => ({
      ...job,
      execute_after_timestamp_seconds: undefined,
    }),
    archived_jobs: (job) => ({
      ...job,
      execute_after_timestamp_seconds: undefined,
    }),
    queued_jobs: (job) => ({
      ...job,
      execute_after_timestamp_seconds: undefined,
    }),
  });
}
