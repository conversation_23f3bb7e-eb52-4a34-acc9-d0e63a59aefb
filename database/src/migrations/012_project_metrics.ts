import { Kysely } from 'kysely';

import { DB as baseDB } from '../generated/011_delayed_queue_types.js';
import { DB as newDB } from '../generated/012_project_metrics_types.js';
import { schema as baseSchema } from './011_delayed_queue.js';
import { mu } from './mu.js';

export const schema = mu.edit<baseDB, newDB>(baseSchema, {
  project_daily_metrics: {
    columns: {
      project_id: { ref: 'projects.project_id', is: ['not-null'], onDelete: 'cascade' },
      day_timestamp_ms: { type: 'bigint', is: ['not-null'] },
      reports_count: { type: 'integer', is: ['not-null'] },
      tests_count: { type: 'integer', is: ['not-null'] },
      reports_bytes: { type: 'bigint', is: ['not-null'] },
      attachments_bytes: { type: 'bigint', is: ['not-null'] },
    },
    uniques: {
      'project_id + day_timestamp_ms': { columns: ['project_id', 'day_timestamp_ms'] },
    },
  },
});

export async function up(db: Kysely<baseDB>): Promise<void> {
  await mu.transform(db, baseSchema, schema);
}

export async function down(db: Kysely<any>): Promise<void> {
  await mu.transform(db, schema, baseSchema);
}
