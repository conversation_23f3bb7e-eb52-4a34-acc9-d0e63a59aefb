import { Kysely } from 'kysely';
import { DB as DB_BASE } from '../generated/001_initial_types.js';
import { DB as DB_NEW } from '../generated/002_queues_types.js';
import { schema as base } from './001_initial.js';
import { mu } from './mu.js';

export const schema: mu.Schema<DB_NEW> = {
  ...base,
  /* adding a bunch of new tables */
  queued_jobs: {
    columns: {
      id: { type: 'serial', is: ['primary'] },
      queue_name: { type: 'varchar(255)', is: ['not-null'] },
      job_id: { type: 'varchar(255)', is: ['not-null'] },
      category: { type: 'varchar(255)' },
      data: { type: 'json', is: ['not-null'] },
      retry: { type: 'integer', is: ['not-null'] },
      submitted_timestamp_seconds: { type: 'integer', is: ['not-null'] },
      execution_timestamp_seconds: { type: 'integer' },
      execution_duration_seconds: { type: 'integer' },
      execution_error: { type: 'text' },
      execution_result: { type: 'varchar(10)' },
    },
    uniques: {
      'queue_name + job_id': { columns: ['queue_name', 'job_id'] },
    },
    indexes: {
      // This index is required to fetch jobs
      'queue_name + job_id': { columns: ['queue_name', 'job_id'], },
      // This index is for querying jobs by category inside queue.
      'queue_name + category': { columns: ['queue_name', 'category'], },
    },
  },

  active_jobs: {
    columns: {
      id: { type: 'integer', is: ['primary'] },
      queue_name: { type: 'varchar(255)', is: ['not-null'] },
      job_id: { type: 'varchar(255)', is: ['not-null'] },
      category: { type: 'varchar(255)' },
      data: { type: 'json', is: ['not-null'] },
      retry: { type: 'integer', is: ['not-null'] },
      submitted_timestamp_seconds: { type: 'integer', is: ['not-null'] },
      execution_timestamp_seconds: { type: 'integer', is: ['not-null'] },
      execution_duration_seconds: { type: 'integer' },
      execution_error: { type: 'text' },
      execution_result: { type: 'varchar(10)' },
    },
    uniques: {
      'queue_name + job_id': { columns: ['queue_name', 'job_id'] },
    },
    indexes: {
      // This index is required for bookkeeping of stale jobs
      'queue_name + job_id': { columns: ['queue_name', 'job_id'], },
      // This index is for querying jobs by category inside queue.
      'queue_name + category': { columns: ['queue_name', 'category'], },
    },
  },

  archived_jobs: {
    columns: {
      id: { type: 'integer', is: ['primary'] },
      queue_name: { type: 'varchar(255)', is: ['not-null'] },
      job_id: { type: 'varchar(255)', is: ['not-null'] },
      category: { type: 'varchar(255)' },
      data: { type: 'json', is: ['not-null'] },
      retry: { type: 'integer', is: ['not-null'] },
      submitted_timestamp_seconds: { type: 'integer', is: ['not-null'] },
      execution_timestamp_seconds: { type: 'integer', is: ['not-null'] },
      execution_duration_seconds: { type: 'integer' },
      execution_error: { type: 'text' },
      execution_result: { type: 'varchar(10)', is: ['not-null'] },
    },
    indexes: {
      // This index is for bookkeeping archived jobs
      'queue_name + execuction_timestamp_seconds': { columns: ['queue_name', 'execution_timestamp_seconds'], }
    }
  }
};

export async function up(db: Kysely<DB_BASE>): Promise<void> {
  await mu.transform(db, base, schema);
}

export async function down(db: Kysely<DB_NEW>): Promise<void> {
  await mu.transform(db, schema, base);
}