import { Kysely } from 'kysely';
import { DB as DB_NEW } from '../generated/005_project_slug_types.js';

import { DB as DB_BASE } from '../generated/004_organizations_types.js';
import { schema as base } from './004_organizations.js';
import { mu } from './mu.js';

export const schema: mu.Schema<DB_NEW> = {
  ...base,
  projects: {
    ...base.projects,
    columns: {
      ...base.projects.columns,
      project_slug: { type: 'varchar(255)', is: ['not-null'] },
      general_access: undefined,
      // This is nullable; visibility is "private" if not set explicitly.
      visibility: { type: 'integer' },
    },
    uniques: {
      'org_id + project_slug': { columns: ['org_id', 'project_slug'] },
    }
  }
};

export async function up(db: Kysely<DB_BASE>): Promise<void> {
  // Fill in project_slug for all projects
  const projects = await db
    .selectFrom('projects')
    .select(['project_id', 'project_name'])
    .execute();
  
  await mu.transform(db, base, schema, async db => {
    for (const project of projects) {
      await db
        .updateTable('projects')
        .set({ project_slug: project.project_name })
        .where(`projects.project_id`, '=', project.project_id)
        .execute();
    }
  });
}

export async function down(db: Kysely<DB_NEW>): Promise<void> {
  await mu.transform(db, schema, base);
}

export async function mock(db: Kysely<DB_NEW>): Promise<void> {
  await db
    .updateTable('projects')
    .set({  
      visibility: 777, // this is "public" visibility - for everybody.
    })
    .execute();
}
