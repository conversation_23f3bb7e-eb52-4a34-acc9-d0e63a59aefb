import { Kysely } from 'kysely';

import * as baseTypes from '../generated/016_queue_heartbeat_types.js';
import * as newTypes from '../generated/017_project_last_updated_types.js';

import { schema as baseSchema } from './016_queue_heartbeat.js';
import { mu } from './mu.js';

export const schema = mu.edit<baseTypes.DB, any>(baseSchema, {
  projects: {
    columns: {
      last_upload_timestamp_seconds: { type: 'integer' },
    }
  },
});

export async function up(db: Kysely<baseTypes.DB>): Promise<void> {
  await mu.transform(db, baseSchema, schema);
}

export async function down(db: Kysely<newTypes.DB>): Promise<void> {
  await mu.transform(db, schema, baseSchema);
}
