import { Kysely } from 'kysely';

import * as baseTypes from '../generated/015_project_settings_types.js';
import * as newTypes from '../generated/016_queue_heartbeat_types.js';
import { schema as baseSchema } from './015_project_settings.js';
import { mu } from './mu.js';

export const schema = mu.edit<baseTypes.DB, any>(baseSchema, {
  queued_jobs: {
    columns: {
      execution_heartbeat_timestamp_seconds: { type: 'integer' },
    },
  },
  active_jobs: {
    columns: {
      execution_heartbeat_timestamp_seconds: { type: 'integer', is: ['not-null'] },
    },
  },
  archived_jobs: {
    columns: {
      execution_heartbeat_timestamp_seconds: { type: 'integer' },
    },
  },
});

export async function up(db: Kysely<baseTypes.DB>): Promise<void> {
  await mu.transform(db, baseSchema, schema);
}

export async function down(db: <PERSON>ysely<newTypes.DB>): Promise<void> {
  await mu.transform(db, schema, baseSchema);
}
