import { randomUUID } from 'crypto';
import { Kysely } from 'kysely';
import { DB as DB_BASE } from '../generated/003_github_access_tokens_types.js';
import { DB as DB_NEW } from '../generated/004_organizations_types.js';
import { schema as base } from './003_github_access_tokens.js';
import { mu } from './mu.js';

export const schema: mu.Schema<DB_NEW> = {
  ...base,

  organizations: {
    columns: {
      org_id: { type: mu.UUID_V4_TEXT_TYPE,is: ['primary'] },
      org_name: { type: 'varchar(255)', is: ['not-null'] },
      org_slug: { type: 'varchar(255)', is: ['not-null'] },
      owner_id: { type: mu.UUID_V4_TEXT_TYPE, is: ['not-null'] },
    },
    indexes: {
     'org_slug': { columns: ['org_slug'], }, // find orgs by slug
    },
  },

  organization_members: {
    columns: {
      org_id: { ref: 'organizations.org_id', is: ['not-null'], onDelete: 'cascade' },
      user_id: { ref: 'users.user_id', is: ['not-null'], onDelete: 'cascade' },
      access_role: { type: `integer`, is: ['not-null'] },
    },
    uniques: {
      'org_id + user_id': { columns: ['org_id', 'user_id'] },
    }
  },
  
  projects: {
    ...base.projects,
    columns: {
      ...base.projects.columns,
      // Add an org_id
      org_id: { ref: 'organizations.org_id', is: ['not-null'], onDelete: 'cascade' },
      // Drop owner_id
      owner_id: undefined,
    },
    indexes: {
      'org_id': { columns: ['org_id'] }, // an index to please mysql
    },
    uniques: {
      'org_id + project_name': { columns: ['org_id', 'project_name'] },
    }
  },
};

export async function up(db: Kysely<DB_BASE>): Promise<void> {
  // For all existing users, create an organization with the same name and
  // remember which userId moves to which ownerId.
  const users = await db
    .selectFrom('users')
    .select(['user_id', 'user_login'])
    .execute();
  // Fill in org_id for all projects
  const projects = await db
    .selectFrom('projects')
    .select(['owner_id', 'project_id'])
    .execute();

  await mu.transform(db, base, schema, async db => {
    const userToOrgs = new Map<string, string>();
    for (const user of users) {
      const orgId = randomUUID();
      userToOrgs.set(user.user_id, orgId);
      await db
        .insertInto('organizations')
        .values({
          org_id: orgId,
          org_name: user.user_login,
          org_slug: user.user_login,
          owner_id: user.user_id,
        })
        .execute();
    }
    for (const project of projects) {
      await db
        .updateTable('projects')
        .set({ org_id: userToOrgs.get(project.owner_id) })
        .where(`projects.project_id`, '=', project.project_id)
        .execute();
    }  
  });
}

export async function down(db: Kysely<DB_NEW>): Promise<void> {
    // For all existing users, create an organization with the same name and
  // remember which userId moves to which ownerId.
  const users = await db
    .selectFrom('users')
    .select(['user_id', 'user_login'])
    .execute();
  const orgs = await db
    .selectFrom('organizations')
    .selectAll()
    .execute();
  // Fill in org_id for all projects
  const projects = await db
    .selectFrom('projects')
    .select(['org_id', 'project_id'])
    .execute();

  await mu.transform(db, schema, base, async db => {
    for (const project of projects) {
      await db
        .updateTable('projects')
        .set({ owner_id: orgs.find(org => org.org_id === project.org_id)!.owner_id })
        .where(`projects.project_id`, '=', project.project_id)
        .execute();
    }
  });
}