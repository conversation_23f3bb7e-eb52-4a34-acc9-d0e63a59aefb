import { Kysely } from 'kysely';

import * as baseTypes from '../generated/018_queue_worker_name_types.js';
import * as newTypes from '../generated/019_drop_github_oauth_user_tokens_types.js';
import { schema as baseSchema } from './018_queue_worker_name.js';

import { mu } from './mu.js';

export const schema = mu.edit<baseTypes.DB, any>(baseSchema, {
  github_oauth_user_tokens: undefined,
});

export async function up(db: Kysely<baseTypes.DB>): Promise<void> {
  await mu.transform(db, baseSchema, schema);
}

export async function down(db: Kysely<newTypes.DB>): Promise<void> {
  await mu.transform(db, schema, baseSchema);
}
