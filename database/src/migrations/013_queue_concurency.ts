import { Kysely } from 'kysely';


import * as baseTypes from '../generated/012_project_metrics_types.js';
import * as newTypes from '../generated/013_queue_concurency_types.js';
import { schema as baseSchema } from './012_project_metrics.js';
import { mu } from './mu.js';

export const schema = mu.edit<baseTypes.DB, newTypes.DB>(baseSchema, {
  queued_jobs: {
    columns: {
      concurrency_id: { type: 'varchar(255)', },
    }
  },

  active_jobs: {
    columns: {
      concurrency_id: { type: 'varchar(255)', },
    },
    uniques: {
      'queue_name + concurrency_id': { columns: ['queue_name', 'concurrency_id'] },
    },
  },

  archived_jobs: {
    columns: {
      concurrency_id: { type: 'varchar(255)', },
    },
  }
});

export async function up(db: Kysely<baseTypes.DB>): Promise<void> {
  await mu.migration(db, baseSchema, schema).run({
    active_jobs: job => ({  
      ...job,
      concurrency_id: undefined,
    }),
    archived_jobs: (job) => ({
      ...job,
      concurrency_id: undefined,
    }),
    queued_jobs: (job) => ({
      ...job,
      concurrency_id: undefined,
    }),
  });
}

export async function down(db: Kysely<newTypes.DB>): Promise<void> {
  await mu.migration(db, schema, baseSchema).run({
    active_jobs: job => ({  
      ...job,
      concurrency_id: undefined,
    }),
    archived_jobs: (job) => ({
      ...job,
      concurrency_id: undefined,
    }),
    queued_jobs: (job) => ({
      ...job,
      concurrency_id: undefined,
    }),
  });
}
