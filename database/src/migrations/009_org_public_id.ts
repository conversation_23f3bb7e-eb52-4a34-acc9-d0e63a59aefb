import { Kysely } from 'kysely';

import { randomUUID } from 'crypto';
import * as baseTypes from '../generated/008_int_user_id_types.js';
import * as newTypes from '../generated/009_org_public_id_types.js';
import { schema as base } from './008_int_user_id.js';
import { mu } from './mu.js';

export const schema = mu.edit<baseTypes.DB, newTypes.DB>(base, {
  organizations: {
    columns: {
      // Create public id for organization
      org_public_id: { type: mu.UUID_V4_TEXT_TYPE, is: ['not-null'], onDelete: 'cascade' },
    }
  }
});

export async function up(db: Kysely<baseTypes.DB>): Promise<void> {
  await mu.migration(db, base, schema).run({
    organizations: (element) => ({
      ...element,
      org_public_id: randomUUID(),
    })
  });
}

export async function down(db: Kysely<newTypes.DB>): Promise<void> {
  await mu.migration(db, schema, base).run({
    organizations: (element) => ({
      ...element,
      org_public_id: undefined,
    })
  });
}
