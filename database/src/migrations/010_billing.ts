import { Kysely } from 'kysely';

import * as baseTypes from '../generated/009_org_public_id_types.js';
import * as newTypes from '../generated/010_billing_types.js';
import { schema as base } from './009_org_public_id.js';
import { mu } from './mu.js';

export const schema = mu.edit<baseTypes.DB, newTypes.DB>(base, {
  stripe_customers: {
    columns: {
      customer_id: { type: 'varchar(255)', is: ['primary'] },
      org_id: { ref: 'organizations.org_id', onDelete: 'cascade', is: ['not-null'] },
      email: { type: 'varchar(255)' },
    },
    uniques: {
      'customer_id + org_id': { columns: ['customer_id', 'org_id'] },
      'email': { columns: ['email'] },
    },
  },
  stripe_subscriptions: {
    columns: {
      subscription_id: { type: 'varchar(255)', is: ['primary'] },
      subscription: { type: 'json', is: ['not-null'] },
    },
  },
});

export async function up(db: Kysely<baseTypes.DB>): Promise<void> {
  await mu.transform(db, base, schema);
}

export async function down(db: Kysely<newTypes.DB>): Promise<void> {
  await mu.transform(db, schema, base);
}
