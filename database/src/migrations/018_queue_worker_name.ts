import { Kysely } from 'kysely';

import * as baseTypes from '../generated/017_project_last_updated_types.js';
import * as newTypes from '../generated/018_queue_worker_name_types.js';

import { schema as baseSchema } from './017_project_last_updated.js';

import { mu } from './mu.js';

export const schema = mu.edit<baseTypes.DB, any>(baseSchema, {
  queued_jobs: {
    columns: {
      worker_name: { type: 'varchar(255)', },
    },
  },
  active_jobs: {
    columns: {
      worker_name: { type: 'varchar(255)', },
    },
  },
  archived_jobs: {
    columns: {
      worker_name: { type: 'varchar(255)', },
    },
  },
});

export async function up(db: Kysely<baseTypes.DB>): Promise<void> {
  await mu.transform(db, baseSchema, schema);
}

export async function down(db: Kysely<newTypes.DB>): Promise<void> {
  await mu.transform(db, schema, baseSchema);
}
