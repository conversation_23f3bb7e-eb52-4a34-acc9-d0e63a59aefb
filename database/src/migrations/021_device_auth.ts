import { Kysely } from 'kysely';

import * as baseTypes from '../generated/020_user_sessions_types.js';
import * as newTypes from '../generated/021_device_auth_types.js';
import { schema as baseSchema } from './020_user_sessions.js';

import { mu } from './mu.js';

export const schema = mu.edit<baseTypes.DB, newTypes.DB>(baseSchema, {
  device_auth_requests: {
    columns: {
      request_id: { type: 'integer', is: ['primary', 'auto-inc'] },
      expiration_timestamp_seconds: { type: 'integer', is: ['not-null'] },
      device_name: { type: 'varchar(100)', is: ['not-null'] },
      client_id: { type: 'varchar(100)', is: ['not-null'] },
      user_code: { type: 'varchar(50)', is: ['not-null' ] },
      device_code: { type: mu.UUID_V4_TEXT_TYPE, is: ['not-null' ]},
      approver_id: { ref: 'users.user_id', onDelete: 'cascade' },
    },
    indexes: {
    },
    uniques: {
      'device_code': { columns: ['device_code'] },
      'user_code': { columns: ['user_code'] },
    },
  }
});

export async function up(db: Kysely<baseTypes.DB>): Promise<void> {
  await mu.transform(db, baseSchema, schema);
}

export async function down(db: Kysely<newTypes.DB>): Promise<void> {
  await mu.transform(db, schema, baseSchema);
}
