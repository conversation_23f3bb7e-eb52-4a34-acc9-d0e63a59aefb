import { Kysely } from 'kysely';

import { randomUUID } from 'crypto';
import * as baseTypes from '../generated/006_int_project_id_types.js';
import * as newTypes from '../generated/007_int_org_id_types.js';
import { schema as base } from './006_int_project_id.js';
import { mu } from './mu.js';

export const schema = mu.edit<baseTypes.DB, newTypes.DB>(base, {
  organizations: {
    columns: {
      // Introduce integer id to manage all inter-relationships.
      // There are no external resources associated with orgs (yet), so there's no need
      // for public ID.
      org_id: { type: 'integer', is: ['primary', 'auto-inc'] },
    },
  },
});

export async function up(db: Kysely<baseTypes.DB>): Promise<void> {
  await mu.migration(db, base, schema).run({
    organizations: (e) => ({
      ...e,
      org_id: undefined,
    }),
    organization_members: (e, pushed, old) => ({
      ...e,
      org_id: pushed('organizations', {
        org_slug: old('organizations', { org_id: e.org_id }).org_slug,
      }).org_id,
    }),
    projects: (e, pushed, old) => ({
      ...e,
      org_id: pushed('organizations', {
        org_slug: old('organizations', { org_id: e.org_id }).org_slug,
      }).org_id,
    }),
  });
}

export async function down(db: Kysely<newTypes.DB>): Promise<void> {
  await mu.migration(db, schema, base).run({
    organizations: (e) => ({
      ...e,
      org_id: randomUUID(),
    }),
    organization_members: (e, pushed, old) => ({
      ...e,
      org_id: pushed('organizations', {
        org_slug: old('organizations', { org_id: e.org_id }).org_slug,
      }).org_id,
    }),
    projects: (e, pushed, old) => ({
      ...e,
      org_id: pushed('organizations', {
        org_slug: old('organizations', { org_id: e.org_id }).org_slug,
      }).org_id,
    }),
  });
}
