import { Kysely } from 'kysely';

import * as baseTypes from '../generated/021_device_auth_types.js';
import * as newTypes from '../generated/022_acceptable_flakiness_types.js';
import { schema as baseSchema } from './021_device_auth.js';

import { mu } from './mu.js';

export const schema = mu.edit<baseTypes.DB, newTypes.DB>(baseSchema, {
  projects: {
    columns: {
      accepted_flakiness_ratio: { type: 'float8' },
      regression_window_days: { type: 'int4' },
      default_timezone: { type: 'varchar(64)'},
    },
  },
});

export async function up(db: Kysely<baseTypes.DB>): Promise<void> {
  await mu.transform(db, baseSchema, schema);
}

export async function down(db: Kysely<newTypes.DB>): Promise<void> {
  await mu.transform(db, schema, baseSchema);
}
