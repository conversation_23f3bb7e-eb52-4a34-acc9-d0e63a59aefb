import { Kysely } from 'kysely';

import { randomUUID } from 'crypto';
import * as baseTypes from '../generated/007_int_org_id_types.js';
import * as newTypes from '../generated/008_int_user_id_types.js';
import { schema as base } from './007_int_org_id.js';
import { mu } from './mu.js';

export const schema = mu.edit<baseTypes.DB, newTypes.DB>(base, {
  users: {
    columns: {
      // So we introduce integer id to manage all inter-relationships
      user_id: { type: 'integer', is: ['primary', 'auto-inc'] },
      // However, we also need to have a public_id that we will use to refer to users.
      user_public_id: { type: mu.UUID_V4_TEXT_TYPE, is: ['not-null'] },
    },
  },
  organizations: {
    columns: {
      // fix wrong schema from the previous migraitons.
      owner_id: { ref: 'users.user_id', is: ['not-null'], onDelete: 'cascade' },
    }
  }
});

export async function up(db: Kysely<baseTypes.DB>): Promise<void> {
  await mu.migration(db, base, schema).run({
    users: (e, pushed, old) => ({
      ...e,
      user_id: undefined,
      user_public_id: e.user_id,
    }),
    organizations: (e, pushed, old) => ({
      ...e,
      owner_id: pushed('users', {
        user_public_id: e.owner_id,
      }).user_id,
    }),
    github_oauth_user_tokens: (e, pushed, old) => ({
      ...e,
      user_id: pushed('users', {
        user_public_id: e.user_id,
      }).user_id,
    }),
    organization_members: (e, pushed, old) => ({
      ...e,
      user_id: pushed('users', {
        user_public_id: e.user_id,
      }).user_id,
    }),
    project_collaborators: (e, pushed, old) => ({
      ...e,
      user_id: pushed('users', {
        user_public_id: e.user_id,
      }).user_id,
    }),
  });
}

export async function down(db: Kysely<newTypes.DB>): Promise<void> {
  await mu.migration(db, schema, base).run({
    users: (e, pushed, old) => ({
      ...e,
      user_id: randomUUID(),
      user_public_id: undefined,
    }),
    organizations: (e, pushed, old) => ({
      ...e,
      owner_id: pushed('users', {
        github_id: old('users', { user_id: e.owner_id }).github_id,
      }).user_id,
    }),
    github_oauth_user_tokens: (e, pushed, old) => ({
      ...e,
      user_id: pushed('users', {
        github_id: old('users', { user_id: e.user_id }).github_id,
      }).user_id,
    }),
    organization_members: (e, pushed, old) => ({
      ...e,
      user_id: pushed('users', {
        github_id: old('users', { user_id: e.user_id }).github_id,
      }).user_id,
    }),
    project_collaborators: (e, pushed, old) => ({
      ...e,
      user_id: pushed('users', {
        github_id: old('users', { user_id: e.user_id }).github_id,
      }).user_id,
    }),
  });
}
