import { Kysely } from 'kysely';

import * as baseTypes from '../generated/013_queue_concurency_types.js';
import * as newTypes from '../generated/014_product_plans_types.js';
import { schema as baseSchema } from './013_queue_concurency.js';
import { mu } from './mu.js';

export const schema = mu.edit<baseTypes.DB, any>(baseSchema, {
  project_daily_metrics: undefined,
  stripe_customers: undefined,
  stripe_subscriptions: undefined,

  product_plans: {
    columns: {
      // So we introduce integer id to manage all inter-relationships
      plan_id: { type: 'integer', is: ['primary', 'auto-inc'] },
      plan_public_id: { type: mu.UUID_V4_TEXT_TYPE, is: ['not-null'] },
      name: { type: 'varchar(255)', is: ['not-null'] },
      org_public_id: { ref: 'organizations.org_public_id', onDelete: 'cascade' },
      seats: { type: 'integer' },
      seats_price_id: { type: 'varchar(255)' },
      storage_price_id: { type: 'varchar(255)' },
      testruns_price_id: { type: 'varchar(255)' },
      trial_days: { type: 'integer' },
    },
  },

  organizations: {
    columns: {},
    uniques: {
      'org_public_id': { columns: ['org_public_id'] },
    }
  },
});

export async function up(db: Kysely<baseTypes.DB>): Promise<void> {
  await mu.transform(db, baseSchema, schema);
}

export async function down(db: Kysely<newTypes.DB>): Promise<void> {
  await mu.transform(db, schema, baseSchema);
}
