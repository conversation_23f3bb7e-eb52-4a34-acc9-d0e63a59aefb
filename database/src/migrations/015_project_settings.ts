import { Kysely } from 'kysely';

import * as baseTypes from '../generated/014_product_plans_types.js';
import * as newTypes from '../generated/015_project_settings_types.js';
import { schema as baseSchema } from './014_product_plans.js';
import { mu } from './mu.js';

export const schema = mu.edit<baseTypes.DB, any>(baseSchema, {
  projects: {
    columns: {
      report_time_is_upload_time: { type: 'boolean' },
      preferred_data_retention_days: { type: 'integer' },
    }
  },
  product_plans: {
    columns: {
      max_data_retention_days: { type: 'integer', is: ['not-null'] },
    },
  },
});

export async function up(db: Kysely<baseTypes.DB>): Promise<void> {
  await mu.transform(db, baseSchema, schema);
}

export async function down(db: Kysely<newTypes.DB>): Promise<void> {
  await mu.transform(db, schema, baseSchema);
}
