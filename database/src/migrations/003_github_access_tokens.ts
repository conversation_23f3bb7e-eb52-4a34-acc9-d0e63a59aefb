import assert from 'assert';
import { Kysely } from 'kysely';
import { aes } from '../aes.js';
import { DB as DB_BASE } from '../generated/002_queues_types.js';
import { DB as DB_NEW } from '../generated/003_github_access_tokens_types.js';
import { schema as base } from './002_queues.js';

import { mu } from './mu.js';

export const schema: mu.Schema<DB_NEW> = {
  ...base,
  // Add new tables
  github_oauth_user_tokens: {
    columns: {
      user_id: { ref: 'users.user_id', is: ['primary'], onDelete: 'cascade', customName: 'fk__github_oauth_user_tokens__user_id' },
      access_token_encoded: { type: 'text', is: ['not-null'] },
      refresh_token_encoded: { type: 'text', is: ['not-null'] },
      access_expiration_timestamp_seconds: { type: 'integer', is: ['not-null'] },
      refresh_expiration_timestamp_seconds: { type: 'integer', is: ['not-null'] },  
    },
  },
  github_app_installations: {
    columns: {
      project_id: { ref: 'projects.project_id', is: ['primary'], onDelete: 'cascade', customName: 'fk__github_app_installations__project_id' },
      installation_id_encoded: { type: 'text', is: ['not-null'] },
    }
  },

  // encode & rename token value.
  personal_access_tokens: {
    ...base.personal_access_tokens,
    columns: {
      ...base.personal_access_tokens.columns,
      value: undefined, // remove old value
      pat_encoded: { type: 'varchar(255)', is: ['not-null'] }, // and add a new pat_encoded
    }
  },
};

export async function up(db: Kysely<DB_BASE>): Promise<void> {
  // encode all previous tokens with crypto.
  const tokens = await db
    .selectFrom('personal_access_tokens')
    .select(['project_id', 'value'])
    .execute();

  const key = process.env.DB_ENCRYPTION_KEY;
  assert(key, 'DB_ENCRYPTION_KEY must be provided');
  
  await mu.transform(db, base, schema, async (db) => {
    for (const token of tokens) {
      const encodedValue = aes.encrypt(key, token.value);
      await db
        .updateTable('personal_access_tokens')
        .set({ pat_encoded: encodedValue })
        .where('project_id', '=', token.project_id)
        .execute();
    }
  });
}

export async function down(db: Kysely<DB_NEW>): Promise<void> {
  // decode all encrypted tokens.
  const tokens = await db
    .selectFrom('personal_access_tokens')
    .select(['project_id', 'pat_encoded'])
    .execute();

  const key = process.env.DB_ENCRYPTION_KEY;
  assert(key, 'DB_ENCRYPTION_KEY must be provided');

  await mu.transform(db, schema, base, async db => {
    for (const token of tokens) {
      const decodedValue = aes.decrypt(key, token.pat_encoded);
      await db
        .updateTable('personal_access_tokens')
        .set({ value: decodedValue })
        .where('project_id', '=', token.project_id)
        .execute();
    }
  });
}
