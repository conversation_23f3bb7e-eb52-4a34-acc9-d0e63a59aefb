import { $ } from 'execa';
import fs from 'fs';
import * as path from 'path';
import { Postgres } from 'podkeeper';
import url from 'url';
import { Database, DatabaseConfig } from './database.js';

const __filename = url.fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function typesFile(outputDir: string, migrationName: string): string {
  return path.join(outputDir, migrationName.split('.')[0] + '_types.ts');
}

const ProjectPublicId = `import('../database.js').ProjectPublicId`;
const UserPublicId = `import('../database.js').UserPublicId`;
const OrgPublicId = `import('../database.js').OrgPublicId`;
const ProductPlanPublicId = `import('../database.js').ProductPlanPublicId`;
const UserSessionId = `import('../database.js').UserSessionId`;
const UserSessionPublicId = `import('../database.js').UserSessionPublicId`;
const ProjectId = `import('../database.js').ProjectId`;
const OrgId = `import('../database.js').OrgId`;
const UserId = `import('../database.js').UserId`;
const ProductPlanId = `import('../database.js').ProductPlanId`;
const SourceAuthType = `import('../database.js').SourceAuthType`;
const ProjectAccessRole = `import('../database.js').ProjectAccessRole`;
const OrgAccessRole = `import('../database.js').OrgAccessRole`;
const ProjectVisibility = `import('../database.js').ProjectVisibility`;
const OrgBillingStatus = `import('../database.js').OrgBillingStatus`;
const AppClientId = `import('../database.js').AppClientId`;
const DeviceAuthRequestId = `import('../database.js').DeviceAuthRequestId`;

const TYPE_OVERRIDES = {
  // Users
  'users.user_id': `Generated<${UserId}>`,
  'users.user_public_id': UserPublicId,

  // User Tokens
  'user_sessions.session_id': `Generated<${UserSessionId}>`,
  'user_sessions.session_public_id': UserSessionPublicId,
  'user_sessions.user_id': UserId,
  'user_sessions.client_id': AppClientId,

  // Device Auth
  'device_auth_requests.request_id': `Generated<${DeviceAuthRequestId}>`,
  'device_auth_requests.client_id': AppClientId,
  'device_auth_requests.approver_id': `${UserId} | null`,

  // Projects
  'projects.org_id': OrgId,
  'projects.project_id': `Generated<${ProjectId}>`,
  'projects.project_public_id': ProjectPublicId,
  'projects.source_auth_type': SourceAuthType,
  'projects.visibility': `${ProjectVisibility} | null`,
  
  // Project Collaborators
  'project_collaborators.access_role': ProjectAccessRole,
  'project_collaborators.project_id': ProjectId,
  'project_collaborators.user_id': UserId,

  // Product Plans
  'product_plans.plan_id': `Generated<${ProductPlanId}>`,
  'product_plans.plan_public_id': ProductPlanPublicId,
  'product_plans.org_public_id': `${OrgPublicId} | null`,

  // Organizations
  'organizations.org_id': `Generated<${OrgId}>`,
  'organizations.org_public_id': OrgPublicId,
  'organizations.owner_id': UserId,

  // Organization Members
  'organization_members.access_role': OrgAccessRole,
  'organization_members.org_id': OrgId,
  'organization_members.user_id': UserId,

  // github app installations
  'github_app_installations.project_id': ProjectId,

  // Github oauth
  'github_oauth_user_tokens.user_id': UserId,

  // Personal access tokens
  'personal_access_tokens.project_id': ProjectId,
};

async function runCodegen(service: Postgres, outputFile: string, overrides: any = {}) {
  await $({
    cwd: path.join(__dirname, '..'),
  })`npx kysely-codegen --url ${service.databaseUrl()} --out-file ${outputFile} --overrides=${JSON.stringify({ columns: overrides })}`;
}

export async function generateTypes(outputDir: string, generateAll: boolean) {
  const service = await Postgres.start();
 
  const options: DatabaseConfig = {
    ...service.connectOptions(),
    encryptionKey: 'randomkey',
  };
  if (generateAll) {
    await fs.promises.rm(outputDir, { force: true, recursive: true }).catch(e => {});
    await fs.promises.mkdir(outputDir, { recursive: true });  

    const migrations = await Database.migrate(options, migrator => migrator.getMigrations());
    for (const migration of migrations) {
      const { error, results } = await Database.migrate(options, migrator => migrator.migrateTo(migration.name));
      results?.forEach((it) => {
        if (it.status === 'Success') {
          console.log(`migration "${it.migrationName}" was executed successfully`)
        } else if (it.status === 'Error') {
          console.error(`failed to execute migration "${it.migrationName}"`)
        }
      });
  
      if (error) {
        console.error('failed to migrate');
        console.error(error);
        process.exitCode = 1;
        return;
      }

      await runCodegen(service, typesFile(outputDir, migration.name));
    }
  } else {
    const error = await Database.migrate(options, async migrator => {
      const migrations = await migrator.getMigrations();
      const last = await migrations.at(-1)!;
      const { error, results } = await migrator.migrateToLatest();
      if (error)
        return error;
      await runCodegen(service, typesFile(outputDir, last.name));
    });

    if (error) {
      console.error('failed to migrate');
      console.error(error);
      process.exitCode = 1;
      return;
    }
  }

  // All migration types are generated WITHOUT overrides.
  // However, type overrides are required for the latest migration so that database.ts can compile
  // against it.
  await runCodegen(service, typesFile(outputDir, 'latest'), TYPE_OVERRIDES);
  await service.stop();  
}
