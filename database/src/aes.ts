#!/usr/bin/env node
import crypto from 'crypto';

// Generate a random 32-byte encryption key (store this securely, e.g., in an environment variable)

const IV_LENGTH = 16; // AES block size
const KEY_PREFIX = 'fkdb_';
const ALGO = 'aes-256-gcm';

export namespace aes {

  export function createKey() {
    return KEY_PREFIX + crypto.randomBytes(32).toString('hex');
  }

  function keyToBytes(key: string) {
    return Buffer.from(key.substring(KEY_PREFIX.length), 'hex');
  }

  export function encrypt(key: string, plaintext: string) {
    const iv = crypto.randomBytes(IV_LENGTH);
    const cipher = crypto.createCipheriv(ALGO, keyToBytes(key), iv);

    let encrypted = cipher.update(plaintext, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    // Get the authentication tag to ensure integrity
    const authTag = cipher.getAuthTag().toString('hex');

    // Return the iv, auth tag, and encrypted text in a single string
    return `${iv.toString('hex')}:${authTag}:${encrypted}`;
  }

  // Decrypt function
  export function decrypt(key: string, encryptedText: string) {
    const [iv, authTag, encrypted] = encryptedText.split(':');
    const decipher = crypto.createDecipheriv(
      ALGO,
      keyToBytes(key),
      Buffer.from(iv, 'hex')
    );

    // Set the authentication tag for integrity check
    decipher.setAuthTag(Buffer.from(authTag, 'hex'));

    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');

    return decrypted;
  }
}
