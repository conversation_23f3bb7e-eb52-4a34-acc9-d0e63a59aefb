import { Brand } from '@flakiness/shared/common/utils.js';
import assert from 'assert';
import { randomInt, randomUUID } from 'crypto';
import { promises as fs } from 'fs';
import {
  Dialect,
  FileMigrationProvider,
  Insertable,
  Kysely,
  Migrator,
  PostgresDialect,
  Selectable,
  sql,
  Transaction,
  Updateable
} from 'kysely';
import * as path from 'path';
import pg from 'pg';
import url from "url";
import { aes } from './aes.js';
import * as dbtypes from './generated/latest_types.js';
import { ActiveJob, ArchivedJob, Queue, QueuedJob } from './queue.js';

const __filename = url.fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);


export type ProjectPublicId = Brand<string, 'ProjectPublicId'>;
export type UserPublicId = Brand<string, 'UserPublicId'>;
export type OrgPublicId = Brand<string, 'OrgPublicId'>;
export type UserSessionPublicId = Brand<string, 'UserSessionPublicId'>;
export type ProductPlanPublicId = Brand<string, 'ProductPlanPublicId'>;

// This is the id of the application that authorizes with flakiness.io.
export type AppClientId = Brand<string, 'AppClientId'>;
export type DeviceAuthRequestId = Brand<number, 'DeviceAuthRequestId'>;

export type UserId = Brand<number, 'UserId'>;
export type UserSessionId = Brand<number, 'UserSessionId'>;
export type ProjectId = Brand<number, 'ProjectId'>;
export type OrgId = Brand<number, 'OrgId'>;
export type ProductPlanId = Brand<number, 'ProductPlanId'>;
export type SourceAuthType = Brand<number, 'SourceAuthType'>;
export type ProjectAccessRole = Brand<number, 'ProjectAccessRole'>;
export type OrgAccessRole = Brand<number, 'OrgAccessRole'>;
export type ProjectVisibility = Brand<number, 'ProjectVisibility'>;

export type User = Selectable<dbtypes.Users>;
export type NewUser = Insertable<dbtypes.Users>
export type UserUpdate = Updateable<dbtypes.Users>

export type UserSession = Selectable<dbtypes.UserSessions>;
export type DeviceAuthRequest = Selectable<dbtypes.DeviceAuthRequests>;

export type Project = Selectable<dbtypes.Projects>;
export type NewProject = Insertable<dbtypes.Projects>;
export type ProjectUpdate = Updateable<dbtypes.Projects>;

export type Organization = Selectable<dbtypes.Organizations>;
export type NewOrganization = Insertable<dbtypes.Organizations>;
export type OrganizationUpdate = Updateable<dbtypes.Organizations>;

export type ProductPlan = Selectable<dbtypes.ProductPlans>;
export type NewProductPlan = Insertable<dbtypes.ProductPlans>;
export type ProductPlanUpdate = Updateable<dbtypes.ProductPlans>;

export type DatabaseConfig = {
  host: string,
  port: number,
  user: string,
  password: string,
  database: string,
  encryptionKey: string,
}

export class Database {
  static sourceAuthType = {
    githubPat: 1 as SourceAuthType,
    githubApp: 2 as SourceAuthType,
  };

  static orgAccessRole = {
    member: 100 as OrgAccessRole,
    admin: 200 as OrgAccessRole,
  };

  static projectAccessRole = {
    viewer: 1 as ProjectAccessRole,
    editor: 2 as ProjectAccessRole,
  };

  static projectVisibility = {
    private: 666 as ProjectVisibility,
    public: 777 as ProjectVisibility,
  };

  static createEncryptionKey() {
    return aes.createKey();
  }

  static async initializeOrDie(options: DatabaseConfig) {
    try {
      const db = await Database.initialize(options);
      console.log(`Connected to Postgres database at ${options.host}:${options.port}/${options.database}`);
      return db;
    } catch (e) {
      console.error(e);
      process.exit(1);
    }
  }

  static createEncryptionKeyForTest() {
    return aes.createKey();
  }

  static createKyselyForTest(options: DatabaseConfig) {
    return connectToPostgres(options, false);
  }

  static async initialize(options: DatabaseConfig) {
    const { db, migrator } = connectToPostgres(options, true);
    const migrations = await migrator.getMigrations();
    const unappliedMigrations = migrations.filter(migration => !migration.executedAt);
    if (unappliedMigrations.length) {
      const text = [
        `Cannot connect to the database!`,
        `The server is expecting a newer version of the database; the following migrations are not applied:`,
        ...unappliedMigrations.map(migration => `- ` + migration.name),
      ];
      throw new Error(text.join('\n'));
    }
    return new Database(db, options.encryptionKey);
  }

  static async migrate<T>(options: DatabaseConfig, migratorCallback: (migrator: Migrator, db: Kysely<any>) => Promise<T>): Promise<T> {
    const { db, migrator } = connectToPostgres(options, true);
    const oldEnvValue = process.env.DB_ENCRYPTION_KEY;
    try {
      // We use environment variables to propagate information down to migartors. (sigh)
      process.env.DB_ENCRYPTION_KEY = options.encryptionKey;
      return await migratorCallback(migrator, db);
    } finally {
      process.env.DB_ENCRYPTION_KEY = oldEnvValue;
      await db.destroy();
    }
  }

  private _db: Kysely<dbtypes.DB>;

  readonly users: Users;
  readonly orgs: Organizations;
  readonly projects: Projects;
  readonly projectSharing: ProjectSharing;
  readonly projectSource: ProjectSource;
  readonly orgSharing: OrganizationSharing;
  readonly queues: Queues;
  readonly productPlans: ProductPlans;
  readonly userSessions: UserSessions;
  readonly deviceAuthRequests: DeviceAuthRequests;

  constructor(
    db: Kysely<dbtypes.DB>,
    encryptionKey: string,
  ) {
    this._db = db;
    this.queues = new Queues(db, encryptionKey);
    this.users = new Users(db, encryptionKey);
    this.orgs = new Organizations(db, encryptionKey);
    this.projects = new Projects(db, encryptionKey);
    this.projectSharing = new ProjectSharing(db, encryptionKey);
    this.projectSource = new ProjectSource(db, encryptionKey);
    this.orgSharing = new OrganizationSharing(db, encryptionKey);
    this.productPlans = new ProductPlans(db, encryptionKey);
    this.userSessions = new UserSessions(db, encryptionKey);
    this.deviceAuthRequests = new DeviceAuthRequests(db, encryptionKey);
  }

  async close() {
    await this._db.destroy();
  }
}

class Users {
  constructor(
    private _db: Kysely<dbtypes.DB>,
    private _encryptionKey: string,
  ) {}

  async get(userId: UserId): Promise<User|undefined> {
    return await this._db.selectFrom('users')
      .selectAll('users')
      .where('user_id', '=', userId)
      .executeTakeFirst();
  }

  async getOrganizations(userId: UserId): Promise<OrgId[]> {
    const orgs = await this._db
      .selectFrom('organizations')
      .where('organizations.owner_id', '=', userId)
      .select('org_id')
      .execute();
    return orgs.map(org => org.org_id);
  }

  async getByPublicId(userPublicId: UserPublicId): Promise<User|undefined> {
    return await this._db.selectFrom('users')
      .selectAll()
      .where('users.user_public_id', '=', userPublicId)
      .executeTakeFirst();
  }

  async getByGithubId(githubId: number): Promise<User|undefined> {
    return await this._db.selectFrom('users')
      .selectAll('users')
      .where('github_id', '=', githubId)
      .executeTakeFirst();
  }

  async findUsers(query: string): Promise<User[]> {
    const users = await this._db.selectFrom('users')
      .selectAll('users')
      .where(eb => eb.or([
        eb(sql`LOWER(user_name)`, 'like', query.toLowerCase() + '%'),
        eb(sql`LOWER(user_login)`, 'like', query.toLowerCase() + '%'),
      ]))
      .limit(10)
      .execute();
    return users;
  }

  async create(user: NewUser): Promise<User> {
    return await this._db.transaction().execute(async trx => {
      await trx
        .insertInto('users')
        .values(user)
        .execute();
      const result = await trx.selectFrom('users').where('user_public_id', '=', user.user_public_id).selectAll().executeTakeFirst();
      assert(result);
      return result;
    })
  }
}

class Organizations {
  constructor(
    private _db: Kysely<dbtypes.DB>,
    private _encryptionKey: string,
  ) {}

  async all(): Promise<Organization[]> {
    return await this._db.selectFrom('organizations')
      .selectAll('organizations')
      .execute();
  }

  async getProjects(orgId: OrgId): Promise<ProjectId[]> {
    const result = await this._db.selectFrom('projects')
      .where('org_id', '=', orgId)
      .select('project_id')
      .execute();
    return result.map(entry => entry.project_id);
  }

  async getProjectPublicIds(orgId: OrgId): Promise<ProjectPublicId[]> {
    const result = await this._db.selectFrom('projects')
      .where('org_id', '=', orgId)
      .select('project_public_id')
      .execute();
    return result.map(entry => entry.project_public_id);
  }

  async update(orgId: OrgId, update: Partial<OrganizationUpdate>): Promise<Organization|undefined> {
    return await this._db.transaction().execute(async trx => {
      const old = trx.selectFrom('organizations')
        .where('org_id', '=', orgId)
        .selectAll()
        .executeTakeFirst();
      if (!old)
        return old;

      await trx
        .updateTable('organizations')
        .set(update)
        .where('org_id', '=', orgId)
        .execute();
      return old;
    });
  }

  async create(org: NewOrganization): Promise<Organization> {
    return await this._db.transaction().execute(async trx => {
      await trx.insertInto('organizations').values(org).execute();
      const result = await trx.selectFrom('organizations').where('org_slug', '=', org.org_slug).selectAll().executeTakeFirst();
      assert(result);
      return result;
    })
  }

  async get(orgId: OrgId): Promise<Organization|undefined> {
    return await this._db.selectFrom('organizations')
      .where('org_id', '=', orgId)
      .selectAll()
      .executeTakeFirst();
  }

  async getBySlug(slug: string): Promise<Organization|undefined> {
    return await this._db.selectFrom('organizations')
      .selectAll()
      .where('org_slug', '=', slug)
      .executeTakeFirst();
  }

  async getByPublicId(publicId: OrgPublicId): Promise<Organization|undefined> {
    return await this._db.selectFrom('organizations')
      .selectAll()
      .where('org_public_id', '=', publicId)
      .executeTakeFirst();
  }

  async delete(orgId: OrgId): Promise<Organization|undefined> {
    return await this._db.transaction().execute(async trx => {
      const result = await trx.selectFrom('organizations')
        .where('org_id', '=', orgId)
        .selectAll()
        .forUpdate()
        .executeTakeFirst();
      if (!result)
        return undefined;
      await trx.deleteFrom('organizations')
        .where('org_id', '=', orgId)
        .execute();
      return result;
    });
  }
}

class Projects {
  constructor(
    private _db: Kysely<dbtypes.DB>,
    private _encryptionKey: string,
  ) {}

  async all(): Promise<Project[]> {
    return await this._db.selectFrom('projects')
      .selectAll('projects')
      .execute();
  }

  async get(projectId: ProjectId): Promise<Project|undefined> {
    return await this._db.selectFrom('projects')
      .selectAll('projects')
      .where('projects.project_id', '=', projectId)
      .executeTakeFirst();
  }

  async getBySlug(orgId: OrgId, projectSlug: string): Promise<Project|undefined> {
    return await this._db.selectFrom('projects')
      .selectAll()
      .where('org_id', '=', orgId)
      .where(`project_slug`, '=', projectSlug)
      .executeTakeFirst();
  }

  async getByAccessToken(accessToken: string): Promise<Project|undefined> {
    return await this._db.selectFrom('projects')
      .selectAll()
      .where('flakiness_access_token', '=', accessToken)
      .executeTakeFirst();
  }

  async getByPublicId(projectPublicId: ProjectPublicId): Promise<Project|undefined> {
    return await this._db.selectFrom('projects')
      .selectAll()
      .where('projects.project_public_id', '=', projectPublicId)
      .executeTakeFirst();
  }

  async delete(projectId: ProjectId): Promise<Project|undefined> {
    return await this._db.transaction().execute(async trx => {
      const result = await trx.selectFrom('projects')
        .where('project_id', '=', projectId)
        .selectAll()
        .forUpdate()
        .executeTakeFirst();
      if (!result)
        return undefined;
      await trx.deleteFrom('projects')
        .where('project_id', '=', projectId)
        .execute();
      return result;
    });
  }

  async incReportCount(projectId: ProjectId): Promise<number> {
    const result = await this._db.transaction().execute(async trx => {
      const updated = await trx
        .updateTable('projects')
        .set('reports_count', sql`reports_count + 1`)
        .where('project_id', '=', projectId)
        .returning('reports_count')
        .executeTakeFirstOrThrow();
      return updated.reports_count;
    });
    return result;
  }

  async update(projectId: ProjectId, update: Partial<ProjectUpdate>): Promise<Project|undefined> {
    return await this._db.transaction().execute(async trx => {
      const old = await trx.selectFrom('projects')
        .where('project_id', '=', projectId)
        .selectAll()
        .executeTakeFirst();
      if (!old)
        return old;

      await trx
        .updateTable('projects')
        .set(update)
        .where('project_id', '=', projectId)
        .execute();
      return old;
    });
  }

  async create(proj: NewProject, auth: { pat?: string, installationId?: string }): Promise<Project> {
    assert(auth.installationId || auth.pat, 'Either auth or pat must be defined');
    return await this._db.transaction().execute(async trx => {
      await trx
        .insertInto('projects')
        .values({
          ...proj,
          source_auth_type: auth.pat ? Database.sourceAuthType.githubPat : Database.sourceAuthType.githubApp,
        })
        .execute();
      const project = await trx.selectFrom('projects')
        .selectAll()
        .where('org_id', '=', proj.org_id)
        .where('project_slug', '=', proj.project_slug)
        .executeTakeFirst();
      assert(project);

      if (auth.installationId) {
        await trx
          .insertInto('github_app_installations')
          .values({
            project_id: project.project_id,
            installation_id_encoded: aes.encrypt(this._encryptionKey, auth.installationId),
          })
          .execute();
      } else if (auth.pat) {
        await trx
          .insertInto('personal_access_tokens')
          .values({
            project_id: project.project_id,
            pat_encoded: aes.encrypt(this._encryptionKey, auth.pat),
          })
          .execute();
      } else {
        throw new Error('Either auth or pat must be defined');
      }
      return project;
    });
  }
}

class OrganizationSharing {
  constructor(
    private _db: Kysely<dbtypes.DB>,
    private _encryptionKey: string,
  ) {}

  async getOrganizations(userId: UserId): Promise< { orgId: OrgId, accessRole: OrgAccessRole }[]> {
    const result = await this._db
      .selectFrom('organization_members')
      .where('organization_members.user_id', '=', userId)
      .select(['organization_members.access_role', 'organization_members.org_id'])
      .execute();
    return result.map(entry => ({
      accessRole: entry.access_role,
      orgId: entry.org_id,
    }));
  }

  async getUsers(orgId: OrgId): Promise<{ userId: UserId, accessRole: OrgAccessRole }[]> {
    const result = await this._db
      .selectFrom('organization_members')
      .where('organization_members.org_id', '=', orgId)
      .select(['organization_members.access_role', 'organization_members.user_id'])
      .execute();
    return result.map(entry => ({
      accessRole: entry.access_role,
      userId: entry.user_id,
    }));
  }

  async setAccess(orgId: OrgId, userId: UserId, access: OrgAccessRole | undefined) {
    if (!access) {
      await this._db
        .deleteFrom('organization_members')
        .where('org_id', '=', orgId)
        .where('user_id', '=', userId)
        .execute();
    } else {
      await this._db
        .insertInto('organization_members')
        .values({
          org_id: orgId,
          user_id: userId,
          access_role: access,
        })
        .$call((ib) => ib.onConflict(oc =>
          oc.columns(['org_id', 'user_id'])
          .doUpdateSet({ access_role: access })
        ))
        .execute();
    }
  }
}

class ProjectSharing {
  constructor(
    private _db: Kysely<dbtypes.DB>,
    private _encryptionKey: string,
  ) {}

  /**
   * Return all the projects the user has explicit role at.
   * @param userId 
   * @returns 
   */
  async getProjects(userId: UserId): Promise<{ projectId: ProjectId, accessRole: ProjectAccessRole }[]> {
    const projectsAndRoles = await this._db
      .selectFrom('project_collaborators')
      .where('project_collaborators.user_id', '=', userId)
      .select(['project_collaborators.access_role', 'project_collaborators.project_id'])
      .execute();
    return projectsAndRoles.map(entry => ({
      projectId: entry.project_id,
      accessRole: entry.access_role,
    }));
  }

  async getUsers(projectId: ProjectId): Promise<{ userId: UserId, accessRole: ProjectAccessRole }[]> {
    const projectsAndRoles = await this._db
      .selectFrom('project_collaborators')
      .where('project_collaborators.project_id', '=', projectId)
      .select(['project_collaborators.access_role', 'project_collaborators.user_id'])
      .execute();
    return projectsAndRoles.map(entry => ({
      userId: entry.user_id,
      accessRole: entry.access_role,
    }));
  }

  async setAccess(projectId: ProjectId, userId: UserId, access: ProjectAccessRole | undefined) {
    if (!access) {
      await this._db
        .deleteFrom('project_collaborators')
        .where('project_id', '=', projectId)
        .where('user_id', '=', userId)
        .execute();
    } else {
      await this._db
        .insertInto('project_collaborators')
        .values({
          project_id: projectId,
          user_id: userId,
          access_role: access,
        })
        .$call((ib) => ib.onConflict(oc =>
          oc.columns(['project_id', 'user_id'])
          .doUpdateSet({ access_role: access })
        ))
        .execute();
    }
  }
}


class ProjectSource {
  constructor(
    private _db: Kysely<dbtypes.DB>,
    private _encryptionKey: string,
  ) {}

  async getPersonalAccessToken(projectId: ProjectId): Promise<string|undefined> {
    const result = await this._db.selectFrom('personal_access_tokens')
      .selectAll('personal_access_tokens')
      .where('project_id', '=', projectId)
      .executeTakeFirst();
    return result?.pat_encoded ? aes.decrypt(this._encryptionKey, result.pat_encoded) : undefined;
  }

  async getInstallationId(projectId: ProjectId): Promise<string|undefined> {
    const result = await this._db.selectFrom('github_app_installations')
      .selectAll('github_app_installations')
      .where('project_id', '=', projectId)
      .executeTakeFirst();
    return result?.installation_id_encoded ? aes.decrypt(this._encryptionKey, result.installation_id_encoded) : undefined;
  }

  async setProjectSource(options: { projectId: ProjectId, sourceOwnerName: string, sourceRepo: string, personalAccessToken?: string, installationId?: string }) {
    assert(options.personalAccessToken || options.installationId, 'Either PAT or installation id must be present')
    await this._db.transaction().execute(async trx => {
      await trx
        .deleteFrom('personal_access_tokens')
        .where('project_id', '=', options.projectId)
        .execute();
      await trx
        .deleteFrom('github_app_installations')
        .where('project_id', '=', options.projectId)
        .execute();
      
      if (options.installationId) {
        await trx
          .insertInto('github_app_installations')
          .values({
            project_id: options.projectId,
            installation_id_encoded: aes.encrypt(this._encryptionKey, options.installationId),
          })
          .execute();
        await trx
          .updateTable('projects')
          .where('project_id', '=', options.projectId)
          .set({
            source_owner_name: options.sourceOwnerName,
            source_repo_name: options.sourceRepo,
            source_auth_type: Database.sourceAuthType.githubApp,
          })
          .execute();
      } else if (options.personalAccessToken) {
        await trx
          .insertInto('personal_access_tokens')
          .values({
            project_id: options.projectId,
            pat_encoded: aes.encrypt(this._encryptionKey, options.personalAccessToken),
          })
          .execute();
        await trx
          .updateTable('projects')
          .where('project_id', '=', options.projectId)
          .set({
            source_owner_name: options.sourceOwnerName,
            source_repo_name: options.sourceRepo,
            source_auth_type: Database.sourceAuthType.githubPat,
          })
          .execute();
      } else {
        throw new Error('either PAT or installation id must be present')
      }
    });
  }
}

class Queues {
  constructor(
    private _db: Kysely<dbtypes.DB>,
    private _encryptionKey: string,
  ) {}


  createQueue<T extends {}>(name: string): Queue<T> {
    return new Queue<T>({
      db: this._db,
      name,
    });
  }

  private async _listJobs<T>(trx: Transaction<dbtypes.DB>, table: 'archived_jobs'|'queued_jobs'|'active_jobs', offset: number, pageSize: number) {
    const countResult = await trx
        .selectFrom(table)
        .select(this._db.fn.countAll().as("count"))
        .executeTakeFirst();
      if (!countResult)
        return { count: 0, jobs: [] };
      const jobs = await trx
        .selectFrom(table)
        .selectAll()
        .orderBy(table === 'archived_jobs' ? sql`execution_duration_seconds + execution_timestamp_seconds` : 'id', 'desc')
        .limit(pageSize)
        .offset(offset)
        .execute();
      const count = typeof countResult.count === 'string' ? parseInt(countResult.count, 10) :
          typeof countResult.count === 'number' ? countResult.count :
          Number(countResult.count);
      return { count, jobs: jobs as T[] };
  }

  // This is queue introspection method; SHOULD NOT be used for service operation.
  async listJobs(pageSize: number, offsetQueued: number, offsetActive: number, offsetArchived: number) {
    return await this._db.transaction().execute(async trx => {
      return {
        queued: await this._listJobs<QueuedJob>(trx, 'queued_jobs', offsetQueued, pageSize),
        active: await this._listJobs<ActiveJob>(trx, 'active_jobs', offsetActive, pageSize),
        archived: await this._listJobs<ArchivedJob>(trx, 'archived_jobs', offsetArchived, pageSize),
      }
    });
  }

  // This is queue introspection method; SHOULD NOT be used for service operation.
  async listArchivedJobs(offset: number, pageSize: number): Promise<{ count: number, jobs: ArchivedJob[] }> {
    return await this._db.transaction().execute(async trx => this._listJobs(trx, 'archived_jobs', offset, pageSize));
  }

  // This is queue introspection method; SHOULD NOT be used for service operation.
  async listQueuedJobs(offset: number, pageSize: number): Promise<{ count: number, jobs: QueuedJob[] }> {
    return await this._db.transaction().execute(async trx => this._listJobs(trx, 'queued_jobs', offset, pageSize));
  }

  // This is queue introspection method; SHOULD NOT be used for service operation.
  async listActiveJobs(offset: number, pageSize: number): Promise<{ count: number, jobs: ActiveJob[] }> {
    return await this._db.transaction().execute(async trx => this._listJobs(trx, 'active_jobs', offset, pageSize));
  }
}

function generateUserCode() {
  // A carefully chosen character set for readability and to prevent accidental words.
  const chars = 'BCDFGHJKLMNPQRSTVWXYZ';
  const codeLength = 8;
  let code = '';

  for (let i = 0; i < codeLength; i++) {
    const randomIndex = randomInt(chars.length);
    code += chars[randomIndex];
  }
  return code;
}

class DeviceAuthRequests {
  constructor(
    private _db: Kysely<dbtypes.DB>,
    private _encryptionKey: string,
  ) {}
  
  async createRequest(options: {
    clientId: AppClientId,
    deviceName: string,
    deadlineMs: number,
  }): Promise<DeviceAuthRequest> {
    return await this._db.transaction().execute(async trx => {
      // Before creating new request, let's drop all old requests first.
      await trx
        .deleteFrom('device_auth_requests')
        .where('expiration_timestamp_seconds', '<', Math.floor(Date.now() / 1000))
        .execute();

      const allCodes = await trx
        .selectFrom('device_auth_requests')
        .select('user_code')
        .forUpdate()
        .execute();
      const allUserCodes = new Set(allCodes.map(code => code.user_code));
      let userCode = generateUserCode();
      while (allUserCodes.has(userCode))
        userCode = generateUserCode();

      const deviceCode = randomUUID();
      await trx
        .insertInto('device_auth_requests')
        .values({
          expiration_timestamp_seconds: Math.floor(options.deadlineMs / 1000),
          device_code: deviceCode,
          user_code: userCode,
          device_name: options.deviceName,
          client_id: options.clientId,
        })
        .execute();
      const request = await trx.selectFrom('device_auth_requests').selectAll().where('device_code', '=', deviceCode).executeTakeFirst();
      assert(request);
      return request;
    });
  }

  async approve(requestId: DeviceAuthRequestId, userId: UserId) {
    await this._db
      .updateTable('device_auth_requests')
      .set({ approver_id: userId, })
      .where('request_id', '=', requestId)
      .execute();
  }

  async getByDeviceCode(deviceCode: string): Promise<DeviceAuthRequest|undefined> {
    return await this._db
      .selectFrom('device_auth_requests')
      .selectAll()
      .where('device_code', '=', deviceCode)
      .executeTakeFirst();
  }

  async getByUserCode(userCode: string): Promise<DeviceAuthRequest|undefined> {
    return await this._db
      .selectFrom('device_auth_requests')
      .selectAll()
      .where('user_code', '=', userCode)
      .executeTakeFirst();
  }

  async delete(requestId: DeviceAuthRequestId): Promise<DeviceAuthRequest|undefined> {
    return await this._db.transaction().execute(async trx => {
      const request = await trx
        .selectFrom('device_auth_requests')
        .selectAll()
        .where('request_id', '=', requestId)
        .forUpdate()
        .executeTakeFirst();
      if (!request)
        return undefined;
      await trx.deleteFrom('device_auth_requests').where('request_id', '=', requestId).execute();
      return request;
    });
  }
}

class UserSessions {
  constructor(
    private _db: Kysely<dbtypes.DB>,
    private _encryptionKey: string,
  ) {}

  async createSession(options: {
    userId: UserId,
    tokenHash: string,
    name?: string,
    clientId: AppClientId,
  }) {
    return await this._db.transaction().execute(async trx => {
      await trx
        .insertInto('user_sessions')
        .values({
          session_public_id: randomUUID() as UserSessionPublicId,
          user_id: options.userId,
          token_sha256: options.tokenHash,
          session_name: options.name,
          client_id: options.clientId,
          last_access_timestamp_seconds: Math.floor(Date.now() / 1000),
        })
        .execute();
    });
  }

  async recordSessionAccess(sessionId: UserSessionId, options: {
    accessTimestampMs: number, 
  }): Promise<void> {
    await this._db.updateTable('user_sessions')
      .set({
        last_access_timestamp_seconds: Math.floor(options.accessTimestampMs / 1000),
      })
      .where('session_id', '=', sessionId)
      .execute();
  }

  async listSessions(userId: UserId): Promise<UserSession[]> {
    return await this._db.selectFrom('user_sessions').selectAll().where('user_id', '=', userId).execute();
  }

  async dropSession(sessionId: UserSessionId): Promise<UserSession|undefined> {
    return await this._db.transaction().execute(async trx => {
      const session = await trx
        .selectFrom('user_sessions')
        .selectAll()
        .where('session_id', '=', sessionId)
        .forUpdate()
        .executeTakeFirst();
      if (!session)
        return undefined;
      await trx.deleteFrom('user_sessions')
        .where('session_id', '=', session.session_id)
        .execute();
      return session;
    });
  }

  async dropSessionByTokenHash(tokenHash: string): Promise<UserSession|undefined> {
    return await this._db.transaction().execute(async trx => {
      const session = await trx
        .selectFrom('user_sessions')
        .selectAll()
        .where('token_sha256', '=', tokenHash)
        .forUpdate()
        .executeTakeFirst();
      if (!session)
        return undefined;
      await trx.deleteFrom('user_sessions')
        .where('session_id', '=', session.session_id)
        .execute();
      return session;
    });
  }

  async getByPublicId(sessionPublicId: UserSessionPublicId): Promise<UserSession|undefined> {
    return this._db.selectFrom('user_sessions').selectAll().where('session_public_id', '=', sessionPublicId).executeTakeFirst();
  }

  async getByTokenHash(hash: string): Promise<UserSession|undefined> {
    return this._db.selectFrom('user_sessions').selectAll().where('token_sha256', '=', hash).executeTakeFirst();
  }
}

class ProductPlans {
  constructor(
    private _db: Kysely<dbtypes.DB>,
    private _encryptionKey: string,
  ) {}

  async list(): Promise<ProductPlan[]> {
    return await this._db.selectFrom('product_plans')
      .selectAll()
      .execute();
  }

  async get(planId: ProductPlanId): Promise<ProductPlan|undefined> {
    return await this._db.selectFrom('product_plans')
      .selectAll()
      .where('plan_id', '=', planId)
      .executeTakeFirst();
  }

  async getByPublicId(planPublicId: ProductPlanPublicId): Promise<ProductPlan|undefined> {
    return await this._db.selectFrom('product_plans')
      .selectAll()
      .where('plan_public_id', '=', planPublicId)
      .executeTakeFirst();
  }

  async update(planId: ProductPlanId, update: Partial<ProductPlanUpdate>): Promise<ProductPlan|undefined> {
    return await this._db.transaction().execute(async trx => {
      const old = await trx.selectFrom('product_plans')
        .where('plan_id', '=', planId)
        .selectAll()
        .executeTakeFirst();
      if (!old)
        return old;

      await trx
        .updateTable('product_plans')
        .set(update)
        .where('plan_id', '=', planId)
        .execute();
      return old;
    });
  }

  async create(plan: NewProductPlan): Promise<ProductPlan> {
    return await this._db.transaction().execute(async trx => {
      await trx
        .insertInto('product_plans')
        .values(plan)
        .execute();
      
      const result = await trx.selectFrom('product_plans')
        .selectAll()
        .where('plan_public_id', '=', plan.plan_public_id)
        .executeTakeFirst();
      
      assert(result);
      return result;
    });
  }

  async delete(planId: ProductPlanId): Promise<ProductPlan|undefined> {
    return await this._db.transaction().execute(async trx => {
      const result = await trx.selectFrom('product_plans')
        .where('plan_id', '=', planId)
        .selectAll()
        .forUpdate()
        .executeTakeFirst();

      if (!result)
        return undefined;
      
      await trx.deleteFrom('product_plans')
        .where('plan_id', '=', planId)
        .execute();
      
      return result;
    });
  }
}

function connectToPostgres(options: DatabaseConfig, logError: boolean) {
  const int8TypeId = 20;
  // Map int8 to number.
  pg.types.setTypeParser(int8TypeId, (val) => {
    return parseInt(val, 10)
  });

  const pool = new pg.Pool({
    host: options.host,
    port: options.port,
    database: options.database,
    user: options.user,
    password: options.password,
    // These are important so that PGClient re-connects after
    // the connection is dropped.
    connectionTimeoutMillis: 60000,
    idleTimeoutMillis: 30000,
  });
  // Automatically reconnect on errors
  pool.on('error', (err) => {
    console.error('Unexpected pool error:', err);
  });

  return connect(new PostgresDialect({
    pool,
    async onCreateConnection(connection) {
      const execute = connection.executeQuery.bind(connection);
      connection.executeQuery = async (...args) => {
        // We occasionally fail to connect to the database with the weird 'timeout exceeded when trying to connect'
        // error. In this case, retrying helps.
        // This patch adds retries to handle this error. Out of safety, we limit
        // retries to some sensible value, like 10.
        // After that, an error will be thrown.
        let lastError: any;
        for (let i = 0; i < 10; ++i) {
          try {
            return await execute(...args);
          } catch (error: any) {
            // continue retries if we hit a connection error; otherwise,
            // re-throw the error.
            if (DATABASE_CONNECTION_ERRORS.includes(error.message))
              lastError = error;
            else
              throw error;
          }
        }
        throw lastError;
      }
    },
  }), logError);
}

function connect(dialect: Dialect, logError: boolean) {
  const db = new Kysely<dbtypes.DB>({
    dialect,
    log: logError ? ['error'] : [],
  });
  const migrator = new Migrator({
    db,
    provider: new FileMigrationProvider({
      fs,
      path,
      // This needs to be an absolute path.
      migrationFolder: path.join(__dirname, './migrations'),
    }),
  });
  return { db, migrator };
}


const DATABASE_CONNECTION_ERRORS = [
  'timeout exceeded when trying to connect',
  'Connection terminated due to connection timeout'
];
