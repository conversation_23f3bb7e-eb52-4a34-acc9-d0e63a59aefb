import { Insertable, Kysely, Selectable, Transaction } from 'kysely';
import ms from 'ms';
import assert from 'node:assert';
import { randomUUID } from 'node:crypto';
import { EventEmitter } from 'node:events';
import { setTimeout } from 'node:timers/promises';
import { ActiveJobs, DB, QueuedJobs } from './generated/latest_types.js';

export type ArchivedJob = Selectable<ActiveJobs>;
export type QueuedJob = Selectable<QueuedJobs>;
export type ActiveJob = Selectable<ActiveJobs>;

export type Job<T> = Omit<Selectable<ActiveJobs>, 'data'> & { data: T };

/**
 * Queue Implementation for Reliable Event Processing
 *
 * This queue implementation uses PostgreSQL's "skip locked" feature to provide reliable
 * job processing. It's specifically designed to support Cloud Compiler-like scenarios where:
 * - Resources are identified by unique `resourceId`
 * - Each resource has inputs that produce a single output
 * - When inputs change, the resource is rebuilt
 * - If inputs change during rebuilding, another rebuild is scheduled after completion
 *
 * Core Components:
 * - Queue: A named queue where jobs can be added
 * - Job: An element to be added to the queue, carrying additional information
 * - Worker: A compute unit attached to a named queue. Multiple workers can process jobs
 *   from the same queue. Workers execute jobs and mark them as completed or failed
 *
 * Job Lifecycle:
 * 1. Queued: Job starts in the "queued" state
 * 2. Active: Before execution, job moves to "active" state and is given to a worker
 * 3. Archived: After processing, job is archived with success/failure status
 *   - On failure: Job is either retried (moved back to "queued") or archived as failed
 *
 * Key Features:
 *
 * Job ID:
 * - Each job has a unique "job_id" (default: random UUID, can be custom)
 * - No two jobs can have the same job_id within the same status
 * - Allows scheduling rebuilds of resources even while they're being processed
 *
 * Concurrency Control:
 * - Multiple workers can process queue jobs in parallel
 * - Jobs sharing a `concurrency_id` are executed serially
 * - Useful for coordinating related jobs that shouldn't overlap
 *
 * Delayed Execution:
 * - Jobs can be scheduled for future execution
 * - Minimum delay is 1000ms
 * - Actual execution time depends on worker polling & execution rate
 *
 * Worker Management:
 * - Automatic retry handling for failed jobs
 * - Bookkeeping process to:
 *   * Detect and handle stale jobs (too long in active state)
 *   * Clean up old archived jobs (configurable retention period)
 *   * Maintain system health and resource usage
 *
 * Stalled jobs:
 * - When worker starts running a job, it issues "heartbeat" to the job every once in a while
 * - This way the worker shows that it's alive and runs the job
 * - If there's been no "heartbeat" during the "stalledMS" interval, then the job is considered stalled
 * - Heartbeats are powered by the setTimeout call, so worker should not block main thread with heavy CPU.
 * 
 * Shortcomings:
 * - If the worker has staled, it should self-explode, thus terminating the callback.
 *   This is tricky in general case since workers live either in workerThreads or in processes.
 *   The other problematic thing is that when hosting process is being killed, all other workers
 *   that are hosted inside this process will be killed as well; this should be handled gracefully.
 *   WORKAROUND: make sure jobs don't stall! Use `deadlineMs` argument to preemptively wrap up execution.
 */

interface TestEventsTypes<T> {
  'will-start-job': (eventData: Job<T>) => void;
  'did-finish-job': (eventData: Job<T>) => void;
}

class TestEvents<T> extends EventEmitter {
  emit<K extends keyof TestEventsTypes<T>>(event: K, ...args: Parameters<TestEventsTypes<T>[K]>): boolean {
    return super.emit(event, ...args);
  }

  on<K extends keyof TestEventsTypes<T>>(event: K, listener: TestEventsTypes<T>[K]): this {
    return super.on(event, listener);
  }

  once<K extends keyof TestEventsTypes<T>>(event: K, listener: TestEventsTypes<T>[K]): this {
    return super.once(event, listener);
  }

  off<K extends keyof TestEventsTypes<T>>(event: K, listener: TestEventsTypes<T>[K]): this {
    return super.off(event, listener);
  }
}

async function submitJobs(db: Kysely<DB>, jobs: Insertable<QueuedJobs>[]) {
  if (!jobs.length)
    return false;
  const result = await db
    .insertInto('queued_jobs')
    .values(jobs.map(job => ({
      ...job,
      data: job.data as any,
      submitted_timestamp_seconds: seconds(Date.now()),
    })))
    .$call(b => b.onConflict(cb => cb.doNothing()))
    .execute();
  return result.length > 0 && !!result[0].numInsertedOrUpdatedRows;
}

export type JobOptions = {
  jobId?: string,
  category?: string,
  delayMs?: number,
  concurrencyId?: string
};

export class Queue<T extends {}> {

  private _db: Kysely<DB>;
  private _name: string;

  public testEvents = new TestEvents<T>();

  constructor(options: {
    db: Kysely<DB>,
    name: string,
  }) {
    this._db = options.db;
    this._name = options.name;
  }

  async setJobDelay(jobId: string, delayMs: number) {
    assert(delayMs === 0 || delayMs >= 1000, `Delay must be either 0 or larger than 1000ms; found ${delayMs}`);
    await this._db
      .updateTable('queued_jobs')
      .set({
        // note: we must add +1 so due to the rounding errors from ms -> s
        execute_after_timestamp_seconds: seconds(Date.now()) + (delayMs ? seconds(delayMs) + 1 : 0),
      })
      .where('job_id', '=', jobId)
      .execute();
  }

  private async _sendManyInner(jobs: { data: T, options?: JobOptions }[]): Promise<boolean> {
    const now = Date.now();
    return await submitJobs(this._db, jobs.map(({ data, options }) => {
      const delayMs = options?.delayMs ?? 0;
      assert(delayMs === 0 || delayMs >= 1000, `Delay must be either 0 or larger than 1000ms; found ${delayMs}`);
      return {
        job_id: options?.jobId ?? randomUUID(),
        queue_name: this._name,
        concurrency_id: options?.concurrencyId,
        retry: 0,
        category: options?.category,
        // note: we must add +1 so due to the rounding errors from ms -> s
        execute_after_timestamp_seconds: seconds(now) + (delayMs ? seconds(delayMs) + 1 : 0),
        submitted_timestamp_seconds: seconds(now),
        data,
      };
    }));
  }

  async sendMany(jobs: { data: T, options?: JobOptions }[]): Promise<void> {
    // Do not return any value since it doesn't make sense.
    await this._sendManyInner(jobs);
  }

  async send(data: T, options?: JobOptions): Promise<boolean> {
    // For a single job, it makes sense to return true/false if it was or was not inserted.
    return this._sendManyInner([{ data, options }]);
  }

  async hasJobsWithCategory(category: string): Promise<boolean> {
    const hasActiveJob = await this._db
      .selectFrom('active_jobs')
      .where('active_jobs.queue_name', '=', this._name)
      .where('active_jobs.category', '=', category)
      .selectAll()
      .limit(1)
      .executeTakeFirst();
    const hasQueuedJob = await this._db
      .selectFrom('queued_jobs')
      .where('queued_jobs.queue_name', '=', this._name)
      .where('queued_jobs.category', '=', category)
      .selectAll()
      .limit(1)
      .executeTakeFirst();
    return !!hasActiveJob || !!hasQueuedJob;
  }

  createWorker(name: string, callback: QueueWorkerCallback<T>, options: QueueWorkerOptions = {}): QueueWorker<T> {
    const worker = new QueueWorker(name, {
      ...options,
      callback,
      db: this._db,
      queueName: this._name,
      testEvents: this.testEvents,
    });
    return worker;
  }
}

const QUEUE_POLL_INTERVAL_MS = ms('5 seconds');
const QUEUE_BOOKKEEPINT_INTERVAL_MS = ms('1 minute');
const QUEUE_STALE_TIMEOUT_MS = ms('15 minutes');
const QUEUE_KEEP_IN_ARCHIVE_MS = ms('7 days');
const QUEUE_HEARTBEAT_INTERVAL_MS = ms('30 seconds');

type QueueWorkerOptions = {
  retries?: number,
  pollIntervalMs?: number,
  heartbeatIntervalMs?: number,
  staleTimeoutMs?: number,
  bookkeepingIntervalMs?: number,
  keepInArchiveMs?: number,
}

export type QueueWorkerCallbackOptions = { signal: AbortSignal };
export type QueueWorkerCallback<T> = (jobInfo: Job<T>, options: QueueWorkerCallbackOptions) => Promise<void>;

export class QueueWorker<T extends {}> {

  static jobExecutionResult = {
    failed: 'failed',
    passed: 'passed',
    stalled: 'stalled',
  }

  private _abortController = new AbortController();
  private _workLoopPromise: Promise<void>;
  private _activeBookkeepingPromise: Promise<void>;


  private _db: Kysely<DB>;
  private _workerName: string;
  private _queueName: string;
  private _maxRetries: number;
  private _pollIntervalMs: number;
  private _heartbeatIntervalMs: number;
  private _staleTimeoutMs: number;
  private _bookkeepingIntervalMs: number;
  private _keepInArchiveMs: number;
  private _callback: QueueWorkerCallback<T>;
  private _testEvents: TestEvents<T>;

  constructor(workerName: string, options: QueueWorkerOptions & {
    db: Kysely<DB>,
    queueName: string,
    testEvents: TestEvents<T>,
    callback: QueueWorkerCallback<T>,
  }) {
    this._db = options.db;
    this._workerName = workerName;
    this._queueName = options.queueName;
    this._maxRetries = options.retries ?? 3;
    this._pollIntervalMs = options.pollIntervalMs?? QUEUE_POLL_INTERVAL_MS;
    this._heartbeatIntervalMs = options.heartbeatIntervalMs ?? QUEUE_HEARTBEAT_INTERVAL_MS;
    this._staleTimeoutMs = options.staleTimeoutMs ?? QUEUE_STALE_TIMEOUT_MS;
    this._bookkeepingIntervalMs = options.bookkeepingIntervalMs ?? QUEUE_BOOKKEEPINT_INTERVAL_MS;
    this._keepInArchiveMs = options.keepInArchiveMs ?? QUEUE_KEEP_IN_ARCHIVE_MS;
    this._callback = options.callback;
    this._testEvents = options.testEvents;
    assert(this._staleTimeoutMs >= 1000, `Stale Timeout cannot be less than 1000ms, found ${this._staleTimeoutMs}ms`);
    assert(this._keepInArchiveMs >= 1000, `KeepInArchive cannot be less than 1000ms, found ${this._keepInArchiveMs}ms`);

    this._activeBookkeepingPromise = this._bookkeeping();
    this._workLoopPromise = this._workLoop();
  }

  private async _queueToActive(): Promise<Selectable<ActiveJobs>|undefined> {
    return await this._db.transaction().execute(async trx => {
      const job = await trx
        .selectFrom('queued_jobs')
        .where(({ not, exists, selectFrom }) =>
          not(exists(
            selectFrom('active_jobs')
            .where('active_jobs.queue_name', '=', this._queueName)
            .whereRef('active_jobs.job_id', '=', 'queued_jobs.job_id'))
          ))
        .where((eb) =>
          eb.or([
            eb('queued_jobs.concurrency_id', 'is', null),
            eb.not(eb.exists(
              eb.selectFrom('active_jobs')
              .where('active_jobs.queue_name', '=', this._queueName)
              .whereRef('active_jobs.concurrency_id', '=', 'queued_jobs.concurrency_id')
            )),
          ])
        )
        .where('queued_jobs.queue_name', '=', this._queueName)
        .where('queued_jobs.execute_after_timestamp_seconds', '<=', seconds(Date.now()))
        .orderBy('queued_jobs.id', 'asc')
        .selectAll(['queued_jobs'])
        .forUpdate()
        .skipLocked()
        .limit(1)
        .executeTakeFirst();

      if (!job)
        return undefined;

      const execution_timestamp_seconds = seconds(Date.now());
      const result = await trx.insertInto('active_jobs')
        .values({
          ...job,
          worker_name: this._workerName,
          data: job.data as any,
          execution_timestamp_seconds,
          execution_heartbeat_timestamp_seconds: execution_timestamp_seconds,
        })
        .$call(b => b.onConflict(cb => cb.doNothing()))
        .execute();

      if (!result.length || !result[0].numInsertedOrUpdatedRows)
        return undefined;

      // Delete job only if the insertion worked.
      await trx.deleteFrom('queued_jobs')
        .where('id', '=', job.id)
        .execute();

      return {
        ...job,
        execution_timestamp_seconds,
        execution_heartbeat_timestamp_seconds: execution_timestamp_seconds,
      };
    });
  }

  private async _activeToQueue(job: Selectable<QueuedJobs>, trx?: Transaction<DB>) {
    const body = async (trx: Transaction<DB>) => {
      await trx.deleteFrom('active_jobs')
        .where('id', '=', job.id)
        .execute();

      await trx.insertInto('queued_jobs')
        .values({
          ...job,
          worker_name: this._workerName,
          data: job.data as any,
        })
        .$call(b => b.onConflict(cb => cb.doNothing()))
        .execute();
    }
    return trx ? body(trx) : this._db.transaction().execute(body);
  }

  private async _activeToArchive(job: Selectable<ActiveJobs> & { execution_result: string }, trx?: Transaction<DB>) {
    const body = async (trx: Transaction<DB>) => {
      await trx
        .deleteFrom('active_jobs')
        .where('id', '=', job.id)
        .execute();
      await trx
        .insertInto('archived_jobs')
        .values({
          ...job,
          worker_name: this._workerName,
          data: job.data as any,
        })
        // Stalled jobs might be moved to archive twice: once from bookkeeping, and then once worker completes them.
        .$call(b => b.onConflict(cb => cb.doNothing()))
        .execute();
    }
    trx ? await body(trx) : await this._db.transaction().execute(body);
    this._testEvents.emit('did-finish-job', job as Job<T>);
  }

  private _heartbeatTimeoutId?: NodeJS.Timeout;

  private _scheduleHeartbeat(job: Selectable<ActiveJob>) {
    this._heartbeatTimeoutId = global.setTimeout(async () => {
      if (!this._heartbeatTimeoutId)
        return;
      await this._db
        .updateTable('active_jobs')
        .set({
          execution_heartbeat_timestamp_seconds: seconds(Date.now()),
        })
        .where('id', '=', job.id)
        .execute();
      this._scheduleHeartbeat(job);
    }, this._heartbeatIntervalMs);
  }

  private _stopHeartbeat() {
    global.clearTimeout(this._heartbeatTimeoutId);
    this._heartbeatTimeoutId = undefined;
  }

  private async _workLoop() {
    const signal = this._abortController.signal;
    while (!signal.aborted) {
      const job = await this._queueToActive();
      // If we didn't fetch anything, then wait a bit and continue later on.
      if (!job) {
        await setTimeout(this._pollIntervalMs, undefined, { signal }).catch(e => void e);
        continue;
      }

      // Finally, start processing the job.
      this._testEvents.emit('will-start-job', job as Job<T>);
      const startMs = Date.now();

      this._scheduleHeartbeat(job);
      // Make a copy of the job just in case the callback will mess with the job.
      const info = await this._callback(structuredClone(job) as Job<T>, { signal }).then(() => ({ success: true, error: undefined })).catch(error => ({ success: false, error }));
      this._stopHeartbeat();

      const durationMs = Date.now() - startMs;

      if (info.success) {
        await this._activeToArchive({
          ...job,
          execution_result: QueueWorker.jobExecutionResult.passed,
          execution_duration_seconds: seconds(durationMs),
          execution_error: null,
        });
        continue;
      }

      // Move this job to a "pending" state again so that workers can pick it up.
      let errorText: string|null = null;
      try {
        if (info.error instanceof Error)
          errorText = info.error.stack ?? null;
        else
          errorText = JSON.stringify(info.error);
      } catch (e) {
        console.error(e);
      }
      const jobToMove = {
        ...job,
        execution_error: errorText,
        execution_result: QueueWorker.jobExecutionResult.failed,
        execution_duration_seconds: seconds(durationMs),
        submitted_timestamp_seconds: seconds(Date.now()),
      }
      if (job.retry >= this._maxRetries) {
        await this._activeToArchive(jobToMove);
      } else {
        await this._activeToQueue({ ...jobToMove, retry: job.retry + 1 });
      }
    }
  }

  async stop() {
    if (this._abortController.signal.aborted)
      return;
    this._abortController.abort();
    await Promise.all([
      this._workLoopPromise,
      this._activeBookkeepingPromise,
    ]);
  }

  private async _bookkeeping() {
    while (!this._abortController.signal.aborted) {
      await this._db.transaction().execute(async trx => {
        const staleJobs = await trx
          .selectFrom('active_jobs')
          .where('active_jobs.queue_name', '=', this._queueName)
          .where('active_jobs.execution_heartbeat_timestamp_seconds', '<', seconds(Date.now() - this._staleTimeoutMs))
          .selectAll()
          .forUpdate()
          .execute();

        // All stalled jobs are moved to archive right away, withoug retrying.
        for (const staleJob of staleJobs) {
          await this._activeToArchive({
            ...staleJob,
            execution_result: QueueWorker.jobExecutionResult.stalled,
            execution_duration_seconds: seconds(Date.now() - staleJob.execution_timestamp_seconds * 1000),
            submitted_timestamp_seconds: seconds(Date.now()),
          }, trx);
        }

        // Drop all old jobs from archive.
        await trx
          .deleteFrom('archived_jobs')
          .where('archived_jobs.queue_name', '=', this._queueName)
          .where('archived_jobs.execution_timestamp_seconds', '<', seconds(Date.now() - this._keepInArchiveMs))
          .execute();
      });
      await setTimeout(this._bookkeepingIntervalMs, undefined, { signal: this._abortController.signal }).catch(e => void e);
    }
  }
}

function seconds(ms: number): number {
  return Math.ceil(ms / 1000);
}
