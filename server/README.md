# Flakiness.io server

This is a monolith server implementing the backend for Flakiness.io.

This monolith implements:
- static file serving
- API handling: report uploads and front-end needs
- background job processing

Multiple instances of this monolith could be launched simultaneously,
effectively managing computational avaiability and enhancing reliability. The different process types are called "units", all available options are located
in `[`./src/units`](./src/units).

## Main Concepts

There are a few cornerstone ideas in the service design:

1. All flakiness reports are marked with `commitId` that they tested. This way
   we can establish clear history between branches and compute flakiness
   reliably. Additionally, reports can be marked with metadata to define the
   environment that reports were executed with. Environment is a set of "key:
   value" properties.
1. As a consequence, we require connection to the upstream repository. We use
   it to order commits and to build reports: while `commitId` are immutable,
   there are multiple ever-changing histories in the repository. For example:
   tags can change, branches can change, and so on.
1. All computation & processing should be done via jobs and queues. This way we
   can make sure that serving is always as fast as possible, while processing
   could be easily scaled by adding more processing servers.
1. Serving should aggressively cache information. We use LRU cache for
   aggressive caching
1. We use a simple datastructure (called "Ranges") to compactly store sets of
   integer numbers. For example, a set of `[1,7,2,8,9]` would be stored as 2
   ranges: `1-2, 7-9`. This makes for a very efficient storage, and that's what
   we use to store stats on tests and reports. We also efficiently implement a
   bunch of classic set operations, see [`ranges.ts`](./src/common/ranges.ts)
   for details.
1. Flakiness.io has **organizations**, and each organization might have
   multiple **projects**. Organization administrators add / remove members,
   setup billing and create projects.  All stats and reports are computed on
   project-level, while organizations are used to organize projects, setup
   billing, manage permissions etc.

## Report aggregation

To effectively store, compute & query analytical information about test results,
flakiness.io maintains the following files (that are stored on S3) for each `project`:

1. `testIndex`: a list of all tests that we have ever seen. Tests are
   considered "different" if they have different names inside the same file of
   the repository. Test are only added to the `testIndex` and never removed
   from it, so test's position defines its integer ID. We use this id to store
   information compactly in the `stats` using `Ranges`.
2. `reportIndex`: a list of all reports along with their commits. Since all
   reports have sequential integer `reportId`, we can store them compactly
   using `Ranges`. `reportIndex` also defines how reports are sharded into
   `stat shards`. This index stores information regarding uploaded reports,
   total bytes & total test runs.
3. `stats`: a JSON containing a heavily optimized information about certain
   reports. We have many of these files, so these are called "stat shards". The
   only strict rule about sharding is that all reports for the same commitId
   always belong to the same shard.

These files are stored on S3 and aggressively cached by the serving
infrastructure. As a result, this system allows us to store and query 10000's
of **reports** and millions of test results efficiently.

# Querying

To query stats, we implemented a "Flakiness Query Language": a special language
that allows us to query stat shards.

## A life of a report

### 1. A client uploads a Flakiness Report to the server using the `upload` API.

When a client wants to upload a report to the server, it hits the upload api
and gets a list of pre-signed URLs that should be used to upload report itself
and its attachments. The preload URLs make sure that no traffic is actually
flowing through the Flakiness.io server.

> **NOTE**: The service expects clients to upload Flakiness Report
> brotli-compressed, and to upload attachments without compression: this way
> they can be served and read in the front-end.

The URLs to upload report and its attachment are pre-signed URLs that are only
valid for a limited time (e.g. 10 minutes). As this time expires (or earlier if
the client notified the us via the API "uploadComplete" call), server schedules the report processing task.

### 2. Report processing & indexing.

This job happens for each uploaded report, and thus we try to keep it relatively small.

To process report, we:
1. Read the report and make sure that it satisfies our format. If not, remove it.
2. Add the report to the `reportIndex`
3. Schedule recomputation of the stat shard where the report ended up.

Notice that since report processing changes `reportIndex`, we must serialize all jobs that belong to the same project.

### 3. Stats computation

There's a job queue to recompute a stat shard for a given project. The
recomputation reads the `testIndex`, `reportIndex` and `statsShard`, determines
which reports should be added or removed, and applies changes to the `statsShard`.

Note that since shard processing changes `testIndex`, we must serialize all jobs the belong to the same project.

##  Data Retention

Flakiness.io implements manual data retention. For this, there's a cron job that fires daily to schedule removal of old reports and to recompute shards accordingly. The reports are dropped from the `reportIndex`.

