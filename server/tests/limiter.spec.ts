import { expect, test } from '@playwright/test';

import { ManualPromise } from '@flakiness/shared/common/manualPromise.js';
import { Limiter } from '../src/common/limiter.js';

class ConcurrentLogger {
  private _currentConcurrency: number = 0;

  constructor(private _limiter: Limiter) {

  }

  async work(id: string, timeout: number, signal?: AbortSignal) {
    using slot = await this._limiter.acquireSlot(signal);
    const concurrency = ++this._currentConcurrency;
    await new Promise(x => setTimeout(x, timeout));
    --this._currentConcurrency;
    return { concurrency, id };
  }
}

test.describe('Limiter', () => {
  test('should enforce serial execution', async () => {
    const limiter = new Limiter(1);
    const worker = new ConcurrentLogger(limiter);

    const results = await Promise.all([
      worker.work('1', 20),
      worker.work('2', 20),
      worker.work('3', 20),
    ]);
    expect(results).toEqual([
      { concurrency: 1, id: '1' },
      { concurrency: 1, id: '2' },
      { concurrency: 1, id: '3' },
    ]);
  });

  test('should enforce concurrency', async () => {
    const limiter = new Limiter(3);
    const worker = new ConcurrentLogger(limiter);
    const results = await Promise.all(Array(100).fill(0).map((_, idx) => worker.work(String(idx), 10)));
    expect(results.every(x => x.concurrency <= 3)).toBe(true);
  });

  test('should handle abort signal before waiting', async () => {
    const limiter = new Limiter(1);
    const controller = new AbortController();
    controller.abort();
    await expect(limiter.acquireSlot(controller.signal)).rejects.toThrow('This operation was aborted');
  });

  test('should handle abort signal while waiting', async () => {
    const limiter = new Limiter(1);
    
    // Start a blocking operation
    const blockingPromise = new ManualPromise<void>();
    const blockingWork = (async () => {
      using slot = await limiter.acquireSlot();
      await blockingPromise.promise;
    })();

    // Wait a bit to ensure the slot is acquired
    await expect.poll(() => limiter.activeSlots()).toBe(1);
    expect(limiter.pendingSlots()).toBe(0);

    // Try to acquire with abort signal
    const controller = new AbortController();
    const abortedWork = limiter.acquireSlot(controller.signal);
    await expect.poll(() => limiter.pendingSlots()).toBe(1);

    controller.abort();
    await expect(abortedWork).rejects.toThrow('This operation was aborted');

    // Clean up
    blockingPromise.resolve();
    await blockingWork;
  });

  test('should handle abort signal with timeout', async () => {
    const limiter = new Limiter(1);
    const blockingPromise = new ManualPromise<void>();
    
    // Start blocking operation
    const blockingWork = (async () => {
      using slot = await limiter.acquireSlot();
      await blockingPromise.promise;
    })();

    await expect.poll(() => limiter.activeSlots()).toBe(1);

    // Use AbortSignal.timeout
    const timeoutSignal = AbortSignal.timeout(100);
    const timedOutWork = limiter.acquireSlot(timeoutSignal);
    await expect(timedOutWork).rejects.toThrow('The operation was aborted due to timeout');
    
    blockingPromise.resolve();
    await blockingWork;
  });

  test('should properly dispose slots', async () => {
    const limiter = new Limiter(1);

    {
      expect(limiter.activeSlots()).toBe(0);
      using slot = await limiter.acquireSlot();
      expect(limiter.activeSlots()).toBe(1);
    } // slot disposed here
    expect(limiter.activeSlots()).toBe(0);
  });
});
