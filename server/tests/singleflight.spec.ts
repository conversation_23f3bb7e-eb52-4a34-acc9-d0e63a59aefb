import { expect, test } from '@playwright/test';

import { ManualPromise } from '@flakiness/shared/common/manualPromise.js';
import { createAbortPromise } from '@flakiness/shared/common/utils.js';
import { Singleflight } from '../src/common/singleflight.js';


test.describe('Singleflight', () => {
  test('should deduplicate concurrent requests', async () => {
    let fetchCount = 0;
    const singleflight = new Singleflight<string, string>({
      key: input => input,
      fetch: async (input) => {
        return `result-${input}-${++fetchCount}`;
      },
    });

    // Make concurrent requests for the same key
    const [result1, result2, result3] = await Promise.all([
      singleflight.fetch('test'),
      singleflight.fetch('test'),
      singleflight.fetch('test'),
    ]);

    expect(result1).toBe('result-test-1');
    expect(result2).toBe('result-test-1');
    expect(result3).toBe('result-test-1');
    expect(fetchCount).toBe(1);

    // Make concurrent requests for the same key, after the first one concluded.
    const [result4, result5] = await Promise.all([
      singleflight.fetch('test'),
      singleflight.fetch('test'),
    ]);
    expect(result4).toBe('result-test-2');
    expect(result5).toBe('result-test-2');
    expect(fetchCount).toBe(2);
  });

  test('should handle different keys separately', async () => {
    let fetchCount = 0;
    const singleflight = new Singleflight<string, string>({
      key: input => input,
      fetch: async input => {
        fetchCount++;
        return `result-${input}-${fetchCount}`;
      },
    });

    const [result1, result2] = await Promise.all([
      singleflight.fetch('key1'),
      singleflight.fetch('key2'),
    ]);

    expect(result1).toBe('result-key1-1');
    expect(result2).toBe('result-key2-2');
    expect(fetchCount).toBe(2);
  });

  test('should handle fetch errors', async () => {
    const singleflight = new Singleflight<string, string>({
      key: input => input,
      fetch: async input => {
        throw new Error(`Failed to fetch ${input}`);
      },
    });
    const error = await singleflight.fetch('test').then(() => undefined).catch(e => e);
    expect(error.message).toBe('Failed to fetch test');
  });

  test('should ignore invalidate for non-running requests', async () => {
    let fetchCount = 0;
    const singleflight = new Singleflight<string, string>({
      key: input => input,
      fetch: async input => {
        return `${++fetchCount}`;
      },
    });
    expect(await singleflight.fetch('foo')).toBe('1');
    expect(await singleflight.fetch('foo')).toBe('2');
    expect(await singleflight.fetch('bar')).toBe('3');
    expect(fetchCount).toBe(3);
    singleflight.invalidate('foo');
    expect(fetchCount).toBe(3);
  });

  test('should invalidate and refetch', async () => {
    let fetchCount = 0;
    const fetchPromises: ManualPromise<void>[] = [];
    const singleflight = new Singleflight({
      key: (input: string) => input,
      fetch: async (input: string, key, signal) => {
        fetchCount++;
        const promise = new ManualPromise<void>();
        fetchPromises.push(promise);
        await Promise.race([
          promise.promise,
          createAbortPromise(signal).promise,
        ]);
        return `result-${input}-${fetchCount}`;
      },
    });

    // Start first request
    const promise1 = singleflight.fetch('test');

    // Wait for fetch to start
    await expect.poll(() => fetchPromises.length).toBe(1);
    
    // Invalidate while in flight
    singleflight.invalidate('test');
    
    // Wait for second fetch to start
    await expect.poll(() => fetchPromises.length).toBe(2);

    // Resolve both promises
    fetchPromises[0].resolve();
    fetchPromises[1].resolve();
    
    const result = await promise1;
    expect(result).toBe('result-test-2'); // Should be refetched
    expect(fetchCount).toBe(2);
  });

  test('should abort requests', async () => {
    let aborted = false;
    const fetchPromises: ManualPromise<string>[] = [];
    const singleflight = new Singleflight({
      key: (input: string) => input,
      fetch: async (input: string, key: string, signal: AbortSignal) => {
        signal.addEventListener('abort', () => { aborted = true; });
        const promise = new ManualPromise<string>();
        fetchPromises.push(promise);
        return Promise.race([
          promise.promise,
          createAbortPromise(signal).promise,
        ]);
      },
    });

    const promise = singleflight.fetch('test');
    
    // Wait for fetch to start
    await expect.poll(() => fetchPromises.length).toBe(1);

    singleflight.abort('test');
    
    expect(await promise).toBeUndefined()
    expect(aborted).toBe(true);
    expect(singleflight.has('test')).toBe(false);
  });

  test('should abort requests that throw some weird error on abortion', async () => {
    const fetchPromises: ManualPromise<string>[] = [];
    const singleflight = new Singleflight({
      key: (input: string) => input,
      fetch: async (input: string, key: string, signal: AbortSignal) => {
        const abortionPromise = new Promise((resolve, reject) => {
          signal.addEventListener('abort', () => {
            reject('not_an_abortion_error_really');
          });
        })
        const promise = new ManualPromise<string>();
        fetchPromises.push(promise);
        return Promise.race([
          promise.promise,
          abortionPromise,
        ]);
      },
    });

    const promise = singleflight.fetch('test');
    
    // Wait for fetch to start
    await expect.poll(() => fetchPromises.length).toBe(1);

    singleflight.abort('test');
    
    expect(await promise).toBeUndefined()
    expect(singleflight.has('test')).toBe(false);
  });

  test('refetch right after abort should work', async () => {
    const fetchPromises: ManualPromise<string>[] = [];
    const singleflight = new Singleflight({
      key: (input: string) => input,
      fetch: async (input: string, key: string, signal: AbortSignal) => {
        const promise = new ManualPromise<string>();
        fetchPromises.push(promise);
        return Promise.race([
          promise.promise,
          createAbortPromise(signal).promise,
        ]);
      },
    });

    const promise1 = singleflight.fetch('test');
    
    // Wait for fetch to start
    await expect.poll(() => fetchPromises.length).toBe(1);

    // The test point: re-fetch immediately after abort.
    singleflight.abort('test');
    const promise2 = singleflight.fetch('test');

    expect(await promise1).toBeUndefined();
    await expect.poll(() => fetchPromises.length).toBe(2);

    // wait a full task before resolving eveyrthing.
    await new Promise(x => process.nextTick(x));
    const promise3 = singleflight.fetch('test');

    fetchPromises[0].resolve('p-1');
    fetchPromises[1].resolve('p-2');    
    
    expect(await promise2).toBe('p-2');
    expect(await promise3).toBe('p-2');
  });

  test('should abort all requests', async () => {
    let abortedCount = 0;
    const fetchPromises: ManualPromise<string>[] = [];
    const singleflight = new Singleflight({
      key: (input: string) => input,
      fetch: async (input: string, key: string, signal: AbortSignal) => {
        signal.addEventListener('abort', () => { abortedCount++; });
        const promise = new ManualPromise<string>();
        fetchPromises.push(promise);
        return Promise.race([
          promise.promise,
          createAbortPromise(signal).promise,
        ]);
      },
    });

    const promises = [
      singleflight.fetch('test1'),
      singleflight.fetch('test2'),
      singleflight.fetch('test3'),
    ];

    // Wait for all fetches to start
    await expect.poll(() => fetchPromises.length).toBe(3);

    singleflight.abortAll();

    await Promise.allSettled(promises);
    expect(abortedCount).toBe(3);
  });

  test('should check if request is in flight', async () => {
    const fetchPromises: ManualPromise<string>[] = [];
    const singleflight = new Singleflight({
      key: (input: string) => input,
      fetch: async (input: string) => {
        const promise = new ManualPromise<string>();
        fetchPromises.push(promise);
        return promise.promise;
      },
    });

    expect(singleflight.has('test')).toBe(false);
    const promise = singleflight.fetch('test');
    // Wait for fetch to start
    await expect.poll(() => fetchPromises.length).toBe(1);
    expect(singleflight.has('test')).toBe(true);

    // Resolve the promise
    fetchPromises[0].resolve(`result-test`);
    expect(await promise).toBe('result-test');
    expect(singleflight.has('test')).toBe(false);
  });

  test('should respect concurrency limit', async () => {
    let activeCount = 0;
    
    const singleflight = new Singleflight({
      key: (input: string) => input,
      concurrency: 2,
      fetch: async (input: string) => {
        const result = ++activeCount;
        await new Promise(x => setTimeout(x, 20));
        --activeCount;
        return result;
      },
    });

    // Start 4 requests with different keys (so they don't get deduplicated)
    const concurrencies = await Promise.all([
      singleflight.fetch('key1'),
      singleflight.fetch('key2'), 
      singleflight.fetch('key3'),
      singleflight.fetch('key4'),
      singleflight.fetch('key5'),
    ]);

    expect(concurrencies.every(x => x && x <= 2)).toBe(true);
  });
});
