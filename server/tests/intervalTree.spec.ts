import { expect, test } from '@playwright/test';
import { IntervalTree } from '../src/common/intervalTree.js';

test('should initialize empty', () => {
  const tree = new IntervalTree([0, 1, 2, 3, 4], (a, b) => a + b, 0);
  // Add assertions for empty tree behavior
  expect(tree.query(0, 0)).toBe(0);
  expect(tree.query(0, 4)).toBe(10); // sum of [0,1,2,3,4]
});

test('should handle single element queries', () => {
  const tree = new IntervalTree([5, 10, 15, 20], (a, b) => a + b, 0);
  expect(tree.query(0, 0)).toBe(5);
  expect(tree.query(1, 1)).toBe(10);
  expect(tree.query(2, 2)).toBe(15);
  expect(tree.query(3, 3)).toBe(20);
});

test('should handle range queries with addition', () => {
  const tree = new IntervalTree([1, 2, 3, 4, 5], (a, b) => a + b, 0);
  expect(tree.query(0, 2)).toBe(6); // 1+2+3
  expect(tree.query(1, 3)).toBe(9); // 2+3+4
  expect(tree.query(0, 4)).toBe(15); // 1+2+3+4+5
  expect(tree.query(2, 4)).toBe(12); // 3+4+5
});

test('should work with different combine functions', () => {
  // Test with max function
  const maxTree = new IntervalTree([3, 1, 4, 1, 5], Math.max, -Infinity);
  expect(maxTree.query(0, 4)).toBe(5);
  expect(maxTree.query(0, 2)).toBe(4);
  expect(maxTree.query(3, 4)).toBe(5);
  
  // Test with min function
  const minTree = new IntervalTree([3, 1, 4, 1, 5], Math.min, Infinity);
  expect(minTree.query(0, 4)).toBe(1);
  expect(minTree.query(0, 2)).toBe(1);
  expect(minTree.query(2, 4)).toBe(1);
});

test('should handle empty ranges', () => {
  const tree = new IntervalTree([1, 2, 3], (a, b) => a + b, 0);
  // Empty range should return identity
  expect(tree.query(1, 0)).toBe(0);
  expect(tree.query(2, 1)).toBe(0);
});

test('should work with object values', () => {
  const combine = (a: { count: number }, b: { count: number }) => ({ count: a.count + b.count });
  const identity = { count: 0 };
  const values = [{ count: 1 }, { count: 2 }, { count: 3 }, { count: 4 }];
  
  const tree = new IntervalTree(values, combine, identity);
  
  expect(tree.query(0, 1)).toEqual({ count: 3 }); // {1} + {2}
  expect(tree.query(0, 3)).toEqual({ count: 10 }); // {1} + {2} + {3} + {4}
  expect(tree.query(2, 3)).toEqual({ count: 7 }); // {3} + {4}
});

test('should handle large arrays efficiently', () => {
  const size = 10000;
  const values = Array.from({length: size}, (_, i) => i + 1);
  const tree = new IntervalTree(values, (a, b) => a + b, 0);
  
  // Test various range queries
  expect(tree.query(0, 99)).toBe(5050); // sum of 1 to 100
  expect(tree.query(0, size - 1)).toBe((size * (size + 1)) / 2); // sum of 1 to size
  expect(tree.query(size - 100, size - 1)).toBe(995050); // sum of last 100 numbers
});

test('should handle negative numbers', () => {
  const tree = new IntervalTree([-5, -2, 3, -1, 4], (a, b) => a + b, 0);
  
  expect(tree.query(0, 1)).toBe(-7); // -5 + -2
  expect(tree.query(2, 4)).toBe(6); // 3 + -1 + 4
  expect(tree.query(0, 4)).toBe(-1); // -5 + -2 + 3 + -1 + 4
});

test('should work with string concatenation', () => {
  const tree = new IntervalTree(['a', 'b', 'c', 'd'], (a, b) => a + b, '');

  expect(tree.query(0, 1)).toBe('ab');
  expect(tree.query(1, 3)).toBe('bcd');
  expect(tree.query(0, 3)).toBe('abcd');
});

