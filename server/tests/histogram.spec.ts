import { Multimap } from '@flakiness/shared/common/multimap.js';
import { expect, test } from '@playwright/test';
import { Ranges } from '../src/common/ranges.js';
import { Histogram } from '../src/common/stats/histogram.js';

function dSort(a: [number, Ranges.Ranges<number>], b: [number, Ranges.Ranges<number>]) {
  return a[0] - b[0];
}

function simpleIntersectBuckets(b1: Histogram.Histogram, b2: Histogram.Histogram) {
  const all = Ranges.unionAll([
    ...b1.map(b => b[1]),
    ...b2.map(b => b[1]),
  ]);
  const domain = Ranges.domain(all)!;
  const m = new Int32Array(domain.max - domain.min + 1);
  for (const [duration, ranges] of b1) {
    for (const x of Ranges.iterate(ranges))
      m[x - domain.min] += duration;
  }
  for (const [duration, ranges] of b2) {
    for (const x of Ranges.iterate(ranges))
      m[x - domain.min] += duration;
  }
  const buckets = new Multimap<number, number>();
  for (const x of Ranges.iterate(all))
    buckets.set(m[x - domain.min], x);
  return buckets.map((duration, values) => [duration, Ranges.fromList([...values])] as [number, Ranges.Ranges<number>]).sort(dSort);
}

test.describe('Histogram.sum', () => {
  test('basic intersection', async () => {
    const b1 = [
      [10, [1, 10]],
      [20, [21, 30]],
    ] as Histogram.Histogram;

    const b2 = [
      [3, [5, 25]],
    ] as Histogram.Histogram;

    expect(Histogram.sum(b1, b2).sort(dSort)).toEqual(simpleIntersectBuckets(b1, b2));
  });

  test('single elements', async () => {
    const b1 = [
      [10, [1, 1, 3, 3, 7, 7]],
    ] as Histogram.Histogram;
    
    const b2 = [
      [100, [1, 100]],
    ] as Histogram.Histogram;
    expect(Histogram.sum(b1, b2).sort(dSort)).toEqual(simpleIntersectBuckets(b1, b2));
  });

  test('no intersection', async () => {
    const b1 = [
      [10, [1, 5]],
    ] as Histogram.Histogram;

    const b2 = [
      [20, [10, 15]],
    ] as Histogram.Histogram;

    expect(Histogram.sum(b1, b2).sort(dSort)).toEqual(simpleIntersectBuckets(b1, b2));
  });

  test('partial overlaps', async () => {
    const b1 = [
      [5, [1, 10]],
    ] as Histogram.Histogram;
    
    const b2 = [
      [7, [5, 15]],
    ] as Histogram.Histogram;
    expect(Histogram.sum(b1, b2).sort(dSort)).toEqual(simpleIntersectBuckets(b1, b2));
  });

  test('empty buckets', async () => {
    const b1 = [] as Histogram.Histogram;
    const b2 = [[10, [1, 5]]] as Histogram.Histogram;
    expect(Histogram.sum(b1, b2).sort(dSort)).toEqual(simpleIntersectBuckets(b1, b2));
  });

  test('multiple ranges per bucket', async () => {
    const b1 = [
      [10, [1, 3, 8, 10]],
      [15, [5, 7]],
    ] as Histogram.Histogram;
    
    const b2 = [
      [5, [2, 4, 9, 12]],
    ] as Histogram.Histogram;
    
    expect(Histogram.sum(b1, b2).sort(dSort)).toEqual(simpleIntersectBuckets(b1, b2));
  });

  test('identical ranges', async () => {
    const b1 = [
      [10, [1, 5, 10, 15]],
    ] as Histogram.Histogram;
    
    const b2 = [
      [20, [1, 5, 10, 15]],
    ] as Histogram.Histogram;
    
    expect(Histogram.sum(b1, b2).sort(dSort)).toEqual(simpleIntersectBuckets(b1, b2));
  });

  test('complex scenario', async () => {
    const b1 = [
      [100, [1, 1, 20, 30]],
      [200, [2, 19, 31, 40]],
    ] as Histogram.Histogram;

    const b2 = [
      [50, [5, 5, 7, 7]],
      [75, [1, 4]],
      [80, [6, 6]],
      [90, [8, 20, 30, 50]],
      [100, [21, 29]],
    ] as Histogram.Histogram;
    
    expect(Histogram.sum(b1, b2).sort(dSort)).toEqual(simpleIntersectBuckets(b1, b2));
  });
});

test.describe('computeTimeBuckets', () => {
  test('zero precision computation', async () => {
    const timings = new Map([
      [1, 1000],
      [2, 1001], 
      [3, 1002],
    ]);
    
    expect(Histogram.compute(timings, { maxRelativeError: 0, minRadius: 0 })).toEqual([
      [1000, [1, 1]],
      [1001, [2, 2]],
      [1002, [3, 3]],
    ])
  });

  test('10% precision computation', async () => {
    const timings = new Map([
      [1, 1000],
      [2, 1001], 
      [3, 1002],
      [4, 1002],
      [5, 1002],
    ]);
    expect(Histogram.compute(timings, { maxRelativeError: 0.1, minRadius: 0 })).toEqual([
      [1001, [1, 5]],
    ])
  });
});

test.describe('Histogram.expand', () => {
  test('basic expansion', async () => {
    const h = [
      [10, [1, 3]],
      [20, [5, 7]],
    ] as Histogram.Histogram;

    const expanded = Histogram.expand(h);
    
    expect(expanded(0)).toBeUndefined(); // outside domain
    expect(expanded(1)).toBe(10);
    expect(expanded(2)).toBe(10);
    expect(expanded(3)).toBe(10);
    expect(expanded(4)).toBe(0);
    expect(expanded(5)).toBe(20);
    expect(expanded(6)).toBe(20);
    expect(expanded(7)).toBe(20);
    expect(expanded(8)).toBeUndefined(); // outside domain
  });

  test('empty histogram', async () => {
    const h = [] as Histogram.Histogram;
    const expanded = Histogram.expand(h);
    
    expect(expanded(0)).toBeUndefined();
    expect(expanded(1)).toBeUndefined();
  });

  test('single point ranges', async () => {
    const h = [
      [100, [5, 5, 10, 10]],
    ] as Histogram.Histogram;

    const expanded = Histogram.expand(h);
    
    expect(expanded(4)).toBeUndefined();
    expect(expanded(5)).toBe(100);
    expect(expanded(6)).toBe(0);
    expect(expanded(9)).toBe(0);
    expect(expanded(10)).toBe(100);
    expect(expanded(11)).toBeUndefined();
  });
});


test.describe('Histogram.get', () => {
  test('basic intersection', async () => {
    const h = [
      [10, [3, 3]],
      [20, [1, 2, 6,7]],
    ] as Histogram.Histogram;

    expect(Histogram.get(h, 0)).toBeUndefined();
    expect(Histogram.get(h, 1)).toBe(20);
    expect(Histogram.get(h, 2)).toBe(20);
    expect(Histogram.get(h, 3)).toBe(10);
    expect(Histogram.get(h, 4)).toBeUndefined();
    expect(Histogram.get(h, 5)).toBeUndefined();
    expect(Histogram.get(h, 6)).toBe(20);
    expect(Histogram.get(h, 7)).toBe(20);
    expect(Histogram.get(h, 8)).toBeUndefined();
  });
});