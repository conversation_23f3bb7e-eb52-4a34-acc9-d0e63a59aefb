import { expect, test } from '@playwright/test';
import { Heap } from '../src/common/heap.js';

test('should initialize empty', () => {
  const heap = Heap.createMin<string>();
  expect(heap.popEntry()).toBeUndefined();
});

test('should set and retrieve minimum value', { tag: '@smoke' }, () => {
  const heap = Heap.createMin<string>();
  heap.push('a', 10);
  heap.push('b', 5);
  heap.push('c', 15);
  
  expect(heap.peekEntry()).toEqual(['b', 5]);
});

test('should handle negative keys', () => {
  const heap = Heap.createMin<string>();
  heap.push('a', 5);
  heap.push('b', -10);
  heap.push('c', 0);
  
  expect(heap.popEntry()).toEqual(['b', -10]);
  expect(heap.popEntry()).toEqual(['c', 0]);
});

test('should handle edge case with one element', () => {
  const heap = Heap.createMin<string>();
  heap.push('a', 5);
  
  expect(heap.peekEntry()).toEqual(['a', 5]);
  
  expect(heap.popEntry()).toEqual(['a', 5]);
  expect(heap.popEntry()).toBeUndefined();
});

test('should initialize with pre-defined elements', () => {
  const heap = Heap.createMin<string>([['c', 1], ['a', 10], ['b', 5]]);
  expect(heap.popEntry()).toEqual(['c', 1]);
  expect(heap.popEntry()).toEqual(['b', 5]);
  expect(heap.peekEntry()).toEqual(['a', 10]);
});

test('should maintain heap property with duplicate keys', () => {
  const heap = Heap.createMin<string>();
  heap.push('a', 5);
  heap.push('b', 5);
  heap.push('c', 5);
  heap.push('d', 3);
  
  expect(heap.popEntry()).toEqual(['d', 3]);
  // Should get one of the 5-priority items
  const next = heap.popEntry();
  expect(next?.[1]).toBe(5);
});

test('should handle large number of elements', () => {
  const heap = Heap.createMin<number>();
  const items = Array.from({ length: 100 }, (_, i) => [i, Math.random() * 100] as [number, number]);
  
  items.forEach(([value, priority]) => heap.push(value, priority));
  
  let prev = -Infinity;
  while (true) {
    const entry = heap.popEntry();
    if (!entry) break;
    expect(entry[1]).toBeGreaterThanOrEqual(prev);
    prev = entry[1];
  }
});

test('should handle mixed push and pop operations', () => {
  const heap = Heap.createMin<string>();
  
  heap.push('a', 10);
  heap.push('b', 5);
  expect(heap.popEntry()).toEqual(['b', 5]);
  
  heap.push('c', 3);
  heap.push('d', 15);
  expect(heap.popEntry()).toEqual(['c', 3]);
  expect(heap.popEntry()).toEqual(['a', 10]);
});

test('should peek without modifying heap', () => {
  const heap = Heap.createMin<string>();
  heap.push('a', 10);
  heap.push('b', 5);
  
  expect(heap.peekEntry()).toEqual(['b', 5]);
  expect(heap.peekEntry()).toEqual(['b', 5]); // Should be same
  expect(heap.popEntry()).toEqual(['b', 5]); // Should match peek
});

test('should handle zero and infinity values', () => {
  const heap = Heap.createMin<string>();
  heap.push('zero', 0);
  heap.push('infinity', Infinity);
  heap.push('negative-infinity', -Infinity);
  heap.push('negative', -100);
  
  expect(heap.popEntry()).toEqual(['negative-infinity', -Infinity]);
  expect(heap.popEntry()).toEqual(['negative', -100]);
  expect(heap.popEntry()).toEqual(['zero', 0]);
  expect(heap.popEntry()).toEqual(['infinity', Infinity]);
});

test('should maintain correct order after multiple operations', () => {
  const heap = Heap.createMin<string>();
  const operations = [
    ['push', 'a', 20],
    ['push', 'b', 10],
    ['push', 'c', 30],
    ['pop'],
    ['push', 'd', 5],
    ['push', 'e', 25],
    ['pop'],
    ['pop']
  ];
  
  const results: any[] = [];
  operations.forEach(op => {
    if (op[0] === 'push') {
      heap.push(op[1] as string, op[2] as number);
    } else {
      results.push(heap.popEntry());
    }
  });
  
  expect(results[0]).toEqual(['b', 10]); // First pop
  expect(results[1]).toEqual(['d', 5]);  // Second pop after adding d(5)
  expect(results[2]).toEqual(['a', 20]); // Third pop
});
