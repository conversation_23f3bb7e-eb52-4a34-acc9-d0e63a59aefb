import { FlakinessReport } from '@flakiness/report';
import { expect, test } from '@playwright/test';
import { randomUUID } from 'crypto';
import { Stats } from '../src/common/stats/stats.js';
import { Timeline, TimelineKey } from '../src/common/timeline/timeline.js';
import { TimelineSplit } from '../src/common/timeline/timelineSplit.js';
import { WireTypes } from '../src/common/wireTypes.js';

test('Timeline.acceptsEnvironment', async () => {
  const env: WireTypes.RunEnvironment = {
    systemData: {
      osArch: 'arm',
      osName: 'Ubuntu',
      osVersion: 'focal',
    },
    category: 'junit',
    envId: 'ff' as Stats.EnvId,
  };

  expect.soft(new Timeline([[TimelineKey.systemKeys.OS_NAME, 'ubuntu focal']]).acceptsEnvironment(env)).toBe(true);
  expect.soft(new Timeline([]).acceptsEnvironment(env)).toBe(true);
  expect.soft(new Timeline([[TimelineKey.systemKeys.OS_ARCH, 'arm']]).acceptsEnvironment(env)).toBe(true);
  expect.soft(new Timeline([[TimelineKey.systemKeys.OS_ARCH, 'x86_64']]).acceptsEnvironment(env)).toBe(false);
});

test.describe('TimelineSplit', () => {
  const createEnv = (category: string, name: string, configPath: string, osName?: string): WireTypes.RunEnvironment => ({
    systemData: {
      osArch: 'x64',
      osName: osName || 'Linux',
      osVersion: '20.04',
    },
    category,
    name,
    configPath: configPath as FlakinessReport.GitFilePath,
    envId: randomUUID() as Stats.EnvId,
  });

  test('should toggle values', async () => {
    const split = new TimelineSplit()
      .toggleValue(TimelineKey.systemKeys.CATEGORY, 'playwright');
    
    const pwEnv = createEnv('playwright', 'test1', 'config.js');
    const junitEnv = createEnv('junit', 'test2', 'config.xml');
    
    expect(split.acceptsEnvironment(pwEnv)).toBe(true);
    expect(split.acceptsEnvironment(junitEnv)).toBe(false);
  });

  test('should check hasValue', async () => {
    const split = new TimelineSplit()
      .toggleValue(TimelineKey.systemKeys.CATEGORY, 'playwright');
    
    expect(split.hasValue(TimelineKey.systemKeys.CATEGORY, 'playwright')).toBe(true);
    expect(split.hasValue(TimelineKey.systemKeys.CATEGORY, 'junit')).toBe(false);
  });

  test('should create from timeline', async () => {
    const timeline = new Timeline([[TimelineKey.systemKeys.CATEGORY, 'playwright']]);
    const split = TimelineSplit.fromTimeline(timeline);
    
    expect(split.acceptsEnvironment(createEnv('playwright', 'test1', 'config.js'))).toBe(true);
    expect(split.acceptsEnvironment(createEnv('junit', 'test1', 'config.js'))).toBe(false);
  });

  test('should clone correctly', async () => {
    const original = new TimelineSplit()
      .toggleValue(TimelineKey.systemKeys.CATEGORY, 'playwright')
      .splitBy(TimelineKey.systemKeys.NAME);
    const cloned = original.clone();
    expect(cloned.isEqual(original)).toBe(true);
    expect(cloned.isSplitBy(TimelineKey.systemKeys.NAME)).toBe(true);
  });

  test('should clear and collapse', async () => {
    const split = new TimelineSplit()
      .splitBy(TimelineKey.systemKeys.CATEGORY)
      .splitBy(TimelineKey.systemKeys.NAME);
    
    const collapsed = split.clearAndCollapse(TimelineKey.systemKeys.CATEGORY);
    expect(collapsed.isSplitBy(TimelineKey.systemKeys.CATEGORY)).toBe(false);
    expect(collapsed.isSplitBy(TimelineKey.systemKeys.NAME)).toBe(true);
  });

  test('should generate timelines from environments', async () => {
    const envs = [
      createEnv('playwright', 'project1', 'config1.js'),
      createEnv('playwright', 'project2', 'config1.js'),
      createEnv('junit', 'suite1', 'config.xml'),
    ];
    expect(TimelineSplit.DEFAULT.timelines(envs).length).toBe(3);
    expect(TimelineSplit.PW_PROJECTS.timelines(envs).length).toBe(2);
    expect(TimelineSplit.JUNIT_ENVS.timelines(envs).length).toBe(1);
    expect(TimelineSplit.PERF_BENCHMARKS.timelines(envs).length).toBe(0);
    expect(new TimelineSplit()
        .toggleValue(TimelineKey.systemKeys.NAME, 'project1')
        .timelines(envs)
        .length
    ).toBe(1);
  });

  test('should work with predefined splits', async () => {
    const pwEnv = createEnv('playwright', 'project1', 'config.js');
    const junitEnv = createEnv('junit', 'suite1', 'config.xml');
    
    expect(TimelineSplit.PW_PROJECTS.acceptsEnvironment(pwEnv)).toBe(true);
    expect(TimelineSplit.PW_PROJECTS.acceptsEnvironment(junitEnv)).toBe(false);
    expect(TimelineSplit.JUNIT_ENVS.acceptsEnvironment(junitEnv)).toBe(true);
    expect(TimelineSplit.JUNIT_ENVS.acceptsEnvironment(pwEnv)).toBe(false);
  });

  test('should serialize and deserialize', async () => {
    const original = new TimelineSplit()
      .toggleValue(TimelineKey.systemKeys.CATEGORY, 'playwright')
      .toggleValue(TimelineKey.createUserKey('browser'), 'chrome')
      .splitBy(TimelineKey.systemKeys.NAME);
    
    const serialized = original.serialize();
    const deserialized = TimelineSplit.deserialize(serialized);
    expect(deserialized.isEqual(original)).toBe(true);
  });

  test('should handle user keys', async () => {
    const split = TimelineSplit.PW_PROJECTS
      .toggleValue(TimelineKey.createUserKey('browser'), 'chrome');
    
    expect(split.acceptsEnvironment({
      ...createEnv('playwright', 'regressions', 'config2.js'),
      userSuppliedData: { browser: 'chrome' }
    })).toBe(true);
    expect(split.acceptsEnvironment({
      ...createEnv('playwright', 'regressions', 'config2.js'),
      userSuppliedData: { browser: 'firefox' }
    })).toBe(false);
  });
});
