
import { expect, test } from '@playwright/test';
import { SharedCacheStore } from '../src/common/caches/cache.js';
import { LRUPolicy } from '../src/common/caches/lruPolicy.js';

test.describe('SharedCacheStore', () => {
  test('should create partitions and share eviction policy', () => {
    const policy = new LRUPolicy<number>(3);
    const store = new SharedCacheStore({ policy });
    
    const evicted1: Array<[string, string]> = [];
    const evicted2: Array<[number, number]> = [];
    
    const partition1 = store.createPartition<string, string>((key, value) => {
      evicted1.push([key, value]);
    });
    
    const partition2 = store.createPartition<number, number>((key, value) => {
      evicted2.push([key, value]);
    });

    // Add items to both partitions
    partition1.set('a', 'value-a');
    partition1.set('b', 'value-b');
    partition2.set(1, 100);
    
    expect(store.size).toBe(3);
    expect(partition1.size).toBe(2);
    expect(partition2.size).toBe(1);

    // Adding one more should evict the oldest
    partition2.set(2, 200);
    
    expect(store.size).toBe(3);
    expect(evicted1).toEqual([['a', 'value-a']]);
    expect(evicted2).toEqual([]);
  });

  test('should handle admission control', () => {
    const policy = new LRUPolicy<number>(2);
    let admission = true;
    const store = new SharedCacheStore({ policy, shouldAdmit: () => admission });
    
    const evicted: Array<[string, string]> = [];
    const partition = store.createPartition<string, string>((key, value) => {
      evicted.push([key, value]);
    });

    partition.set('a', 'value-a');
    partition.set('b', 'value-b');
    expect(store.size).toBe(2);

    // Disable admission
    admission = false;
    partition.set('c', 'value-c');
    
    expect(store.size).toBe(2); // Should not add new items
    expect(partition.has('c')).toBe(false);
    expect(evicted).toEqual([['c', 'value-c']]);

    // Re-enable admission
    admission = true;
    partition.set('c', 'value-c');
    
    expect(store.size).toBe(2);
    expect(partition.has('c')).toBe(true);
    expect(evicted).toEqual([
      ['c', 'value-c'],
      ['a', 'value-a'],
    ]);
  });

  test('should support manual eviction', () => {
    const policy = new LRUPolicy<number>(3);
    const store = new SharedCacheStore({ policy });

    const evicted: Array<[string, string]> = [];
    const partition = store.createPartition<string, string>((key, value) => {
      evicted.push([key, value]);
    });

    partition.set('a', 'value-a');
    partition.set('b', 'value-b');
    
    expect(store.size).toBe(2);
    
    store.evict();
    
    expect(store.size).toBe(1);
    expect(evicted).toEqual([['a', 'value-a']]);
  });

  test('should clear all partitions', () => {
    const policy = new LRUPolicy<number>(5);
    const store = new SharedCacheStore({ policy });
    
    const evicted1: Array<[string, string]> = [];
    const evicted2: Array<[number, number]> = [];
    
    const partition1 = store.createPartition<string, string>((key, value) => {
      evicted1.push([key, value]);
    });
    
    const partition2 = store.createPartition<number, number>((key, value) => {
      evicted2.push([key, value]);
    });

    partition1.set('a', 'value-a');
    partition1.set('b', 'value-b');
    partition2.set(1, 100);
    partition2.set(2, 200);
    
    expect(store.size).toBe(4);
    
    store.clear();
    
    expect(store.size).toBe(0);
    expect(partition1.size).toBe(0);
    expect(partition2.size).toBe(0);
    expect(evicted1).toEqual([['a', 'value-a'], ['b', 'value-b']]);
    expect(evicted2).toEqual([[1, 100], [2, 200]]);
  });

  test('partition should support all CacheStore operations', () => {
    const policy = new LRUPolicy<number>(5);
    const store = new SharedCacheStore({ policy });
    
    const partition = store.createPartition<string, string>(() => {});

    // Test set/get/has
    partition.set('key1', 'value1');
    partition.set('key2', 'value2');
    
    expect(partition.get('key1')).toBe('value1');
    expect(partition.get('key2')).toBe('value2');
    expect(partition.get('nonexistent')).toBeUndefined();
    
    expect(partition.has('key1')).toBe(true);
    expect(partition.has('nonexistent')).toBe(false);
    
    // Test delete
    partition.delete('key1');
    expect(partition.has('key1')).toBe(false);
    expect(partition.get('key1')).toBeUndefined();
    expect(partition.size).toBe(1);
    
    // Test iterators
    partition.set('key3', 'value3');
    
    const entries = [...partition.entries()];
    const keys = [...partition.keys()];
    const values = [...partition.values()];
    
    expect(entries).toEqual([['key2', 'value2'], ['key3', 'value3']]);
    expect(keys).toEqual(['key2', 'key3']);
    expect(values).toEqual(['value2', 'value3']);
    
    // Test Symbol.iterator
    const iteratorEntries = [...partition];
    expect(iteratorEntries).toEqual([['key2', 'value2'], ['key3', 'value3']]);
  });

  test('should handle policy hits correctly', () => {
    const store = new SharedCacheStore({ policy: new LRUPolicy<number>(2) });
    
    const partition = store.createPartition<string, string>(() => {});

    partition.set('a', 'value-a');
    partition.set('b', 'value-b');
    
    // Access 'a' to make it more recently used
    partition.get('a');
    
    // Add 'c' - should evict 'b' since 'a' was accessed more recently
    partition.set('c', 'value-c');
    
    expect(partition.has('a')).toBe(true);
    expect(partition.has('b')).toBe(false);
    expect(partition.has('c')).toBe(true);
    expect(partition.size).toBe(2);
  });

  test('partition should support clear operation', () => {
    const policy = new LRUPolicy<number>(5);
    const store = new SharedCacheStore({ policy });
    
    const evicted: Array<[string, string]> = [];
    const partition = store.createPartition<string, string>((key, value) => {
      evicted.push([key, value]);
    });

    partition.set('key1', 'value1');
    partition.set('key2', 'value2');
    partition.set('key3', 'value3');
    
    expect(partition.size).toBe(3);
    expect(store.size).toBe(3);
    
    partition.clear();
    
    expect(partition.size).toBe(0);
    expect(store.size).toBe(0);
    expect(partition.has('key1')).toBe(false);
    expect(evicted).toEqual([]);
  });

  test('partition clear should not affect other partitions', () => {
    const policy = new LRUPolicy<number>(10);
    const store = new SharedCacheStore({ policy });
    
    const partition1 = store.createPartition<string, string>(() => {});
    const partition2 = store.createPartition<number, number>(() => {});

    partition1.set('a', 'value-a');
    partition1.set('b', 'value-b');
    partition2.set(1, 100);
    partition2.set(2, 200);
    
    expect(store.size).toBe(4);
    expect(partition1.size).toBe(2);
    expect(partition2.size).toBe(2);
    
    partition1.clear();
    
    expect(store.size).toBe(2);
    expect(partition1.size).toBe(0);
    expect(partition2.size).toBe(2);
    expect(partition1.has('a')).toBe(false);
    expect(partition2.has(1)).toBe(true);
  });
});
