import { FlakinessReport } from '@flakiness/report';
import { expect, test } from '@playwright/test';
import ms from 'ms';
import { MatchResult } from '../src/common/fql/matcher.js';
import { Query } from '../src/common/fql/query.js';
import { Token } from '../src/common/fql/tokens.js';
import { Stats } from '../src/common/stats/stats.js';
import { WireTypes } from '../src/common/wireTypes.js';

function simplify(token: InstanceType<typeof Token>) {
  return {
    type: token.type,
    value: token.value,
    index: token.from,
  }
}

function tokenize(text: string): Token[] {
  return Token.tokenize(text).tokens;
}

test.describe('Token.tokenize', () => {
  test('from-to indexes are correct', () => {
    expect(tokenize('"1 1"foo@')).toEqual([
      expect.objectContaining({ type: 'word', value: '1 1', from: 0, to: 5 }),
      expect.objectContaining({ type: 'word', value: 'foo', from: 5, to: 8 }),
      expect.objectContaining({ type: 'punctuation', value: '@', from: 8, to: 9 }),
    ]);
  });

  test('escaping', () => {
    expect.soft(tokenize(`'the \\'St. Petersburg\\' city'`).map(simplify)).toEqual([
      { type: 'word', value: `the 'St. Petersburg' city` , index: 0 }
    ]);

    expect.soft(tokenize(`"the \\"Moscow\\" city"`).map(simplify)).toEqual([
      { type: 'word', value: 'the "Moscow" city' , index: 0 }
    ]);
  
    expect.soft(tokenize(`geo:"the \\"Moscow\\" city"`).map(simplify)).toEqual([
      { type: 'word', value: 'geo', index: 0 },
      { type: 'punctuation', value: ':', index: 3 },
      { type: 'word', value: 'the "Moscow" city' , index: 4 }
    ]);

    expect.soft(Token.tokenize(`'bad \\"escape'`).error).toEqual({
      position: 6,
      message: 'bad escape sequence'
    });
  });

  test('general tokenization', async () => {
    expect.soft(tokenize('server/tests/ranges.spec.ts').map(simplify)).toEqual([
      { type: 'word', value: 'server/tests/ranges.spec.ts', index: 0 },
    ])
    expect.soft(tokenize('-foo').map(simplify)).toEqual([
      { type: 'punctuation', value: '-', index: 0 },
      { type: 'word', value: 'foo', index: 1 },
    ]);
    expect.soft(tokenize('-$foo').map(simplify)).toEqual([
      { type: 'punctuation', value: '-', index: 0 },
      { type: 'punctuation', value: '$', index: 1 },
      { type: 'word', value: 'foo', index: 2 },
    ]);
    expect.soft(tokenize('-@foo').map(simplify)).toEqual([
      { type: 'punctuation', value: '-', index: 0 },
      { type: 'punctuation', value: '@', index: 1 },
      { type: 'word', value: 'foo', index: 2 },
    ]);
    expect.soft(tokenize('bar-foo').map(simplify)).toEqual([
      { type: 'word', value: 'bar-foo', index: 0 },
    ]);
    expect.soft(tokenize('b-r -wo').map(simplify)).toEqual([
      { type: 'word', value: 'b-r', index: 0 },
      { type: 'punctuation', value: '-', index: 4 },
      { type: 'word', value: 'wo', index: 5 },
    ]);
    expect.soft(tokenize('(foo @bar)').map(simplify)).toEqual([
      { type: 'punctuation', value: '(', index: 0 },
      { type: 'word', value: 'foo', index: 1 },
      { type: 'punctuation', value: '@', index: 5 },
      { type: 'word', value: 'bar', index: 6 },
      { type: 'punctuation', value: ')', index: 9 },
    ]);

    expect.soft(tokenize('#smoke').map(simplify)).toEqual([
      { type: 'punctuation', value: '#', index: 0 },
      { type: 'word', value: 'smoke', index: 1 },
    ]);
  
    expect.soft(tokenize('foo').map(simplify)).toEqual([
      { type: 'word', value: 'foo', index: 0 },
    ]);
    expect.soft(tokenize('foo.bar').map(simplify)).toEqual([
      { type: 'word', value: 'foo.bar', index: 0 },
    ]);
    expect.soft(tokenize('foo @bar').map(simplify)).toEqual([
      { type: 'word', value: 'foo', index: 0 },
      { type: 'punctuation', value: '@', index: 4 },
      { type: 'word', value: 'bar', index: 5 },
    ]);
    expect.soft(tokenize('foo $bar').map(simplify)).toEqual([
      { type: 'word', value: 'foo', index: 0 },
      { type: 'punctuation', value: '$', index: 4 },
      { type: 'word', value: 'bar', index: 5 },
    ]);
  
    expect.soft(tokenize('foo=bar').map(simplify)).toEqual([
      { type: 'word', value: 'foo', index: 0 },
      { type: 'punctuation', value: '=', index: 3 },
      { type: 'word', value: 'bar', index: 4 }
    ]);
    expect.soft(tokenize(`foo="wtf dUdE"`).map(simplify)).toEqual([
      { type: 'word', value: 'foo', index: 0 },
      { type: 'punctuation', value: '=', index: 3 },
      { type: 'word', value: 'wtf dUdE' , index: 4 }
    ]);
  
    expect.soft(tokenize(`foo=bar OR baz=bad`).map(simplify)).toEqual([
      { type: 'word', value: 'foo', index: 0 },
      { type: 'punctuation', value: '=', index: 3 },
      { type: 'word', value: 'bar', index: 4 },
      { type: 'word', value: 'OR', index: 8 },
      { type: 'word', value: 'baz', index: 11 },
      { type: 'punctuation', value: '=', index: 14 },
      { type: 'word', value: 'bad', index: 15 }
    ]);
    expect.soft(tokenize(`@E)!OR __F`).map(simplify)).toEqual([
      { type: 'punctuation', value: '@', index: 0 },
      { type: 'word', value: 'E', index: 1 },
      { type: 'punctuation', value: ')', index: 2 },
      { type: 'punctuation', value: '!', index: 3 },
      { type: 'word', value: 'OR', index: 4 },
      { type: 'word', value: '__F', index: 7 }
    ]);
    expect.soft(tokenize(`foo = bar OR foo ≠ (a, b, 'c d')`).map(simplify)).toEqual([
      { type: 'word', value: 'foo', index: 0 },
      { type: 'punctuation', value: '=', index: 4 },
      { type: 'word', value: 'bar', index: 6 },
      { type: 'word', value: 'OR', index: 10 },
      { type: 'word', value: 'foo', index: 13 },
      { type: 'punctuation', value: '≠', index: 17 },
      { type: 'punctuation', value: '(', index: 19 },
      { type: 'word', value: 'a', index: 20 },
      { type: 'punctuation', value: ',', index: 21 },
      { type: 'word', value: 'b', index: 23 },
      { type: 'punctuation', value: ',', index: 24 },
      { type: 'word', index: 26, value: 'c d' },
      { type: 'punctuation', value: ')', index: 31 }
    ]);
  });
});

test('Query.serialize', async () => {
  expect.soft(Query.parse(`
    y   x  z)
  `).serialize()).toBe('y x z');

  expect.soft(Query.parse(`
    s:failed s:flaked
  `).serialize()).toBe('s:failed s:flaked');

  //NOTE: serialization DOES NOT RETAIN unparsed tokens!
  expect.soft(Query.parse(`
    @@@@click
  `).serialize()).toBe('@click');
  expect.soft(Query.parse(`
    @@@@click @env
  `).serialize()).toBe('@click @env');
});

test('Query.normalize', async () => {
  expect.soft(Query.parse(`
    y   x  z
  `).normalize().serialize()).toBe('x y z');
  expect.soft(Query.parse(`
    status : (passed, missed)
  `).normalize().serialize()).toBe('status:(missed, passed)');
  expect.soft(Query.parse(`
    s:failed s:flaked s:fire
  `).normalize().serialize()).toBe('status:failed status:fire status:flaked');
  expect.soft(Query.parse(`
    s:passed s:failed -s:failed
  `).normalize().serialize()).toBe('status:failed -status:failed status:passed');
  expect.soft(Query.parse(`
    -s:passed -s:failed
  `).normalize().serialize()).toBe('-status:failed -status:passed');
});

test('Query.toggleQuery', async () => {
  expect(Query.parse('').toggleQuery(Query.parse('status:bar')).serialize()).toBe('status:bar');
  expect(Query.parse('status:bar').toggleQuery(Query.parse('status:bar')).serialize()).toBe('');
  expect(Query.parse('status:(bar,baz)').toggleQuery(Query.parse('status:foo status:bar')).serialize()).toBe('status:baz status:foo');
  expect(Query.parse('status:(foo, bar, baz)').toggleQuery(Query.parse('status:bar')).serialize()).toBe('status:(foo, baz)');
  expect(Query.parse('$"undefined status"').toggleQuery(Query.parse('status:bar')).serialize()).toBe('$"undefined status" status:bar');
});

test('Query.acceptsTest', async () => {
  const test: WireTypes.Test = {
    filePath: 'tests/library/capabilities.spec.ts' as FlakinessReport.GitFilePath,
    testId: 'ff' as Stats.TestId,
    titles: [`should play video @smoke`],
  };
  expect.soft(Query.parse(``).acceptsTest(test)).toBe(true);
  expect.soft(Query.parse(`
    (page-click @smoke)
  `).acceptsTest(test)).toBe(false);
  expect.soft(Query.parse(`
    f:capabilities.spec.ts
  `).acceptsTest(test)).toBe(true);
  expect.soft(Query.parse(`
    f:should
  `).acceptsTest(test)).toBe(false);
  expect.soft(Query.parse(`
    f:library.spec.ts
  `).acceptsTest(test)).toBe(false);

  expect.soft(Query.parse(`
    should
  `).acceptsTest(test)).toBe(true);
  expect.soft(Query.parse(`
    play should
  `).acceptsTest(test)).toBe(true);
  expect.soft(Query.parse(`
    "play should"
  `).acceptsTest(test)).toBe(false);
  expect.soft(Query.parse(`
    "should play"
  `).acceptsTest(test)).toBe(true);
  expect.soft(Query.parse(`
    -"should play"
  `).acceptsTest(test)).toBe(false);
});

test('Query.acceptsError', async () => {
  const error: WireTypes.TestError = {
    errorId: 'fff' as Stats.ErrorId,
    message: 'undefined is not a function'
  };
  expect.soft(Query.parse(``).acceptsError(error)).toBe(true);
  expect.soft(Query.parse(`$jjj`).acceptsError(error)).toBe(false);
  expect.soft(Query.parse(`$undefined`).acceptsError(error)).toBe(true);
  expect.soft(Query.parse(`$undefined $foo`).acceptsError(error)).toBe(false);
  expect.soft(Query.parse(`$is $undefined`).acceptsError(error)).toBe(true);
  expect.soft(Query.parse(`$'undefined is not a function'`).acceptsError(error)).toBe(true);
  expect.soft(Query.parse(`$'is undefined a'`).acceptsError(error)).toBe(false);
  expect.soft(Query.parse(`$'undefined is not a'`).acceptsError(error)).toBe(true);
  expect.soft(Query.parse(`-$'undefined is not a'`).acceptsError(error)).toBe(false);
});

test('Query.acceptsOutcome', async () => {
  expect.soft(Query.parse(``).acceptsOutcome('expected')).toBe(true);
  expect.soft(Query.parse(``).acceptsOutcome('unexpected')).toBe(true);
  expect.soft(Query.parse(`s:failed`).acceptsOutcome('unexpected')).toBe(true);
  expect.soft(Query.parse(`s:fire`).acceptsOutcome('unexpected')).toBe(false);
  expect.soft(Query.parse(`s:fire`).acceptsOutcome('regressed')).toBe(true);
  expect.soft(Query.parse(`s:PASSED`).acceptsOutcome('unexpected')).toBe(false);
  expect.soft(Query.parse(`-s:PASSED`).acceptsOutcome('unexpected')).toBe(true);
  expect.soft(Query.parse(`-s:PASSED`).matchesOutcome('unexpected')).toBe(MatchResult.MATCH);
  expect.soft(Query.parse(`-s:PASSED`).matchesOutcome('expected')).toBe(MatchResult.EXCLUDE);
  expect.soft(Query.parse(`s ≠  PASSED`).acceptsOutcome('unexpected')).toBe(true);
});

test('Query.acceptsTag', async () => {
  expect.soft(Query.parse(`#smoke`).acceptsTag('smoke')).toBe(true);
  expect.soft(Query.parse(`#SmOkE`).acceptsTag('smoke')).toBe(true);
  expect.soft(Query.parse(`#smoke`).acceptsTag('SMOKE')).toBe(true);
  expect.soft(Query.parse(`#smo`).acceptsTag('smo')).toBe(true);
  expect.soft(Query.parse(`#smoke`).acceptsTag('smo')).toBe(false);
  expect.soft(Query.parse(`#'hello world'`).acceptsTag('hello world')).toBe(true);
});

test('Query.acceptsLine', async () => {
  expect.soft(Query.parse(`:1234`).acceptsLine(1234)).toBe(true);
  expect.soft(Query.parse(`:1234`).acceptsLine(123)).toBe(false);
  expect.soft(Query.parse(`-:1234`).acceptsLine(123)).toBe(true);
  expect.soft(Query.parse(`:1234`).acceptsLine(234)).toBe(false);
  expect.soft(Query.parse(`<100`).acceptsLine(10)).toBe(true);
  expect.soft(Query.parse(`<100`).acceptsLine(100)).toBe(false);
  expect.soft(Query.parse(`<=100`).acceptsLine(100)).toBe(true);
  expect.soft(Query.parse(`>=100`).acceptsLine(100)).toBe(true);
  expect.soft(Query.parse(`<100`).acceptsLine(1000)).toBe(false);
  expect.soft(Query.parse(`>100`).acceptsLine(10)).toBe(false);
  expect.soft(Query.parse(`>100`).acceptsLine(100)).toBe(false);
  expect.soft(Query.parse(`>100`).acceptsLine(1000)).toBe(true);
});

test('Query.acceptsDuration', async () => {
  expect.soft(Query.parse(`d>100`).acceptsDuration(ms('1 sec'))).toBe(true);
  expect.soft(Query.parse(`d>1sec`).acceptsDuration(ms('1 sec'))).toBe(false);
  expect.soft(Query.parse(`d>=1second`).acceptsDuration(ms('1 sec'))).toBe(true);
  expect.soft(Query.parse(`d<=1.2s`).acceptsDuration(ms('1.2 sec'))).toBe(true);
  expect.soft(Query.parse(`d<=1.2s`).acceptsDuration(ms('1.2 sec'))).toBe(true);
});
