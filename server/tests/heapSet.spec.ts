import { expect, test } from '@playwright/test';
import { HeapSet } from '../src/common/heapSet.js';

test('should initialize empty', () => {
  const heap = HeapSet.createMin<string>();
  expect(heap.peekEntry()).toBeUndefined();
});

test('should set and retrieve minimum value', { tag: '@smoke' }, () => {
  const heap = HeapSet.createMin<string>();
  heap.add('a', 10);
  heap.add('b', 5);
  heap.add('c', 15);
  
  expect(heap.peekEntry()).toEqual(['b', 5]);
});

test('should clear', { tag: '@smoke' }, () => {
  const heap = HeapSet.createMin<string>();
  heap.add('a', 10);
  heap.add('b', 5);
  heap.add('c', 15);
  
  expect(heap.peekEntry()).toEqual(['b', 5]);
  heap.clear();
  expect(heap.peekEntry()).toBeUndefined();
});

test('should update existing values', () => {
  const heap = HeapSet.createMin<string>();
  heap.add('a', 10);
  heap.add('b', 5);
  heap.add('c', 15);
  
  // Update 'a' to be the minimum
  heap.add('a', 1);
  expect(heap.peekEntry()).toEqual(['a', 1]);
  
  // Update 'b' to be higher
  heap.add('b', 20);
  heap.add('a', 20);
  expect(heap.peekEntry()).toEqual(['c', 15]);
});

test('should delete values correctly', () => {
  const heap = HeapSet.createMin<string>();
  heap.add('a', 10);
  heap.add('b', 5);
  heap.add('c', 15);
  
  expect(heap.peekEntry()).toEqual(['b', 5]);

  heap.delete('b');
  expect(heap.peekEntry()).toEqual(['a', 10]);
  
  heap.delete('a');
  expect(heap.peekEntry()).toEqual(['c', 15]);
  
  heap.delete('c');
  expect(heap.peekEntry()).toBeUndefined();
});

test('should handle deleting non-existent values', () => {
  const heap = HeapSet.createMin<string>();
  heap.add('a', 10);
  
  // Should not throw
  heap.delete('non-existent');
  expect(heap.peekEntry()).toEqual(['a', 10]);
});

test('should maintain heap property with many operations', () => {
  const heap = HeapSet.createMin<number>();
  
  // Add values in non-sorted order
  for (let i = 20; i >= 1; i--) {
    heap.add(i, i);
  }
  
  // Min should be 1
  expect(heap.peekEntry()).toEqual([1, 1]);
  
  // Delete minimum
  heap.delete(1);
  expect(heap.peekEntry()).toEqual([2, 2]);
  
  // Update a middle value to be the minimum
  heap.add(15, 0);
  expect(heap.peekEntry()).toEqual([15, 0]);
  
  // Update the minimum to be higher
  heap.add(15, 30);
  expect(heap.peekEntry()).toEqual([2, 2]);
});

test('should handle duplicate keys', () => {
  const heap = HeapSet.createMin<string>();
  heap.add('a', 10);
  heap.add('b', 10);
  heap.add('c', 10);
  
  // Any of these could be the minimum since they have the same key
  const min = heap.peekEntry()!;
  expect(['a', 'b', 'c']).toContain(min[0]);

  heap.delete(min[0]);
  const newMin = heap.peekEntry();
  expect(['a', 'b', 'c']).toContain(newMin![0]);
  expect(newMin).not.toBe(min);
});

test('should handle negative keys', () => {
  const heap = HeapSet.createMin<string>();
  heap.add('a', 5);
  heap.add('b', -10);
  heap.add('c', 0);
  
  expect(heap.peekEntry()).toEqual(['b', -10]);
  
  heap.delete('b');
  expect(heap.peekEntry()).toEqual(['c', 0]);
});

test('should handle edge case with one element', () => {
  const heap = HeapSet.createMin<string>();
  heap.add('a', 5);
  
  expect(heap.peekEntry()).toEqual(['a', 5]);
  
  heap.delete('a');
  expect(heap.peekEntry()).toBeUndefined();
  
  heap.add('a', 5);
  expect(heap.peekEntry()).toEqual(['a', 5]);
});

test('should initialize with pre-defined elements', () => {
  const heap = HeapSet.createMin<string>([['c', 15], ['a', 10], ['b', 5]]);
  expect(heap.peekEntry()).toEqual(['b', 5]);
  expect(heap.size).toBe(3);
  
  heap.delete('b');
  expect(heap.peekEntry()).toEqual(['a', 10]);
  
  heap.delete('a');
  expect(heap.peekEntry()).toEqual(['c', 15]);
});

test('should initialize with sorted array', () => {
  const heap = HeapSet.createMin<string>([['a', 1], ['b', 2], ['c', 3]]);
  expect(heap.has('a')).toBe(true);
});
