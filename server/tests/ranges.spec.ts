import { expect, test } from '@playwright/test';
import { Ranges } from '../src/common/ranges.js';

test('Ranges.intersect', { tag: ['@smoke'], }, async () => {
  expect.soft(Ranges.toString(Ranges.intersect(
    Ranges.fromList([1,2,3, 5,6,7,8,9, 12]), 
    Ranges.fromList([  2,3,     7        ])
  ))).toBe(`[ 2-3, 7 ]`);
  expect.soft(Ranges.toString(Ranges.intersect(
    Ranges.fromList([1,  5, 12]), 
    Ranges.fromList([  2      ])
  ))).toBe(`[]`);
  expect.soft(Ranges.toString(Ranges.intersect(
    Ranges.fromList([1,2,3,4      ]), 
    Ranges.fromList([      4,5,6,7])
  ))).toBe(`[ 4 ]`);
  expect.soft(Ranges.toString(Ranges.intersect(
    Ranges.fromList([1,2,3,4,    7,8,9,10    ]), 
    Ranges.fromList([    3,4,5,6,7,8,9,   11 ])
  ))).toBe(`[ 3-4, 7-9 ]`);

  expect.soft(Ranges.toString(Ranges.intersect(
    Ranges.fromList([1,2,3,4,5,6,7,8,9,10,11,12,]), 
    Ranges.fromList([  2,  4,5,6,  8,  10,   12,])
  ))).toBe(`[ 2, 4-6, 8, 10, 12 ]`);

  expect.soft(Ranges.toString(Ranges.intersect(
    Ranges.fromList([1,2,3,4,5,6,7,8,9,10,11,12,]), 
    Ranges.EMPTY,
  ))).toBe(`[]`);

  expect.soft(Ranges.toString(Ranges.intersect(
    Ranges.fromList([1,2,  5,  8]),
    Ranges.complement(Ranges.EMPTY),
  ))).toBe(`[ 1-2, 5, 8 ]`);

  // Both empty
  expect.soft(Ranges.toString(Ranges.intersect(
    Ranges.EMPTY,
    Ranges.EMPTY
  ))).toBe(`[]`);

  // One empty, one full
  expect.soft(Ranges.toString(Ranges.intersect(
    Ranges.EMPTY,
    Ranges.FULL
  ))).toBe(`[]`);

  // Both full
  expect.soft(Ranges.toString(Ranges.intersect(
    Ranges.FULL,
    Ranges.FULL
  ))).toBe(`[ -Infinity-Infinity ]`);

  // Single point intersections
  expect.soft(Ranges.toString(Ranges.intersect(
    Ranges.fromList([5]),
    Ranges.fromList([5])
  ))).toBe(`[ 5 ]`);

  // Adjacent ranges (no intersection)
  expect.soft(Ranges.toString(Ranges.intersect(
    [1,2] as Ranges.Ranges<number>,
    [3,4] as Ranges.Ranges<number>
  ))).toBe(`[]`);

  // Touching ranges (intersection at boundary)
  expect.soft(Ranges.toString(Ranges.intersect(
    [1,3] as Ranges.Ranges<number>,
    [3,5] as Ranges.Ranges<number>
  ))).toBe(`[ 3 ]`);

  // Multiple disjoint intersections
  expect.soft(Ranges.toString(Ranges.intersect(
    [1,5, 10,15, 20,25] as Ranges.Ranges<number>,
    [3,7, 12,17, 22,27]  as Ranges.Ranges<number>
  ))).toBe(`[ 3-5, 12-15, 22-25 ]`);

  // Partial overlaps with gaps
  expect.soft(Ranges.toString(Ranges.intersect(
    [1,10, 20,30] as Ranges.Ranges<number>,
    [5,15, 25,35] as Ranges.Ranges<number>
  ))).toBe(`[ 5-10, 25-30 ]`);

  // One range completely contains multiple others
  expect.soft(Ranges.toString(Ranges.intersect(
    [1,100] as Ranges.Ranges<number>,
    [10,20, 30,40, 50,60] as Ranges.Ranges<number>,
  ))).toBe(`[ 10-20, 30-40, 50-60 ]`);

  // Multiple ranges contain one
  expect.soft(Ranges.toString(Ranges.intersect(
    [10,20, 30,40, 50,60] as Ranges.Ranges<number>,
    [1,100] as Ranges.Ranges<number>,
  ))).toBe(`[ 10-20, 30-40, 50-60 ]`);
});

test('Ranges.isIntersecting', { tag: ['@smoke'], }, async () => {
  expect.soft(Ranges.isIntersecting(
    Ranges.fromList([1,2,3, 5,6,7,8,9, 12]), 
    Ranges.fromList([  2,3,     7        ])
  )).toBe(true);
  expect.soft(Ranges.isIntersecting(
    Ranges.fromList([1,  5, 12]), 
    Ranges.fromList([  2      ])
  )).toBe(false);
  expect.soft(Ranges.isIntersecting(
    Ranges.fromList([1,2,3,4      ]), 
    Ranges.fromList([      4,5,6,7])
  )).toBe(true);
  expect.soft(Ranges.isIntersecting(
    Ranges.fromList([1,2,3,4,    7,8,9,10    ]), 
    Ranges.fromList([    3,4,5,6,7,8,9,   11 ])
  )).toBe(true);

  expect.soft(Ranges.isIntersecting(
    Ranges.fromList([1,2,3,4,5,6,7,8,9,10,11,12,]), 
    Ranges.fromList([  2,  4,5,6,  8,  10,   12,])
  )).toBe(true);

  expect.soft(Ranges.isIntersecting(
    Ranges.fromList([1,2,3,4,5,6,7,8,9,10,11,12,]), 
    Ranges.EMPTY,
  )).toBe(false);

  expect.soft(Ranges.isIntersecting(
    Ranges.fromList([1,2,  5,  8]),
    Ranges.complement(Ranges.EMPTY),
  )).toBe(true);

  // Both empty
  expect.soft(Ranges.isIntersecting(
    Ranges.EMPTY,
    Ranges.EMPTY
  )).toBe(false);

  // One empty, one full
  expect.soft(Ranges.isIntersecting(
    Ranges.EMPTY,
    Ranges.FULL
  )).toBe(false);

  // Both full
  expect.soft(Ranges.isIntersecting(
    Ranges.FULL,
    Ranges.FULL
  )).toBe(true);

  // Single point intersections
  expect.soft(Ranges.isIntersecting(
    Ranges.fromList([5]),
    Ranges.fromList([5])
  )).toBe(true);

  // Adjacent ranges (no intersection)
  expect.soft(Ranges.isIntersecting(
    [1,2] as Ranges.Ranges<number>,
    [3,4] as Ranges.Ranges<number>
  )).toBe(false);

  // Touching ranges (intersection at boundary)
  expect.soft(Ranges.isIntersecting(
    [1,3] as Ranges.Ranges<number>,
    [3,5] as Ranges.Ranges<number>
  )).toBe(true);

  // Multiple disjoint intersections
  expect.soft(Ranges.isIntersecting(
    [1,5, 10,15, 20,25] as Ranges.Ranges<number>,
    [3,7, 12,17, 22,27]  as Ranges.Ranges<number>
  )).toBe(true);

  // Partial overlaps with gaps
  expect.soft(Ranges.isIntersecting(
    [1,10, 20,30] as Ranges.Ranges<number>,
    [5,15, 25,35] as Ranges.Ranges<number>
  )).toBe(true);

  // One range completely contains multiple others
  expect.soft(Ranges.isIntersecting(
    [1,100] as Ranges.Ranges<number>,
    [10,20, 30,40, 50,60] as Ranges.Ranges<number>,
  )).toBe(true);

  // Multiple ranges contain one
  expect.soft(Ranges.isIntersecting(
    [10,20, 30,40, 50,60] as Ranges.Ranges<number>,
    [1,100] as Ranges.Ranges<number>,
  )).toBe(true);
});

test('Ranges.intersect - negative numbers', async () => {
  expect.soft(Ranges.toString(Ranges.intersect(
    [-10,-5, 0,5] as Ranges.Ranges<number>,
    [-7,-2, 2,7] as Ranges.Ranges<number>,
  ))).toBe(`[ -7--5, 2-5 ]`);

  expect.soft(Ranges.toString(Ranges.intersect(
    [-100,-50] as Ranges.Ranges<number>,
    [-75,-25] as Ranges.Ranges<number>
  ))).toBe(`[ -75--50 ]`);

  // Mixed positive and negative
  expect.soft(Ranges.toString(Ranges.intersect(
    [-20,20] as Ranges.Ranges<number>,
    [-10,10] as Ranges.Ranges<number>
  ))).toBe(`[ -10-10 ]`);

  // Intersection with half-infinite ranges
  expect.soft(Ranges.toString(Ranges.intersect(
    [-Infinity, 10] as Ranges.Ranges<number>,
    [5, Infinity] as Ranges.Ranges<number>
  ))).toBe(`[ 5-10 ]`);

  expect.soft(Ranges.toString(Ranges.intersect(
    [-Infinity, 0] as Ranges.Ranges<number>,
    [-5,5] as Ranges.Ranges<number>
  ))).toBe(`[ -5-0 ]`);

  expect.soft(Ranges.toString(Ranges.intersect(
    [10, Infinity] as Ranges.Ranges<number>,
    [5,15] as Ranges.Ranges<number>,
  ))).toBe(`[ 10-15 ]`);

  // Multiple single elements
  expect.soft(Ranges.toString(Ranges.intersect(
    Ranges.fromList([1, 3, 5, 7, 9]),
    Ranges.fromList([2, 3, 6, 7, 10])
  ))).toBe(`[ 3, 7 ]`);

  // Single elements vs ranges
  expect.soft(Ranges.toString(Ranges.intersect(
    Ranges.fromList([1, 5, 10]),
    [1,3, 8,12] as Ranges.Ranges<number>
  ))).toBe(`[ 1, 10 ]`);

  // No single element intersections
  expect.soft(Ranges.toString(Ranges.intersect(
    Ranges.fromList([1, 3, 5]),
    Ranges.fromList([2, 4, 6])
  ))).toBe(`[]`);

  // Large ranges with many intersections
  const range1 = [];
  const range2 = [];
  for (let i = 0; i < 100; i += 10) {
    range1.push(i, i + 5);
    range2.push(i + 2, i + 7);
  }
  
  const result = Ranges.intersect(
    range1 as Ranges.Ranges<number>,
    range2 as Ranges.Ranges<number>
  );
  
  // Should have intersections at [2-5, 12-15, 22-25, ...]
  expect(Ranges.cardinality(result)).toBe(40); // 10 intersections of 4 elements each
});

test('Ranges.intersectAll', async () => {
  expect.soft(Ranges.toString(Ranges.intersectAll([
    Ranges.fromList([1,2,3,     ]), 
    Ranges.fromList([1,  3,4,5,6]),
    Ranges.fromList([1,2,    5,6]),
  ]))).toBe(`[ 1 ]`);
  expect.soft(Ranges.toString(Ranges.intersectAll([
    Ranges.fromList([1,2,3,     ]), 
  ]))).toBe(`[ 1-3 ]`);
  expect.soft(Ranges.toString(Ranges.intersectAll([
  ]))).toBe(`[]`);
});

test('Ranges.capAt', async () => {
  expect.soft(Ranges.toString(
    Ranges.capAt(Ranges.fromList([1,2,3,4,  6,7]), 0) 
  )).toBe(`[]`);
  expect.soft(Ranges.toString(
    Ranges.capAt(Ranges.fromList([1,2,3,4,  6,7]), -Infinity) 
  )).toBe(`[]`);
  expect.soft(Ranges.toString(
    Ranges.capAt(Ranges.fromList([1,2,3,4,  6,7]), 1) 
  )).toBe(`[ 1 ]`);
  expect.soft(Ranges.toString(
    Ranges.capAt(Ranges.fromList([1,2,3,4,  6,7]), 4) 
  )).toBe(`[ 1-4 ]`);
  expect.soft(Ranges.toString(
    Ranges.capAt(Ranges.fromList([1,2,3,4,  6,7]), 5) 
  )).toBe(`[ 1-4 ]`);
  expect.soft(Ranges.toString(
    Ranges.capAt(Ranges.fromList([1,2,3,4,  6,7]), 6) 
  )).toBe(`[ 1-4, 6 ]`);
  expect.soft(Ranges.toString(
    Ranges.capAt(Ranges.fromList([1,2,3,4,  6,7]), 7) 
  )).toBe(`[ 1-4, 6-7 ]`);
  expect.soft(Ranges.toString(
    Ranges.capAt(Ranges.fromList([1,2,3,4,  6,7]), 10) 
  )).toBe(`[ 1-4, 6-7 ]`);
});

test('Ranges.unionAll', async () => {
  expect.soft(Ranges.toString(Ranges.unionAll([
    Ranges.fromList([1,2,  4,   ]), 
    Ranges.fromList([1,  3,  5, ]),
    Ranges.fromList([1,2,      6]),
  ]))).toBe(`[ 1-6 ]`);
  expect.soft(Ranges.toString(Ranges.unionAll([
    Ranges.fromList([1,2,3,     ]), 
  ]))).toBe(`[ 1-3 ]`);
  expect.soft(Ranges.toString(Ranges.unionAll([
  ]))).toBe(`[]`);
});

test('Ranges.union', async () => {
  expect.soft(Ranges.toString(Ranges.union(
    Ranges.fromList([1,2,3,     ]), 
    Ranges.fromList([      4,5,6])
  ))).toBe(`[ 1-6 ]`);

  expect.soft(Ranges.toString(Ranges.union(
    Ranges.fromList([1,  3,  5, ]), 
    Ranges.fromList([  2,  4,  6]), 
  ))).toBe(`[ 1-6 ]`);

  expect.soft(Ranges.toString(Ranges.union(
    Ranges.fromList([1,2,     7,8]), 
    Ranges.fromList([      5     ]), 
  ))).toBe(`[ 1-2, 5, 7-8 ]`);

  expect.soft(Ranges.toString(Ranges.union(
    Ranges.fromList([1,2,       7,8]), 
    Ranges.fromList([    3,4,5,]), 
  ))).toBe(`[ 1-5, 7-8 ]`);

  expect.soft(Ranges.toString(Ranges.union(
    Ranges.fromList([1,2,       7,8]), 
    Ranges.fromList([    ]), 
  ))).toBe(`[ 1-2, 7-8 ]`);
});

test('Ranges.iterate', async () => {
  expect.soft([...Ranges.iterate(Ranges.fromList([-1,2,3]))]).toEqual([-1,2,3]);
  expect.soft([...Ranges.iterate(Ranges.fromList([]))]).toEqual([]);
  expect.soft([...Ranges.iterate(Ranges.fromList([0]))]).toEqual([0]);
});

test('Ranges.pop', async () => {
  {
    const r = Ranges.fromList([-1,-2,-3,3,4,5]);
    expect(Ranges.popInplace(r)).toBe(5);
    expect(Ranges.popInplace(r)).toBe(4);
    expect(Ranges.popInplace(r)).toBe(3);
    expect(Ranges.popInplace(r)).toBe(-1);
    expect(Ranges.popInplace(r)).toBe(-2);
    expect(Ranges.popInplace(r)).toBe(-3);
    expect(Ranges.popInplace(r)).toBe(undefined);
    expect(Ranges.popInplace(r)).toBe(undefined);
  }
  {
    const r = Ranges.fromList([1]);
    expect(Ranges.popInplace(r)).toBe(1);
    expect(Ranges.popInplace(r)).toBe(undefined);
    expect(Ranges.popInplace(r)).toBe(undefined);
  }
  {
    const r = Ranges.fromList([]);
    expect(Ranges.popInplace(r)).toBe(undefined);
    expect(Ranges.popInplace(r)).toBe(undefined);
  }
});

test('Ranges.subtract', async () => {
  expect.soft(Ranges.toString(Ranges.subtract(
    Ranges.fromList([1,2,3,4,5,6]), 
    Ranges.fromList([  2,  4,5, ])
  ))).toBe(`[ 1, 3, 6 ]`);
  expect.soft(Ranges.toString(Ranges.subtract(
    Ranges.fromList([1          ]), 
    Ranges.fromList([  2,  4,5, ])
  ))).toBe(`[ 1 ]`);
  expect.soft(Ranges.toString(Ranges.subtract(
    Ranges.fromList([1]), 
    Ranges.fromList([1, 2,  4,5, ])
  ))).toBe(`[]`);
  expect.soft(Ranges.toString(Ranges.subtract(
    Ranges.fromList([1,2,3,4,5  ]), 
    Ranges.fromList([1,2,  4,5, ])
  ))).toBe(`[ 3 ]`);
  expect.soft(Ranges.toString(Ranges.subtract(
    Ranges.fromList([1,2,3,4,5      ]), 
    Ranges.fromList([      4,5,6,7,8])
  ))).toBe(`[ 1-3 ]`);
  expect.soft(Ranges.toString(Ranges.subtract(
    Ranges.fromList([ ]), 
    Ranges.fromList([      4])
  ))).toBe(`[]`);
  expect.soft(Ranges.toString(Ranges.subtract(
    Ranges.fromList([3]), 
    Ranges.fromList([])
  ))).toBe(`[ 3 ]`);
});

test('Ranges.cardinality', async () => {
  expect.soft(Ranges.cardinality(
    Ranges.fromList([1,2,3,4,5,6]), 
  )).toBe(6);
  expect.soft(Ranges.cardinality(
    Ranges.fromList([-1,0,1,2,3,   5,6, 100]), 
  )).toBe(8);
});

test('Ranges.toSortedList', async () => {
  expect.soft(Ranges.toSortedList(
    [1,1,5,8] as Ranges.Ranges<number>
  )).toEqual([1,5,6,7,8]);

  expect.soft(() => Ranges.toSortedList(Ranges.FULL)).toThrowError('cannot convert infinite ranges!');
});

test('Ranges.fromSortedList', async () => {
  expect.soft(Ranges.fromSortedList(
    [1,1,1,1,1,1]
  )).toEqual([1,1]);
  expect.soft(Ranges.fromSortedList(
    [1,1,5,6,7,8]
  )).toEqual([1,1,5,8]);
});

test('Ranges.fromList', async () => {
  expect.soft(Ranges.fromList(
    [1,1,1,1,1,1]
  )).toEqual([1,1]);
  expect.soft(Ranges.fromList(
    [8,1,5,6,1,7]
  )).toEqual([1,1,5,8]);
});

test('Ranges.isInfinite', async () => {
  expect.soft(Ranges.isInfinite(Ranges.FULL)).toBe(true);
  expect.soft(Ranges.isInfinite(Ranges.EMPTY)).toBe(false);
  expect.soft(Ranges.isInfinite(Ranges.fromList([1,2,3]))).toBe(false);
  expect.soft(Ranges.isInfinite(Ranges.complement(Ranges.fromList([1,2,3])))).toBe(true);
});

test('Ranges.complement', async () => {
  expect.soft(Ranges.complement(Ranges.FULL)).toEqual(Ranges.EMPTY);
  expect.soft(Ranges.complement(Ranges.EMPTY)).toEqual(Ranges.FULL);

  expect.soft(Ranges.complement([-Infinity, 0] as Ranges.Ranges<number>)).toEqual([1, Infinity]);
  expect.soft(Ranges.complement([-100, 100] as Ranges.Ranges<number>)).toEqual([-Infinity, -101, 101, Infinity]);
  expect.soft(Ranges.complement([0, 0, 10, 20] as Ranges.Ranges<number>)).toEqual([-Infinity, -1, 1, 9, 21, Infinity]);
});

test('Ranges.compress and Ranges.decompress', async () => {
  expect.soft(Ranges.decompress(Ranges.compress(Ranges.fromList([1])))).toEqual([1, 1]);
  expect.soft(Ranges.decompress(Ranges.compress(Ranges.fromList([-2, -1])))).toEqual([-2, -1]);
  expect.soft(Ranges.decompress(Ranges.compress(Ranges.fromList([1, 2, 3, 5, 7,8])))).toEqual([1, 3, 5, 5, 7, 8]);
});

test('Ranges.domain', async () => {
  test.slow(true, 'This test is slow to test annotation rendering; it also always fails.');
  expect.soft(Ranges.domain(Ranges.fromList([-1, 200, 3, 1]))).toEqual({ min: -1, max: 200 });
});

test('Ranges.includes', async () => {
  // Empty
  expect.soft(Ranges.includes(Ranges.EMPTY, -Infinity)).toBe(false);
  expect.soft(Ranges.includes(Ranges.EMPTY, 0)).toBe(false);

  // Full
  expect.soft(Ranges.includes(Ranges.FULL, 0)).toBe(true);
  expect.soft(Ranges.includes(Ranges.FULL, Infinity)).toBe(true);
  expect.soft(Ranges.includes(Ranges.FULL, -0)).toBe(true);

  // Half interval
  expect.soft(Ranges.includes([-Infinity, 0] as Ranges.Ranges<number>, 1)).toBe(false);
  expect.soft(Ranges.includes([-Infinity, 0] as Ranges.Ranges<number>, -1)).toBe(true);
  expect.soft(Ranges.includes([-Infinity, 0] as Ranges.Ranges<number>, Infinity)).toBe(false);
  expect.soft(Ranges.includes([-Infinity, 0] as Ranges.Ranges<number>, -Infinity)).toBe(true);

  // Custom ranges
  expect.soft(Ranges.includes([-100, 100, 200, 200] as Ranges.Ranges<number>, 0)).toBe(true);
  expect.soft(Ranges.includes([-100, 100, 200, 200] as Ranges.Ranges<number>, 200)).toBe(true);
  expect.soft(Ranges.includes([-100, 100, 200, 200] as Ranges.Ranges<number>, 201)).toBe(false);
  expect.soft(Ranges.includes([-100, 100, 200, 200] as Ranges.Ranges<number>, -100)).toBe(true);
  expect.soft(Ranges.includes([-100, 100, 200, 200] as Ranges.Ranges<number>, -101)).toBe(false);
  expect.soft(Ranges.includes([-100, 100, 200, 200] as Ranges.Ranges<number>, 101)).toBe(false);
  expect.soft(Ranges.includes([-100, 100, 200, 200] as Ranges.Ranges<number>, 100)).toBe(true);

  const list = [-20,   -11,-10,-9    -1,0,1,2,   5,    9,10,    15,    20,21];
  const r = Ranges.fromList(list);

  expect(Ranges.includes(r, 1)).toBe(true);
  for (let x = -100; x <= 100; ++x)
    expect.soft(Ranges.includes(r, x), `check ranges ${list.includes(x) ? 'include' : 'does not include'} ${x}`).toBe(list.includes(x));
});

test('Ranges.sequence', async () => {
  const list = [1,2,  5,6,7,   10,11];
  const rs = Ranges.sequence(Ranges.fromList(list));

  const slice = (from: number, to: number) => Iterator.from(rs.seek(from)).take(Math.max(0, to - from)).toArray();
  expect(slice(1, 3)).toEqual([2, 5]);
  expect(slice(0, 1)).toEqual([1]);
  expect(slice(0, Infinity)).toEqual(list);
  expect(slice(Infinity, 1)).toEqual([]);
  expect(slice(5, 10)).toEqual([10, 11]);
  for(let i = 0; i < list.length + 10; ++i) {
    for (let j = 0; j < list.length + 10; ++j)
      expect(slice(i, j), `slice[${i}, ${j}]`).toEqual(list.slice(i, j));
  }
});

test('Ranges.intervalSequence', async () => {
  const list = [1,2,  5,6,7,   10,11];
  const ranges = Ranges.fromList(list);
  const intervals = Ranges.intervalSequence(ranges);

  expect(intervals.length).toBe(3);
  expect([...intervals.seek(0)]).toEqual([[1, 2], [5, 7], [10, 11]]);
  expect([...intervals.seek(1)]).toEqual([[5, 7], [10, 11]]);
  expect([...intervals.seek(2)]).toEqual([[10, 11]]);
  expect([...intervals.seek(3)]).toEqual([]);

  // Test with single element ranges
  const singleRanges = Ranges.fromList([1, 3, 5]);
  const singleIntervals = Ranges.intervalSequence(singleRanges);
  expect(singleIntervals.length).toBe(3);
  expect([...singleIntervals.seek(0)]).toEqual([[1, 1], [3, 3], [5, 5]]);

  // Test with empty ranges
  const emptyIntervals = Ranges.intervalSequence(Ranges.EMPTY);
  expect(emptyIntervals.length).toBe(0);
  expect([...emptyIntervals.seek(0)]).toEqual([]);

  // Test with negative numbers
  const negativeRanges = Ranges.fromList([-5, -4, -3, -1, 0, 2]);
  const negativeIntervals = Ranges.intervalSequence(negativeRanges);
  expect(negativeIntervals.length).toBe(3);
  expect([...negativeIntervals.seek(0)]).toEqual([[-5, -3], [-1, 0], [2, 2]]);
});

test('Ranges.toInt32Array', async () => {
  expect.soft(Ranges.toInt32Array(
    Ranges.fromList([1,2,3,4,5,6])
  )).toEqual(new Int32Array([1,2,3,4,5,6]));
  
  expect.soft(Ranges.toInt32Array(
    Ranges.fromList([-1,0,1,2,3, 5,6, 100])
  )).toEqual(new Int32Array([-1,0,1,2,3,5,6,100]));
  
  expect.soft(Ranges.toInt32Array(
    Ranges.fromList([])
  )).toEqual(new Int32Array([]));
  
  expect.soft(Ranges.toInt32Array(
    Ranges.fromList([42])
  )).toEqual(new Int32Array([42]));
  
  expect.soft(() => Ranges.toInt32Array(Ranges.FULL)).toThrowError('cannot convert infinite ranges!');
});
