
import { expect, test } from '@playwright/test';
import { SegmentedLRUPolicy } from '../src/common/caches/slruPolicy.js';

test.describe('SegmentedLRUPolicy', () => {
  test('should add new items to probation segment', () => {
    const policy = new SegmentedLRUPolicy<string>({ probation: 2, protected: 8 });

    policy.hit('a');
    policy.hit('b');
    expect(policy.size).toBe(2);
    expect(policy.has('a')).toBe(true);
    expect(policy.has('b')).toBe(true);
  });

  test('should promote items from probation to protected on second hit', () => {
    const policy = new SegmentedLRUPolicy<string>({ probation: 1, protected: 5 });
    
    // Add to probation
    policy.hit('a');
    // Hit 'a' again - should move to protected
    policy.hit('a');

    // Hit 'b' - should be in probation.
    policy.hit('b');

    expect(policy.has('a')).toBe(true);
    expect(policy.has('b')).toBe(true);
  });

  test('should handle capacity limits in probation segment', () => {
    const policy = new SegmentedLRUPolicy<string>({ probation: 1, protected: 4 });
    
    // Fill probation (capacity 1)
    policy.hit('a');

    // Add another item - should evict 'a'
    const evicted = policy.hit('b');
    expect(evicted).toBe('a');
    expect(policy.has('a')).toBe(false);
    expect(policy.has('b')).toBe(true);
  });

  test('should demote items from protected to probation when protected is full', () => {
    const policy = new SegmentedLRUPolicy<string>({ probation: 1, protected: 4 });

    for (const key of ['a', 'b', 'c', 'd']) {
      // Fill probation and promote to protected
      policy.hit(key);
      policy.hit(key);
    }

    // Protected is now full, add one more
    policy.hit('e');
    policy.hit('e');

    expect(policy.has('e')).toBe(true); // In protected
    expect(policy.has('a')).toBe(true); // Demoted to probation
    const victim = policy.hit('f');
    expect(victim).toBe('a');
  });

  test('should handle hits on protected segment items', () => {
    const policy = new SegmentedLRUPolicy<string>({ probation: 1, protected: 4 });

    policy.hit('a');
    policy.hit('a');
    // Hit again - should stay in protected
    policy.hit('a');
    expect(policy.has('a')).toBe(true);
  });

  test('should delete items from both segments', () => {
    const policy = new SegmentedLRUPolicy<string>({ probation: 1, protected: 4 });
    
    // Add to protected
    policy.hit('a');
    policy.hit('a');
    
    // Add to probation
    policy.hit('b');
    
    expect(policy.size).toBe(2);
    
    // Delete from probation
    policy.delete('a');
    expect(policy.has('a')).toBe(false);
    expect(policy.has('b')).toBe(true);
    expect(policy.size).toBe(1);
    
    // Delete from protected
    policy.delete('b');
    expect(policy.has('b')).toBe(false);
    expect(policy.size).toBe(0);
  });

  test('should return correct victim from probation first', () => {
    const policy = new SegmentedLRUPolicy<string>({ probation: 2, protected: 4 });

    policy.hit('a');
    policy.hit('a'); // Move to protected
    policy.hit('b');

    // Should return victim from probation first
    expect(policy.victim()).toBe('b');
  });

  test('should handle victim with newKey parameter', () => {
    const policy = new SegmentedLRUPolicy<string>({ probation: 1, protected: 2 }); // probation=1, protected=2

    policy.hit('a');
    policy.hit('a'); // Move to protected
    policy.hit('b');

    // Adding existing key shouldn't require eviction
    expect(policy.victim('a')).toBeUndefined();
    expect(policy.victim('b')).toBeUndefined();

    // Adding new key when probation is full should return victim
    expect(policy.victim('c')).toBe('b');
  });

  test('should handle zero capacity', () => {
    const policy = new SegmentedLRUPolicy<string>({ protected: 0, probation: 0 });
    
    const evicted = policy.hit('a');
    expect(evicted).toBe('a');
    expect(policy.size).toBe(0);
    expect(policy.has('a')).toBe(false);
  });

  test('should clear both segments', () => {
    const policy = new SegmentedLRUPolicy<string>({ protected: 4, probation: 4});
    
    policy.hit('a');
    policy.hit('b');
    policy.hit('b'); // Move to protected
    
    expect(policy.size).toBe(2);

    policy.clear();
    expect(policy.size).toBe(0);
    expect(policy.has('a')).toBe(false);
    expect(policy.has('b')).toBe(false);
  });
});
