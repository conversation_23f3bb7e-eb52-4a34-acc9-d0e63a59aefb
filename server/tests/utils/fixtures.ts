import { test as baseTest } from '@playwright/test';
import crypto from 'crypto';
import { Minio } from 'podkeeper';
import { S3Configuration } from '../../src/node/s3.js';

export function guid() {
    // Generate 16 random bytes
    const randomBytes = crypto.randomBytes(16);

    // Modify bits as per UUID v4 spec
    randomBytes[6] = (randomBytes[6] & 0x0F) | 0x40; // Set version to 4 (random)
    randomBytes[8] = (randomBytes[8] & 0x3F) | 0x80; // Set variant to 2 (IETF)

    // Convert bytes to hex string
    const parts = [
        randomBytes.subarray(0, 4).toString('hex'),
        randomBytes.subarray(4, 6).toString('hex'),
        randomBytes.subarray(6, 8).toString('hex'),
        randomBytes.subarray(8, 10).toString('hex'),
        randomBytes.subarray(10, 16).toString('hex'),
    ];

    return parts.join('-');
}

export const s3test = baseTest.extend<{randomBucketName: string }, { s3config: S3Configuration }>({
  randomBucketName: ({}, use) => {
    use('flakiness-s3-test-' + guid());
  },

  s3config: [async ({ }, use, workerInfo) => {
    const minio = await Minio.start();
    await use({
      endpoint: minio.apiEndpoint(),
      accessKeyId: minio.accessKeyId(),
      secretAccessKey: minio.secretAccessKey(),
      region: 'wnam',
    });
    await minio.stop();
  }, { scope: 'worker' }],
});

