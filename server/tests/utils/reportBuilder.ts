import { FlakinessReport } from "@flakiness/report";
import { ReportUtils } from "@flakiness/report";
import { Multimap } from "@flakiness/shared/common/multimap.js";
import { xxHashObject } from "@flakiness/shared/common/utils.js";
import assert from "assert";

export type TestsConfig = Record<string, RunConfig[]>

export type RunConfig = {
  commitId: FlakinessReport.CommitId,
  env: FlakinessReport.Environment,
  timeout?: FlakinessReport.DurationMS;
  expectedStatus?: FlakinessReport.TestStatus,
  annotations?: FlakinessReport.Annotation[],
  attempts: RunAttemptConfig[],
};

export type RunAttemptConfig = {
  duration?: FlakinessReport.DurationMS,
  status?: FlakinessReport.TestStatus,
  error?: string,
  errors?: string[],
  workerIndex?: number,
}

export class ReportBuilder {
  private _report: FlakinessReport.Report;
  private _testRuns = new Multimap<string, FlakinessReport.RunAttempt>();

  static createReports(suites: FlakinessReport.Suite[], testConfig: TestsConfig): FlakinessReport.Report[] {
    const reportBuilders = new Map<string, ReportBuilder>();
    // We have report per operating system.
    for (const [testName, values] of Object.entries(testConfig)) {
      for (const runConfig of values) {
        const key = xxHashObject({ commitId: runConfig.commitId, env: runConfig.env });
        let builder = reportBuilders.get(key);
        if (!builder) {
          builder = new ReportBuilder(runConfig.commitId, suites);
          reportBuilders.set(key, builder);
        }
        builder.addTestRun(testName, runConfig, runConfig.attempts);
      }
    }
    return [...reportBuilders.values()].map(builder => builder.report());
  }

  static createSuites(suitesConfig: Record<string, string[]>): FlakinessReport.Suite[] {
    const suites: FlakinessReport.Suite[] = [];
    for (const [fileName, testNames] of Object.entries(suitesConfig)) {
      const tests: FlakinessReport.Test[] = testNames.map((testName, index) => {
        const tokens = testName.split(' ');
        const tags: string[] = tokens.filter(token => token.startsWith('#')).map(token => token.substring(1));
        const title = tokens.filter(token => !token.startsWith('#')).join(' ');
        return {
          location: createLocation(fileName, index + 1),
          title: title,
          attempts: [],
          tags: tags,
        };
      });
      const suite: FlakinessReport.Suite = {
        type: 'file',
        location: createLocation(fileName),
        title: fileName,
        tests: [],
        suites: [],
      };
      suite.tests = tests;
      suites.push(suite);
    }
    return suites;
  }

  constructor(commitId: FlakinessReport.CommitId, suites: FlakinessReport.Suite[]) {
    this._report = {
      category: 'testreport',
      commitId: commitId,
      environments: [],
      unattributedErrors: [],
      suites,
      startTimestamp: Date.now() as FlakinessReport.UnixTimestampMS,
      duration: 0  as FlakinessReport.DurationMS,
    };
  }

  addTestRun(testName: string, runConfig: RunConfig, attemptConfigs: RunAttemptConfig[]) {
    assert(attemptConfigs.length > 0);
    const envIdx = this._report.environments.push(runConfig.env) - 1;
    const runTimeout = runConfig.timeout ?? 30000 as FlakinessReport.DurationMS;
    let expectedStatus = runConfig.expectedStatus ?? 'passed';
    for (const attemptConfig of attemptConfigs) {
      this._testRuns.set(testName, {
        duration: attemptConfig.status === 'timedOut' ? runTimeout : (attemptConfig.duration ?? 1000 as FlakinessReport.DurationMS),
        errors: allErrors(attemptConfig).map(error => ({ message: error })),
        startTimestamp: Date.now() as FlakinessReport.UnixTimestampMS,
        status: attemptConfig.error && !attemptConfig.status ? 'failed' : (attemptConfig.status ?? 'passed'),
        parallelIndex: attemptConfig.workerIndex ?? 0,
        annotations: runConfig.annotations ?? [],
        environmentIdx: envIdx,
        expectedStatus,
        timeout: runTimeout,
      });
    }
    return this;
  }

  report() {
    const dupe = structuredClone(this._report);
    ReportUtils.visitTests(dupe, test => {
      test.attempts = this._testRuns.getAll(test.title);
    });
    const result = ReportUtils.dedupeSuitesTestsEnvironments(dupe);
    return result;
  }
}

function createLocation(file: string, line?: number, column?: number): FlakinessReport.Location {
  return {
    file: file as FlakinessReport.GitFilePath,
    line: (line ?? 1) as FlakinessReport.Number1Based,
    column: (column ?? 1) as FlakinessReport.Number1Based,
  };
}

function allErrors(attempt: RunAttemptConfig) {
  const errors: string[] = [];
  if (attempt.error)
    errors.push(attempt.error);
  if (attempt.errors)
    errors.push(...attempt.errors);
  return errors;
}
