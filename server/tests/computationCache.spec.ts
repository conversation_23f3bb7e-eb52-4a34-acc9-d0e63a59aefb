
import { ManualPromise } from '@flakiness/shared/common/manualPromise.js';
import { expect, test } from '@playwright/test';
import { SharedCacheStore } from '../src/common/caches/cache.js';
import { LRUPolicy } from '../src/common/caches/lruPolicy.js';
import { ComputationCache, SyncComputationCache } from '../src/common/computationCache.js';

test.describe('ComputationCache', () => {
  test('should cache computed values', async () => {
    let computeCount = 0;
    const cache = new ComputationCache<string, { value: string }>({
      size: 10,
      etag: (input) => input,
      compute: async (input, etag, signal) => {
        computeCount++;
        return { value: `computed-${input}` };
      },
    });

    // First call should compute
    const result1 = await cache.get('key');
    expect(result1.value).toBe('computed-key');
    expect(computeCount).toBe(1);

    // Second call should use cache
    const result2 = await cache.get('key');
    expect(result2.value).toBe('computed-key');
    expect(computeCount).toBe(1);
  });

  test('should handle concurrent requests', async () => {
    let computeCount = 0;
    const computePromise = new ManualPromise<{ value: string }>();
    
    const cache = new ComputationCache<string, { value: string }>({
      size: 10,
      etag: (input) => input,
      compute: async (input, etag, signal) => {
        computeCount++;
        return computePromise.promise;
      },
    });

    // Start multiple concurrent requests
    const promise1 = cache.get('key');
    const promise2 = cache.get('key');
    const promise3 = cache.get('key');

    // Compute should only be called once
    await expect.poll(() => computeCount).toBe(1);

    // Resolve the computation
    computePromise.resolve({ value: 'computed-key' });

    // All promises should resolve to the same value
    const [result1, result2, result3] = await Promise.all([promise1, promise2, promise3]);
    expect(result1.value).toBe('computed-key');
    expect(result2.value).toBe('computed-key');
    expect(result3.value).toBe('computed-key');
    expect(computeCount).toBe(1);
  });

  test('should work when cache disables admission', async () => {
    let admission = true;
    const cache = new SharedCacheStore({ policy: new LRUPolicy(10), shouldAdmit: () => admission });

    let fetchCount = 0;

    const cc = new ComputationCache({
      size: 10,
      etag: (input: string) => input,
      compute: async (input) => {
        fetchCount++;
        return `result-${input}-${fetchCount}`;
      },
      cache,
    });

    // Disable cache admission.
    // Since there's no cache, every request is re-computed.
    admission = false;
    expect(await cc.get('test')).toBe('result-test-1');
    expect(await cc.get('test')).toBe('result-test-2');

    // however, the same requests are de-duped.
    expect(await Promise.all([
      cc.get('foo'),
      cc.get('foo'),
    ])).toEqual(['result-foo-3', 'result-foo-3']);
    expect(cache.size).toBe(0);

    // But when admission is being re-enabled, computation cache works.
    admission = true;
    expect(await cc.get('test')).toBe('result-test-4');
    expect(await cc.get('test')).toBe('result-test-4');
  });

  test('should use different cache keys for different inputs', async () => {
    let computeCount = 0;
    const cache = new ComputationCache<string, { value: string }>({
      size: 10,
      etag: (input) => input,
      compute: async (input, etag, signal) => {
        computeCount++;
        return { value: `computed-${input}` };
      },
    });

    const result1 = await cache.get('key1');
    const result2 = await cache.get('key2');

    expect(result1.value).toBe('computed-key1');
    expect(result2.value).toBe('computed-key2');
    expect(computeCount).toBe(2);
  });

  test('should pass etag and signal to compute function', async () => {
    let receivedEtag: string | undefined;
    let receivedSignal: AbortSignal | undefined;

    const cache = new ComputationCache<string, { value: string }>({
      size: 10,
      etag: (input) => `etag-${input}`,
      compute: async (input, etag, signal) => {
        receivedEtag = etag;
        receivedSignal = signal;
        return { value: `computed-${input}` };
      },
    });

    await cache.get('test');

    expect(receivedEtag).toBe('etag-test');
    expect(receivedSignal).toBeInstanceOf(AbortSignal);
  });

  test('should handle compute function errors', async () => {
    const cache = new ComputationCache<string, { value: string }>({
      size: 10,
      etag: (input) => input,
      compute: async (input, etag, signal) => {
        throw new Error('Compute failed');
      },
    });

    await expect(cache.get('key')).rejects.toThrow('Compute failed');
  });

  test('should respect shouldCache option', async () => {
    let computeCount = 0;
    const cache = new ComputationCache<string, { value: string }>({
      size: 10,
      etag: (input) => input,
      compute: async (input, etag, signal) => {
        computeCount++;
        return { value: `computed-${input}` };
      },
      shouldCache: (value, durationMs) => value.value !== 'computed-nocache',
    });

    // This should be cached
    await cache.get('key1');
    await cache.get('key1');
    expect(computeCount).toBe(1);

    // This should not be cached
    await cache.get('nocache');
    await cache.get('nocache');
    expect(computeCount).toBe(3);
  });
});

test.describe('SyncComputationCache', () => {
  test('should cache computed values', () => {
    let computeCount = 0;
    const cache = new SyncComputationCache<string, { value: string }>({
      size: 10,
      etag: (input) => input,
      compute: (input, etag) => {
        computeCount++;
        return { value: `computed-${input}` };
      },
    });

    // First call should compute
    const result1 = cache.get('key');
    expect(result1.value).toBe('computed-key');
    expect(computeCount).toBe(1);

    // Second call should use cache
    const result2 = cache.get('key');
    expect(result2.value).toBe('computed-key');
    expect(computeCount).toBe(1);
  });

  test('should use different cache keys for different inputs', () => {
    let computeCount = 0;
    const cache = new SyncComputationCache<string, { value: string }>({
      size: 10,
      etag: (input) => input,
      compute: (input, etag) => {
        computeCount++;
        return { value: `computed-${input}` };
      },
    });

    const result1 = cache.get('key1');
    const result2 = cache.get('key2');

    expect(result1.value).toBe('computed-key1');
    expect(result2.value).toBe('computed-key2');
    expect(computeCount).toBe(2);
  });

  test('should pass etag to compute function', () => {
    let receivedEtag: string | undefined;

    const cache = new SyncComputationCache<string, { value: string }>({
      size: 10,
      etag: (input) => `etag-${input}`,
      compute: (input, etag) => {
        receivedEtag = etag;
        return { value: `computed-${input}` };
      },
    });

    cache.get('test');
    expect(receivedEtag).toBe('etag-test');
  });

  test('should call onGetTiming callback', () => {
    const timings: Array<{ input: string, key: string, duration: number }> = [];
    
    const cache = new SyncComputationCache<string, { value: string }>({
      size: 10,
      etag: (input) => input,
      compute: (input, etag) => ({ value: `computed-${input}` }),
      onGetTiming: (input, key, since, until) => {
        timings.push({ input, key, duration: until - since });
      },
    });

    cache.get('test');
    
    expect(timings).toHaveLength(1);
    expect(timings[0].input).toBe('test');
    expect(timings[0].key).toBe('test');
    expect(timings[0].duration).toBeGreaterThanOrEqual(0);
  });

  test('should work when cache disables admission', () => {
    let admission = true;
    const memCache = new SharedCacheStore({ policy: new LRUPolicy(Infinity), shouldAdmit: () => admission });
    let computeCount = 0;

    const cache = new SyncComputationCache({
      size: 10,
      etag: (input: string) => input,
      compute: (input) => {
        computeCount++;
        return `result-${input}-${computeCount}`;
      },
      cache: memCache,
    });

    // Disable cache admission
    admission = false;
    expect(cache.get('test')).toBe('result-test-1');
    expect(cache.get('test')).toBe('result-test-2');
    expect(memCache.size).toBe(0);

    // Re-enable admission
    admission = true;
    expect(cache.get('test')).toBe('result-test-3');
    expect(cache.get('test')).toBe('result-test-3');
  });

  test('should respect shouldCache option', () => {
    let computeCount = 0;
    const cache = new SyncComputationCache<string, { value: string }>({
      size: 10,
      etag: (input) => input,
      compute: (input, etag) => {
        computeCount++;
        return { value: `computed-${input}` };
      },
      shouldCache: (value, durationMs) => value.value !== 'computed-nocache',
    });

    // This should be cached
    cache.get('key1');
    cache.get('key1');
    expect(computeCount).toBe(1);

    // This should not be cached
    cache.get('nocache');
    cache.get('nocache');
    expect(computeCount).toBe(3);
  });
});
