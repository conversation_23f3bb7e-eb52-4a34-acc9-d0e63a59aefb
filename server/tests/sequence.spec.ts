import { expect, test } from '@playwright/test';
import { Sequence } from '../src/common/sequence.js';
  
test('Sequence.map', async () => {
  const seq = Sequence.fromList([1, 2, 3, 4, 5]);
  const mapped = seq.map(x => x * 2);
  
  expect(mapped.length).toBe(5);
  expect([...mapped.seek(0)]).toEqual([2, 4, 6, 8, 10]);
  expect([...mapped.seek(2)]).toEqual([6, 8, 10]);
  expect([...mapped.seek(5)]).toEqual([]);
});

test.describe('Sequence.chain', () => {
  test('should work', async () => {
    const lists = [
      [1, 2],
      [],
      [3, 4, 5],
      [6],
    ]
    const seqs = lists.map(l => Sequence.fromList(l));
    const chained = Sequence.chain(seqs);
    const flat = lists.flat();

    expect(chained.length).toBe(6);
    expect([...chained.seek(0)]).toEqual([[1, 0], [2, 0], [3, 2], [4, 2], [5, 2], [6, 3]]);
    expect([...chained.seek(2)]).toEqual([[3, 2], [4, 2], [5, 2], [6, 3]]);
    expect([...chained.seek(5)]).toEqual([[6, 3]]);
    expect([...chained.seek(6)]).toEqual([]);

    const slice = (from: number, to: number) => Iterator.from(chained.seek(from)).take(Math.max(0, to - from)).toArray().map(x => x[0]);
    for(let i = 0; i < flat.length; ++i) {
      for (let j = 0; j < flat.length; ++j)
        expect(slice(i, j)).toEqual(flat.slice(i, j));
    }
  });

  test('should match array results', async () => {
    const lists = [
      [5, 6, 7],
      [],
      [],
      [1, 2],
      [10, 11],
    ];
    const list = lists.flat();
    const seq = Sequence.chain(lists.map(list => Sequence.fromList(list)));
    const slice = (from: number, to: number) => Iterator.from(seq.seek(from)).take(Math.max(0, to - from)).toArray().map(x => x[0]);

    for(let i = 0; i < list.length; ++i) {
      for (let j = 0; j < list.length; ++j)
        expect(slice(i, j)).toEqual(list.slice(i, j));
    }
  });

  test('should work when seeking to a non-boundary index in a later sequence', () => {
    const seqs = [
      Sequence.fromList([10, 20]),
      Sequence.fromList([30, 40, 50]),
    ];
    const chained = Sequence.chain(seqs);
    const expected = [[40, 1], [50, 1]];    
    const resultIterator = chained.seek(3);
    const actual = [...resultIterator];
    expect(actual).toEqual(expected);
  });
});

test.describe('Sequence.merge', () => {
  test('should work', { tag: ['@smoke'], }, async () => {
    const seqs = [
      Sequence.fromList([1, 4, 7]),
      Sequence.fromList([2, 3, 8]),
      Sequence.fromList([5, 6])
    ];
    const merged = Sequence.merge(seqs, (a, b) => a - b);
    
    expect(merged.length).toBe(8);
    expect([...merged.seek(0)]).toEqual([1, 2, 3, 4, 5, 6, 7, 8]);
    expect([...merged.seek(3)]).toEqual([4, 5, 6, 7, 8]);
    expect([...merged.seek(8)]).toEqual([]);
  });

  test('should work when all elements are equal', async () => {
    const seqs = [
      Sequence.fromList([1, 4, 7]),
      Sequence.fromList([2, 3, 8]),
      Sequence.fromList([5, 6])
    ];
    const merged = Sequence.merge(seqs, (a, b) => 0);
    expect(merged.length).toBe(8);
    expect([...merged.seek(0)].sort()).toEqual([1, 2, 3, 4, 5, 6, 7, 8]);
  });

  test('should work with a single seq', { tag: ['@smoke'], }, async () => {
    const seqs = [
      Sequence.fromList([1, 4, 7]),
    ];
    const merged = Sequence.merge(seqs, (a, b) => a - b);
    
    expect(merged.length).toBe(3);
    expect([...merged.seek(0)]).toEqual([1, 4,7]);
    expect([...merged.seek(8)]).toEqual([]);
  });

  test('should work with no sequences', { tag: ['@smoke'], }, async () => {
    const merged = Sequence.merge<number>([], (a, b) => a - b);
    
    expect(merged.length).toBe(0);
    expect([...merged.seek(0)]).toEqual([]);
  });

  test('should match array results', async () => {
    const arrays = [
      [1, 1, 1],
      [2, 4, 7],
      [5, 6, 8],
      [-1],
      [], [], 
      [18, 18, 19]
    ];
    const seqs = arrays.map(Sequence.fromList);

    const all = arrays.flat().sort((a, b) => a - b);
    for (let i = 0; i < all.length; i++) {
      const seq = Sequence.merge(seqs, (a, b) => a - b);
      expect([...seq.seek(i)]).toEqual(all.slice(i));
    }
  });
});

test.describe('Sequence.partitionPoint', () => {
  test('should find partition point in sorted sequence', async () => {
    const seq = Sequence.fromList([1, 3, 5, 7, 9]);
    
    expect(seq.partitionPoint(x => x < 5)).toBe(2); // elements 1,3 are < 5
    expect(seq.partitionPoint(x => x <= 5)).toBe(3); // elements 1,3,5 are <= 5
    expect(seq.partitionPoint(x => x < 1)).toBe(0); // no elements < 1
    expect(seq.partitionPoint(x => x < 10)).toBe(5); // all elements < 10
  });

  test('should work with duplicate elements', async () => {
    const seq = Sequence.fromList([1, 1, 3, 3, 3, 5, 5]);
    
    expect(seq.partitionPoint(x => x < 3)).toBe(2); // first two 1's
    expect(seq.partitionPoint(x => x <= 3)).toBe(5); // 1's and 3's
    expect(seq.partitionPoint(x => x < 5)).toBe(5); // everything except 5's
  });

  test('should work with empty sequence', async () => {
    const seq = Sequence.fromList([]);
    
    expect(seq.partitionPoint(x => x < 5)).toBe(0);
    expect(seq.partitionPoint(x => true)).toBe(0);
  });

  test('should work with single element', async () => {
    const seq = Sequence.fromList([42]);
    
    expect(seq.partitionPoint(x => x < 42)).toBe(0);
    expect(seq.partitionPoint(x => x <= 42)).toBe(1);
    expect(seq.partitionPoint(x => x > 42)).toBe(0);
  });
});
