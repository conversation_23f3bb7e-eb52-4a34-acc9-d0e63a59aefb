import { ManualPromise } from '@flakiness/shared/common/manualPromise.js';
import { expect, test } from '@playwright/test';
import { SharedCacheStore } from '../src/common/caches/cache.js';
import { LRUPolicy } from '../src/common/caches/lruPolicy.js';
import { SingleflightCache } from '../src/common/singleflightcache.js';

test.describe('SingleflightCache', () => {
  test('should return cached value when fresh', async () => {
    let fetchCount = 0;
    
    const sfc = new SingleflightCache({
      key: (input: string) => input,
      fetch: async (input) => {
        fetchCount++;
        return `result-${input}-${fetchCount}`;
      },
      size: 10,
      ttl: 1000,
    });

    const result1 = await sfc.get('test');
    const result2 = await sfc.get('test');
    
    expect(result1).toBe(result2);
    expect(fetchCount).toBe(1);
  });

test('should work when cache disables admission', async () => {
    let admission = true;
    const cache = new SharedCacheStore({ policy: new LRUPolicy(10), shouldAdmit: () => admission, });

    let fetchCount = 0;

    const sfc = new SingleflightCache({
      key: (input: string) => input,
      size: 10,
      fetch: async (input) => {
        fetchCount++;
        return `result-${input}-${fetchCount}`;
      },
      cache,
      ttl: 1000,
    });

    // since there's no cache, every request is re-computed.
    admission = false;
    expect(await sfc.get('test')).toBe('result-test-1');
    expect(await sfc.get('test')).toBe('result-test-2');
    // however, the same requests are de-duped.
    expect(await Promise.all([
      sfc.get('foo'),
      sfc.get('foo'),
    ])).toEqual(['result-foo-3', 'result-foo-3']);
    expect(cache.size).toBe(0);

    admission = true;
    expect(await sfc.get('test')).toBe('result-test-4');
    expect(await sfc.get('test')).toBe('result-test-4');
  });

  test('should deduplicate concurrent requests for same key', async () => {
    const cache = new SharedCacheStore({ policy: new LRUPolicy(Infinity) });
    let fetchCount = 0;
    
    const sfc = new SingleflightCache({
      size: 10,
      key: (input: string) => input,
      fetch: async (input) => {
        fetchCount++;
        return `result-${input}-${fetchCount}`;
      },
      cache,
      ttl: 1000,
    });

    const [result1, result2, result3] = await Promise.all([
      sfc.get('test'),
      sfc.get('test'),
      sfc.get('test'),
    ]);

    expect(result1).toBe(result2);
    expect(result2).toBe(result3);
    expect(fetchCount).toBe(1);
  });

  test('should return stale value immediately while refetching in background', async () => {
    const cache = new SharedCacheStore({ policy: new LRUPolicy(Infinity) });
    const fetchPromises: ManualPromise<string>[] = [];
    let fetchCount = 0;
    let currentTime = 0;
    
    const sfc = new SingleflightCache({
      key: (input: string) => input,
      size: 10,
      fetch: async (input, stale) => {
        fetchCount++;
        const promise = new ManualPromise<string>();
        fetchPromises.push(promise);
        return promise.promise;
      },
      cache,
      currentTime: () => currentTime,
      ttl: 100,
    });

    // First fetch
    const promise1 = sfc.get('test');
    await expect.poll(() => fetchPromises.length).toBe(1);
    fetchPromises[0].resolve('fresh-1');
    expect(await promise1).toBe('fresh-1');

    // Wait for TTL to expire
    currentTime += 150;

    // Second fetch should return stale immediately
    const result2 = await sfc.get('test');
    expect(result2).toBe('fresh-1'); // Stale value returned immediately

    // Background fetch should be running
    await expect.poll(() => fetchPromises.length).toBe(2);
    fetchPromises[1].resolve('fresh-2');
    await expect.poll(() => sfc.get('test')).toBe('fresh-2');
  });

  test('should pass stale value to fetch function', async () => {
    const cache = new SharedCacheStore({ policy: new LRUPolicy(Infinity) });
    const staleValues: (string | undefined)[] = [];
    let currentTime = 0;
    
    const sfc = new SingleflightCache<string, string>({
      key: (input: string) => input,
      size: 10,
      fetch: async (input, stale) => {
        staleValues.push(stale);
        return `fresh-${input}`;
      },
      cache,
      currentTime: () => currentTime,
      ttl: 100,
    });

    await sfc.get('test');
    expect(staleValues[0]).toBeUndefined();

    // Wait for TTL to expire
    currentTime += 150;
    expect(await sfc.get('test')).toEqual('fresh-test');
    await expect.poll(() => staleValues[1]).toEqual('fresh-test');
  });

  test('should handle invalidate when cache is at rest', async () => {
    const cache = new SharedCacheStore({ policy: new LRUPolicy(Infinity) });
    let fetchCount = 0;
    
    const sfc = new SingleflightCache({
      size: 10,
      key: (input: string) => input,
      fetch: async (input) => {
        fetchCount++;
        return `result-${fetchCount}`;
      },
      cache,
      ttl: 1000,
    });

    await sfc.get('test');
    expect(fetchCount).toBe(1);

    await sfc.refresh('test');
    
    // Should trigger background refetch
    await expect.poll(() => fetchCount).toBe(2);
  });

  test('should handle invalidate when fetch is in flight', async () => {
    const cache = new SharedCacheStore({ policy: new LRUPolicy(Infinity) });
    const fetchPromises: ManualPromise<string>[] = [];

    const sfc = new SingleflightCache({
      key: (input: string) => input,
      size: 10,
      fetch: async (input, stale, signal) => {
        const promise = new ManualPromise<string>();
        fetchPromises.push(promise);
        return promise.promise;
      },
      cache,
      ttl: 1000,
    });

    const getPromise = sfc.get('test');
    await expect.poll(() => fetchPromises.length).toBe(1);
    await sfc.refresh('test');
    
    // Should not start new fetch while one is in flight
    expect(fetchPromises.length).toBe(1);
    fetchPromises[0].resolve('result-1');
    await expect.poll(() => fetchPromises.length).toBe(2);
    fetchPromises[1].resolve('result-2');
    expect(await getPromise).toBe('result-2');
  });
});