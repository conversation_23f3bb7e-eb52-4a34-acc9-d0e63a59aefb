import { expect, test } from '@playwright/test';
import { DoubleLinkedList } from '../src/common/doubleLinkedList.js';

test('should initialize empty', () => {
  const list = new DoubleLinkedList<string>();
  expect(list.size()).toBe(0);
  expect(list.head()).toBeUndefined();
  expect(list.tail()).toBeUndefined();
});

test('should add elements to head and tail', () => {
  const list = new DoubleLinkedList<string>();
  
  list.moveToHead('a');
  expect(list.size()).toBe(1);
  expect(list.head()).toBe('a');
  expect(list.tail()).toBe('a');
  
  list.moveToHead('b');
  expect(list.size()).toBe(2);
  expect(list.head()).toBe('b');
  expect(list.tail()).toBe('a');
  
  list.moveToTail('c');
  expect(list.size()).toBe(3);
  expect(list.head()).toBe('b');
  expect(list.tail()).toBe('c');
});

test('should move existing elements to head', () => {
  const list = new DoubleLinkedList<string>();
  
  list.moveToTail('a');
  list.moveToTail('b');
  list.moveToTail('c');
  
  // Initial state: head=a, tail=c
  expect(list.head()).toBe('a');
  expect(list.tail()).toBe('c');
  
  // Move middle element to head
  list.moveToHead('b');
  expect(list.head()).toBe('b');
  expect(list.tail()).toBe('c');
  
  // Move tail element to head
  list.moveToHead('c');
  expect(list.head()).toBe('c');
  expect(list.tail()).toBe('a');
});

test('should move existing elements to tail', () => {
  const list = new DoubleLinkedList<string>();
  
  list.moveToHead('a');
  list.moveToHead('b');
  list.moveToHead('c');
  
  // Initial state: head=c, tail=a
  expect(list.head()).toBe('c');
  expect(list.tail()).toBe('a');
  
  // Move middle element to tail
  list.moveToTail('b');
  expect(list.head()).toBe('c');
  expect(list.tail()).toBe('b');
  
  // Move head element to tail
  list.moveToTail('c');
  expect(list.head()).toBe('a');
  expect(list.tail()).toBe('c');
});

test('should delete elements correctly', () => {
  const list = new DoubleLinkedList<string>();
  
  list.moveToTail('a');
  list.moveToTail('b');
  list.moveToTail('c');
  
  // Initial state: head=a, tail=c
  expect(list.size()).toBe(3);
  
  // Delete middle element
  list.delete('b');
  expect(list.size()).toBe(2);
  expect(list.head()).toBe('a');
  expect(list.tail()).toBe('c');
  
  // Delete head element
  list.delete('a');
  expect(list.size()).toBe(1);
  expect(list.head()).toBe('c');
  expect(list.tail()).toBe('c');
  
  // Delete last element
  list.delete('c');
  expect(list.size()).toBe(0);
  expect(list.head()).toBeUndefined();
  expect(list.tail()).toBeUndefined();
});

test('should handle deleting non-existent elements', () => {
  const list = new DoubleLinkedList<string>();
  
  list.moveToTail('a');
  
  // Should not throw
  list.delete('non-existent');
  expect(list.size()).toBe(1);
  expect(list.head()).toBe('a');
});

test('should iterate elements in order from tail to head', () => {
  const list = new DoubleLinkedList<string>();
  
  list.moveToTail('a');
  list.moveToTail('b');
  list.moveToTail('c');
  
  const values: string[] = [];
  for (const value of list) {
    values.push(value);
  }
  
  expect(values).toEqual(['c', 'b', 'a']);
});

test('should clear all elements', () => {
  const list = new DoubleLinkedList<string>();
  
  list.moveToTail('a');
  list.moveToTail('b');
  list.moveToTail('c');
  
  expect(list.size()).toBe(3);
  
  list.clear();
  expect(list.size()).toBe(0);
  expect(list.head()).toBeUndefined();
  expect(list.tail()).toBeUndefined();
});

test('should handle complex operations sequence', () => {
  const list = new DoubleLinkedList<string>();
  
  // Add elements
  list.moveToHead('a');
  list.moveToTail('b');
  list.moveToHead('c');
  
  // State: head=c, tail=b, middle=a
  expect(list.head()).toBe('c');
  expect(list.tail()).toBe('b');
  
  // Move elements
  list.moveToTail('c');
  
  // State: head=a, tail=c, middle=b
  expect(list.head()).toBe('a');
  expect(list.tail()).toBe('c');
  
  // Delete and add
  list.delete('b');
  list.moveToHead('d');
  
  // State: head=d, tail=c, middle=a
  expect(list.head()).toBe('d');
  expect(list.tail()).toBe('c');
  expect(list.size()).toBe(3);
  
  // Verify iteration order
  const values: string[] = [];
  for (const value of list) {
    values.push(value);
  }
  
  expect(values).toEqual(['c', 'a', 'd']);
});
