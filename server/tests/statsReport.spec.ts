import { FlakinessReport } from '@flakiness/sdk';
import { expect, test } from '@playwright/test';
import { Query } from '../src/common/fql/query.js';
import { CommitAnalyzer } from '../src/common/stats/commitAnalyzer.js';
import { HistoryAnalyzer } from '../src/common/stats/historyAnalyzer.js';
import { SpanAnalyzer } from '../src/common/stats/spanAnalyzer.js';
import { Stats } from '../src/common/stats/stats.js';
import { StatsAnalyzer } from '../src/common/stats/statsAnalyzer.js';
import { StatsBuilder } from '../src/common/stats/statsBuilder.js';
import { TestIndex } from '../src/common/stats/testIndex.js';
import { TestOutcomes } from '../src/common/stats/testOutcomes.js';
import { TestsReport } from '../src/common/stats/testsReport.js';
import { TimelineCommitReport } from '../src/common/stats/timelineCommitReport.js';
import { TimelineTestsReport } from '../src/common/stats/timelineTestsReport.js';
import { Timeline } from '../src/common/timeline/timeline.js';
import { WireTypes } from '../src/common/wireTypes.js';
import { ReportBuilder } from './utils/reportBuilder.js';

const S_PASSED: FlakinessReport.TestStatus = 'passed';
const S_FAILED: FlakinessReport.TestStatus = 'failed';
const S_SKIPPED: FlakinessReport.TestStatus = 'skipped';
const S_TIMEOUT: FlakinessReport.TestStatus = 'timedOut';
const S_INTERRUPT: FlakinessReport.TestStatus = 'interrupted';

const commitA: WireTypes.Commit = {
  commitId: 'commit-A' as FlakinessReport.CommitId,
  message: '',
  timestamp: Date.now() as FlakinessReport.UnixTimestampMS,
  walkIndex: 0,
};
const commitB: WireTypes.Commit = {
  commitId: 'commit-B' as FlakinessReport.CommitId,
  message: '',
  timestamp: Date.now() as FlakinessReport.UnixTimestampMS,
  walkIndex: 1,
};
const commitC: WireTypes.Commit = {
  commitId: 'commit-C' as FlakinessReport.CommitId,
  message: '',
  timestamp: Date.now() as FlakinessReport.UnixTimestampMS,
  walkIndex: 2,
};
const commitD: WireTypes.Commit = {
  commitId: 'commit-D' as FlakinessReport.CommitId,
  message: '',
  timestamp: Date.now() as FlakinessReport.UnixTimestampMS,
  walkIndex: 3,
};
const envUbuntu: FlakinessReport.Environment = {
  name: 'ubuntu',
  systemData: {
    osName: 'Ubuntu',
    osVersion: '22.04',
    osArch: 'x86_64',
  },
  userSuppliedData: {},
};
const envMacOS: FlakinessReport.Environment = {
  name: 'macos',
  systemData: {
    osName: 'macOS',
    osVersion: 'Sonoma',
    osArch: 'aarch64',
  },
  userSuppliedData: {},
};
const envWindows: FlakinessReport.Environment = {
  name: 'windows',
  systemData: {
    osName: 'Windows',
    osVersion: '10',
    osArch: 'x86_64',
  },
  userSuppliedData: {},
};

test('make sure ReportBuilder works as expected', async () => {
  const commits = [commitA, commitB, commitC, commitD];
  const suites = ReportBuilder.createSuites({
    'foo.spec.ts': [
      'test-A', // this one always passes everywhere
      'test-B', // this one fails 50% of times on Windows, flakes 25% on MacOS, passes on linux
      'test-C', // this one flakes 100% of times on Linux only
      'test-D', // this one is just permanently skipped everywhere
    ],
  });

  const reports = ReportBuilder.createReports(suites, {
    // This one always passes everywhere
    'test-A': commits.map(({ commitId }) => [
      { commitId, env: envUbuntu, attempts: [{ status: S_PASSED }] },
      { commitId, env: envMacOS, attempts: [{ status: S_PASSED }] },
      { commitId, env: envWindows, attempts: [{ status: S_PASSED }] },
    ]).flat(),
    // this one fails 50% of times on Windows, works otherwise
    'test-B': commits.map(({ commitId }, idx) => [
      { commitId, env: envUbuntu, attempts: [{ status: S_PASSED }] },
      { commitId, env: envMacOS, attempts: [{ status: S_PASSED }] },
      idx % 2 === 0 ?
        { commitId, env: envWindows, attempts: [{ error: 'undefined is not a function' }] } :
        { commitId, env: envWindows, attempts: [{ status: S_PASSED }] }
      ,
    ]).flat(),
    // this one flakes 100% of times on Windows, works otherwise
    'test-C': commits.map(({ commitId }, idx) => [
      { commitId, env: envUbuntu, attempts: [{ status: S_PASSED }] },
      { commitId, env: envMacOS, attempts: [{ status: S_PASSED }] },
      { commitId, env: envWindows, attempts: [{ status: S_TIMEOUT }, { status: S_PASSED }] },
    ]).flat(),
    // this one is skipped on Windows, passes everywhere else.
    'test-D': commits.map(({ commitId }, idx) => [
      { commitId, env: envWindows, attempts: [{ status: S_SKIPPED }] },
      { commitId, env: envUbuntu, attempts: [{ status: S_PASSED }] },
      { commitId, env: envMacOS, attempts: [{ status: S_PASSED }] },
    ]).flat(),
  });

  const [report, commitStats] = createReport(reports, commits, '');
  expect(report.counters().tests).toBe(12);
  expect(report.timelines.all().length).toBe(3);
  expect(commitStats.length).toBe(4);
  expect(report.errors.all().length).toBe(1);
});

test('test error filtering', { tag: '@smoke' }, async () => {
  // Report for single commit.
  const commit = commitA;
  const commitId = commit.commitId;
  // We have 2 environments. Both run all the tests.
  // Everything passes in env1
  const env1 = envMacOS;
  // Some failures happen in env2
  const env2 = envUbuntu;
  // We have 5 tests, 2 environments, 2 errors, 1 commit.
  // One environment has no errors - everything passes there.
  // In another environment:
  // 2 tests never fail
  // 1 test fails with one error
  // 2 tests fail with another error.
  const err1 = 'NOT_FOUND';
  const err2 = 'TEAPOT';
  const suites = ReportBuilder.createSuites({
    'foo.spec.ts': [
      'test-A', // This test fails with err1
      'test-B', // This test fails with err1 and err2
      'test-C', // This test fails with err2
      'test-D', // This test doesn't fail
      'test-E', // Doesn't fail either.
    ],
  });

  const reports = ReportBuilder.createReports(suites, {
    'test-A': [
      { commitId, env: env1, attempts: [{ status: S_PASSED }] },
      { commitId, env: env2, attempts: [{ error: err1 }] },
    ],
    'test-B': [
      { commitId, env: env1, attempts: [{ status: S_PASSED }] },
      { commitId, env: env2, attempts: [{ errors: [err1, err2] }] },
    ],
    'test-C': [
      { commitId, env: env1, attempts: [{ status: S_PASSED }] },
      { commitId, env: env2, attempts: [{ error: err2 }] },
    ],
    'test-D': [
      { commitId, env: env1, attempts: [{ status: S_PASSED }] },
      { commitId, env: env2, attempts: [{ status: S_PASSED }] },
    ],
    'test-E': [
      { commitId, env: env1, attempts: [{ status: S_PASSED }] },
      { commitId, env: env2, attempts: [{ status: S_PASSED }] },
    ],
  });

  await test.step('empty error query', async () => {
    const [report, commitsReport] = createReport(reports, [commit], '');
    expect(report.counters().tests).toBe(10);
    expect(report.errors.all().length).toBe(2);
  });
  await test.step('filter by err1', async () => {
    const [report, commitsReport] = createReport(reports, [commit], `$${err1}`);
    expect(report.counters().tests).toBe(2);
    expect(report.errors.all().length).toBe(1);
  });
  await test.step('exclude err1', async () => {
    const [report, commitsReport] = createReport(reports, [commit], `-$${err1}`);
    expect(report.counters().tests).toBe(8);
    expect(report.errors.all().length).toBe(1);
  });
  await test.step('filter by both err1 and err2', async () => {
    const [report, commitsReport] = createReport(reports, [commit], `$${err1} $${err2}`);
    expect(report.counters().tests).toBe(0);
    expect(report.errors.all().length).toBe(0);
  });
  await test.step('include err1 and exclude err2', async () => {
    const [report, commitsReport] = createReport(reports, [commit], `$${err1} -$${err2}`);
    expect(report.counters().tests).toBe(1);
    expect(report.errors.all().length).toBe(1);
  });
  await test.step('filter by non-existing error text', async () => {
    const [report, commitsReport] = createReport(reports, [commit], `$DOESNOTEXISTFORSURE`);
    expect(report.counters().tests).toBe(0);
    expect(report.errors.all().length).toBe(0);
  });
  await test.step('exclude non-existing error text', async () => {
    const [report, commitsReport] = createReport(reports, [commit], `-$DOESNOTEXISTFORSURE`);
    expect(report.counters().tests).toBe(10);
    expect(report.errors.all().length).toBe(2);
  });
});

test('s:fire should filter runs, commits and tests', async () => {
  // We have 1 test that fails in 1 run and doesn't fail in 2 other.
  const suites = ReportBuilder.createSuites({
    'foo.spec.ts': [
      'test-A', // This test fails with err1
    ],
  });

  const commits = [commitA, commitB, commitC];
  const env = envMacOS;
  const reports = ReportBuilder.createReports(suites, {
    'test-A': [
      { commitId: commits[0].commitId, env, attempts: [{ status: S_FAILED }] },
      { commitId: commits[1].commitId, env, attempts: [{ status: S_PASSED }] },
      { commitId: commits[2].commitId, env, attempts: [{ status: S_PASSED }] },
    ],
  });

  await test.step('no status filter', async () => {
    const [report, commitStats] = createReport(reports, commits, '');
    const allRunStats = commitStats.map(s => s.runs).flat();
    expect(report.counters().tests).toBe(1);
    expect(commitStats.length).toBe(3);
    expect(allRunStats.length).toBe(3);
  });
  await test.step('s:fire', async () => {
    const [report, commitStats] = createReport(reports, commits, 's:fire');
    const allRunStats = commitStats.map(s => s.runs).flat();
    expect(report.counters().tests).toBe(1);
    expect(commitStats.length).toBe(3);
    expect(allRunStats.length).toBe(3);
  });
});

function createReport(reports: FlakinessReport.Report[], commits: WireTypes.Commit[], fql: string): [TestsReport, WireTypes.CommitStats[]] {
  const index = new TestIndex();
  const builder = StatsBuilder.create(index);
  for (let runId = 0 as Stats.RunId; runId < reports.length; ++runId) {
    index.addReport(reports[runId]);
    builder.addRun(runId, reports[runId]);
  }
  const statsAnalyzer = new StatsAnalyzer(builder.jsonStats());
  const commitAnalyzers = commits.map(commit => statsAnalyzer.getCommitAnalyzer(commit.commitId));
  const envs = CommitAnalyzer.runEnvironments(commitAnalyzers);
  const filter = index.createFilterContext(Query.parse(fql));
  const timelines = envs.map(env => Timeline.fromEnv(env));
  const timelineTestsReports = timelines.map(timeline => TimelineTestsReport.create(timeline, new SpanAnalyzer(commitAnalyzers), TestOutcomes.EMPTY_TESTS).applyFilter(filter));

  const historyAnalyzer = new HistoryAnalyzer([new SpanAnalyzer(commitAnalyzers)], 1);
  const commitStats = commits.map((commit, idx) => {
    const reports = timelines.map(timeline => TimelineCommitReport
      .create(timeline, commitAnalyzers[idx], historyAnalyzer.unhealthyTests(timeline, commit.commitId))
      .applyFilter(filter)
    );
    return TimelineCommitReport.wireCommitStats(commit, reports);
  });
  return [new TestsReport(index, timelineTestsReports, 0), commitStats];
}
