import { expect } from '@playwright/test';
import { S3 } from "../src/node/s3.js";
import { s3test } from "./utils/fixtures.js";

s3test('cannot create the same bucket twice', { tag: '@smoke' }, async ({ s3config, randomBucketName }) => {
  const s3 = new S3(s3config);
  
  await s3.createBucket(randomBucketName);
  let error;
  await s3.createBucket(randomBucketName).catch(e => error = e);
  expect(error).not.toBe(null);
});

s3test('should create and list buckets', async ({ s3config, randomBucketName }) => {
  const s3 = new S3(s3config);
  await expect.poll(() => s3.listBuckets().then(buckets => buckets.map(bucket => bucket.name()))).not.toContain(randomBucketName);
  const bucket = await s3.createBucket(randomBucketName);
  expect(bucket.name()).toBe(randomBucketName);
  await expect.poll(() => s3.listBuckets().then(buckets => buckets.map(bucket => bucket.name()))).toContain(randomBucketName);
});

s3test('should be able to upload, list and get a file', async ({ s3config, randomBucketName }) => {
  const s3 = new S3(s3config);
  const bucket = await s3.createBucket(randomBucketName);

  const listDirectoryEntries = async (prefix: string) => {
    const result: string[][] = [];
    for await (const files of bucket.listDirectoryEntries(prefix))
      result.push(files);
    return result.flat();
  }

  expect(await listDirectoryEntries('')).toEqual([]);

  await bucket.uploadFile('bar/baz/yo.txt', {
    metadata: {},
    data: Buffer.from('foobar')
  });
  // Files take some time to appear on S3. After that, results should be consistent.
  await expect.poll(async () => await listDirectoryEntries('')).toEqual(['bar/']);
  expect(await listDirectoryEntries('bar/')).toEqual(['bar/baz/']);
  expect(await listDirectoryEntries('bar')).toEqual(['bar/']);
  expect(await listDirectoryEntries('bar/baz/')).toEqual(['bar/baz/yo.txt']);

  expect(await bucket.getFileAsText('bar/baz/yo.txt')).toBe('foobar');
  //console.log(await bucket.getUploadURL('fofofo/fofofo/jojo.txt', 3600));  
});


s3test('should be able to list objects with prefix', async ({ s3config, randomBucketName }) => {
  const s3 = new S3(s3config);
  const bucket = await s3.createBucket(randomBucketName);

  const listObjects = async (prefix: string) => {
    const result: string[][] = [];
    for await (const files of bucket.listObjects(prefix))
      result.push(files);
    return result.flat().sort((a, b) => a < b ? -1 : 1);  
  }

  expect(await listObjects('')).toEqual([]);

  await bucket.uploadFile('bar/a.txt', { metadata: {}, data: Buffer.from('foobar') });
  await bucket.uploadFile('bar/b.txt', { metadata: {}, data: Buffer.from('foobar') });
  await bucket.uploadFile('foo/c.txt', { metadata: {}, data: Buffer.from('foobar') });
  await bucket.uploadFile('foo/d.txt', { metadata: {}, data: Buffer.from('foobar') });

  // Files take some time to appear on S3. After that, results should be consistent.
  await expect.poll(async () => await listObjects('')).toEqual(['bar/a.txt', 'bar/b.txt', 'foo/c.txt', 'foo/d.txt']);
  await expect.poll(async () => await listObjects('foo/')).toEqual(['foo/c.txt', 'foo/d.txt']);
  await expect.poll(async () => await listObjects('bar/')).toEqual(['bar/a.txt', 'bar/b.txt']);
});


s3test('should be able to list object sizes with prefix', async ({ s3config, randomBucketName }) => {
  const s3 = new S3(s3config);
  const bucket = await s3.createBucket(randomBucketName);

  const listObjectSizes = async (prefix: string) => {
    const result: [string, number][][] = [];
    for await (const files of bucket.listObjectSizes(prefix))
      result.push(files);
    return result.flat().sort((a, b) => a < b ? -1 : 1);  
  }

  expect(await listObjectSizes('')).toEqual([]);

  await bucket.uploadFile('bar/a.txt', { metadata: {}, data: Buffer.from('foobar') });
  await bucket.uploadFile('bar/b.txt', { metadata: {}, data: Buffer.from('f') });
  await bucket.uploadFile('foo/c.txt', { metadata: {}, data: Buffer.from('fo') });
  await bucket.uploadFile('foo/d.txt', { metadata: {}, data: Buffer.from('foob') });

  // Files take some time to appear on S3. After that, results should be consistent.
  await expect.poll(async () => await listObjectSizes('')).toEqual([
    ['bar/a.txt', 6], ['bar/b.txt', 1], ['foo/c.txt', 2], ['foo/d.txt', 4]
  ]);
  await expect.poll(async () => await listObjectSizes('foo/')).toEqual([
    ['foo/c.txt', 2], ['foo/d.txt', 4]
  ]);
  await expect.poll(async () => await listObjectSizes('bar/')).toEqual([
    ['bar/a.txt', 6], ['bar/b.txt', 1]
  ]);
});

s3test('should be able to upload using signed URL', async ({ s3config, randomBucketName, request }) => {
  const s3 = new S3(s3config);
  const bucket = await s3.createBucket(randomBucketName);
  const key = 'jojo.txt';
  const signedURL = await bucket.getUploadURL(key, 3600);
  const response = await request.put(signedURL, {
    data: 'mew mew',
  });
  expect(response.status()).toBe(200);
  await expect.poll(() => bucket.getFileAsText(key).catch(e => '')).toBe('mew mew');
});