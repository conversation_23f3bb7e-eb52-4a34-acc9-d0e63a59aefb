
import { expect, test } from '@playwright/test';
import { LRUPolicy } from '../src/common/caches/lruPolicy.js';

test.describe('LRUPolicy', () => {
  test('should work', { tag: '@smoke' }, async () => {
    const policy = new LRUPolicy<string>(Infinity);
    expect(policy.size).toBe(0);

    await test.step('set', async () => {
      policy.hit('foo');
      policy.hit('bar');
      expect(policy.size).toBe(2);
      expect(policy.has('key')).toBe(false);
      expect(policy.has('foo')).toBe(true);
      expect(policy.has('bar')).toBe(true);
    });

    await test.step('delete', async () => {
      policy.delete('foo');
      expect(policy.size).toBe(1);
      expect(policy.has('foo')).toBe(false)
      expect(policy.has('bar')).toBe(true);
    });

    await test.step('has', async() => {
      expect(policy.has('foo')).toBe(false);
      expect(policy.has('bar')).toBe(true);
    });

    await test.step('evict', async () => {
      expect(policy.victim()).toEqual('bar');
      expect(policy.size).toBe(1);
    });
  });

  test('get/set should define eviction order', () => {
    const policy = new LRUPolicy<string>(Infinity);
    
    // Add items
    policy.hit('a');
    policy.hit('b');
    policy.hit('c');
    
    // Access 'a' to make it most recently used
    policy.hit('a');
    
    expect(policy.victim()).toEqual('b');
    policy.delete(policy.victim()!);
    expect(policy.victim()).toEqual('c');
    policy.delete(policy.victim()!);
    expect(policy.victim()).toEqual('a');
    policy.delete(policy.victim()!);
    expect(policy.victim()).toBeUndefined();
  });

  test('should handle capacity limits', () => {
    const policy = new LRUPolicy<string>(2);
    
    policy.hit('a');
    policy.hit('b');
    expect(policy.size).toBe(2);
    
    // Adding third item should evict oldest
    const evicted = policy.hit('c');
    expect(evicted).toBe('a');
    expect(policy.size).toBe(2);
    expect(policy.has('a')).toBe(false);
    expect(policy.has('b')).toBe(true);
    expect(policy.has('c')).toBe(true);
  });

  test('should handle zero capacity', () => {
    const policy = new LRUPolicy<string>(0);
    
    const evicted = policy.hit('a');
    expect(evicted).toBe('a');
    expect(policy.size).toBe(0);
    expect(policy.has('a')).toBe(false);
  });

  test('victim should return undefined for empty policy', () => {
    const policy = new LRUPolicy<string>(5);
    expect(policy.victim()).toBeUndefined();
  });

  test('victim with newKey should handle capacity checks', () => {
    const policy = new LRUPolicy<string>(2);
    policy.hit('a');
    policy.hit('b');
    
    // Adding existing key shouldn't require eviction
    expect(policy.victim('a')).toBeUndefined();
    
    // Adding new key when at capacity should return victim
    expect(policy.victim('c')).toBe('a');
  });

  test('should handle duplicate hits correctly', () => {
    const policy = new LRUPolicy<string>(3);
    
    policy.hit('a');
    policy.hit('b');
    policy.hit('a'); // Update access time
    policy.hit('c');
    
    // 'b' should be oldest now
    expect(policy.victim()).toBe('b');
  });
});

