import { expect, test } from '@playwright/test';
import { bytes } from '../src/common/bytes.js';

test('bytes', async () => {
  expect.soft(bytes('100')).toBe(100);

  // Basic units
  expect.soft(bytes('1B')).toBe(1);
  expect.soft(bytes('1KB')).toBe(1024);
  expect.soft(bytes('1MB')).toBe(1024 * 1024);
  expect.soft(bytes('1GB')).toBe(1024 * 1024 * 1024);

  // Different values
  expect.soft(bytes('512B')).toBe(512);
  expect.soft(bytes('2KB')).toBe(2048);
  expect.soft(bytes('5MB')).toBe(5 * 1024 * 1024);
  
  // Case insensitive
  expect.soft(bytes('1kb')).toBe(1024);
  expect.soft(bytes('1Mb')).toBe(1024 * 1024);
  expect.soft(bytes('1gb')).toBe(1024 * 1024 * 1024);
  
  // With whitespace
  expect.soft(bytes(' 1 KB ')).toBe(1024);
  expect.soft(bytes('10 MB')).toBe(10 * 1024 * 1024);
  
  // Edge cases
  expect.soft(bytes('0B')).toBe(0);
  expect.soft(bytes('1000B')).toBe(1000);
});