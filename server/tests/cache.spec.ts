
import { expect, test } from '@playwright/test';
import { Cache, SharedCacheStore } from '../src/common/caches/cache.js';
import { LRUPolicy } from '../src/common/caches/lruPolicy.js';

test.describe('Cache', () => {
  test('should work with basic operations', () => {
    const cache = new Cache(new LRUPolicy<string>(3));

    // Test set/get
    cache.set('key1', 'value1');
    cache.set('key2', 'value2');
    
    expect(cache.get('key1')).toBe('value1');
    expect(cache.get('key2')).toBe('value2');
    expect(cache.get('nonexistent')).toBeUndefined();
    
    expect(cache.size).toBe(2);
    expect(cache.has('key1')).toBe(true);
    expect(cache.has('nonexistent')).toBe(false);
  });

  test('should respect policy eviction', () => {
    const cache = new Cache(new LRUPolicy<string>(2));

    cache.set('a', 'value-a');
    cache.set('b', 'value-b');
    expect(cache.size).toBe(2);

    // Adding third item should evict oldest
    cache.set('c', 'value-c');
    expect(cache.size).toBe(2);
    expect(cache.has('a')).toBe(false);
    expect(cache.has('b')).toBe(true);
    expect(cache.has('c')).toBe(true);
  });

  test('should handle policy rejection', () => {
    const policy = new LRUPolicy<string>(1);
    const cache = new Cache({
      hit: (key: string) => {
        if (key === 'rejected')
          return key;
        return policy.hit(key);
      },
      clear: policy.clear.bind(policy),
      delete: policy.delete.bind(policy),
      has: policy.has.bind(policy),
      victim: policy.victim.bind(policy),
      get size() {
        return policy.size;
      },
    });

    cache.set('key1', 'value1');
    expect(cache.size).toBe(1);

    cache.set('rejected', 'should-not-store');
    expect(cache.size).toBe(1);
    expect(cache.has('rejected')).toBe(false);
    expect(cache.get('rejected')).toBeUndefined();
  });

  test('should update existing keys', () => {
    const policy = new LRUPolicy<string>(3);
    const cache = new Cache(policy);

    cache.set('key', 'value1');
    expect(cache.get('key')).toBe('value1');

    cache.set('key', 'value2');
    expect(cache.get('key')).toBe('value2');
    expect(cache.size).toBe(1);
  });

  test('should handle delete operations', () => {
    const policy = new LRUPolicy<string>(3);
    const cache = new Cache(policy);

    cache.set('key1', 'value1');
    cache.set('key2', 'value2');
    expect(cache.size).toBe(2);

    cache.delete('key1');
    expect(cache.has('key1')).toBe(false);
    expect(cache.get('key1')).toBeUndefined();
    expect(cache.size).toBe(1);
    expect(cache.has('key2')).toBe(true);
  });

  test('should support iteration', () => {
    const policy = new LRUPolicy<string>(5);
    const cache = new Cache(policy);

    cache.set('a', 'value-a');
    cache.set('b', 'value-b');
    cache.set('c', 'value-c');

    const entries = Array.from(cache);
    expect(entries).toHaveLength(3);
    expect(entries).toContainEqual(['a', 'value-a']);
    expect(entries).toContainEqual(['b', 'value-b']);
    expect(entries).toContainEqual(['c', 'value-c']);

    const keys = Array.from(cache.keys());
    expect(keys).toContain('a');
    expect(keys).toContain('b');
    expect(keys).toContain('c');

    const values = Array.from(cache.values());
    expect(values).toContain('value-a');
    expect(values).toContain('value-b');
    expect(values).toContain('value-c');
  });

  test('should work with shared storage', () => {
    const sharedPolicy = new LRUPolicy<number>(3);
    const sharedStore = new SharedCacheStore({ policy: sharedPolicy });

    const policy1 = new LRUPolicy<string>(10);
    const policy2 = new LRUPolicy<string>(10);
    
    const cache1 = new Cache(policy1, sharedStore);
    const cache2 = new Cache(policy2, sharedStore);

    cache1.set('key1', 'value1');
    cache2.set('key2', 'value2');
    cache1.set('key3', 'value3');

    expect(cache1.size).toBe(2);
    expect(cache2.size).toBe(1);
    expect(sharedStore.size).toBe(3);

    // Adding one more should trigger shared eviction
    cache2.set('key4', 'value4');
    
    expect(sharedStore.size).toBe(3);
    // One of the caches should have lost an entry due to shared eviction
    const totalCacheSize = cache1.size + cache2.size;
    expect(totalCacheSize).toBe(3);
  });

  test('should handle external evictions from shared storage', () => {
    const sharedPolicy = new LRUPolicy<number>(2);
    const sharedStore = new SharedCacheStore({ policy: sharedPolicy });

    const policy = new LRUPolicy<string>(10);
    const cache = new Cache(policy, sharedStore);

    cache.set('key1', 'value1');
    cache.set('key2', 'value2');
    expect(cache.size).toBe(2);
    expect(cache.has('key1')).toBe(true);
    expect(cache.has('key2')).toBe(true);

    // Manual eviction from shared store should update cache
    sharedStore.evict();
    
    expect(sharedStore.size).toBe(1);
    expect(cache.size).toBe(1);
    // The evicted key should no longer be tracked by cache policy
    expect(cache.has('key1')).toBe(false);
    expect(cache.get('key1')).toBeUndefined();
  });

  test('should handle admission control with shared storage', () => {
    const sharedPolicy = new LRUPolicy<number>(5);
    let admission = true;
    const sharedStore = new SharedCacheStore({ policy: sharedPolicy, shouldAdmit: (() => admission) });

    const policy = new LRUPolicy<string>(10);
    const cache = new Cache(policy, sharedStore);

    cache.set('key1', 'value1');
    expect(cache.has('key1')).toBe(true);

    // Disable admission
    admission = false;
    
    cache.set('key2', 'value2');
    expect(cache.has('key2')).toBe(false);
    expect(cache.get('key2')).toBeUndefined();
    
    // key1 should still be accessible
    expect(cache.has('key1')).toBe(true);
    expect(cache.get('key1')).toBe('value1');
  });

  test('should handle get operations with policy hits', () => {
    const cache = new Cache(new LRUPolicy<string>(2));

    // Key not in policy should return undefined even if somehow in store
    expect(cache.get('nonexistent')).toBeUndefined();

    cache.set('key1', 'value1');
    cache.set('key2', 'value2');
    
    // Access key1 to make it more recently used
    expect(cache.get('key1')).toBe('value1');
    
    // Add key3, should evict key2 (least recently used)
    cache.set('key3', 'value3');
    
    expect(cache.has('key1')).toBe(true);
    expect(cache.has('key2')).toBe(false);
    expect(cache.has('key3')).toBe(true);
  });

  test('should clear all entries', () => {
    const policy = new LRUPolicy<string>(5);
    const cache = new Cache(policy);

    cache.set('key1', 'value1');
    cache.set('key2', 'value2');
    cache.set('key3', 'value3');
    
    expect(cache.size).toBe(3);
    expect(cache.has('key1')).toBe(true);
    
    cache.clear();
    
    expect(cache.size).toBe(0);
    expect(cache.has('key1')).toBe(false);
    expect(cache.has('key2')).toBe(false);
    expect(cache.has('key3')).toBe(false);
    expect(cache.get('key1')).toBeUndefined();
  });

  test('should clear cache with shared storage', () => {
    const sharedPolicy = new LRUPolicy<number>(5);
    const sharedStore = new SharedCacheStore({ policy: sharedPolicy });

    const policy1 = new LRUPolicy<string>(10);
    const policy2 = new LRUPolicy<string>(10);
    
    const cache1 = new Cache(policy1, sharedStore);
    const cache2 = new Cache(policy2, sharedStore);

    cache1.set('key1', 'value1');
    cache1.set('key2', 'value2');
    cache2.set('key3', 'value3');
    
    expect(cache1.size).toBe(2);
    expect(cache2.size).toBe(1);
    expect(sharedStore.size).toBe(3);
    
    cache1.clear();
    
    expect(cache1.size).toBe(0);
    expect(cache2.size).toBe(1);
    expect(sharedStore.size).toBe(1);
    expect(cache1.has('key1')).toBe(false);
    expect(cache2.has('key3')).toBe(true);
  });
});
