{"include": ["src", "tests"], "exclude": ["lib"], "compilerOptions": {"allowJs": true, "allowSyntheticDefaultImports": true, "checkJs": true, "composite": true, "declarationMap": true, "emitDeclarationOnly": true, "esModuleInterop": true, "experimentalDecorators": true, "isolatedModules": true, "lib": ["esnext", "DOM", "DOM.Iterable"], "module": "NodeNext", "moduleResolution": "NodeNext", "outDir": "./types", "resolveJsonModule": true, "strict": true, "target": "ESNext", "types": ["node"]}, "references": [{"path": "../report"}, {"path": "../database"}, {"path": "../shared"}]}