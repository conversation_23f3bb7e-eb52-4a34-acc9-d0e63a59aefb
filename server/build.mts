#!/usr/bin/env npx kubik

import esbuild from 'esbuild';
import fs from 'fs';
import { Task } from 'kubik';
import path from 'path';

const { __dirname, $ } = Task.init(import.meta, {
  name: 'server',
  watch: [ './src', './package.json', './tests', 'tsconfig.json' ],
  ignore: [ './src/generated' ],
  deps: [
    '../database/build.mts',
    '../shared/build.mts',
    '../report/build.mts',
  ],
});

const outDir = path.join(__dirname, 'lib');
const typesDir = path.join(__dirname, 'types');
const srcDir = path.join(__dirname, 'src');
await fs.promises.rm(outDir, { recursive: true, force: true });
await fs.promises.rm(typesDir, { recursive: true, force: true });

const { errors } = await esbuild.build({
  color: true,
  entryPoints: [
    path.join(srcDir, '**/*.ts'),
  ],
  define: {
    __BUILD_TYPE__: JSON.stringify(process.env.BUILD_TYPE === 'production' ? 'production' : 'development'),
    __FLAKINESS_LICENSE_PUBLIC_KEY__: JSON.stringify(process.env.FLAKINESS_LICENSE_PUBLIC_KEY ?? ''),
  },
  packages: 'external',
  outdir: outDir,
  format: 'esm',
  platform: 'node',
  target: ['node22'],
  sourcemap: true,
  bundle: false,
  minify: false,
});

if (!errors.length)
  await $`tsc --pretty -p .`;
