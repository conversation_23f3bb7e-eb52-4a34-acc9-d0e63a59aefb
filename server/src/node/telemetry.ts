import debug from 'debug';
import express from 'express';
import 'express-async-errors';
import http from 'http';
import Prometheus from 'prom-client';
import { Config } from './configuration.js';

const logTelemetry = debug('fk:telemetry');

// Configure metrics
export const register = new Prometheus.Registry();
register.setDefaultLabels({
  app: 'flakiness-server'
});
Prometheus.collectDefaultMetrics({ register });

export function createCounter<T extends string>(config: Prometheus.CounterConfiguration<T>) {
  const metric = new Prometheus.Counter(config);
  register.registerMetric(metric);
  return metric;
}

export function createHistogram<T extends string>(config: Prometheus.HistogramConfiguration<T>) {
  const metric = new Prometheus.Histogram(config);
  register.registerMetric(metric);
  return metric;
}

export function createGauge<T extends string>(config:Prometheus.GaugeConfiguration<T>) {
  const metric = new Prometheus.Gauge(config);
  register.registerMetric(metric);
  return metric;
}

export function measureMethod(options: { histogram: Prometheus.Histogram<'method'> }) {
  return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value;

    options.histogram.zero({ method: propertyKey });
    descriptor.value = function(...args: any[]) {
      const end = options.histogram.startTimer({
        method: propertyKey,
      });
      let result = originalMethod.apply(this, args);
      if (result instanceof Promise) {
        result = result.then((value: any) => {
          end();
          return value;
        })
      } else {
        end();
      }
      return result;
    };

    return descriptor;
  }
}

export type TelemetryServerOptions = {
  port: number,
}

export class TelemetryServer {
  static async configFromEnv(): Promise<TelemetryServerOptions|undefined> {
    const config = await Config.fromEnvironmentOrDie('Telemetry configuration', async env => ({
      port: await env.integer({
        env: 'TELEMETRY_PORT',
        required: false,
        description: `Port to expose Prometheus telemetry`,
      }),
    }));
    return config.port ? {
      port: config.port,
    } : undefined;
  }

  static async installRoutes(app: express.Express) {
    app.get('/metrics', async (req, res) => {
      res.set('Content-Type', register.contentType);
      res.end(await register.metrics());
    });
  }

  static async create(options: TelemetryServerOptions) {
    const app = express();
    TelemetryServer.installRoutes(app);
    const server = http.createServer(app);
    server.listen(options.port, '127.0.0.1');

    await new Promise<void>(resolve => server.listen(options.port, () => {
      const port = (server.address() as any).port;
      logTelemetry(`Prometheus endpoint is live on http://localhost:${port}/metrics`);
      resolve();
    }));
    return new TelemetryServer(server);
  }

  constructor(private _server: http.Server) {}

  async dispose() {
    await new Promise(x => this._server.close(x));
  }
}
