export const systemNames = new Set([
  // > Your existing list
  'flakiness',
  'legal',
  'login',
  'logout',
  'docs',
  'landing',
  'api',
  'new-organization',
  'administration',
  'settings',
  'cli',
  'device',
  'localreport',
  'report',
  'viewreport',
  'billing',

  // > Authentication & Accounts
  'signin',
  'signout',
  'signup',
  'register',
  'auth',
  'oauth',
  'recover',
  'password',
  'reset',
  'verify',
  'profile',
  'account',
  'user',
  'users',
  'me',
  'dashboard',
  'invites',
  'sessions',

  // > Marketing & Static Pages
  'home',
  'about',
  'contact',
  'help',
  'support',
  'status',
  'pricing',
  'plans',
  'blog',
  'news',
  'press',
  'jobs',
  'careers',
  'team',
  'terms',
  'privacy',
  'security',
  'cookies',
  'compliance',
  'gdpr',
  'sitemap',
  'robots',
  
  // > Technical & Assets
  'app',
  'assets',
  'static',
  'public',
  'images',
  'img',
  'css',
  'js',
  'fonts',
  'dist',
  'build',
  'uploads',
  'downloads',
  'webhook',
  'webhooks',
  'health',
  'healthcheck',
  'well-known', // Standard for SSL/Apple associations
  'manifest',
  'favicon',

  // > Common App Features (Future-proofing)
  'search',
  'explore',
  'notifications',
  'messages',
  'inbox',
  'projects',
  'teams',
  'organizations',
  'orgs',
  'admin',
  'staff',
  'moderator',
  'superuser',
  'stats',
  'analytics',
  'integrations',
  'marketplace',
]);

export const highProfileNames = new Set([
  // > The Big Tech (FAANG+)
  'google',
  'alphabet',
  'facebook',
  'meta',
  'amazon',
  'aws',
  'apple',
  'microsoft',
  'netflix',
  'tesla',
  'spacex',
  'nvidia',
  'openai',
  'oracle',
  'ibm',
  'intel',
  'adobe',
  'salesforce',

  // > Dev Tools & Platforms (Your peers/competitors)
  'github',
  'gitlab',
  'bitbucket',
  'atlassian',
  'docker',
  'kubernetes',
  'vercel',
  'netlify',
  'heroku',
  'stripe',
  'shopify',
  'spotify',
  'slack',
  'discord',
  'zoom',
  'twilio',
  'cloudflare',
  'circleci',
  'jenkins',
  'datadog',
  'sentry',
  'newrelic',
  'grafana',
  'hashicorp',
  
  // > Open Source & Foundations
  'linux',
  'gnu',
  'apache',
  'mozilla',
  'python',
  'node',
  'nodejs',
  'javascript',
  'typescript',
  'rust',
  'go',
  'golang',
  'react',
  'vue',
  'angular',
  'git',

  // > Socials
  'twitter',
  'x',
  'instagram',
  'linkedin',
  'tiktok',
  'youtube',
  'twitch',
  'reddit',
  'wikipedia',
]);