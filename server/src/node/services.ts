import { Database, DatabaseConfig } from "@flakiness/database";
import path from 'path';
import { CachedDatabase } from "./cachedDatabase.js";
import { Config } from "./configuration.js";
import { CronService } from "./cronService.js";
import { GitWorker } from "./gitWorker.js";
import { GithubApp, GithubAppConfig } from "./integrations/githubApp.js";
import { License } from "./license.js";
import { ManagedMemoryCache } from "./managedMemoryCache.js";
import { QueryService } from "./queryService.js";
import { S3, S3Bucket, S3Config } from "./s3.js";
import { S3GarbageCollector } from "./s3GarbageCollector.js";
import { S3Objects } from "./s3object.js";
import { ServerTiming } from "./serverTiming.js";
import { StatsWorker } from "./statsWorker.js";
import { StripeApp, StripeConfig } from "./stripeApp.js";
import { UploadWorker } from "./uploadWorker.js";
import { XNotify } from "./xnotify.js";

// This is a collection of all services required to run flakiness.io and that should exist on both
// Main thread (the one serving requests) and background thread (the one processing work queues).
// Note taht QueryService for this reason is NOT a part of "services".

export type ServicesConfig = {
  dbConfig: DatabaseConfig,
  s3config: S3Config,
  licenseKey: string,
  githubAppConfig: GithubAppConfig,
  stripeConfig?: StripeConfig,
  hddCache: {
    rootDir?: string,
    maxStatsSizeMB?: number,
  }
};

export class Services {
  static async initializeOrDie(options: ServicesConfig) {
    const serverTiming = new ServerTiming();
    const managedMemoryCache = ManagedMemoryCache.createWithDefaultSettings();
    const license = await License.initializeOrDie(options.licenseKey);
    const s3data = await S3.initialize(options.s3config);
    const xnotify = new XNotify(options.dbConfig);
    const db = new CachedDatabase(await Database.initialize(options.dbConfig), xnotify, serverTiming);

    const githubApp = new GithubApp(options.githubAppConfig);
    const cron = new CronService(db);
    const s3objects = new S3Objects(s3data);
    const s3gc = new S3GarbageCollector(s3data, s3objects, db);

    const statsWorker = await StatsWorker.create({
      memoryCache: managedMemoryCache.sharedCacheStore(),
      s3objects,
      db,
      xnotify,
      serverTiming,
      hddCache: options.hddCache.maxStatsSizeMB && options.hddCache.rootDir ? {
        dir: path.join(options.hddCache.rootDir, 'stats-cache'),
        maxSizeBytes: options.hddCache.maxStatsSizeMB * 1024 * 1024,
      } : undefined,
    });
    const gitWorker = new GitWorker(managedMemoryCache.sharedCacheStore(), s3objects, githubApp, db.uncachedDatabase(), xnotify, serverTiming);
    const uploadWorker = new UploadWorker(managedMemoryCache.sharedCacheStore(), cron, db, s3objects, statsWorker, gitWorker, s3gc, xnotify, serverTiming);
    const stripeApp = options.stripeConfig ? new StripeApp(uploadWorker, db, cron, xnotify, options.stripeConfig, serverTiming) : undefined;
    //TODO: unfortunate cyclic dependency uploadWorker <-> stripe
    uploadWorker.setStripeApp(stripeApp);
    //TODO: unfortunate cyclic dependency gitWorker <-> uploadWorker
    gitWorker.setUploadWorker(uploadWorker);

    const queryService = new QueryService(
      managedMemoryCache.sharedCacheStore(),
      serverTiming,
      ppid => uploadWorker.reportIndex(ppid),
      s3id => statsWorker.statsAnalyzer(s3id),
      ppid => gitWorker.getRepo(ppid),
    );

    const services = new Services(
      managedMemoryCache,
      serverTiming,
      license,
      s3data,
      xnotify,
      db,
      githubApp,
      s3gc,
      statsWorker,
      gitWorker,
      uploadWorker,
      s3objects,
      queryService,
      cron,
      stripeApp,
    );
    await services.initialize();
    return services;
  }

  static async configFromEnvOrDie(): Promise<ServicesConfig> {
    return {
      dbConfig: await CachedDatabase.configFromEnvOrDie(),
      githubAppConfig: await GithubApp.configFromEnvOrDie(),
      licenseKey: await License.licenseKeyFromEnvOrDie(),
      s3config: await S3.configFromEnvOrDie(),
      stripeConfig: await StripeApp.configFromEnv(),
      hddCache: await Config.fromEnvironmentOrDie('HDD Cache', async env => ({
        rootDir: await env.path({
          env: 'HDD_CACHE_ROOTDIR',
          description: ``,
          required: false,
        }),
        maxStatsSizeMB: await env.integer({
          env: 'HDD_CACHE_STATS_MB',
          required: false,
        }),
      })),
    }
  }

  constructor(
    readonly managedMemoryCache: ManagedMemoryCache,
    readonly serverTiming: ServerTiming,
    readonly license: License,
    readonly s3data: S3Bucket,
    readonly xnotify: XNotify,
    readonly db: CachedDatabase,
    readonly githubApp: GithubApp,
    readonly s3gc: S3GarbageCollector,
    readonly statsWorker: StatsWorker,
    readonly gitWorker: GitWorker,
    readonly uploadWorker: UploadWorker,
    readonly s3objects: S3Objects,
    readonly query: QueryService,
    readonly cron: CronService,
    readonly stripeApp?: StripeApp,
  ) {
  }

  async initialize() {
    await this.uploadWorker.initialize();
    await this.stripeApp?.initialize();

    // Drop all dead projects
    await this.s3gc.collectDeadProjects();
  }

  async startBackgroundProcessing(workerName: string) {
    await this.s3gc.start(workerName);
    await this.statsWorker.start(workerName);
    await this.gitWorker.start(workerName);
    await this.uploadWorker.start(workerName);
    await this.cron.start(workerName);
  }

  async stopBackgroundProcessing() {
    await this.s3gc.stop();
    await this.statsWorker.stop();
    await this.gitWorker.stop();
    await this.uploadWorker.stop();
    await this.cron.stop();
  }

  async dispose() {
    await this.managedMemoryCache.dispose();
    await this.xnotify.close();
    await this.db.close();
  }
}