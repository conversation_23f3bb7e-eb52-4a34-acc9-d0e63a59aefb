import { mb } from '@flakiness/shared/common/utils.js';
import assert from 'assert';
import debug from 'debug';
import * as fs from 'fs/promises';
import * as path from 'path';
import { HeapSet } from '../common/heapSet.js';

const log = debug('fk:hdd_cache');

interface CacheEntry<META> {
  fsKey: string;
  meta: META;
  size: number; 
  lastAccessed: number;
}

const DATA_NAME = 'data.bin';
const META_NAME = 'meta.json';

function getBufferPath(dataPath: string, fsKey: string) {
  return path.join(dataPath, fsKey, DATA_NAME);
}

function getMetaPath(dataPath: string, fsKey: string) {
  return path.join(dataPath, fsKey, META_NAME);
}

export class HDDCache<KEY, META> {
  public static async create<KEY, META>(
    rootDir: string,
    keyFn: (key: KEY) => string,
    maxCacheSize: number,
  ): Promise<HDDCache<KEY, META>> {

    const dataDir = path.join(rootDir, 'data');
    const tmpDir = path.join(rootDir, 'tmp');
    await fs.mkdir(dataDir, { recursive: true });

    const map = new Map<string, CacheEntry<META>>();
    let currentSize = 0;

    const entries = await fs.readdir(dataDir, { withFileTypes: true });    
    for (const entry of entries) {
      if (!entry.isDirectory())
        continue;
      const fsKey = entry.name;
      const entryPath = path.join(dataDir, fsKey);
      try {
        const meta = JSON.parse(await fs.readFile(getMetaPath(dataDir, fsKey), 'utf-8')) as META;
        const stats = await fs.stat(getBufferPath(dataDir, fsKey));
        map.set(fsKey, {
          fsKey,
          meta,
          size: stats.size,
          // On certain systems, atime doesn't update well.
          lastAccessed: stats.atimeMs || stats.mtimeMs
        });
        currentSize += stats.size;
      } catch (e) {
        // If an entry folder is corrupted (missing meta or buffer), nuke the folder
        await fs.rm(entryPath, { recursive: true, force: true }).catch(() => {});
      }
    }

    const instance = new HDDCache(dataDir, tmpDir, keyFn, maxCacheSize, map, currentSize);
    await instance._enforceCapacity();
    return instance;
  }

  private _currentSize: number;
  private _map: Map<string, CacheEntry<META>>;
  private _heapSet: HeapSet<string, number>;

  private constructor(
    private _dataDir: string,
    private _tmpDir: string,
    private _key: (key: KEY) => string,
    private _maxCacheSize: number,
    initialMap: Map<string, CacheEntry<META>>,
    initialSize: number
  ) {
    this._map = initialMap;
    this._currentSize = initialSize;
    this._heapSet = HeapSet.createMin(Array.from(this._map.values(), e => [e.fsKey, e.lastAccessed]));
    log(`Initialized HDD cache in ${this._dataDir} of size ${mb(this._currentSize)}`);
  }

  getMetadata(key: KEY, signal?: AbortSignal): META | undefined {
    signal?.throwIfAborted();

    const fsKey = this._toFsKey(key);
    return this._map.get(fsKey)?.meta;
  }

  async getBuffer(key: KEY, signal?: AbortSignal): Promise<{ buffer: Buffer; metadata: META } | undefined> {
    signal?.throwIfAborted();
    log(`getBuffer: ${key}`);

    const fsKey = this._toFsKey(key);
    const entry = this._map.get(fsKey);
    if (!entry)
      return undefined;

    try {
      const buffer = await fs.readFile(getBufferPath(this._dataDir, fsKey), { signal });
      const now = Date.now();
      entry.lastAccessed = now;
      this._heapSet.add(fsKey, now);
      // Explicitly update all times for the file, since certain Linux distributions
      // do NOT update file modifiers on access.
      await fs.utimes(getBufferPath(this._dataDir, fsKey), now, now).catch(() => {});
      return { buffer, metadata: entry.meta };
    } catch (e: any) {
      if (e.code === 'ENOENT') {
        this._map.delete(fsKey);
        this._heapSet.delete(fsKey);
        this._currentSize -= entry.size;
        return undefined;
      }
      throw e;
    }
  }

  async saveBuffer(key: KEY, data: { buffer: Buffer; metadata: META }, signal?: AbortSignal) {
    signal?.throwIfAborted();

    const fsKey = this._toFsKey(key);
    const finalFolderPath = path.join(this._dataDir, fsKey);
    const tmpFolderPath = path.join(this._tmpDir, fsKey);
    try {
      // Write files in temp
      await fs.rm(tmpFolderPath, { recursive: true, force: true }).catch(() => {});
      await fs.mkdir(tmpFolderPath, { recursive: true });
      await fs.writeFile(getBufferPath(this._tmpDir, fsKey), data.buffer, { signal });
      await fs.writeFile(getMetaPath(this._tmpDir, fsKey), JSON.stringify(data.metadata), { signal });
      signal?.throwIfAborted();

      // Handle entry replacement diff.
      const existingEntry = this._map.get(fsKey);
      const newSize = data.buffer.byteLength;
      const sizeDiff = newSize - (existingEntry?.size || 0);

      // Remove the existing entry, if any.
      this._map.delete(fsKey);
      this._heapSet.delete(fsKey);
      await fs.rm(finalFolderPath, { recursive: true, force: true }).catch(() => {});

      // Write the new entry inplace.
      await fs.rename(tmpFolderPath, finalFolderPath);
      const now = Date.now();
      const entry: CacheEntry<META> = {
        fsKey,
        meta: data.metadata,
        size: newSize,
        lastAccessed: now,
      }
      this._map.set(fsKey, entry);
      this._heapSet.add(fsKey, now);
      this._currentSize += sizeDiff;
      await this._enforceCapacity(signal);
    } catch (error) {
      await fs.rm(tmpFolderPath, { recursive: true, force: true }).catch(() => {});
      throw error;
    }
  }

  private _toFsKey(key: KEY): string {
    return encodeURIComponent(this._key(key));
  }

  private async _enforceCapacity(signal?: AbortSignal) {
    let start = this._currentSize;
    while (this._currentSize > this._maxCacheSize) {
      const fsKey = this._heapSet.pop();
      const entry = fsKey ? this._map.get(fsKey) : undefined;
      assert(entry);
      const folderPath = path.join(this._dataDir, entry.fsKey);
      await fs.rm(folderPath, { recursive: true, force: true }).catch(() => {});
      signal?.throwIfAborted();
      this._currentSize -= entry.size;
      this._map.delete(entry.fsKey);
    }
    if (this._currentSize !== start) {
      log(`Pruned ${mb(start - this._currentSize)}`)
    }
  }
}
