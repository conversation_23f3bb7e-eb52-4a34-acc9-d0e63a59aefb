import { ProjectPublicId } from "@flakiness/database";
import { FlakinessReport, ReportUtils } from "@flakiness/report";
import { Multimap } from "@flakiness/shared/common/multimap.js";
import { randomUUID, xxHashObject } from "@flakiness/shared/common/utils.js";
import assert from "assert";
import ms from "ms";
import { Ranges } from "../common/ranges.js";
import { Stats } from "../common/stats/stats.js";
import { TestIndex } from "../common/stats/testIndex.js";
import { WireTypes } from "../common/wireTypes.js";
import { Git } from "./git.js";
import { S3Report, S3Stats } from "./s3layout.js";

export namespace ReportIndex {
  // TODO: all clients have to compare JSON version with REPORT_INDEX_VERSION
  // before creating this object. This is error-prone; we should unify this.
  export const REPORT_INDEX_VERSION = 8;

  export type JSONReportIndex = {
    version: number,
    tests: Stats.JSONTests,
    shards: JSONShard[];
    dailyRuns: JSONDayRuns[],
    unparsedReports: JSONUnparsedReport[],
  }

  export type JSONUnparsedReport = {
    runId: Stats.RunId,
    error: string,
  }

  export type JSONDayRuns = {
    dayTimestampMs: FlakinessReport.UnixTimestampMS,
    runs: Ranges.CompressedRanges<Stats.RunId>,
    reportBytes: number,
    attachmentBytes: number,
    testRunsCount: number,
  }

  export type JSONShard = {
    shardId: S3Stats.ShardId,
    commits: JSONCommit[];
  }

  export type JSONCommit = {
    commitId: FlakinessReport.CommitId,
    relatedCommitIds?: FlakinessReport.CommitId[],
    runs: Ranges.CompressedRanges<Stats.RunId>,
  }

  type ParsedDayRuns = {
    dayTimestampMs: FlakinessReport.UnixTimestampMS,
    runs: Ranges.Ranges<Stats.RunId>,
    reportBytes: number,
    attachmentBytes: number,
    testRunsCount: number,
  }

  type ParsedShard = {
    shardId: S3Stats.ShardId,
    commits: Map<FlakinessReport.CommitId, ParsedCommit>;
  }

  type ParsedCommit = {
    commitId: FlakinessReport.CommitId,
    runs: Ranges.Ranges<Stats.RunId>,
  }

  export class ReportIndex {
    private _shards = new Map<S3Stats.ShardId, ParsedShard>();
    private _dailyRuns = new Map<FlakinessReport.UnixTimestampMS, ParsedDayRuns>();
    private _commitIdToShard = new Map<Stats.CommitId, ParsedShard>();
    private _unparsedReportErrors = new Map<Stats.RunId, string>();
    private _allRunIds: Ranges.Ranges<Stats.RunId>;
    private _testIndex: TestIndex;

    constructor(private _projectPublicId: ProjectPublicId, json: JSONReportIndex|undefined) {
      // We should not try to work with data that has a version from future.
      assert(!json || json.version <= REPORT_INDEX_VERSION);
      // If JSON's version is less than our version, than bail out.
      if (json && json.version < REPORT_INDEX_VERSION)
        json = undefined;
      for (const jsonShard of json?.shards ?? []) {
        const shard: ParsedShard = {
          shardId: jsonShard.shardId,
          commits: new Map(),
        }
        for (const jsonCommit of jsonShard.commits) {
          this._commitIdToShard.set(jsonCommit.commitId, shard);
          shard.commits.set(jsonCommit.commitId, {
            commitId: jsonCommit.commitId,
            runs: Ranges.decompress(jsonCommit.runs),
          });
        }
        this._shards.set(shard.shardId, shard);
      }
      for (const dayRuns of json?.dailyRuns ?? []) {
        this._dailyRuns.set(dayRuns.dayTimestampMs, {
          dayTimestampMs: dayRuns.dayTimestampMs,
          runs: Ranges.decompress(dayRuns.runs),
          reportBytes: dayRuns.reportBytes,
          attachmentBytes: dayRuns.attachmentBytes,
          testRunsCount: dayRuns.testRunsCount,
        });
      }
      for (const unparsedReport of json?.unparsedReports ?? [])
        this._unparsedReportErrors.set(unparsedReport.runId, unparsedReport.error);

      this._testIndex = new TestIndex(json?.tests ?? Stats.createEmptyTests());
      this._allRunIds = Ranges.unionAll([...this._dailyRuns.values()].map(parsed => parsed.runs));
    }

    testIndex(): TestIndex {
      return this._testIndex;
    }

    allRunIds(): Ranges.Ranges<Stats.RunId> {
      return this._allRunIds;
    }

    unparsedReportErrors(): Map<Stats.RunId, string> {
      return this._unparsedReportErrors;
    }

    allCommitIds() {
      return Array.from(this._commitIdToShard.keys());
    }

    findCommitId(runId: Stats.RunId): Stats.CommitId | undefined {
      for (const shard of this._shards.values()) {
        for (const commit of shard.commits.values()) {
          if (Ranges.includes(commit.runs, runId))
            return commit.commitId;
        }
      }
      return undefined;
    }

    serialize(): JSONReportIndex {
      return {
        version: REPORT_INDEX_VERSION,
        unparsedReports: [...this._unparsedReportErrors].map(([runId, error]) => ({ runId, error })),
        dailyRuns: [...this._dailyRuns.values()].map(parsedDay => ({
          dayTimestampMs: parsedDay.dayTimestampMs,
          runs: Ranges.compress(parsedDay.runs),
          reportBytes: parsedDay.reportBytes,
          attachmentBytes: parsedDay.attachmentBytes,
          testRunsCount: parsedDay.testRunsCount,
        })),
        shards: [...this._shards.values()].map(parsedShard => ({
          shardId: parsedShard.shardId,
          commits: [...parsedShard.commits.values()].map(parsedCommit => ({
            commitId: parsedCommit.commitId,
            runs: Ranges.compress(parsedCommit.runs),
          })),
        })),
        tests: this._testIndex.serialize(),
      }
    }

    private _toStatId(shardId: S3Stats.ShardId): S3Stats.Id {
      return {
        projectPublicId: this._projectPublicId,
        shardId,
      };
    }

    hasRun(reportId: Stats.RunId) {
      return Ranges.includes(this._allRunIds, reportId);
    }

    // Returns a shard that has this commitId, if any - otherwise, returns nothing.
    getShard(commitId: FlakinessReport.CommitId): S3Stats.Id|undefined {
      const shard = this._commitIdToShard.get(commitId);
      return shard ? this._toStatId(shard.shardId) : undefined;
    }

    splitIntoShards(commits: Git.Commit[]): Multimap<S3Stats.Id, Git.Commit> {
      const shardToCommits = new Multimap<S3Stats.ShardId, Git.Commit>();
      for (const commit of commits) {
        const shard = this._commitIdToShard.get(commit.commitId);
        if (shard !== undefined)
          shardToCommits.set(shard.shardId, commit);
      }
      const result = new Multimap<S3Stats.Id, Git.Commit>();
      for (const [shardIndex, commits] of shardToCommits) {
        const statId = this._toStatId(shardIndex);
        result.setAll(statId, commits);
      }
      return result;
    }

    allShards(): S3Stats.Id[] {
      return Array.from(this._shards.keys(), shardId => this._toStatId(shardId));
    }

    getLastShard(): S3Stats.Id|undefined {
      if (!this._shards.size)
        return undefined;
      const lastShardId = [...this._shards.keys()].pop()!;
      return this._toStatId(lastShardId);
    }

    shardS3Reports(shardId: S3Stats.Id): S3Report.Id[] {
      const shard = this._shards.get(shardId.shardId);
      if (!shard)
        return [];
      const reports = Ranges.unionAll([...shard.commits.values()].map(commit => commit.runs));
      return Ranges.toSortedList(reports).map(reportId => ({
        projectPublicId: this._projectPublicId,
        reportId
      }));
    }

    commitRuns(commitId: FlakinessReport.CommitId): Ranges.Ranges<Stats.RunId> {
      const shard = this._commitIdToShard.get(commitId);
      const commit = shard?.commits.get(commitId);
      return commit ? commit.runs : Ranges.EMPTY as Ranges.Ranges<Stats.RunId>;
    }

    metrics(): WireTypes.DailyMetrics[] {
      return Array.from(this._dailyRuns.values()).map(parsedDay => ({
        dayTimestampMs: parsedDay.dayTimestampMs,
        runsCount: Ranges.cardinality(parsedDay.runs),
        testsCount: parsedDay.testRunsCount,
        totalBytes: parsedDay.attachmentBytes + parsedDay.reportBytes,
      })).sort((a, b) => a.dayTimestampMs - b.dayTimestampMs);
    }

    createShard(): S3Stats.Id {
      const newShard: ParsedShard = {
        // prefix with timestamp to order shards by creation time. Might help with debugging later.
        shardId: Date.now() + '-' + randomUUID() as S3Stats.ShardId,
        commits: new Map(),
      };
      this._shards.set(newShard.shardId, newShard);
      return this._toStatId(newShard.shardId);
    }

    hasShard(shardId: S3Stats.Id): boolean {
      return this._shards.has(shardId.shardId);
    }

    etag() {
      return xxHashObject({
        runs: Ranges.toString(this._allRunIds),
        version: REPORT_INDEX_VERSION,
      });
    }

    listRunsSince(sinceTimestampMs: number): Ranges.Ranges<Stats.RunId> {
      const days = [...this._dailyRuns.values()].filter(report => report.dayTimestampMs >= sinceTimestampMs);
      return Ranges.unionAll(days.map(day => day.runs));
    }

    enforceDataRetention(dataRetentionDays: number): { reportsToDelete: Ranges.Ranges<Stats.RunId>, shardsToRebuild: S3Stats.Id[] } {
      if (Object.is(dataRetentionDays, Infinity)) {
        return {
          reportsToDelete: Ranges.EMPTY as Ranges.Ranges<Stats.RunId>,
          shardsToRebuild: [],
        };
      }
      const timestamp = utcDayStart(Date.now()) - ms('1 day') * dataRetentionDays;
      const daysToRemove = [...this._dailyRuns.values()].filter(report => report.dayTimestampMs < timestamp);
      const shardsToRecompute = new Set<S3Stats.ShardId>();
      const reportsToBeRemoved = Ranges.unionAll(daysToRemove.map(day => day.runs));

      for (const day of daysToRemove)
        this._dailyRuns.delete(day.dayTimestampMs);

      for (const [shardIndex, shard] of this._shards) {
        let isShardChanged = false;
        for (const [commitId, commit] of shard.commits) {
          if (!Ranges.isIntersecting(commit.runs, reportsToBeRemoved))
            continue;
          isShardChanged = true;
          commit.runs = Ranges.subtract(commit.runs, reportsToBeRemoved);
          if (!commit.runs.length) {
            shard.commits.delete(commitId);
            this._commitIdToShard.delete(commitId);
          }
        }
        if (!shard.commits)
          this._shards.delete(shardIndex);
        if (isShardChanged)
          shardsToRecompute.add(shardIndex);
      }

      for (const reportId of this._unparsedReportErrors.keys()) {
        if (Ranges.includes(reportsToBeRemoved, reportId))
          this._unparsedReportErrors.delete(reportId);
      }

      this._allRunIds = Ranges.subtract(this._allRunIds, reportsToBeRemoved);
      return {
        reportsToDelete: reportsToBeRemoved,
        shardsToRebuild: [...shardsToRecompute].map(shardIdx => this._toStatId(shardIdx)),
      };
    }

    addFaultyReport(options: {
      reportId: Stats.RunId,
      error: string,
      uploadTimestampMs: FlakinessReport.UnixTimestampMS,
      reportBytes: number,
      attachmentBytes: number,
    }) {
      this._unparsedReportErrors.set(options.reportId, options.error);
      this._addDayStats({
        attachmentBytes: options.attachmentBytes,
        reportBytes: options.reportBytes,
        reportId: options.reportId,
        testRunsCount: 0,
        uploadTimestampMs: options.uploadTimestampMs,
      });
    }

    addReport(options: {
      shard: S3Stats.Id,
      reportId: Stats.RunId,
      report: FlakinessReport.Report,
      uploadTimestampMs: FlakinessReport.UnixTimestampMS,
      reportBytes: number,
      attachmentBytes: number,
    }) {
      const shard = this._shards.get(options.shard.shardId);
      assert(shard);
      let commit = shard.commits.get(options.report.commitId);
      if (!commit) {
        commit = {
          commitId: options.report.commitId,
          runs: Ranges.EMPTY as Ranges.Ranges<Stats.RunId>,
        };
        shard.commits.set(options.report.commitId, commit);
        this._commitIdToShard.set(options.report.commitId, shard);
      }
      commit.runs = Ranges.union(commit.runs, Ranges.fromList([options.reportId]))
      // Add test to tests.
      let testRunsCount = 0;
      ReportUtils.visitTests(options.report, (test, parentSuites) => {
        ++testRunsCount;
      });
      this._testIndex.addReport(options.report);

      this._addDayStats({
        reportId: options.reportId,
        attachmentBytes: options.attachmentBytes,
        reportBytes: options.reportBytes,
        testRunsCount,
        uploadTimestampMs: options.uploadTimestampMs,
      });
    }

    private _addDayStats(options: {
      reportId: Stats.RunId,
      uploadTimestampMs: FlakinessReport.UnixTimestampMS,
      reportBytes: number,
      attachmentBytes: number,
      testRunsCount: number,
    }) {
      this._allRunIds = Ranges.union(this._allRunIds, Ranges.from(options.reportId));
      const dayMs = utcDayStart(options.uploadTimestampMs);
      let parsedDay = this._dailyRuns.get(dayMs);
      if (!parsedDay) {
        parsedDay = {
          dayTimestampMs: dayMs,
          runs: Ranges.EMPTY as Ranges.Ranges<Stats.RunId>,
          attachmentBytes: 0,
          reportBytes: 0,
          testRunsCount: 0,
        };
        this._dailyRuns.set(dayMs, parsedDay);
      }
      parsedDay.runs = Ranges.union(parsedDay.runs, Ranges.fromList([options.reportId]))
      parsedDay.reportBytes += options.reportBytes;
      parsedDay.attachmentBytes += options.attachmentBytes;
      parsedDay.testRunsCount += options.testRunsCount;
    }
  }
}

export function utcDayStart(timestamp: number): FlakinessReport.UnixTimestampMS {
  const date = new Date(timestamp);
  return Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate()) as FlakinessReport.UnixTimestampMS;
}
