import { FlakinessReport } from "@flakiness/report";
import { Multimap } from "@flakiness/shared/common/multimap.js";
import { xxHash, xxHashObject } from "@flakiness/shared/common/utils.js";
import debug from "debug";
import { HeapSet } from "../common/heapSet.js";

const log = debug('fk:git');

export namespace Git {
  export class RateLimitError extends Error {
    constructor(public readonly rateLimitUntil: Date) {
      super();
    }
  }

  export class HTTPError extends Error {
    constructor(public readonly status: number) {
      super();
    }
  }

  export interface Provider {
    listCommits(options: {
      sha: string,
      since?: Date,
    }, signal: AbortSignal|undefined): AsyncGenerator<Commit[]>;
    listPullRequests(options: {
      sort: 'created'|'updated',
      since?: Date,
    }, signal: AbortSignal|undefined): AsyncGenerator<PullRequest[]>;
    listBranches(signal: AbortSignal|undefined): AsyncGenerator<{ name: string, sha: string }[]>;
    defaultBranch(signal: AbortSignal|undefined): Promise<string>;
  }

  export type Commit = {
    commitId: FlakinessReport.CommitId,
    timestamp: FlakinessReport.UnixTimestampMS, // ms
    message: string,
    avatar_url?: string,
    author?: string,
    parents: FlakinessReport.CommitId[],
    walkIndex: number,
  }

  type RefType = 'branch'|'commit';

  export type Ref = {
    type: RefType,
    name: string,
    commit: Commit,  
  }

  export type PullRequest = {
    url: string,
    headSha: string;
    baseSha: string;
    description: string;
    avatar_url?: string;
    author?: string;
    state: 'open' | 'closed' | 'merged',
    number: number;
    title: string;
    createdTimestamp: FlakinessReport.UnixTimestampMS;
    updatedTimestamp: FlakinessReport.UnixTimestampMS;
  }

  type JSONPullRequest = {
    url: string,
    headSha: string;
    baseSha: string;
    description: string;
    avatar_url?: string;
    author?: string;
    state: 'open' | 'closed' | 'merged',
    number: number;
    title: string;
    createdTimestamp: FlakinessReport.UnixTimestampMS;
    updatedTimestamp: FlakinessReport.UnixTimestampMS;
    lastTestedCommit?: string;
  }

  type JSONCommit = {
    commitId: FlakinessReport.CommitId,
    timestamp: FlakinessReport.UnixTimestampMS, // ms
    message: string,
    avatar_url?: string,
    author?: string,
  }

  export type JSONRepo = {
    version: number,
    heads: {
      type: 'branch',
      name: string,
      commitIndex: number,
    }[];
    defaultBranch: string,
    commits: JSONCommit[];
    strands: number[][],
    // Sometimes we get reports for commits that were created only temporary,
    // i.e. merge commits for pull request branches.
    // These merge commits are temporary, they are recreated every once-in-a-while,
    // and upstream provider might even collect them later.
    // We, however, would like to retain them, since we have
    // reports that rely upon them.
    orphanedCommitIndexes?: number[],
    pullRequests?: JSONPullRequest[],
  }

  class CommitIterator {
    private _walkIndex = 0;
    private _frontier = HeapSet.createMax<JSONCommit>();
    
    constructor(
      private _historyCutoff: number,
      private _parentSha = new Multimap<string, string>(),
      private _shaToCommit = new Map<string, JSONCommit>(),
      sha: string|undefined,
    ) {
      const commit = sha ? this._shaToCommit.get(sha) : undefined;
      if (commit)
        this._frontier.add(commit, commit.timestamp);
    }

    collect(options: {
      headOffset?: number,
      maxCount?: number,
      sinceTimestamp?: number,
      untilTimestamp?: number,
    }): Commit[] {
      const result: Commit[] = [];
      const { maxCount = Infinity, headOffset = 0 } = options;

      const sinceTimestamp = options.sinceTimestamp ?? -Infinity;
      const untilTimestamp = options.untilTimestamp ?? Infinity;

      const visited = new Set<string>();
      const toBeAdded: JSONCommit[] = [];
      while (this._frontier.size && result.length < maxCount + headOffset) {
        const commit = this._frontier.pop()!;
        if (visited.has(commit.commitId))
          continue;
        visited.add(commit.commitId);

        if (commit.timestamp < this._historyCutoff)
          continue;

        if (commit.timestamp < sinceTimestamp) {
          toBeAdded.push(commit);
          continue;
        }
        const parents = this._parentSha.getAll(commit.commitId);
        if (sinceTimestamp <= commit.timestamp && commit.timestamp < untilTimestamp) {
          result.push({
            ...commit,
            parents: parents as FlakinessReport.CommitId[],
            walkIndex: this._walkIndex++,
          });
        }
        for (const parentId of parents) {
          const parentCommit = this._shaToCommit.get(parentId);
          if (parentCommit)
            this._frontier.add(parentCommit, parentCommit.timestamp);
        }
      }
      for (const jsonCommit of toBeAdded)
        this._frontier.add(jsonCommit, jsonCommit.timestamp);

      return headOffset ? result.slice(headOffset) : result;
    }
  }

  export class Repository {
    static DATA_VERSION = 1;

    static deserialize(json: JSONRepo | undefined, historyCutoff: number) {
      if (json?.version === this.DATA_VERSION) {
        return new Repository(json, historyCutoff);
      }
      return undefined;
    }

    static createEmpty(historyCutoff: number) {
      return new Repository({
        version: this.DATA_VERSION,
        defaultBranch: '',
        heads: [],
        commits: [],
        strands: [],
        pullRequests: [],
      }, historyCutoff);
    }

    serialize(): JSONRepo {
      const result: Required<JSONRepo> = {
        version: Repository.DATA_VERSION,
        defaultBranch: this._defaultBranch,
        heads: [],
        commits: [],
        strands: [],
        orphanedCommitIndexes: [],
        pullRequests: Array.from(this._pullRequests.values()),
      };

      const commitsToIndex = new Map<string, number>();
      const writeStrand = (sha: string, strand: number[] = []) => {
        let wp: string | undefined = sha;
        while (wp) {
          const commit = this._shaToCommit.get(wp);
          if (!commit)
            break;

          let commitIndex = commitsToIndex.get(commit.commitId);
          if (commitIndex === undefined) {
            result.commits.push({
              commitId: commit.commitId,
              message: commit.message,
              timestamp: commit.timestamp,
              author: commit.author,
              avatar_url: commit.avatar_url,
            });
            commitIndex = result.commits.length - 1;
            commitsToIndex.set(commit.commitId, commitIndex);
            strand.push(commitIndex);
          } else {
            // We've encountered a seen commit. Abort building this strand.
            strand.push(commitIndex);
            break;
          }
          // If the commit's timestamp is over CUTOFF_DATE, then bail out.
          // We actually made sure to record it first, so that
          // we will NOT fetch data in future for this tail.
          if (commit.timestamp < this._historyCutoffTimestamp)
            break;

          // we have an unseen commit here! Get its parents.
          const parents = this._parentSha.getAll(wp);
          if (parents.length === 0)
            break;

          wp = parents[0];
          for (let i = 1; i < parents.length; ++i)
            writeStrand(parents[i], [commitIndex]);
        }
        if (strand.length > 1)
          result.strands.push(strand);
        return commitsToIndex.get(sha)!;
      }

      for (const [branchName, sha] of this._branchNameToSha) {
        result.heads.push({
          type: 'branch',
          name: branchName,
          commitIndex: writeStrand(sha),
        });
      }
      for (const sha of this._orphanedCommits) {
        // If we iterated this commit as part of some branch,
        // then it is no longer orphaned.
        if (commitsToIndex.has(sha))
          continue;
        result.orphanedCommitIndexes.push(writeStrand(sha));
      }
      return result;
    }

    private _parentSha = new Multimap<string, string>();
    private _branchNameToSha = new Map<string, string>();
    private _orphanedCommits = new Set<string>();
    private _shaToCommit = new Map<string, JSONCommit>();
    private _defaultBranch: string;
    private _etag?: string;
    private _pullRequests = new Map<number, JSONPullRequest>();

    constructor(json: JSONRepo, private _historyCutoffTimestamp: number) {
      this._defaultBranch = json.defaultBranch;
      for (const commit of json.commits)
        this._shaToCommit.set(commit.commitId, commit);
      for (const head of json.heads) {
        if (head.type === 'branch') {
          this._branchNameToSha.set(head.name, json.commits[head.commitIndex].commitId);
        }
      }
      for (const orphanedCommitIdx of json.orphanedCommitIndexes ?? [])
        this._orphanedCommits.add(json.commits[orphanedCommitIdx].commitId);
      for (const commitIndexes of json.strands) {
        for (let i = 0; i < commitIndexes.length - 1; ++i) {
          const fromCommitId = json.commits[commitIndexes[i]]!.commitId;
          const toCommitId = json.commits[commitIndexes[i + 1]]!.commitId;
          this._parentSha.set(fromCommitId, toCommitId);
        }
      }
      for (const pr of json.pullRequests ?? [])
        this._pullRequests.set(pr.number, pr);
    }

    pullRequests() {
      return Array.from(this._pullRequests.values());
    }

    defaultBranch(): Ref {
      return {
        type: 'branch',
        name: this._defaultBranch,
        commit: this.commit(this._branchNameToSha.get(this._defaultBranch)!)!,
      }
    }

    ref(headOrCommitId: string): Ref|undefined {
      const branchHead = this._branchNameToSha.get(headOrCommitId);
      const jsonCommit = this._shaToCommit.get(branchHead ?? headOrCommitId);
      return jsonCommit ? {
        commit: {
          ...jsonCommit,
          parents: Array.from(this._parentSha.getAll(jsonCommit.commitId)) as FlakinessReport.CommitId[],
          walkIndex: 0
        },
        name: headOrCommitId,
        type: branchHead ? 'branch' : 'commit',
      } : undefined;
    }

    branches(): Ref[] {
      return [...this._branchNameToSha].map(([branchName, sha]) => ({
        type: 'branch',
        commit: this.commit(sha)!,
        name: branchName,
      }));
    }

    etag() {
      if (!this._etag) {
        const shas = xxHash(Array.from(this._shaToCommit.keys()).sort((a, b) => a < b ? -1 : 1));
        const branches = [...this._branchNameToSha].sort((b1, b2) => b1[0] < b2[0] ? -1 : 1);
        this._etag = xxHashObject({
          version: Repository.DATA_VERSION,
          shas,
          branches,
        });
      }
      return this._etag;
    }

    commitsSize() {
      return this._shaToCommit.size;
    }

    commit(headOrCommitId: string): Commit | undefined {
      const commitId = this._branchNameToSha.get(headOrCommitId) ?? headOrCommitId;
      const jsonCommit = this._shaToCommit.get(commitId);
      return jsonCommit ? {
        ...jsonCommit,
        parents: this._parentSha.getAll(commitId) as FlakinessReport.CommitId[],
        walkIndex: 0,
      } : jsonCommit;
    }

    iterator(head: string): CommitIterator {
      let sha: string | undefined = this._branchNameToSha.get(head) ?? head;
      sha = this._shaToCommit.has(sha) ? sha : undefined;
      return new CommitIterator(this._historyCutoffTimestamp, this._parentSha, this._shaToCommit, sha);
    }

    private _addCommits(commits: Commit[]) {
      for (const commit of commits) {
        this._shaToCommit.set(commit.commitId, {
          commitId: commit.commitId,
          message: commit.message,
          timestamp: commit.timestamp,
          author: commit.author,
          avatar_url: commit.avatar_url,
        });
        for (const parent of commit.parents)
          this._parentSha.set(commit.commitId, parent);
      }
    }

    private async _ensureHistoryForBranch(provider: Git.Provider, sha: string, branchName: string, signal: AbortSignal|undefined) {
      // If we don't have any commits stored for this SHA, then fetch some history.
      if (!this._shaToCommit.has(sha)) {
        for await (const commits of provider.listCommits({ sha }, signal)) {
          this._addCommits(commits);
          // Break right away since we don't want to go deep.
          break;
        }
      }
      // Double-check: if we succeeded fetching commits for the branch, then add these branches.
      if (this._shaToCommit.has(sha))
        this._branchNameToSha.set(branchName, sha);
    }

    async fetch(provider: Git.Provider, requiredCommitIds: FlakinessReport.CommitId[], signal?: AbortSignal): Promise<void> {
      this._etag = undefined;
      // First and foremost, load 1 page of commits for the default branch.
      this._defaultBranch = await provider.defaultBranch(signal);
      for await (const commits of provider.listCommits({ sha: this._defaultBranch }, signal)) {
        this._addCommits(commits);
        this._branchNameToSha.set(this._defaultBranch, commits[0].commitId);
        // Break right away since we don't want to go deep.
        break;
      }

      // Iterate over branches, and for each branches page - load 1 page of their history.
      const allNewBranchNames: string[][] = [];
      for await (const branches of provider.listBranches(signal)) {
        allNewBranchNames.push(branches.map(branch => branch.name));
        await Promise.all(branches.map(branch => this._ensureHistoryForBranch(provider, branch.sha, branch.name, signal)));
      }

      // Iterate over the required commit Ids, and load their history if it hasn't
      // been loaded as part of the branch histories.
      await Promise.all(requiredCommitIds.map(async sha => {
        if (this._shaToCommit.has(sha))
          return;
        try {
          for await (const commits of provider.listCommits({ sha }, signal)) {
            this._addCommits(commits);
            this._orphanedCommits.add(sha);
            // Break right away since we don't want to go deep. We might fetch
            // more history later when fetching tails.
            break;
          }
        } catch (e) {
          // Someone might've uploaded a request from different repo
          // here; then this `requiredCommitId` will NOT be found.
          if ((e instanceof HTTPError) && e.status === 404)
            return;
          throw e;
        }
      }));

      // Delete branches that were removed.
      const currentBranchNames = new Set(this._branchNameToSha.keys());
      const newBranchNames = new Set(allNewBranchNames.flat());
      for (const branchName of currentBranchNames.difference(newBranchNames))
        this._branchNameToSha.delete(branchName);

      // Fetch recorded commits that have no children
      // These could be severed during serialization/deserialization.
      // We will attempt fetching these from provider to restore their
      // parents, if any.
      const allCommitsWithChildren = new Set(this._parentSha.keys());
      const recordedWithoutChildren = new Set(this._shaToCommit.keys()).difference(allCommitsWithChildren);
      await Promise.all(Array.from(recordedWithoutChildren).map(async sha => {

        // CUTOFF: do not fetch tail if it is beyond history cutoff.
        const commit = this._shaToCommit.get(sha);
        if (commit && commit.timestamp < this._historyCutoffTimestamp)
          return;

        // Otherwise, try fetching history once.
        for await (const commits of provider.listCommits({ sha }, signal)) {
          this._addCommits(commits);
          break;
        }
      }));

      // These are the commits we are required to continue fetching: we have
      // parent references, but no commits.

      while (!signal?.aborted) {
        const allRecordedCommits = new Set(this._shaToCommit.keys());
        const allOutgoingShas = new Set(this._parentSha.values());
        const unrecordedOutgoing = allOutgoingShas.difference(allRecordedCommits);

        // CUTOFF: Do not try to fetch any commits or commit parents if they're after
        // history cutoff date.
        for (const commit of this._shaToCommit.values()) {
          if (commit.timestamp >= this._historyCutoffTimestamp)
            continue;
          unrecordedOutgoing.delete(commit.commitId);
          for (const parent of this._parentSha.getAll(commit.commitId))
            unrecordedOutgoing.delete(parent);
        }

        if (!unrecordedOutgoing.size)
          break;

        await Promise.all(Array.from(unrecordedOutgoing).map(async tail => {
          for await (const commits of provider.listCommits({ sha: tail }, signal)) {
            this._addCommits(commits);

            // CUTOFF: if some of the fetched commits beyond cutoff, then bail out.
            // We want to store them though so that some of the commits after cutoff
            // is saved, so that we can mark this history branch as one beyond cutoff.
            if (commits.some(commit => commit.timestamp < this._historyCutoffTimestamp))
              break;

            const reachedExisting = commits.map(c => c.parents).flat().every(c => this._shaToCommit.has(c));
            if (reachedExisting)
              break;
          }
        }))
      }

      // Fetch pull requests by creation time
      for await (const prs of provider.listPullRequests({ sort: 'created' }, signal)) {
        if (prs.every(pr => this._pullRequests.has(pr.number)))
          break;
        for (const pr of prs)
          this._pullRequests.set(pr.number, pr);
      }
      // Fetch 100 updated pull requests (sort by "updated")
      let updatedPRCount = 0;
      for await (const prs of provider.listPullRequests({ sort: 'updated' }, signal)) {
        for (const pr of prs)
          this._pullRequests.set(pr.number, pr);
        updatedPRCount += prs.length;
        if (updatedPRCount > 100)
          break;
      }

      this._computeLastTestedCommit();
    }

    private _computeLastTestedCommit() {
      // Finally, resolve "last tested commit" for each pull request.
      // For each PR, find all commits that are "merge commits".
      const mergeCommits = Array.from(this._parentSha).filter(([sha, parents]) => parents.size > 1);
      // Compute inversion
      const childrenSha = new Multimap<string, string>();
      for (const [sha, parents] of mergeCommits) {
        for (const parent of parents)
          childrenSha.set(parent, sha);
      }
      // For each pull request, get all commits
      for (const pr of this._pullRequests.values()) {
        const mergeCommits = childrenSha.getAll(pr.headSha).map(sha => this._shaToCommit.get(sha)).filter(c => c !== undefined);
        mergeCommits.sort((c1, c2) => c2.timestamp - c1.timestamp);
        pr.lastTestedCommit = mergeCommits.length ? mergeCommits[0].commitId : undefined;
      }
    }
  }
}
