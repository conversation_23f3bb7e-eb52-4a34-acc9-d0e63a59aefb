import { Organization, Project, ProjectPublicId } from '@flakiness/database';
import { TypedHTTP } from '@flakiness/shared/common/typedHttp.js';
import { measure } from '@flakiness/shared/common/utils.js';
import { createTypedHttpExpressMiddleware } from '@flakiness/shared/node/typedHttpExpress.js';
import bodyParser from 'body-parser';
import compression from 'compression';
import cookieParser from 'cookie-parser';
import debug from 'debug';
import express, { NextFunction, Request, Response } from 'express';
import 'express-async-errors';
import http from 'http';
import ms from 'ms';
import path from 'path';
import { APIContext, appRouter } from './api.js';
import { Authentication } from './authentication.js';
import { Config } from './configuration.js';
import { Services } from './services.js';
import { createHistogram, TelemetryServer } from './telemetry.js';

const logHTTPServer = debug('fk:http');

type ServerOptions = {
  // Server configuration
  superuserGithubIds?: string[],
  port: number,
  frontendPath?: string,
  jwtSignSecret: string,

  // Automation can log in as test user using /login-as-test-user endpoint.
  testUser?: {
    githubId: number,
    name: string,
    login: string,
    avatarUrl: string,    
  },

  telemetry?: boolean,
  onlySuperusersCanCreateOrganizations?: boolean,
}

export class HTTPServer {
  static async configFromEnvOrDie(): Promise<ServerOptions> {
    const config = await Config.fromEnvironmentOrDie('General config', async env => ({
      superusers: await env.text({
        env: 'SUPERUSERS',
        required: false,
        description: `comma-separated list of github IDs`,
      }),
      disableOrgCreation: await env.bool({
        env: 'DISABLE_ORG_CREATION',
        required: false,
        description: `disallow organization creation to everybody but super users.`,
      }),
      port: await env.integer({
        env: 'PORT',
        required: true,
        description: ``,
      }),
      frontendPath: await env.path({
        env: 'FLAKINESS_WEB_PATH',
        required: false,
        description: ``,
      }),
      jwtSignSecret: await env.secret({
        env: 'FLAKINESS_JWT_SECRET',
        description: ``,
        required: true,
      }),
      telemetry: await env.bool({
        env: 'TELEMETRY',
        required: false,
        description: `Expose telemetry on /metrics endpoint`,
      }),
    }));
    return {
      frontendPath: config.frontendPath,
      jwtSignSecret: config.jwtSignSecret,
      port: config.port,
      superuserGithubIds: config.superusers?.split(/,;/)?.map(t => t.trim()),
      onlySuperusersCanCreateOrganizations: !!config.disableOrgCreation,
      telemetry: config.telemetry,
    };
  }

  static async create(services: Services, options: ServerOptions) {
    const auth = new Authentication(services.db, new Set(options.superuserGithubIds ?? []));

    const app = express();
    // Disable etag generation by default.
    app.set('etag', false);
    app.use(compression());
    app.use(cookieParser()); // parse cookies
    
    services.stripeApp?.installRoutes(app);

    if (options.telemetry)
      TelemetryServer.installRoutes(app);

    app.use(bodyParser.json({ limit: 256 * 1024 }));

    const httpResponseDurationMs = createHistogram({
      name: 'fk_http_response_duration_seconds',
      help: 'Duration of HTTP responses in seconds.',
      labelNames: ['method', 'route', 'statusCode'],
    });

    app.use((req, res, next) => {
      req.on('aborted', () => logHTTPServer(`REQ ABORTED ${req.method} ${req.originalUrl}`));
      res.on('close', () => {
        if (!res.headersSent) logHTTPServer(`RES CLOSED BEFORE SEND ${req.method} ${req.originalUrl}`);
      });
      next();
    });

    app.use((req, res, next) => {
      const end = httpResponseDurationMs.startTimer();
      const m = measure();
      res.on('finish', () => {
        // m(`${req.ip} [${req.method}] ${req.url}`);
        end({
          method: req.method,
          route: req.path,
          statusCode: res.statusCode
        });
      });
      next();
    });
    // Serve static files if we're asked to.
    if (options.frontendPath) {
      app.use(express.static(options.frontendPath, {
        etag: true,
      })); 
    }

    if (services.githubApp) {
      const middleware = services.githubApp.createRouter(options.jwtSignSecret, (res, data) => auth.loginOrSignUpWithGithub(res, {
        githubId: data.githubId, 
        name: data.name ?? data.login,
        login: data.login,
        avatarUrl: data.avatarUrl,
      }));
      app.use('/', middleware);
    }

    const testUser = options?.testUser;
    if (testUser) {
      app.use('/login-test-user', async (req, res) => {
        await auth.loginOrSignUpWithGithub(res, {
          githubId: testUser.githubId,
          name: testUser.name,
          login: testUser.login,
          avatarUrl: testUser.avatarUrl,
        });
        res.redirect(301, '/');
      });
    }

    app.get('/logout', async (req, res) => {
      await auth.logout(req, res);
      const returnTo = req.query['return_to'];
      res.redirect(typeof returnTo === 'string' ? returnTo : '/');
    });

    app.use('/api', (req, res, next) => services.serverTiming.run(next), createTypedHttpExpressMiddleware<typeof appRouter, APIContext>({
      router: appRouter,
      onBeforeResponse: ({ req, res, timings, ctx }) => {
        if (ctx?.auth.isSuperUser(ctx.user)) {
          if (timings.handler)
            services.serverTiming.recordTraceEvent('handler', timings.handler.since, timings.handler.until);
          if (timings.contextCreation)
            services.serverTiming.recordTraceEvent('context', timings.contextCreation.since, timings.contextCreation.until);
          if (timings.etag)
            services.serverTiming.recordTraceEvent('etag', timings.etag.since, timings.etag.until);
          res.set('server-timing', services.serverTiming.httpServerTimingHeader());
        }
      },
      createRootContext: async ({ req, res, input }) => {
        input ??= {};
        // If there's a projectPublicId in the arguments, then resolve the org/project
        // based on the projectPublicId.
        // Otherwise look for the orgSlug / projectSlug inputs.
        const projectPublicId = input['projectPublicId'] as ProjectPublicId|undefined;

        let org: Organization|undefined;
        let project: Project|undefined;
        if (projectPublicId) {
          project = await services.db.projects.getByPublicId(projectPublicId);
          org = project ? await services.db.orgs.get(project.org_id) : undefined;
        } else {
          const orgSlug = input['orgSlug'] as string|undefined;
          const projectSlug = input['projectSlug'] as string|undefined;
          org = orgSlug ? await services.db.orgs.getBySlug(orgSlug) : undefined;
          project = org && projectSlug ? await services.db.projects.getBySlug(org.org_id, projectSlug) : undefined;
        }

        const timing = services.serverTiming.start('auth');
        const userSession = await auth.getUserSession(req);
        const user = userSession ? await auth.getLoggedInUser(userSession) : undefined;
        const orgRole = org && user ? await auth.getUserOrgRole(user, org) : undefined;
        const projectRole = project ? await auth.getUserProjectRole(user, project) : undefined;
        timing.done();

        const billing = org ? await services.stripeApp?.status(org) : undefined;

        return {
          request: req,
          services, 
          jwtSignSecret: options.jwtSignSecret,
          user,
          userSession,
          project,
          projectRole,
          org,
          orgRole,
          orgBillingStatus: billing,
          auth,
          preferences: {
            onlySuperusersCanCreateOrganizations: !!options.onlySuperusersCanCreateOrganizations,
          }
        };
      },
    }));

    // Handling non matching request from the client
    if (options.frontendPath) {
      app.use((req, res, next) => { 
        res.sendFile(path.join(options.frontendPath!, 'index.html'));
      });
    }
    
    app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
      if (err instanceof TypedHTTP.HttpError)
        return res.status(err.status).send({ error: err.message });
      // Handle other unexpected errors (consider them 500 errors)
      logHTTPServer(err);
      res.status(500).send({ error: 'Internal Server Error' });
    });

    const server = http.createServer(app);

    // Based on the findings in https://github.com/caddyserver/caddy/issues/6452,
    // we'd like to have keepAliveTimeout coordinated with our reverse proxy.
    server.keepAliveTimeout = ms('60s');
    server.headersTimeout = ms('70s');

    server.on('connection', (socket) => {
      // ECONNRESET, EPIPE and HPE_INVALID_EOF_STATE are legit errors given
      // that tab closing aborts outgoing connections to the server.
      // HPE_INVALID_METHOD is a legit error when a client (e.g. Chromium which
      // makes https requests to http sites) makes a https connection to a http server.
      socket.on('error', error => {
        if (!['EPIPE', 'ECONNRESET', 'HPE_INVALID_EOF_STATE', 'HPE_INVALID_METHOD'].includes((error as any).code))
          throw error;
      });
    });

    server.on('error', (err: any) => {
      if (err.code === 'ECONNRESET') {
        logHTTPServer('Client connection reset. Ignoring.');
        return;
      }
      throw err;
    });

    // Use systemd socket if available
    const isSystemdSocket = !!process.env.LISTEN_FDS;
    let address: string;
    if (isSystemdSocket) {
      // File descriptor 3 is passed by systemd when using a socket
      address = await new Promise<string>(resolve => server.listen({ fd: 3 }, () => {
        logHTTPServer('Express server running on systemd socket');
        resolve(process.env.LISTEN_FDS!);
      }));
    } else {
      address = await new Promise<string>(resolve => server.listen(options.port, () => {
        const port = (server.address() as any).port;
        logHTTPServer('Listening on port', port);
        resolve(`http://localhost:${port}`);
      }));
    }

    return new HTTPServer(server, address);
  }

  constructor(
    private _server: http.Server,
    private _address: string,
  ) {}

  address() { return this._address; }

  async dispose() {
    await new Promise(x => this._server.close(x));
  }
}
