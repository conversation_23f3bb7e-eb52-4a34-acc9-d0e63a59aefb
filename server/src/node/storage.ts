import { brotliDecompressAsync, compressTextAsync } from "@flakiness/shared/node/compression.js";
import { S3Bucket } from "./s3.js";

export type StorageMetadata = Record<Lowercase<string>,string>;

export interface Storage<Key, Value, Meta = StorageMetadata> {
  put(key: Key, metadata: Meta, data: Value, signal?: AbortSignal): Promise<void>;
  getMetadataSafe(key: Key, signal?: AbortSignal): Promise<Meta | undefined>;
  getSafe(key: Key, signal?: AbortSignal): Promise<{ data: Value, metadata: Meta, lastModified?: Date } | undefined>;
}

export class S3Storage<Meta extends StorageMetadata = StorageMetadata> implements Storage<string, Buffer, Meta> {
  constructor(private _s3data: S3Bucket) {
  }

  async put(key: string, metadata: Meta, data: Buffer, signal?: AbortSignal): Promise<void> {
    await this._s3data.uploadFile(key, {
      metadata,
      data,
    }, signal);
  }

  async getMetadataSafe(key: string, signal?: AbortSignal): Promise<Meta | undefined> {
    return await this._s3data.getMetadataSafe(key, signal) as Meta;
  }

  async getSafe(key: string, signal?: AbortSignal) {
    const download = await this._s3data.downloadFile(key, signal);
    if (!download?.metadata)
      return undefined;
    const file = await download?.streamFile();
    return {
      data: file.data,
      lastModified: file.lastModified,
      metadata: file.metadata as Meta,
    };
  }
}

export class BrotliTextStorage<Key> implements Storage<Key, string, Record<string, string>> {
  private _storage: Storage<Key, Buffer, Record<string, string>>;

  constructor(options: { next: Storage<Key, Buffer, any> }) {
    this._storage = options.next;
  }

  async put(key: Key, metadata: Record<string, string>, data: string, signal?: AbortSignal): Promise<void> {
    const compressed = await compressTextAsync(data);
    await this._storage.put(key, metadata, compressed, signal);
  }

  async getMetadataSafe(key: Key, signal?: AbortSignal): Promise<Record<string, string> | undefined> {
    return await this._storage.getMetadataSafe(key, signal);
  }

  async getSafe(key: Key, signal?: AbortSignal) {
    const result = await this._storage.getSafe(key, signal);
    if (!result)
      return undefined;
    const data = await brotliDecompressAsync(result.data);
    return {
      metadata: result.metadata,
      lastModified: result.lastModified,
      data: data.toString('utf-8'),
    };
  }
}

export class JSONStorage<Key, Value, Meta extends StorageMetadata = StorageMetadata> implements Storage<Key, Value, Meta> {
  constructor(private _options: {
    keyId: (key: Key) => string,
    next: Storage<string, string>
  }) {  }

  async put(key: Key, metadata: Meta, value: Value, signal?: AbortSignal): Promise<void> {
    const keyId = this._options.keyId(key);
    const raw = JSON.stringify(value);
    await this._options.next.put(keyId, metadata, raw, signal);
  }

  async getMetadataSafe(key: Key, signal?: AbortSignal): Promise<Meta | undefined> {
    return await this._options.next.getMetadataSafe(this._options.keyId(key), signal) as Meta;
  }

  async getSafe(key: Key, signal?: AbortSignal) {
    const result = await this._options.next.getSafe(this._options.keyId(key), signal);
    return result ? {
      metadata: result.metadata as Meta,
      lastModified: result.lastModified,
      data: JSON.parse(result.data),
    } : undefined;
  }
}
