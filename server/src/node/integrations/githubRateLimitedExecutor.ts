import { ManualPromise } from "@flakiness/shared/common/manualPromise.js";
import { RestEndpointMethodTypes as RestInterface } from "@octokit/plugin-rest-endpoint-methods";
import { OctokitResponse } from "@octokit/types";
import debug from "debug";
import ms from "ms";
import { Octokit, RequestError } from "octokit";
import { BlockingQueue } from "../../common/blockingQueue.js";
import { Git } from "../git.js";

const log = debug('fk:gh');

/**
 * As per: https://docs.github.com/en/rest/using-the-rest-api/rate-limits-for-the-rest-api?apiVersion=2022-11-28#about-secondary-rate-limits
 * 1. Make too many concurrent requests. No more than 100 concurrent requests are allowed.
 *    This limit is shared across the REST API and GraphQL API.
 * 2. Make too many requests to a single endpoint per minute.
 *    No more than 900 points per minute are allowed for REST API endpoints,
 *    and no more than 2,000 points per minute are allowed for the GraphQL API endpoint.
 *    For more information about points, see Calculating points for the secondary rate limit.
 * 3. Make too many requests per minute. No more than 90 seconds of CPU time per 60 seconds of real time is allowed.
 *    No more than 60 seconds of this CPU time may be for the GraphQL API. You can roughly estimate the CPU time by measuring the total response time for your API requests.
 * 4. Make too many requests that consume excessive compute resources in a short period of time.
 *    Create too much content on GitHub in a short amount of time.
 *    In general, no more than 80 content-generating requests per minute and no more than 500 content-generating requests per hour are allowed. Some endpoints have lower content creation limits. Content creation limits include actions taken on the GitHub web interface as well as via the REST API and GraphQL API.
 * 
 * This executor implements these limits.
 */
const MAX_CPU_TIME_PER_WALL_MINUTE = 90_000;
const MAX_POINTS_PER_MINUTE = 900;
// While maximum concurrency can be up to 100, this doesn't work well in practice since 
// requests are still getting queued internally.
const MAX_CONCURRENCY = 5;

const LIMITS_LOG_RATE = ms('1 second');

export class GithubRateLimitedExecutor {
  private _queue = new BlockingQueue<{
    scope: string,
    methodName: string,
    options: any,
    manual: ManualPromise<any>,
    points: number,
    signal?: AbortSignal,
    stale?: any,
  }>();

  private _currentCPUTime: number = 0;
  private _currentPoints: number = 0;
  private _wait?: ManualPromise<void>;

  private _rateLimitedUntil?: Date;
  private _rateLimitTimeout?: NodeJS.Timeout;

  // Limit current rate limits every X seconds.
  private _lastLimitsLogTimestamp = 0;

  constructor(private _octokit: Octokit) {
    for (let i = 0; i < MAX_CONCURRENCY; ++i)
      this._spawnWorker();
  }

  private _setRateLimits(response: OctokitResponse<any>) {
    const limits = parseRateLimits(response);
    if (!limits.retryAfter && limits.ratelimitRemaining > 0)
      return;
    this._rateLimitedUntil = limits.retryAfter ?? limits.ratelimitReset;

    clearTimeout(this._rateLimitTimeout);
    this._rateLimitTimeout = setTimeout(() => {
      this._rateLimitedUntil = undefined;
      this._maybeLogCurrentLimits();
    }, (+this._rateLimitedUntil) - Date.now());
  }

  private async _spawnWorker() {
    for await (const { scope, methodName, options, manual, stale, points, signal } of this._queue) {
      const abortListener = () => {
        this._wait?.resolve();
        this._wait = undefined;
      };
      signal?.addEventListener('abort', abortListener);
      while ((this._currentCPUTime >= MAX_CPU_TIME_PER_WALL_MINUTE || this._currentPoints >= MAX_POINTS_PER_MINUTE) && !signal?.aborted) {
        this._wait ??= new ManualPromise();
        await this._wait.promise;
      }
      signal?.removeEventListener('abort', abortListener);
      if (signal?.aborted) {
        manual.reject(new DOMException('Method was aborted', 'AbortError'));
        continue;
      }

      // When we're being rate-limited, reject all requests with RateLimitedError.
      if (this._rateLimitedUntil) {
        manual.reject(new Git.RateLimitError(this._rateLimitedUntil));
        this._maybeLogCurrentLimits();
        continue;
      }

      // Otherwise, proceed to fetch. First and foremost, parse headers.
      const headerPairs: [string, string|number][] = [];
      if (stale?.headers.lastModified)
        headerPairs.push(['If-Modified-Since', stale.headers.lastModified]);
      if (stale?.headers.etag)
        headerPairs.push(['If-None-Match', stale.headers.etag]);
      const headers = headerPairs.length ? Object.fromEntries(headerPairs) : undefined;
      // Then initiate the request.
      const start = Date.now();
      try {
        const result = await (this._octokit.rest as any)[scope][methodName]({ ...options, headers }) as OctokitResponse<any>;
        const ratelimits = parseRateLimits(result);
        log(`${result.status} ${scope}.${methodName} ${JSON.stringify(options)} rl=${ratelimits.ratelimitRemaining} cpu=${Date.now() - start}`);
        manual.resolve(result);
      } catch (error: any) {
        // If request failed, then parse error.
        // If this is a RequestError with a valid response, then
        // look into it.
        if ((error instanceof RequestError) && error.response) {
          log(`${error.response.status} ${scope}.${methodName} ${JSON.stringify(options)} cpu=${Date.now() - start}`);
          // Since we were using if-none-match and if-modified-since headers,
          // we might've received the 304 response. Then just fullfil with `stale`.
          if (error.response.status === 304 && stale) {
            manual.resolve(stale);
          } else {
            // Otherwise, parse rate-limit restrictions.
            this._setRateLimits(error.response);
            // If some apply right now, then reject with the RateLimitError.
            // Otherwise, it's unclear what's going on, so just reject.
            if (this._rateLimitedUntil)
              manual.reject(new Git.RateLimitError(this._rateLimitedUntil));
            else
              manual.reject(new Git.HTTPError(error.response.status));
          }
        } else {
          log(`ERROR ${scope}.${methodName} ${JSON.stringify(options)} - ${error}`);
          // This is a catch-all clause: reject if we don't understand what's going on.
          manual.reject(error);
        }
      }
      // After we did a roundtrip to the server, we should compute quotas.
      const cpuTime = Date.now() - start;
      this._currentCPUTime += cpuTime;
      this._currentPoints += points;
      setTimeout(() => {
        this._currentCPUTime -= cpuTime;
        this._currentPoints -= points;
        this._wait?.resolve();
        this._wait = undefined;
      }, 60_000);
      this._maybeLogCurrentLimits();
    }
  }

  private _maybeLogCurrentLimits() {
    if (Date.now() - this._lastLimitsLogTimestamp < LIMITS_LOG_RATE)
      return;
    this._lastLimitsLogTimestamp = Date.now();
    log(`
      Rate Limited Until: ${this._rateLimitedUntil ?? '<not rate limited>'}
      current CPU time: ${this._currentCPUTime}
      current points: ${this._currentPoints}
    `);
  }

  private async _fetch<K, T extends OctokitResponse<K>>(scope: string, methodName: string, options: any, stale: T|undefined, points: number, signal: AbortSignal|undefined): Promise<T> {
    const manual = new ManualPromise<T>();
    this._queue.push({
      scope,
      methodName,
      options,
      manual,
      stale,
      points,
      signal,
    });
    return await manual.promise;
  }

  async listCommits<T extends RestInterface["repos"]["listCommits"]>(options: T['parameters'], stale?: T['response'], signal?: AbortSignal) {
    return await this._fetch('repos', 'listCommits', options, stale, 1, signal);
  }

  async repoDetails<T extends RestInterface["repos"]['get']>(options: T['parameters'], stale?: T['response'], signal?: AbortSignal) {
    return await this._fetch('repos', 'get', options, stale, 1, signal);
  }

  async listPullRequestCommits<T extends RestInterface["pulls"]["listCommits"]>(options: T['parameters'], stale?: T['response'], signal?: AbortSignal) {
    return await this._fetch('pulls', 'listCommits', options, stale, 1, signal);
  }

  async listPullRequests<T extends RestInterface["pulls"]["list"]>(options: T['parameters'], stale?: T['response'], signal?: AbortSignal) {
    return await this._fetch('pulls', 'list', options, stale, 1, signal);
  }

  async listMatchingRefs<T extends RestInterface["git"]["listMatchingRefs"]>(options: T['parameters'], stale?: T['response'], signal?: AbortSignal) {
    return await this._fetch('git', 'listMatchingRefs', options, stale, 1, signal);
  }
}

function parseRateLimits<T extends OctokitResponse<any>>(response: T) {
  const ratelimitRemaining = response.headers['x-ratelimit-remaining'] ? parseInt(response.headers['x-ratelimit-remaining'], 10) : undefined;
  const ratelimitReset = response.headers['x-ratelimit-reset'] ? parseInt(response.headers['x-ratelimit-reset'], 10) : undefined;
  const retryAfterHeader = (response.headers as any)['retry-after'];
  const retryAfterSeconds = retryAfterHeader ? parseInt(retryAfterHeader, 10) : undefined;
  return {
    // if they didn't tell us how much more we can request, then unbound!
    ratelimitRemaining: ratelimitRemaining ?? Infinity,
    // If they didn't send us time to reset the rate limit, then default to +1 hour, since these are hourly limits.
    ratelimitReset: ratelimitReset ? new Date(ratelimitReset * 1000) : new Date(Date.now() + ms('1 hour')),
    retryAfter: retryAfterSeconds ? new Date(Date.now() + 1000 * retryAfterSeconds) : undefined,
  };
}
