import { User } from '@flakiness/database';
import { TypedHTTP } from '@flakiness/shared/common/typedHttp.js';
import { xxHashObject } from '@flakiness/shared/common/utils.js';
import { Response, Router } from 'express';
import jwt from 'jsonwebtoken';
import ms from 'ms';
import { App, Octokit } from 'octokit';
import { ComputationCache } from '../../common/computationCache.js';
import { SingleflightCache } from '../../common/singleflightcache.js';
import { Config } from '../configuration.js';
import { FlakinessOctokit } from './flakinessOctokit.js';

type GithubOAuthToken = {
  accessToken: string,
  accessExpirationMs: number,
  refreshToken: string,
  refreshExpirationMs: number,
}

export type GithubAppConfig = {
  appId: string,
  privateKey: string,
  clientId: string,
  clientSecret: string,
  callbackUrl: string,
  publicUrl: string,
}

export class GithubApp {
    static async configFromEnvOrDie(): Promise<GithubAppConfig> {
    return await Config.fromEnvironmentOrDie('Github App configuration', async env => ({
      appId: await env.text({
        env: 'GITHUB_APP_ID',
        description: ``,
        required: true,
      }),
      privateKey: await env.secret({
        env: 'GITHUB_APP_PRIVATE_KEY',
        description: ``,
        required: true,
      }),
      clientId: await env.text({
        env: 'GITHUB_APP_CLIENT_ID',
        description: ``,
        required: true,
      }),
      clientSecret: await env.secret({
        env: 'GITHUB_APP_CLIENT_SECRET',
        description: ``,
        required: true,
      }),
      callbackUrl: await env.url({
        env: 'GITHUB_APP_CALLBACK_URL',
        description: ``,
        required: true,
      }),
      publicUrl: await env.url({
        env: 'GITHUB_APP_PUBLIC_URL',
        description: ``,
        required: true,
      }),
    }));
  }

  private _app: App;
  private _installationIdCache = new SingleflightCache<{
    user: User,
    owner: string,
    repo: string,
  }, number|undefined>({
    size: 100,
    ttl: ms('1min'),
    key: (request) => xxHashObject({
      githubId: request.user.github_id,
      owner: request.owner,
      repo: request.repo,
    }),
    fetch: async ({ user, owner, repo }, stale, signal) => {
      if (!user.github_id)
        return undefined;
      const repoInstallation = await this._app.octokit.rest.apps.getRepoInstallation({ owner, repo, }).catch(e => {
        return undefined;
      });
      if (!repoInstallation || !repoInstallation?.data?.id)
        return undefined;

      // Make sure that user name hasn't changed
      const installationOctokit = await this._app.getInstallationOctokit(repoInstallation.data.id);
      const userInfo = await installationOctokit.rest.users.getByUsername({ username: user.user_login }).catch(e => undefined);
      if (!userInfo || userInfo.data.id !== user.github_id)
        return undefined;

      const permissions = await installationOctokit.rest.repos.getCollaboratorPermissionLevel({ owner, repo, username: user.user_login })
        .catch(e => undefined);
      return permissions?.data ? repoInstallation.data.id : undefined;
    }
  });

  private _checkPATAccessCache = new SingleflightCache<{
    owner: string,
    repo: string,
    accessToken: string,
  }, boolean>({
    ttl: ms('1min'),
    size: 100,
    key: xxHashObject,
    fetch: async (request) => {
      const octokit = new Octokit({ auth: request.accessToken });
      return await octokit.request('HEAD /repos/{owner}/{repo}/branches', {
        owner: request.owner,
        repo: request.repo,
      }).then(() => true).catch((error: any) => false);
    }
  });

  private _installationIdToFlakinessOctokit = new ComputationCache<number, FlakinessOctokit>({
    size: 10,
    etag: (request) => String(request),
    compute: async (input: number, etag, signal) => {
      return new FlakinessOctokit(await this._app.getInstallationOctokit(input));
    },
  });

  private _patToFlakinessOctokit = new ComputationCache<string, FlakinessOctokit>({
    size: 10,
    etag: (request) => String(request),
    compute: async (input: string, etag, signal) => {
      return new FlakinessOctokit(new Octokit({ auth: input }));
    }
  });

  constructor(private _options: GithubAppConfig) {
    this._app = new App({ appId: this._options.appId, privateKey: this._options.privateKey });
  }

  publicUrl(): string {
    return this._options.publicUrl;
  }

  async apiForInstallationId(installationId: number): Promise<FlakinessOctokit> {
    return await this._installationIdToFlakinessOctokit.get(installationId);
  }

  async apiForPAT(pat: string): Promise<FlakinessOctokit> {
    return await this._patToFlakinessOctokit.get(pat);
  }

  async getInstallationId({ user, owner, repo } : { user: User, owner: string, repo: string }): Promise<number|undefined> {
    return await this._installationIdCache.get({ user, owner, repo });
  }

  async checkPATAccess(options: { accessToken: string, repo: string, owner: string }): Promise<boolean> {
    return await this._checkPATAccessCache.get(options) ?? false;
  }

  private async _refreshAccessToken(options: { refreshToken?: string, code?: string }): Promise<GithubOAuthToken> {
    const response = await fetch(urlWithParams('https://github.com/login/oauth/access_token', options.code ? {
      'client_id': this._options.clientId,
      'client_secret': this._options.clientSecret,
      'code': options.code,
    } : {
      'client_id': this._options.clientId,
      'client_secret': this._options.clientSecret,
      'refresh_token': options.refreshToken,
      'grant_type': 'refresh_token',
    }), {
      method: 'POST',
      headers: {
        Accept: 'application/json',
      },
    });
    const json = await response.json() as {
      access_token?: string,
      expires_in?: number,
      refresh_token?: string,
      refresh_token_expires_in?: number,
    };
    if (!json || !json.access_token)
      throw TypedHTTP.HttpError.withCode('UNAUTHORIZED', 'Failed to obtain token');
    // If github app doesn't have user-to-server token expiration enabled (optional github app feature),
    // then there will be no refresh token and no expirations.
    // In this case, we'll say that given token is good for 100 years, and that refresh token
    // is expired already.
    if (!json.expires_in || !json.refresh_token || !json.refresh_token_expires_in) {
      return {
        accessToken: json.access_token,
        accessExpirationMs: Date.now() + ms('5 years'),
        refreshToken: '',
        refreshExpirationMs: Date.now() - ms('1 year'),
      };
    }
    return {
      accessToken: json.access_token,
      accessExpirationMs: Date.now() + json.expires_in * 1000,
      refreshToken: json.refresh_token,
      refreshExpirationMs: Date.now() + json.refresh_token_expires_in * 1000,
    };
  }

  createRouter(jwtSignSecret: string, callback: (res: Response<any>, data: {
    githubId: number,
    login: string,
    name?: string,
    avatarUrl: string,
  }) => Promise<void>) {
    const router = Router();

    type State = {
      returnTo: string;
    }

    function encodeState(returnTo: string): string {
      return jwt.sign({ date: Date.now(), returnTo }, jwtSignSecret, { expiresIn: 60 * 60 /* 1 hour */});
    }

    function decodeState(state: string): State {
      return jwt.verify(state, jwtSignSecret) as State;
    }

    router.get('/login/github', (req, res) => {
      const url = new URL('https://github.com/login/oauth/authorize');
      url.searchParams.set('client_id', this._options.clientId);
      url.searchParams.set('redirect_uri', new URL('login/github/callback', this._options.callbackUrl).toString());
      url.searchParams.set('scope', ['read:user'].join(' '));
      const returnTo = req.query['return_to'];
      url.searchParams.set('state', encodeState(typeof returnTo === 'string' ? returnTo : '/'));

      // Set headers to prevent caching
      res.set('Cache-Control', 'no-store, no-cache, must-revalidate, private');
      res.set('Expires', '0');
      res.set('Pragma', 'no-cache');

      res.redirect(301, url.toString());
    });

    router.get('/login/github/callback', async (req, res) => {
      const { state, code } = req.query;
      if (typeof state !== 'string' || typeof code !== 'string')
        throw TypedHTTP.HttpError.withCode('BAD_REQUEST', '"state" parameter must be a string');
      let returnTo: string;
      try {
        returnTo = decodeState(state).returnTo;
      } catch (e) {
        throw TypedHTTP.HttpError.withCode('BAD_REQUEST', e instanceof Error ? e.message : 'failed to verify nonce');
      }

      const token = await this._refreshAccessToken({ code });

      const response = await fetch('https://api.github.com/user', {
        method: 'GET',
        headers: {
          Authorization: 'Bearer ' + token.accessToken,
        },
      });
      if (response.status !== 200)
        throw TypedHTTP.HttpError.withCode('UNAUTHORIZED', response.statusText);
      const json = await response.json() as {
        id: number,
        login: string,
        avatar_url: string,
        name?: string,
      };

      await callback(res, {
        githubId: json.id,
        name: json.name,
        login: json.login,
        avatarUrl: json.avatar_url,
      });

      res.redirect(301, returnTo);
    });
    return router;
  }
}

function urlWithParams(href: string, params: Record<string, string|undefined>): string {
  const url = new URL(href);
  for (const [key, value] of Object.entries(params)) {
    if (value !== undefined)
      url.searchParams.set(key, value);
  }
  return url.toString();
}