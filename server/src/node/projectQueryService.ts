
import { ProjectPublicId } from "@flakiness/database";
import { xxHashObject } from "@flakiness/shared/common/utils.js";
import assert from "assert";
import debug from 'debug';
import { Temporal } from "temporal-polyfill";
import { SharedCacheStore } from "../common/caches/cache.js";
import { SyncComputationCache } from "../common/computationCache.js";
import { Query } from "../common/fql/query.js";
import { Ranges } from "../common/ranges.js";
import { CommitAnalyzer } from "../common/stats/commitAnalyzer.js";
import { Histogram } from "../common/stats/histogram.js";
import { HistoryAnalyzer } from "../common/stats/historyAnalyzer.js";
import { SpanAnalyzer, SpanStats } from "../common/stats/spanAnalyzer.js";
import { Stats } from "../common/stats/stats.js";
import { StatsAnalyzer } from "../common/stats/statsAnalyzer.js";
import { FilterContext, TestIndex } from "../common/stats/testIndex.js";
import { TestOutcomes as T } from "../common/stats/testOutcomes.js";
import { TestsReport } from "../common/stats/testsReport.js";
import { TimelineCommitReport } from "../common/stats/timelineCommitReport.js";
import { TimelineTestsReport } from "../common/stats/timelineTestsReport.js";
import { Timeline } from "../common/timeline/timeline.js";
import { TimelineSplit } from "../common/timeline/timelineSplit.js";
import { wireOutcomesToOutcome, WireTypes } from "../common/wireTypes.js";
import { Git } from "./git.js";
import { ReportIndex } from "./reportIndex.js";
import { S3Stats } from "./s3layout.js";
import { ServerTiming } from "./serverTiming.js";

const log = debug('fk:query_service');

export class ProjectQueryService {
  private _commitAnalyzers: SyncComputationCache<{
    commitId: Stats.CommitId,
    stats: StatsAnalyzer,
  }, CommitAnalyzer>;

  private _spanAnalyzers: SyncComputationCache<CommitAnalyzer[], SpanAnalyzer>;

  private _historyAnalyzers: SyncComputationCache<{
    historyDays: SpanAnalyzer[],
    regressionWindowDays: number,
  }, HistoryAnalyzer>;

  private _filterContexts: SyncComputationCache<{
    testIndex: TestIndex,
    fql: Query,
  }, FilterContext>;

  private _timelineTestsReport: SyncComputationCache<{
    timeline: Timeline,
    span: SpanAnalyzer,
    history: HistoryAnalyzer,
  }, TimelineTestsReport>;

  private _timelineCommitReport: SyncComputationCache<{
    timeline: Timeline,
    commitAnalyzer: CommitAnalyzer,
    history: HistoryAnalyzer,
  }, TimelineCommitReport>;

  private _commitsReport: SyncComputationCache<{
    commits: WireTypes.Commit[],
    analyzers: CommitAnalyzer[],
    history: HistoryAnalyzer,
    timelines: Timeline[],
    filter: FilterContext,
  }, WireTypes.CommitStats[]>;

  private _reportCounters: SyncComputationCache<{
    span: SpanAnalyzer,
    history: HistoryAnalyzer,
    timelines: Timeline[],
    filter: FilterContext,
  }, {
    testOutcomes: T.TestOutcomeCounts,
    tests: number,
    timelines: number,
    errors: number,
    annotations: number,
    tags: number
  }>;

  private _testsReport: SyncComputationCache<{
    testIndex: TestIndex,
    span: SpanAnalyzer,
    timelines: Timeline[],
    filter: FilterContext,
    fullHistory: HistoryAnalyzer,
    fullHistoryBuckets: number,
    partialHistory: HistoryAnalyzer,
    partialHistoryBuckets: number,
    acceptableFlakinessRate: number,
  }, TestsReport>;

  private _dailyOutcomes: SyncComputationCache<{
    testIndex: TestIndex,
    history: HistoryAnalyzer,
    timelines: Timeline[],
    testId?: Stats.TestId,
    resultDays: number,
  }, T.TestOutcomeCounts[]>;

  constructor(
    private _ppid: ProjectPublicId,
    memoryCache: SharedCacheStore,
    serverTiming: ServerTiming,
    private _getReportIndex: (p: ProjectPublicId) => Promise<ReportIndex.ReportIndex>,
    private _getStatsAnalyzer: (s3id: S3Stats.Id) => Promise<StatsAnalyzer>,
    private _getRepo: (p: ProjectPublicId) => Promise<Git.Repository|undefined>,
  ) {
    this._commitAnalyzers = new SyncComputationCache({
      cache: memoryCache,
      size: 1000, // 1000 commits cached per project.
      onGetTiming: (input, key, since, until, cacheHit, cacheSize) => {
        serverTiming.recordTraceEvent('q.commitAnalyzers', since, until);
        serverTiming.recordProjectCacheTelemetry(`q.commitAnalyzers`, this._ppid, until - since, cacheHit, cacheSize);
      },
      shouldCache: (analyzer) => !analyzer.isEmpty(),
      etag: ({ commitId, stats }) => stats.getCommitAnalyzerEtag(commitId),
      compute({ commitId, stats }, etag) {
        return stats.getCommitAnalyzer(commitId);
      },
    });

    this._spanAnalyzers = new SyncComputationCache({
      cache: memoryCache,
      size: 300, // 4 months = 120 days. 300 should be enough for 2 completely distinct branches.
      onGetTiming: (input, key, since, until, cacheHit, cacheSize) => {
        serverTiming.recordTraceEvent('q.spanAnalyzers', since, until);
        serverTiming.recordProjectCacheTelemetry(`q.spanAnalyzers`, this._ppid, until - since, cacheHit, cacheSize);
      },
      etag: commits => SpanAnalyzer.etag(commits),
      shouldCache: (analyzer) => !analyzer.isEmpty(),
      compute: (commits, etag) => new SpanAnalyzer(commits, etag),
    });

    this._filterContexts = new SyncComputationCache({
      cache: memoryCache,
      size: 10,
      etag: ({ testIndex, fql }) => TestIndex.etagFilterContext(testIndex, fql),
      onGetTiming: (input, key, since, until, cacheHit, cacheSize) => {
        serverTiming.recordTraceEvent('q.getFilterContext', since, until);
        serverTiming.recordProjectCacheTelemetry(`q.getFilterContext`, this._ppid, until - since, cacheHit, cacheSize);
      },
      compute: ({ testIndex, fql }, etag) => {
        return testIndex.createFilterContext(fql, etag);
      }
    });

    this._historyAnalyzers = new SyncComputationCache({
      cache: memoryCache,
      size: 10,
      etag: ({ historyDays, regressionWindowDays }) => HistoryAnalyzer.etag(historyDays, regressionWindowDays),
      onGetTiming: (input, key, since, until, cacheHit, cacheSize) => {
        serverTiming.recordTraceEvent('q.getHistoryAnalyzer', since, until);
        serverTiming.recordProjectCacheTelemetry(`q.getHistoryAnalyzer`, this._ppid, until - since, cacheHit, cacheSize);
      },
      compute: ({ historyDays, regressionWindowDays }, etag) => {
        return new HistoryAnalyzer(historyDays, regressionWindowDays, etag);
      },
    });

    this._timelineTestsReport = new SyncComputationCache({
      cache: memoryCache,
      size: Timeline.MAX_TIMELINES,
      etag: ({ timeline, span, history }) => xxHashObject({
        timeline: timeline.etag(),
        span: span.etag(),
        history: history.etag(),
      }),
      onGetTiming: (input, key, since, until, cacheHit, cacheSize) => {
        serverTiming.recordTraceEvent('q.getTestsReport', since, until);
        serverTiming.recordProjectCacheTelemetry(`q.getTestsReport`, this._ppid, until - since, cacheHit, cacheSize);
      },
      shouldCache: report => !report.isEmpty(),
      compute: ({ timeline, span, history }) => {
        const lastCommit = span.commits.at(-1);
        const unhealthyTests = lastCommit ? history.unhealthyTests(timeline, lastCommit.commitId) : T.EMPTY_TESTS;
        return TimelineTestsReport.create(timeline, span, unhealthyTests);
      },
    });

    this._timelineCommitReport = new SyncComputationCache({
      cache: memoryCache,
      size: Timeline.MAX_TIMELINES * 1000,
      etag: ({ timeline, commitAnalyzer, history }) => xxHashObject({
        timeline: timeline.etag(),
        commitAnalyzer: commitAnalyzer.etag(),
        history: history.etag(),
      }),
      onGetTiming: (input, key, since, until, cacheHit, cacheSize) => {
        serverTiming.recordTraceEvent('q.timelineCommitReport', since, until);
        serverTiming.recordProjectCacheTelemetry(`q.timelineCommitReport`, this._ppid, until - since, cacheHit, cacheSize);
      },
      shouldCache: report => !report.isEmpty(),
      compute: ({ timeline, commitAnalyzer, history }) => {
        const unhealthyTests = history.unhealthyTests(timeline, commitAnalyzer.commitId);
        return TimelineCommitReport.create(timeline, commitAnalyzer, unhealthyTests);
      },
    });

    this._commitsReport = new SyncComputationCache({
      cache: memoryCache,
      size: 10,
      etag: ({ analyzers, timelines, history, filter }) => xxHashObject({
        analyzers: SpanAnalyzer.etag(analyzers),
        timelines: Timeline.etagAll(timelines),
        history: history.etag(),
        filter: filter.etag,
      }),
      onGetTiming: (input, key, since, until, cacheHit, cacheSize) => {
        serverTiming.recordTraceEvent('q.commitsReport', since, until);
        serverTiming.recordProjectCacheTelemetry(`q.commitsReport`, this._ppid, until - since, cacheHit, cacheSize);
      },
      shouldCache: commitStats => commitStats.length > 0,
      compute: ({ analyzers, timelines, history, filter, commits }) => {
        return commits.map((commit, idx) => {
          const reports = timelines.map(timeline => this._timelineCommitReport.get({
            commitAnalyzer: analyzers[idx],
            history,
            timeline,
          }).applyFilter(filter));
          return TimelineCommitReport.wireCommitStats(commit, reports);
        });
      },
    });

    this._reportCounters = new SyncComputationCache({
      cache: memoryCache,
      size: 10,
      etag: ({ span, history, timelines, filter }) => xxHashObject({
        span: span.etag(),
        history: history.etag(),
        timelines: Timeline.etagAll(timelines),
        filter: filter.etag,
      }),
      onGetTiming: (input, key, since, until, cacheHit, cacheSize) => {
        serverTiming.recordTraceEvent('q.getReportCounter', since, until);
        serverTiming.recordProjectCacheTelemetry(`q.getReportCounter`, this._ppid, until - since, cacheHit, cacheSize);
      },
      // Only cache if we spent enough time.
      shouldCache: (report, duration) => duration > 10,
      compute: ({ span, history, timelines, filter }) => {
        const result = {
          testOutcomes: T.newOutcomeCounts(),
          errors: new Set<Stats.ErrorId>(),
          timelines: 0,
          tags: new Set<string>,
          annotations: new Set<Stats.AnnotationId>(),
        }
        for (const timeline of timelines) {
          const report = this._timelineTestsReport.get({
            span,
            history,
            timeline,
          }).applyFilter(filter);
          if (T.isEmptyOutcomes(report.outcomes))
            continue;
          ++result.timelines;
          T.accumulateOutcomeCounts(result.testOutcomes, T.newOutcomeCounts(report.outcomes));
          for (const error of report.errorTests.keys())
            result.errors.add(error.errorId);
          for (const tag of report.tagTests.keys())
            result.tags.add(tag);
          for (const annotation of report.annotationTests.keys())
            result.annotations.add(annotation.annotationId);
        }
        return {
          testOutcomes: result.testOutcomes,
          errors: result.errors.size,
          tests: T.sumAllCounts(result.testOutcomes),
          timelines: result.timelines,
          tags: result.tags.size,
          annotations: result.annotations.size,
        };
      },
    });

    this._testsReport = new SyncComputationCache({
      cache: memoryCache,
      size: 10, // Not more than 10 reports are stored per project.
      etag: ({ span, fullHistory, partialHistory, timelines, filter, fullHistoryBuckets, partialHistoryBuckets, acceptableFlakinessRate }) => xxHashObject({
        span: span.etag(),
        timelines: Timeline.etagAll(timelines),
        filter: filter.etag,
        fullHistory: fullHistory.etag(),
        fullHistoryBuckets,
        partialHistory: partialHistory.etag(),
        partialHistoryBuckets,
        acceptableFlakinessRate,
      }),
      onGetTiming: (input, key, since, until, cacheHit) => {
        serverTiming.recordTraceEvent('q.getTestsReport', since, until);
        serverTiming.recordProjectCacheTelemetry(`q.getTestsReport`, this._ppid, until - since, cacheHit, this._testsReport.cacheSize());
      },
      shouldCache: report => !report.isEmpty(),
      compute: ({ testIndex, span, timelines, filter, fullHistory, fullHistoryBuckets, partialHistory, partialHistoryBuckets, acceptableFlakinessRate }) => {
        const timelineReports = timelines.map(timeline => {
          const report = this._timelineTestsReport.get({
            timeline,
            span,
            history: fullHistory,
          });
          const intermediateOutcomes: SpanStats[] = [];
          for (let i = 0; i < fullHistoryBuckets; ++i)
            intermediateOutcomes.push(fullHistory.dayStats(timeline, i, true /* computeDurations */));
          const historyOutcomes: SpanStats[] = [];
          for (let i = 0; i < partialHistoryBuckets; ++i)
            historyOutcomes.push(partialHistory.dayStats(timeline, i, true /* computeDurations */));
          return report.applyFilter(filter).setIntermediateStats(intermediateOutcomes).setHistoryStats(historyOutcomes);
        });
        return new TestsReport(testIndex, timelineReports, acceptableFlakinessRate);
      },
    });

    this._dailyOutcomes = new SyncComputationCache({
      cache: memoryCache,
      size: 100,
      etag: ({ testIndex, history, timelines, testId, resultDays }) => xxHashObject({
        testIndex: testIndex.etag(),
        history: history.etag(),
        timelines: Timeline.etagAll(timelines),
        testId,
        resultDays,
      }),
      onGetTiming: (input, key, since, until, cacheHit, cacheSize) => {
        serverTiming.recordTraceEvent('q.getDailyOutcomes', since, until);
        serverTiming.recordProjectCacheTelemetry(`q.getDailyOutcomes`, this._ppid, until - since, cacheHit, cacheSize);
      },
      compute: ({ testIndex, history, timelines, testId, resultDays }) => {
        const filter = this._filterContexts.get({ testIndex, fql: testId ? Query.create({ testId }) : Query.EMPTY });
        return history.days().slice(0, resultDays).map((day, dayIdx) => {
          const acc = T.newOutcomeCounts();
          if (!day.commits.length)
            return acc;
          for (const timeline of timelines) {
            let c = history.dayStats(timeline, dayIdx, false /* computeDurations */).testOutcomes;
            c = T.intersectRanges(c, filter.testsMask);
            T.accumulateOutcomeCounts(acc, T.newOutcomeCounts(c));
          }
          return acc;
        });
      }
    });
  }

  public async runEnvironments(commitOptions: WireTypes.ListCommitOptions): Promise<WireTypes.RunEnvironment[]> {
    const [repo, index] = await Promise.all([
      this._getRepo(this._ppid),
      this._getReportIndex(this._ppid),
    ]);
    const commits = repo?.iterator(commitOptions.head).collect(commitOptions) ?? [];
    const span = await this._fetchSpan(index, commits)
    return CommitAnalyzer.runEnvironments(span.commits);
  }

  public async runEnvironmentsForTest(commitOptions: WireTypes.ListCommitOptions, testId: Stats.TestId): Promise<WireTypes.RunEnvironment[]> {
    const [repo, index] = await Promise.all([
      this._getRepo(this._ppid),
      this._getReportIndex(this._ppid),
    ]);

    const commits = repo?.iterator(commitOptions.head).collect(commitOptions) ?? [];
    const span = await this._fetchSpan(index, commits)
    const filter = await this._filterContexts.get({ testIndex: index.testIndex(), fql: Query.create({ testId }) });
    if (Ranges.cardinality(filter.testsMask) !== 1)
      return [];
    return CommitAnalyzer.runEnvironmentsForTest(span.commits, filter.testsMask[0]);
  }

  public async reportCounters(options: {
    commitOptions: WireTypes.ListCommitOptions,
    timelineSplit: WireTypes.JSONTimelineSplit,
    fql?: string,
    timeZoneId: string,
    customHistoryHead?: string,
    regressionWindowDays: number,
  }): Promise<{ testOutcomes: T.TestOutcomeCounts, tests: number, timelines: number, errors: number, annotations: number, tags: number }> {
    const [repo, index] = await Promise.all([
      this._getRepo(this._ppid),
      this._getReportIndex(this._ppid),
    ]);
    const reportIterator = repo?.iterator(options.commitOptions.head); 
    const reportCommits = reportIterator?.collect(options.commitOptions) ?? [];
    const lastCommit = reportCommits.at(-1);
    if (!repo || !lastCommit || !reportIterator)
      return { testOutcomes: T.newOutcomeCounts(), tests: 0, timelines: 0, errors: 0, annotations: 0, tags: 0 };

    const historyHead = options.customHistoryHead ? repo.commit(options.customHistoryHead) : lastCommit;
    const historyIterator = options.customHistoryHead && historyHead && !reportCommits.some(commit => commit.commitId === historyHead.commitId) ? repo.iterator(options.customHistoryHead) : reportIterator;
    const historyCommits = historyHead ? historyIterator.collect({
      sinceTimestamp: xxxDaysInPast(historyHead.timestamp, options.timeZoneId, options.regressionWindowDays)
    }) : [];

    const [span, history] = await Promise.all([
      this._fetchSpan(index, reportCommits),
      this._fetchHistoryAnalyzer(index, bucketizeCommits([reportCommits, historyCommits].flat(), options.timeZoneId), options.regressionWindowDays),
    ]);

    const envs = CommitAnalyzer.runEnvironments(span.commits);
    const timelines = TimelineSplit.deserialize(options.timelineSplit).timelines(envs);
    const filter = await this._filterContexts.get({ testIndex: index.testIndex(), fql: Query.parse(options.fql ?? '') });
    const result = await this._reportCounters.get({
      span,
      history,
      filter,
      timelines,
    });
    assert(result);
    return result;
  }

  public async testsReport(options: {
    commitOptions: WireTypes.ListCommitOptions,
    timelineSplit: WireTypes.JSONTimelineSplit,
    fql?: string,
    timeZoneId: string,
    regressionWindowDays: number,
    dailyReportBreakdown: boolean,
    customHistoryHead?: string,
    historyBuckets: number,
    acceptableFlakinessRate: number,
  }): Promise<TestsReport> {
    const [repo, index] = await Promise.all([
      this._getRepo(this._ppid),
      this._getReportIndex(this._ppid),
    ]);
    const reportIterator = repo?.iterator(options.commitOptions.head); 
    const reportCommits = reportIterator?.collect(options.commitOptions) ?? [];
    const lastCommit = reportCommits.at(-1);
    if (!repo || !lastCommit || !reportIterator)
      return new TestsReport(index.testIndex(), [], options.acceptableFlakinessRate);

    const historyHead = options.customHistoryHead ? repo.commit(options.customHistoryHead) : lastCommit;
    const historyIterator = options.customHistoryHead && historyHead && !reportCommits.some(commit => commit.commitId === historyHead.commitId) ? repo.iterator(options.customHistoryHead) : reportIterator;
    const historyCommits = historyHead ? historyIterator.collect({
      sinceTimestamp: xxxDaysInPast(historyHead.timestamp, options.timeZoneId, options.regressionWindowDays + options.historyBuckets),
    }) : [];

    //const reportBuckets = buckets.findIndex(bucket => bucket.some(c => c.commitId === lastCommit.commitId)) + 1;

    const [span, fullHistory, partialHistory] = await Promise.all([
      this._fetchSpan(index, reportCommits),
      this._fetchHistoryAnalyzer(index, bucketizeCommits([reportCommits, historyCommits].flat(), options.timeZoneId), options.regressionWindowDays),
      this._fetchHistoryAnalyzer(index, bucketizeCommits(historyCommits, options.timeZoneId), options.regressionWindowDays),
    ]);



    const envs = CommitAnalyzer.runEnvironments(span.commits);
    const timelines = TimelineSplit.deserialize(options.timelineSplit).timelines(envs);
    const filter = await this._filterContexts.get({ testIndex: index.testIndex(), fql: Query.parse(options.fql ?? '') });
    const report = await this._testsReport.get({
      testIndex: index.testIndex(),
      span,
      filter,
      timelines,
      fullHistory,
      fullHistoryBuckets: options.dailyReportBreakdown ? fullHistory.days().findIndex(day => day.commits.some(commit => commit.commitId === lastCommit.commitId)) + 1 : 0,
      partialHistory,
      partialHistoryBuckets: options.historyBuckets,
      acceptableFlakinessRate: options.acceptableFlakinessRate,
    });
    assert(report);
    return report;
  }

  public async commitsReport(options: {
    commitOptions: WireTypes.ListCommitOptions,
    timelineSplit: WireTypes.JSONTimelineSplit,
    testId?: Stats.TestId,
    timeZoneId: string,
    customHistoryHead?: string,
    regressionWindowDays: number,
  }): Promise<WireTypes.CommitStats[]> {
    const [repo, index] = await Promise.all([
      this._getRepo(this._ppid),
      this._getReportIndex(this._ppid),
    ]);
    if (!repo)
      return [];
    const reportIterator = repo.iterator(options.commitOptions.head);
    const reportCommits = reportIterator.collect(options.commitOptions) ?? [];
    const lastCommit = reportCommits.at(-1);
    if (!lastCommit || !reportIterator)
      return [];

    const historyHead = options.customHistoryHead ? repo.commit(options.customHistoryHead) : lastCommit;
    const historyIterator = options.customHistoryHead && historyHead && !reportCommits.some(commit => commit.commitId === historyHead.commitId) ? repo.iterator(options.customHistoryHead) : reportIterator;
    const historyCommits = historyHead ? historyIterator.collect({
      sinceTimestamp: xxxDaysInPast(historyHead.timestamp, options.timeZoneId, options.regressionWindowDays)
    }) : [];

    const [span, history] = await Promise.all([
      this._fetchSpan(index, reportCommits),
      this._fetchHistoryAnalyzer(index, bucketizeCommits([reportCommits, historyCommits].flat(), options.timeZoneId), options.regressionWindowDays),
    ]);

    const filter = this._filterContexts.get({
      testIndex: index.testIndex(),
      fql: options.testId ? Query.create({ testId: options.testId }) : Query.EMPTY,
    });

    const split = TimelineSplit.deserialize(options.timelineSplit);
    const timelines = split.timelines(CommitAnalyzer.runEnvironments(span.commits));
    return this._commitsReport.get({
      analyzers: span.commits,
      commits: reportCommits,
      timelines,
      filter,
      history,
    });
  }

  public async dailyOutcomesExpanded(options: {
    head: string,
    timeZoneId: string,
    regressionWindowDays: number,
    dailyBucketsCount: number,
    cutoffTestIndex: Stats.TestIndex,
    timelines: WireTypes.JSONTimeline[],
  }): Promise<{ timeline: WireTypes.JSONTimeline, testOutcomes: T.TestOutcomes[], commitCounts: number[], durations: Histogram.CompressedHistogram[][] }[]> {
    const [repo, index] = await Promise.all([
      this._getRepo(this._ppid),
      this._getReportIndex(this._ppid),
    ]);
    const head = repo?.commit(options.head);
    if (!repo || !head)
      return [];

    const reportAndHistoryCommits = repo.iterator(head.commitId).collect({
      sinceTimestamp: xxxDaysInPast(head.timestamp, options.timeZoneId, options.regressionWindowDays + options.dailyBucketsCount),
    });

    const history = await this._fetchHistoryAnalyzer(
      index,
      bucketizeCommits(reportAndHistoryCommits, options.timeZoneId),
      options.regressionWindowDays
    );

    return options.timelines.map(jsonTimeline => {
      const timeline = Timeline.deserialize(jsonTimeline);
      return {
        timeline: timeline.serialize(),
        testOutcomes: history.days().slice(0, options.dailyBucketsCount).map((commits, dayIdx) => {
          return T.capAt(history.dayStats(timeline, dayIdx, true).testOutcomes, options.cutoffTestIndex);
        }),
        commitCounts: history.days().slice(0, options.dailyBucketsCount).map(day => day.commits.length),
        durations: history.days().slice(0, options.dailyBucketsCount).map((commits, dayIdx) => {
          const s = history.dayStats(timeline, dayIdx, true);
          return s.durations.map(h => Histogram.compress(h));
        }),
      };
    });
  }

  public async dailyOutcomes(options: {
    head: string,
    timeZoneId: string,
    sinceDay: Temporal.PlainDate,
    untilDay: Temporal.PlainDate,
    acceptableFlakinessRatio: number,
    regressionWindowDays: number,
    timelineSplit: WireTypes.JSONTimelineSplit,
    testId?: Stats.TestId,
  }): Promise<WireTypes.DayOutcome[]> {
    const [repo, index] = await Promise.all([
      this._getRepo(this._ppid),
      this._getReportIndex(this._ppid),
    ]);
    if (!repo)
      return [];

    const buckets = this._bucketsForCalendar(repo, options.head, options.timeZoneId, options.sinceDay.subtract({ days: options.regressionWindowDays }), options.untilDay);
    const history = await this._fetchHistoryAnalyzer(index, buckets, options.regressionWindowDays);
    
    const split = TimelineSplit.deserialize(options.timelineSplit);
    const days = await this._dailyOutcomes.get({
      testIndex: index.testIndex(),
      testId: options.testId,
      history,
      resultDays: options.sinceDay.until(options.untilDay).days + 1,
      timelines: split.timelines(history.environments()),
    });
    assert(days);
    
    return days.map((acc, dayIdx) => {
      if (!history.days()[dayIdx].commits.length)
        return 'idle';
      return wireOutcomesToOutcome(acc, options.acceptableFlakinessRatio) ?? 'untested';
    });
  }

  public async commitOutcomesExpanded(options: {
    regressionWindowDays: number,
    timeZoneId: string,
    cutoffTestIndex: Stats.TestIndex,
    commitId: Stats.CommitId,
    timelines: WireTypes.JSONTimeline[],
  }): Promise<{ timeline: WireTypes.JSONTimeline, testOutcomes: T.TestOutcomes }[]> {
    const [repo, index] = await Promise.all([
      this._getRepo(this._ppid),
      this._getReportIndex(this._ppid),
    ]);
    const head = repo?.commit(options.commitId);
    if (!head || !repo)
      return [];

    const reportAndHistoryCommits = repo.iterator(head.commitId).collect({
      sinceTimestamp: xxxDaysInPast(head.timestamp, options.timeZoneId, options.regressionWindowDays),
    });
    const history = await this._fetchHistoryAnalyzer(index, bucketizeCommits(reportAndHistoryCommits, options.timeZoneId), options.regressionWindowDays);

    return options.timelines.map(timelineJson => {
      const timeline = Timeline.deserialize(timelineJson);
      return {
        timeline: timeline.serialize(),
        testOutcomes: T.capAt(history.commitStats(timeline, options.commitId, false).testOutcomes, options.cutoffTestIndex),
      };
    });
  }

  private async _fetchSpan(index: ReportIndex.ReportIndex, commits: Git.Commit[]): Promise<SpanAnalyzer> {
    const shards = index.splitIntoShards(commits);
    const analyzers = new Map<Stats.CommitId, CommitAnalyzer>();
    await Promise.all([...shards].map(async ([s3id, commits]) => {
      const statsAnalyzer = await this._getStatsAnalyzer(s3id);
      let time = performance.now();
      for (const commit of commits) {
        analyzers.set(commit.commitId, this._commitAnalyzers.get({ commitId: commit.commitId, stats: statsAnalyzer }));
        if (performance.now() - time > 100) {
          await new Promise(x => process.nextTick(x));
          time = performance.now();
        }
      }
    }));
    const commitAnalyzers = commits.map(commit => analyzers.get(commit.commitId) ?? CommitAnalyzer.createEmpty(commit.commitId));
    return this._spanAnalyzers.get(commitAnalyzers);
  }

  private async _fetchHistoryAnalyzer(index: ReportIndex.ReportIndex, buckets: Git.Commit[][], regressionWindowDays: number) {
    const spans = await Promise.all(buckets.map(bucket => this._fetchSpan(index, bucket)));
    return await this._historyAnalyzers.get({ historyDays: spans, regressionWindowDays });
  }

  private _bucketsForCalendar(
    repo: Git.Repository,
    head: string,
    timeZoneId: string,
    sinceDay: Temporal.PlainDate,
    untilDay: Temporal.PlainDate,
  ): Git.Commit[][] {
    const it = repo.iterator(head);
    const result: Git.Commit[][] = [];
    for (let day = untilDay; Temporal.PlainDate.compare(sinceDay, day) <= 0; day = day.subtract({ days: 1 })) {
      result.push(it.collect({
        sinceTimestamp: day.toZonedDateTime(timeZoneId).epochMilliseconds,
        untilTimestamp: day.add({ days: 1 }).toZonedDateTime(timeZoneId).epochMilliseconds,
      }));
    }
    return result;
  }
}

function xxxDaysInPast(timestamp: number, timezoneId: string, days: number) {
  return Temporal.Instant
    .fromEpochMilliseconds(timestamp)
    .toZonedDateTimeISO(timezoneId)
    .startOfDay()
    .subtract({ days })
    .epochMilliseconds;
}

function bucketizeCommits(commits: Git.Commit[], timezoneId: string) {
  if (!commits.length)
    return [];
  let day = Temporal.Instant
    .fromEpochMilliseconds(commits[0].timestamp)
    .toZonedDateTimeISO(timezoneId)
    .startOfDay()
    .add({ days: 1 });
  const result: Git.Commit[][] = [];
  let commitIdx = 0;
  // Safety check: make sure to never return more than 365 days.
  while (commitIdx < commits.length && result.length < 365) {
    const dayCommits: Git.Commit[] = [];
    result.push(dayCommits);
    day = day.subtract({ days: 1 });
    for (; commitIdx < commits.length && commits[commitIdx].timestamp > day.epochMilliseconds; ++commitIdx)
      dayCommits.push(commits[commitIdx]);
  }
  return result;
}