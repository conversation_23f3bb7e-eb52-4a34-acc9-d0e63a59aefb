import crypto, { KeyObject } from 'crypto';
import debug from 'debug';
import jwt from 'jsonwebtoken';
import ms from 'ms';
import { setTimeout } from 'timers/promises';
import { buildInfo } from './buildInfo.js';
import { Config } from './configuration.js';

const log = debug('fk:license');

const algorithm = { name: "ECDSA", namedCurve: "P-256" };
const keyUsages: KeyUsage[] = [ 'sign', 'verify' ];

export type RegistryTokenCapability = 'push' | 'pull';

const REGISTRY_URL = 'cr.flakiness.io';

export type LicensePayload = {
  username: "v0",
  account_id: string,
  capabilities: RegistryTokenCapability[],
  exp: number, // seconds
  aud: string // regustry url
};

export class License {
  static async createLicense(options: { privateKey: string, accountId: string, caps: RegistryTokenCapability[], untilMs: number }) {
    const licensePrivateKeyJSON = (() => {
      try {
        return JSON.parse(atob(options.privateKey));
      } catch (e) {
        throw new Error(`Failed to parse license private key`);
      }
    })();
    const privateKey = await crypto.subtle.importKey('jwk', licensePrivateKeyJSON, algorithm, true, ['sign']);
    const tokenPayload: LicensePayload = {
      username: "v0",
      account_id: options.accountId,
      capabilities: options.caps,
      exp: Math.floor(options.untilMs / 1000), // must be in seconds.
      aud: REGISTRY_URL,
    };

    const licenseKey = await jwt.sign(tokenPayload, KeyObject.from(privateKey), {
      algorithm: "ES256",
    });
  
    return new License(tokenPayload, licenseKey);
  }
  
  static async generateKeys(): Promise<{ privateKey: string, publicKey: string }> {
    const { privateKey, publicKey } = await crypto.subtle.generateKey(algorithm, true, keyUsages);
  
    return {
      privateKey: btoa(JSON.stringify(await crypto.subtle.exportKey("jwk", privateKey))),
      publicKey: btoa(JSON.stringify(await crypto.subtle.exportKey("jwk", publicKey))),
    };
  }

  static async initialize(licenseKey: string) {
    const licensePublicKeyJSON = (() => {
      try {
        return JSON.parse(atob(buildInfo.flakinessLicensePublicKey));
      } catch (e) {
        throw new Error(`Failed to parse license public key`);
      }
    })();
    const publicKey = await crypto.subtle.importKey('jwk', licensePublicKeyJSON, algorithm, true, ['verify']);
    try {
      const payload = jwt.verify(licenseKey, KeyObject.from(publicKey)) as LicensePayload;
      return new License(payload, licenseKey);
    } catch (e) {
      return undefined;
    }
  }

  static async licenseKeyFromEnvOrDie(): Promise<string> {
    const config = await Config.fromEnvironmentOrDie('License configuration', async env => ({
      licenseKey: await env.secret({
        env: 'FLAKINESS_LICENSE',
        description: `License key for server`,
        required: true,
      }),
    }));
    return config.licenseKey;
  }

  static async initializeOrDie(licenseKey: string): Promise<License> {
    const license = await License.initialize(licenseKey);
    if (!license) {
      log(`ERROR: License key is invalid or expired`);
      process.exit(1);
    }
    return license;
  }

  private _expiredPromise: Promise<void>;

  constructor(private _payload: LicensePayload, private _licenseKey: string) {
    this._expiredPromise = Promise.resolve().then(async () => {
      while (this.deadlineMs() > Date.now()) {
        const timeout = Math.min(ms('1 day'), this.deadlineMs() - Date.now());
        if (timeout > 0)
          await setTimeout(timeout, undefined, { ref: false });
      }
    });
  }

  licenseKey() { 
    return this._licenseKey;
  }

  description() {
    return [
      `- License for: ${this._payload.account_id}`,
      `- status: ${Date.now() < this.deadlineMs() ? 'ACTIVE' : 'EXPIRED'}`,
      `- valid until: ${new Date(this._payload.exp * 1000)}`,
      `- container registry permissions: ${JSON.stringify(this._payload.capabilities)}`,
      `- registry url: ${this._payload.aud}`,
    ].join('\n');
  }

  jsonDescription() {
    return {
      accountId: this._payload.account_id,
      active: Date.now() < this.deadlineMs(),
      until: new Date(this._payload.exp * 1000),
      permissions: this._payload.capabilities,
      registry: this._payload.aud,
    };
  }

  expiredPromise() {
    return this._expiredPromise;
  }

  deadlineMs() {
    return this._payload.exp * 1000;
  }
}
