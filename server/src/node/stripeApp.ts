
import { Organization, OrgId, OrgPublicId, ProductPlan, ProductPlanId, ProductPlanPublicId, UserId } from "@flakiness/database";
import { FlakinessReport } from "@flakiness/report";
import { randomUUID } from "@flakiness/shared/common/utils.js";
import assert from "assert";
import express from "express";
import ms from "ms";
import Stripe from "stripe";
import { SingleflightCache } from "../common/singleflightcache.js";
import { WireTypes } from "../common/wireTypes.js";
import { CachedDatabase } from "./cachedDatabase.js";
import { Config } from "./configuration.js";
import { CronService } from "./cronService.js";
import { utcDayStart } from "./reportIndex.js";
import { ServerTiming } from "./serverTiming.js";
import { UploadWorker } from "./uploadWorker.js";
import { XNotify, XNotifyChannel } from "./xnotify.js";

export type StripeConfig = {
  apiKey: string,
  webhookSecret: string,
  callbackUrl: string,

  seatsProductId: string,
  storageProductId: string,
  testRunsProductId: string,

  storageMeterEventName: string,
  testRunsMeterEventName: string,
}

function parseMeteredPrice(price: Stripe.Price): { freeUnits: number, priceExtra: number, billing: WireTypes.BillingPeriod } {
  const tiers = price.tiers;
  assert(tiers && tiers.length && price.tiers_mode === 'graduated');
  assert(price.recurring?.interval);
  const free = tiers[0].unit_amount_decimal === '0' ? tiers[0].up_to : 0;
  const extra = tiers.find(tier => tier.unit_amount_decimal && parseFloat(tier.unit_amount_decimal) > 0);
  return {
    billing: price.recurring.interval,
    freeUnits: free ?? 0,
    // Convert from cents to US Dollars since Stripe has all prices in cents.
    priceExtra: parseFloat(extra?.unit_amount_decimal ?? '0') / 100,
  };
}

function parseUnitPrice(price: Stripe.Price): { price: number, billing: WireTypes.BillingPeriod } {
  assert(price.recurring?.interval);
  // Convert from cents to US Dollars since Stripe has all prices in cents.
  return {
    price: parseFloat(price.unit_amount_decimal ?? '0') / 100,
    billing: price.recurring.interval,
  };
}

const CRON_JOB_SYNC_WITH_STRIPE = 'report-metrics-to-stripe';

export class StripeApp {
  static async configFromEnv(): Promise<StripeConfig|undefined> {
    return await Config.fromEnvironment('Stripe configuration', async env => ({
      apiKey: await env.secret({
        env: 'STRIPE_API_KEY',
        description: ``,
        required: true,
      }),
      webhookSecret: await env.secret({
        env: 'STRIPE_WEBHOOK_SECRET',
        description: ``,
        required: true,
      }),
      callbackUrl: await env.url({
        env: 'STRIPE_CALLBACK_URL',
        description: ``,
        required: true,
      }),

      // Products:
      seatsProductId: await env.secret({
        env: 'STRIPE_PRODUCT_ID_SEATS',
        description: ``,
        required: true,
      }),
      storageProductId: await env.secret({
        env: 'STRIPE_PRODUCT_ID_STORAGE',
        description: ``,
        required: true,
      }),
      testRunsProductId: await env.secret({
        env: 'STRIPE_PRODUCT_ID_TEST_RUNS',
        description: ``,
        required: true,
      }),

      // Meters:
      storageMeterEventName: await env.secret({
        env: 'STRIPE_METER_STORAGE',
        description: ``,
        required: true,
      }),
      testRunsMeterEventName: await env.secret({
        env: 'STRIPE_METER_TEST_RUNS',
        description: ``,
        required: true,
      }),
    }));
  }

  private _stripe: Stripe;

  private _allSubscriptionsCache = new SingleflightCache<'', Stripe.Subscription[]>({
    size: 500,
    ttl: ms('10min'),
    key: id => id,
    fetch: async (id, key, stale) => {
      const result: Stripe.Subscription[] = [];
      await this._stripe.subscriptions.list({ limit: 100, expand: ['data.items.data.price', 'data.customer'] }).autoPagingEach(sub => {
        if (!sub.metadata.org_public_id)
          return;
        result.push(sub);
      });
      return result;
    },
  });

  private _allPricesCache = new SingleflightCache<'', Stripe.Price[]>({
    size: 500,
    ttl: ms('10min'),
    key: id => id,
    fetch: async (id, key, stale) => {
      const result: Stripe.Price[] = [];
      await this._stripe.prices.list({
        limit: 100,
        expand: ['data.tiers'],
      }).autoPagingEach(price => {
        result.push(price);
      });
      return result;
    },
  });

  private _upcomingInvoice = new SingleflightCache<string, Stripe.UpcomingInvoice>({
    size: 500,
    ttl: ms('5min'),
    key: subscriptionId => subscriptionId,
    fetch: async (subscriptionId, key, stale) => {
      // Retrieve the upcoming invoice for the specified subscription
      return await this._stripe.invoices.retrieveUpcoming({
        subscription: subscriptionId,
        expand: ['lines.data.price'],
      });
    },
  });

  private _xSubscriptionsChanged: XNotifyChannel<unknown>;
  private _xPricesChanged: XNotifyChannel<unknown>;

  constructor(
    private _uploadWorker: UploadWorker,
    private _db: CachedDatabase,
    private _cron: CronService,
    private _xnotify: XNotify,
    private _options: StripeConfig,
    private _serverTiming: ServerTiming,
  ) {
    this._stripe = new Stripe(this._options.apiKey);

    this._xSubscriptionsChanged = this._xnotify.createChannel('stripe-subscriptions-changed', async () => {
      this._allSubscriptionsCache.refresh('', { dropCached: true });
    });
    this._xPricesChanged = this._xnotify.createChannel('stripe-prices-changed', async () => {
      this._allPricesCache.refresh('', { dropCached: true });
    });
  }

  async scheduleSyncWithStripe() {
    await this._cron.runScheduledJobImmediately(CRON_JOB_SYNC_WITH_STRIPE);
  }

  async initialize() {
    await this._cron.setRecurringJob(CRON_JOB_SYNC_WITH_STRIPE, ms('1 hour') as FlakinessReport.DurationMS, async ({ signal }) => {
      // Fetch all customers from stripe.
      const customers: Stripe.Customer[] = [];
      await this._stripe.customers.list({ limit: 100 }).autoPagingEach((customer) => {
        signal.throwIfAborted();
        if (!customer.deleted)
          customers.push(customer);
      });

      for (const customer of customers) {
        signal.throwIfAborted();
        const publicOrgId = customer.metadata.org_public_id;
        // If this customer is not associated with org, then do nothing.
        if (!publicOrgId)
          continue;
        const org = await this._db.orgs.getByPublicId(publicOrgId as OrgPublicId);
        // If customer's associated organization was deleted, then delete this customer. 
        // This will mark all its subscriptions as cancelled.
        if (!org) {
          await this._stripe.customers.del(customer.id, { signal });
          continue;
        }
        // Otherwise, we should report org's metrics to stripe if there's a current subscription.
        const sub = await this._currentOrgSubscription(org);
        if (!sub)
          continue;
        const allMetrics = await this._uploadWorker.orgMetrics(org.org_id);
        const toReport = computeMetricsForBillingPeriod(allMetrics, sub);

        await this._stripe.billing.meterEvents.create({
          event_name: this._options.storageMeterEventName,
          payload: {
            value: String(toReport.averageGB),
            stripe_customer_id: customer.id,
          },
        });

        await this._stripe.billing.meterEvents.create({
          event_name: this._options.testRunsMeterEventName,
          payload: {
            value: String(toReport.testRuns),
            stripe_customer_id: customer.id,
          },
        });
      }
    });
  }

  async toWirePlan(plan: Omit<ProductPlan, 'plan_id'>): Promise<WireTypes.ProductPlan> {
    using timing = this._serverTiming.start('stripe');
    const allPrices = await this._allPricesCache.get('') ?? [];
    const seats = allPrices.find(price => price.id === plan.seats_price_id);
    const storage = allPrices.find(price => price.id === plan.storage_price_id);
    const testRuns = allPrices.find(price => price.id === plan.testruns_price_id);
    const billing = seats?.recurring?.interval ?? storage?.recurring?.interval ?? testRuns?.recurring?.interval;
    // At least some price must be defined as recurring and all prices should have the same billing.
    assert(billing);
    const parsedStoarge = storage ? parseMeteredPrice(storage) : undefined;
    const parsedTestRuns = testRuns ? parseMeteredPrice(testRuns) : undefined;
    const parsedSeats = seats ? parseUnitPrice(seats) : undefined;
    const org = plan.org_public_id ? await this._db.orgs.getByPublicId(plan.org_public_id) : undefined;
    return {
      id: plan.plan_public_id,
      name: plan.name,
      trialDays: plan.trial_days ?? undefined,
      billing,
      orgSlug: org?.org_slug,
      seats: plan.seats ?? undefined,
      storage: parsedStoarge?.freeUnits,
      testRuns: parsedTestRuns?.freeUnits,
      price: plan.seats && parsedSeats ? plan.seats * parsedSeats.price : 0,
      priceExtraStorage: parsedStoarge?.priceExtra ?? 0,
      priceExtraTestRuns: parsedTestRuns?.priceExtra ?? 0,
      maxDataRetentionDays: plan.max_data_retention_days,
    };
  }

  private _computeMaxDataRetention(subscription: Stripe.Subscription): number {
    const maxDataRetentionDays = parseInt(subscription.metadata.max_data_retention_days, 10);
    return isNaN(maxDataRetentionDays) ? 365 : maxDataRetentionDays;
  }

  private async _subscriptionToWirePlan(subscription: Stripe.Subscription): Promise<WireTypes.ProductPlan> {
    const seatsItem = subscription.items.data.find(item => item.price.product === this._options.seatsProductId);
    const storageItem = subscription.items.data.find(item => item.price.product === this._options.storageProductId);
    const testRunsItem = subscription.items.data.find(item => item.price.product === this._options.testRunsProductId);

    const rawPlan: ProductPlan = {
      name: subscription.metadata.plan_name,
      org_public_id: subscription.metadata.plan_org_public_id as OrgPublicId,
      plan_id: 0 as ProductPlanId,
      plan_public_id: (subscription.metadata.plan_public_id ?? randomUUID()) as ProductPlanPublicId,
      seats: seatsItem?.quantity ?? null,
      seats_price_id: seatsItem?.price.id ?? null,
      storage_price_id: storageItem?.price.id ?? null,
      testruns_price_id: testRunsItem?.price.id ?? null,
      trial_days: subscription.trial_end ? Math.ceil((subscription.trial_end * 1000 - Date.now()) / ms('1 day')) : null,
      max_data_retention_days: this._computeMaxDataRetention(subscription),
    };

    return this.toWirePlan(rawPlan);
  }

  async commonProductPlans() {
    using timing = this._serverTiming.start('stripe');
    const plans = await this._db.productPlans.list();
    return await Promise.all(plans.filter(plan => !plan.org_public_id).map(plan => this.toWirePlan(plan)));
  }

  async orgPlans(org: Organization) {
    using timing = this._serverTiming.start('stripe');
    const plans = await this._db.productPlans.list();
    return await Promise.all(plans.filter(plan => plan.org_public_id === org.org_public_id).map(plan => this.toWirePlan(plan)));
  }

  async reportTestRuns(options: {
    orgId: OrgId,
    testRunsCount: number,
    timestampMs: FlakinessReport.UnixTimestampMS,
    jobId: string,
  }) {
    const org = await this._db.orgs.get(options.orgId);
    // Looks like the org got deleted and we didn't have a chance to report metrics.
    if (!org)
      return;
    const customerId = await this._ensureStripeCustomerId(org);

    await this._stripe.billing.meterEvents.create({
      event_name: this._options.testRunsMeterEventName,
      payload: {
        value: String(options.testRunsCount),
        stripe_customer_id: customerId,
      },
      timestamp: Math.floor(options.timestampMs / 1000),
      identifier: options.jobId,
    });
  }

  async reportOrgSize(options: {
    orgId: OrgId,
    totalGB: number,
  }) {
    const org = await this._db.orgs.get(options.orgId);
    // Looks like the org got deleted and we didn't have a chance to report metrics.
    if (!org)
      return;
    const customerId = await this._ensureStripeCustomerId(org);
    await this._stripe.billing.meterEvents.create({
      event_name: this._options.storageMeterEventName,
      payload: {
        value: String(options.totalGB),
        stripe_customer_id: customerId,
      },
    });
  }

  async listPrices() {
    using timing = this._serverTiming.start('stripe');
    const prices = await this._allPricesCache.get('') ?? [];
    const storagePrices = prices.filter(price => price.active && price.product === this._options.storageProductId);
    const testRunsPrices = prices.filter(price => price.active && price.product === this._options.testRunsProductId);
    const seatPrices = prices.filter(price => price.active && price.product === this._options.seatsProductId);

    const compareMeteredPrices = (a: Stripe.Price, b: Stripe.Price) => {
      const pa = parseMeteredPrice(a);
      const pb = parseMeteredPrice(b);
      if (pa.freeUnits !== pb.freeUnits)
        return pa.freeUnits - pb.freeUnits;
      return pa.priceExtra - pb.priceExtra;
    }

    const compareUnitPrices = (a: Stripe.Price, b: Stripe.Price) => {
      const pa = parseUnitPrice(a);
      const pb = parseUnitPrice(b);
      return pa.price - pb.price;
    }

    storagePrices.sort(compareMeteredPrices);
    testRunsPrices.sort(compareMeteredPrices);
    seatPrices.sort(compareUnitPrices);

    return {
      testRuns: testRunsPrices.map(price => {
        const parsed = parseMeteredPrice(price);
        const free1k = Math.round(parsed.freeUnits / 1000);
        const price1kExtra = parsed.priceExtra * 1000;
        return {
          priceId: price.id,
          billing: parsed.billing,
          description: price1kExtra === 0 ? `Free` : `${free1k}K test runs free, $${price1kExtra}K/${parsed.billing} extra`,
        } satisfies WireTypes.ProductPlanPrice;
      }),
      storage: storagePrices.map(price => {
        const parsed = parseMeteredPrice(price);
        return {
          priceId: price.id,
          billing: parsed.billing,
          description: parsed.priceExtra === 0 ? `Free` : `${parsed.freeUnits}GB free, $${parsed.priceExtra}GB-${parsed.billing} extra`,
        } satisfies WireTypes.ProductPlanPrice;
      }),
      seats: seatPrices.map(price => {
        const parsed = parseUnitPrice(price);
        return {
          priceId: price.id,
          billing: parsed.billing,
          description: parsed.price === 0 ? `Free` : `$${parsed.price}/${parsed.billing}`,
        } satisfies WireTypes.ProductPlanPrice;
      }),
    }
  }

  installRoutes(app: express.Express) {
    // Match the raw body to content type application/json
    app.post('/stripewebhook', express.raw({ type: 'application/json' }), async (request, response) => {
      const sig = request.headers['stripe-signature']!;

      let event;
      try {
        event = this._stripe.webhooks.constructEvent(request.body, sig, this._options.webhookSecret);
      } catch (err: any) {
        response.status(400).send(`Webhook Error: ${err.message}`);
        return;
      }
      // What, there's a subscription change? schedule a sync then!
      if (event.type.startsWith('customer.')) {
        this._xSubscriptionsChanged.notify(undefined);
      }
      if (event.type.startsWith('price.created')) {
        this._xPricesChanged.notify(undefined);
      }
      response.send();
    });
  }

  private async _changeSubscription(org: Organization, subscriptionId: string, plan: ProductPlan) {
    const subscription = await this._stripe.subscriptions.retrieve(subscriptionId);
    const items: Stripe.SubscriptionUpdateParams.Item[] = [];
    // Delete all old items
    for (const item of subscription.items.data) {
      items.push({
        id: item.id,
        deleted: true,
      });
    }
    if (plan.seats && plan.seats_price_id) {
      items.push({
        price: plan.seats_price_id,
        quantity: plan.seats ?? 0,
      });
    }
    if (plan.storage_price_id) {
      items.push({
        price: plan.storage_price_id,
      });
    }
    if (plan.testruns_price_id) {
      items.push({
        price: plan.testruns_price_id,
      });
    }

    const sub = await this._stripe.subscriptions.update(subscriptionId, {
      items,
      billing_cycle_anchor: 'now',
      proration_behavior: 'create_prorations',
      payment_behavior: 'default_incomplete',
      trial_end: 'now',
      metadata: this._subscriptionMetadata(org, plan),
    });
    await this._xSubscriptionsChanged.notify(undefined);
    if (!sub.latest_invoice)
      return undefined;
    const invoice = await this._stripe.invoices.retrieve(sub.latest_invoice as string);
    return invoice.status === 'open' ? invoice.hosted_invoice_url : undefined;
  }

  private async _createSubscriptionWithoutCreditCard(org: Organization, plan: ProductPlan) {
    const customerId = await this._ensureStripeCustomerId(org);
    assert(plan.trial_days && plan.trial_days > 0);

    // 1. Update the type definition for items to match Subscription API
    const items: Stripe.SubscriptionCreateParams.Item[] = [];
    
    if (plan.seats && plan.seats_price_id) {
      items.push({
        price: plan.seats_price_id,
        quantity: plan.seats ?? 0,
      });
    }
    if (plan.storage_price_id) {
      items.push({
        price: plan.storage_price_id,
      });
    }
    if (plan.testruns_price_id) {
      items.push({
        price: plan.testruns_price_id,
      });
    }

    // 2. Create the subscription directly
    const subscription = await this._stripe.subscriptions.create({
      customer: customerId,
      items: items,
      metadata: this._subscriptionMetadata(org, plan),
      trial_period_days: plan.trial_days,
      
      // 3. KEY: Handle "No Credit Card" logic
      // 'send_invoice' allows creation without a payment method on file.
      // If you strictly use trials, you can leave this as 'charge_automatically',
      // but 'send_invoice' is safer if trial_days might be 0.
      collection_method: 'charge_automatically',

      // Optional: Keep your trial settings if you want the invoice 
      // to be generated only after the trial ends.
      trial_settings: {
        end_behavior: {
          missing_payment_method: 'create_invoice',
        },
      },
    });
    return undefined;
  }

  private async _createSubscriptionURL(org: Organization, plan: ProductPlan) {
    const customerId = await this._ensureStripeCustomerId(org);
    const items: Stripe.Checkout.SessionCreateParams.LineItem[] = [];
    if (plan.seats && plan.seats_price_id) {
      items.push({
        price: plan.seats_price_id,
        quantity: plan.seats ?? 0,
      });
    }
    if (plan.storage_price_id) {
      items.push({
        price: plan.storage_price_id,
      });
    }
    if (plan.testruns_price_id) {
      items.push({
        price: plan.testruns_price_id,
      });
    }
    const session = await this._stripe.checkout.sessions.create({
      mode: 'subscription',
      line_items: items,
      subscription_data: {
        trial_period_days: plan.trial_days ?? undefined,
        metadata: this._subscriptionMetadata(org, plan),
        trial_settings: plan.trial_days ? {
          end_behavior: {
            missing_payment_method: 'create_invoice',
          },
        } : undefined,
      },
      payment_method_collection: 'if_required',
      customer: customerId,
      success_url: new URL(`/${org.org_slug}/billing`, this._options.callbackUrl).toString(),
      cancel_url: new URL(`/${org.org_slug}/billing`, this._options.callbackUrl).toString(),
    });
    return session.url;
  }

  private _subscriptionMetadata(org: Organization, plan: ProductPlan) {
    return {
      org_public_id: org.org_public_id,
      plan_public_id: plan.plan_public_id,
      plan_org_public_id: plan.org_public_id,
      plan_name: plan.name,
      max_data_retention_days: plan.max_data_retention_days,
    };
  }

  private async _subscriptionToWireSubscription(sub: Stripe.Subscription): Promise<WireTypes.Subscription> {
    const customer = sub.customer as Stripe.Customer;
    const hasPaymentMethod = 
      !!sub.default_payment_method || 
      !!customer.invoice_settings?.default_payment_method || 
      !!customer.default_source;
    const result: WireTypes.Subscription = {
      hasBillingIssues: sub.status === 'unpaid' || sub.status === 'past_due',
      plan: await this._subscriptionToWirePlan(sub),
      missingPaymentMethod: sub.status === 'trialing' && !hasPaymentMethod,
      usedSeats: 0, // these are set later
      usedStorage: 0,
      usedTestRuns: 0,
      periodStart: sub.current_period_start * 1000 as FlakinessReport.UnixTimestampMS,
      periodEnd: sub.current_period_end * 1000 as FlakinessReport.UnixTimestampMS,
      maxDataRetentionDays: this._computeMaxDataRetention(sub),
      trialEnd: sub.status === 'trialing' && sub.trial_end ? sub.trial_end * 1000 as FlakinessReport.UnixTimestampMS : undefined,
    };
    const upcomingInvoice = await this._upcomingInvoice.get(sub.id);
    const storageItems = upcomingInvoice?.lines.data.filter(item => item.price?.product === this._options.storageProductId) ?? [];
    const testRunsItems = upcomingInvoice?.lines.data.filter(item => item.price?.product === this._options.testRunsProductId) ?? [];
    result.usedStorage = storageItems.reduce((sum, storageItem) => sum + (storageItem?.quantity ?? 0), 0);
    result.usedTestRuns = testRunsItems.reduce((sum, testRunsItem) => sum + (testRunsItem?.quantity ?? 0), 0);
    return result;
  }

  private async _currentOrgSubscription(org: Organization): Promise<WireTypes.Subscription|undefined> {
    const allSubscriptions = await this._allSubscriptionsCache.get('') ?? [];
    const customerSubscriptions = allSubscriptions.filter(sub => sub.metadata.org_public_id === org.org_public_id);

    const result: WireTypes.Subscription[] = [];
    for (const sub of customerSubscriptions) {
      if (sub.status === 'active' || sub.status === 'trialing' || sub.status === 'unpaid' || sub.status === 'past_due')
        result.push(await this._subscriptionToWireSubscription(sub));
    }
    assert(result.length <= 1);
    const sub = result[0];
    // Technically, we should always have only a single subscription.
    // Thus we will assign occupied license seats to the first subscription, if there's any.
    if (sub)
      sub.usedSeats = (await this._occupiedLicenseSeats(org.org_id)).length;
    return sub;
  }

  async status(org: Organization): Promise<WireTypes.BillingStatus> {
    using timing = this._serverTiming.start('stripe');
    const sub = await this._currentOrgSubscription(org);
    let restrictedCIUploads = false;
    let restrictedProjectAccess = false;
    if (!sub || sub.usedSeats > (sub.plan.seats ?? Infinity)) {
      restrictedCIUploads = true;
      restrictedProjectAccess = true;
    }
    else if (sub.hasBillingIssues) {
      restrictedProjectAccess = true;
    }

    return {
      restrictedCIUploads,
      restrictedProjectAccess,
      subscription: sub,
    };
  }

  private async _occupiedLicenseSeats(orgId: OrgId): Promise<UserId[]> {
    // We need to list all userIds that have access to the organization projects.
    // These consist of 3 categories of people:
    // 1. org owner
    // 2. org members
    // 3. users with explicit access to org projects
    const licenseSeats = new Set<UserId>();
    const org = await this._db.orgs.get(orgId);
    assert(org);
    licenseSeats.add(org.owner_id);
    for (const { userId } of await this._db.orgSharing.getUsers(orgId))
      licenseSeats.add(userId);

    const projectIds = await this._db.orgs.getProjects(orgId);
    await Promise.all(projectIds.map(async projectId => {
      for (const { userId } of await this._db.projectSharing.getUsers(projectId))
        licenseSeats.add(userId);
    }));
    return [...licenseSeats];
  }

  async customerPortalURL(org: Organization) {
    using timing = this._serverTiming.start('stripe');
    const customerId = await this._ensureStripeCustomerId(org);
    const session = await this._stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: this._options.callbackUrl,
    });
    return session.url;
  }

  async setPlan(org: Organization, plan: ProductPlan) {
    using timing = this._serverTiming.start('stripe');
    assert(!plan.org_public_id || org.org_public_id === plan.org_public_id);

    const allSubscriptions = await this._allSubscriptionsCache.get('') ?? [];
    const orgSubscriptions = allSubscriptions.filter(sub => sub.metadata.org_public_id === org.org_public_id);

    const activeSubs = orgSubscriptions
      .filter(sub =>
        sub.status === 'active' || 
        sub.status === 'unpaid' || 
        sub.status === 'past_due' || 
        sub.status === 'paused' ||
        sub.status === 'trialing'
      );
    assert(activeSubs.length <= 1, `Org ${org.org_name} ${org.org_public_id} has ${activeSubs.length} subscriptions!`);
    if (activeSubs.length === 0 && plan.trial_days && plan.trial_days > 0) {
      await this._createSubscriptionWithoutCreditCard(org, plan);
      this._allSubscriptionsCache.clear();
      return;
    }
    if (activeSubs.length === 0)
      return this._createSubscriptionURL(org, plan);
    if (activeSubs.length === 1)
      return this._changeSubscription(org, activeSubs[0].id, plan);
  }

  private async _ensureStripeCustomerId(org: Organization) {
    // 1. Let's try finding customer with org public id.
    const existingCustomer = await this._stripe.customers.search({
      query: `metadata["org_public_id"]:"${org.org_public_id}" `,
    });
    const existingNonDeleted = existingCustomer.data.filter(customer => !customer.deleted);
    if (existingNonDeleted.length)
      return existingNonDeleted[0].id;

    const stripeCustomer = await this._stripe.customers.create({
      name: org.org_name,
      metadata: {
        org_public_id: org.org_public_id,
      }
    }, {
      idempotencyKey: `create_customer_org_${org.org_public_id}`,
    });
    return stripeCustomer.id;
  }
}

function computeMetricsForBillingPeriod(allMetrics: WireTypes.DailyMetrics[], subscription: WireTypes.Subscription) {
  const fromTimestamp = utcDayStart(subscription.periodStart);
  const untilTimestamp = utcDayStart(subscription.periodEnd);

  const prefix = allMetrics.filter(metric => metric.dayTimestampMs < fromTimestamp);
  // For the given billing period, we INCLUDE the day of period start and EXCLUDE the day of the period end.
  const suffix = allMetrics.filter(metrics => fromTimestamp <= metrics.dayTimestampMs && metrics.dayTimestampMs < untilTimestamp);
  let runningTotal = prefix.reduce((acc, item) => acc + item.totalBytes, 0);
  let dailyAverageBytes = 0;
  let totalTestRuns = 0;
  for (const metric of suffix) {
    runningTotal += metric.totalBytes;
    dailyAverageBytes += runningTotal; 
    totalTestRuns += metric.testsCount;
  }
  if (suffix.length)
    dailyAverageBytes /= suffix.length;

  return {
    averageGB: Math.ceil(dailyAverageBytes / 1000 / 1000 / 1000),
    testRuns: totalTestRuns,
  };
}
