import { Queue, QueueWorker } from "@flakiness/database";
import { FlakinessReport } from "@flakiness/report";
import assert from "assert";
import { CachedDatabase } from "./cachedDatabase.js";

type CronJob = {
  name: string,
}

type CronJobCallbackOptions = {
  signal: AbortSignal,
};

type CronJobCallback = (options: CronJobCallbackOptions) => Promise<unknown>;
type CronJobConfig = {
  name: string,
  delayMs: FlakinessReport.DurationMS,
  callback: CronJobCallback,
}

export class CronService {
  private _queue: Queue<CronJob>;
  private _worker?: QueueWorker<CronJob>;
  private _handlers = new Map<string, CronJobConfig>();

  constructor(private _db: CachedDatabase) {
    this._queue = this._db.queues.createQueue('cronjobs');
  }

  private async _sendJob(config: CronJobConfig) {
    await this._queue.send({ name: config.name }, {
      delayMs: config.delayMs,
      jobId: config.name,
    });
  }

  async setRecurringJob(name: string, delayMs: FlakinessReport.DurationMS, callback: CronJobCallback) {
    const config: CronJobConfig = {
      name,
      callback,
      delayMs,
    }
    this._handlers.set(name, config);
    await this._sendJob(config);
  }

  async runScheduledJobImmediately(name: string) {
    await this._queue.setJobDelay(name, 0);
  }

  async start(workerName: string) {
    assert(!this._worker);
    this._worker = this._queue.createWorker(workerName, async (job, options) => {
      const config = this._handlers.get(job.data.name);
      if (!config)
        return;
      try {
        await config.callback({
          signal: options.signal,
        });
      } finally {
        await this._sendJob(config);
      }
    }, {
      retries: 0,
    });
  }

  async stop() {
    assert(this._worker);
    await this._worker.stop();
    this._worker = undefined;
  }
}
