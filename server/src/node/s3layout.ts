import { ProjectPublicId } from "@flakiness/database";
import { Brand } from "@flakiness/shared/common/utils.js";
import { Stats } from "../common/stats/stats.js";

const PROJECT_PREFIX = `project-`;

export namespace S3Report {
  export type ReportsPrefix = Brand<string, 'ReportsPrefix'>;
  export type Id = {
    projectPublicId: ProjectPublicId;
    reportId: Stats.RunId;
  }

  export function path(id: Id) {
    return [
      `reports`,
      PROJECT_PREFIX + id.projectPublicId,
      String(id.reportId).padStart(8, '0'),
      'report.json.br',
    ].join('/');
  }

  export function attachmentPath(id: Id, attachmentId: string) {
    return [
      `reports`,
      PROJECT_PREFIX + id.projectPublicId,
      String(id.reportId).padStart(8, '0'),
      `attachments`,
      attachmentId,
    ].join('/');
  }

  export function reportPrefix(id: Id) {
    return [
      `reports`,
      PROJECT_PREFIX + id.projectPublicId,
      String(id.reportId).padStart(8, '0'),
      '',
    ].join('/');
  }

  export function attachmentsPrefix(id: Id) {
    return [
      `reports`,
      PROJECT_PREFIX + id.projectPublicId,
      String(id.reportId).padStart(8, '0'),
      `attachments`,
      '',
    ].join('/');
  }

  export function projectPrefix(projectPublicId: ProjectPublicId): ReportsPrefix {
    return  [
      `reports`,
      PROJECT_PREFIX + projectPublicId,
      ``
    ].join('/') as ReportsPrefix;
  }

  export function projectsPrefix() {
    return `reports/`;
  }
}

export namespace S3Stats {
  export type Prefix = Brand<string, 'StatsPrefix'>;
  export type ShardId = Brand<string, 'ShardId'>;

  export type Id = {
    projectPublicId: ProjectPublicId;
    shardId: ShardId,
  }

  export function path(id: Id): string {
    return [
      `stats`,
      PROJECT_PREFIX + id.projectPublicId,
      String(id.shardId) + '.json.br',
    ].join('/');
  }

  export function projectsPrefix() {
    return `stats/`;
  }

  export function projectPrefix(projectPublicId: ProjectPublicId) {
    return [`stats`, PROJECT_PREFIX + projectPublicId, ''].join('/');
  }
}

export namespace S3ReportIndex {
  export type Prefix = Brand<string, 'S3ReportsIndex.Prefix'>;
  export type Id = {
    projectPublicId: ProjectPublicId,
  };

  export function path(id: Id) {
    return [
      `reportsindex`,
      PROJECT_PREFIX + id.projectPublicId,
      'index.json.br',
    ].join('/');
  }

  export function projectsPrefix(): string {
    return `reportsindex/`;
  }
}

export namespace S3Repo {
  export type Prefix = Brand<string, 'S3Repo.Prefix'>;
  export type Id = {
    projectPublicId: ProjectPublicId,
  };

  export function path(id: Id) {
    return [
      `repositories`,
      PROJECT_PREFIX + id.projectPublicId,
      'repo.json.br',
    ].join('/');
  }

  export function projectsPrefix(): string {
    return `repositories/`;
  }
}

export function parseProjectPublicId(s3ProjectDirectoryPath: string): ProjectPublicId|undefined {
  if (s3ProjectDirectoryPath.endsWith('/'))
    s3ProjectDirectoryPath = s3ProjectDirectoryPath.substring(0, s3ProjectDirectoryPath.length - 1);
  const projectDirectory = s3ProjectDirectoryPath.split('/').pop();
  if (!projectDirectory || !projectDirectory.startsWith(PROJECT_PREFIX))
    return undefined;
  return projectDirectory.substring(PROJECT_PREFIX.length) as ProjectPublicId;
}
