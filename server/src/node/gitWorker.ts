import { Database, Project, ProjectPublicId, Queue, QueueWorker } from "@flakiness/database";
import { Git } from "./git.js";
import { FlakinessOctokit } from "./integrations/flakinessOctokit.js";

import assert from "assert";
import debug from 'debug';
import ms from "ms";
import { Temporal } from "temporal-polyfill";
import { setTimeout } from "timers/promises";
import { SharedCacheStore } from "../common/caches/cache.js";
import { Singleflight } from "../common/singleflight.js";
import { SingleflightCache } from "../common/singleflightcache.js";
import { GithubApp } from "./integrations/githubApp.js";
import { S3Repo } from "./s3layout.js";
import { S3Objects } from "./s3object.js";
import { ServerTiming } from "./serverTiming.js";
import { UploadWorker } from "./uploadWorker.js";
import { XNotify, XNotifyChannel } from "./xnotify.js";

const log = debug('fk:git_worker');

function createProvider(octokit: FlakinessOctokit, owner: string, repo: string): Git.Provider {
  return {
    listBranches: (signal) => octokit.listBranches({ owner, repo }, signal),
    listPullRequests: (options, signal) => octokit.listPullRequests({ ...options, owner, repo }, signal),
    listCommits: (commitOptions, signal) => octokit.listCommits({ ...commitOptions, owner, repo }, signal),
    defaultBranch: (signal) => octokit.defaultBranch({ owner, repo }, signal),
  };
}

export const HISTORY_CUTOFF_DATE = Temporal.PlainDate.from('2025-01-01').toZonedDateTime('UTC').epochMilliseconds;

export class GitWorker {

  private _queue: Queue<S3Repo.Id>;
  private _worker?: QueueWorker<S3Repo.Id>;
  private _channel: XNotifyChannel<S3Repo.Id>;
  private _cachedStorage: SingleflightCache<S3Repo.Id, Git.Repository>;
  private _uploadWorker?: UploadWorker;

  private _throttledGitUpdater = new Singleflight<S3Repo.Id, undefined>({
    key: repoId => repoId.projectPublicId,
    fetch: async (repoId, key, signal) => {
      await this._queue.send(repoId, {
        jobId: S3Repo.path(repoId),
        category: repoId.projectPublicId,
      });
      await setTimeout(30000, undefined, { ref: false, signal });
    }
  });

  constructor(
    memoryCache: SharedCacheStore,
    private _s3objects: S3Objects,
    private _githubApp: GithubApp,
    private _db: Database,
    xnotify: XNotify,
    serverTiming: ServerTiming,
  ) {
    this._queue = this._db.queues.createQueue<S3Repo.Id>('repo-builder');

    this._cachedStorage = new SingleflightCache({
      cache: memoryCache,
      ttl: ms('10min'),
      size: 100,
      key: repoId => S3Repo.path(repoId),
      onGetTiming: (repoId, key, method, since, until, cacheHit, cacheSize) => {
        serverTiming.recordTraceEvent('getGitRepository', since, until);
        serverTiming.recordServerCacheTelemetry('getGitRepository', until - since, cacheHit, cacheSize);
      },
      fetch: async (repoId, stale, signal) => {
        if (stale) {
          const metadata = await this._s3objects.repositories.getMetadata(repoId, signal);
          if (metadata?.etag === stale.etag())
            return stale;
        }
        const jsonRepo = await this._s3objects.repositories.get(repoId, signal);
        signal.throwIfAborted();
        return Git.Repository.deserialize(jsonRepo, HISTORY_CUTOFF_DATE);
      },
    });

    this._channel = xnotify.createChannel('clean-repo-builder-cache', async id => {
      this._cachedStorage.refresh(id);
    });
  }

  setUploadWorker(uploadWorker?: UploadWorker) {
    this._uploadWorker = uploadWorker;
  }

  requestThrottledGitFetch(repoId: S3Repo.Id) {
    this._throttledGitUpdater.fetch(repoId).catch(e => {});
  }

  async scheduleBuild(repoId: S3Repo.Id, after?: Date) {
    const now = Date.now();
    await this._queue.send(repoId, {
      jobId: S3Repo.path(repoId),
      category: repoId.projectPublicId,
      delayMs: after && +after > now ? +after - now : undefined,
    });
  }

  async getProvider(project: Project): Promise<Git.Provider> {
    const owner = project.source_owner_name;
    const repo = project.source_repo_name;

    const sourceType = project.source_auth_type;
    if (sourceType === Database.sourceAuthType.githubPat) {
      const personalAccessToken = await this._db.projectSource.getPersonalAccessToken(project.project_id);
      if (!personalAccessToken)
        throw new Error(`Internal error: failed to fetch github_pat for ${project.project_id}`);
      const octokit = await this._githubApp.apiForPAT(personalAccessToken);
      return createProvider(octokit, owner, repo);
    } else if (sourceType === Database.sourceAuthType.githubApp) {
      const installationId = await this._db.projectSource.getInstallationId(project.project_id);
      if (!installationId)
        throw new Error(`Internal error: failed to fetch installation id for ${project.project_id}`);
      const octokit = await this._githubApp.apiForInstallationId(parseInt(installationId, 10));
      return createProvider(octokit, owner, repo);
    }
    throw new Error(`project ${project.project_id} has unsupported source type ${sourceType}`)
  }

  async start(workerName: string) {
    assert(!this._worker);
    this._worker = this._queue.createWorker(workerName, jobInfo => this._fetchRepository(jobInfo.data), {
      staleTimeoutMs: ms('1 minute'),
    });
  }

  async stop() {
    assert(this._worker);
    await this._worker.stop();
    this._worker = undefined;
  }

  async getRepo(projectPublicId: ProjectPublicId): Promise<Git.Repository|undefined> {
    return await this._cachedStorage.get({ projectPublicId });
  }

  private async _fetchRepository(repoId: S3Repo.Id) {
    // Keep using uncached database to have latest information.
    const project = await this._db.projects.getByPublicId(repoId.projectPublicId);
    assert(project);
    const org = await this._db.orgs.get(project.org_id);
    assert(org);
    const provider = await this.getProvider(project);
    const jsonRepo = await this._s3objects.repositories.get(repoId);
    const repo = Git.Repository.deserialize(jsonRepo, HISTORY_CUTOFF_DATE) ?? Git.Repository.createEmpty(HISTORY_CUTOFF_DATE);
    const commitsBefore = repo.commitsSize();
    const branchesBefore = repo.branches().length;
    const prsBefore = repo.pullRequests().length;
    log(`Started fetching ${org.org_slug}/${project.project_slug}. repo commits = ${commitsBefore} branches = ${branchesBefore}`);

    const etag = repo.etag();
    // For empty repository, do a quick first pull.
    // Otherwise, give us some hefty time.
    const signal = commitsBefore === 0 ? AbortSignal.timeout(ms('2 minutes')) : AbortSignal.timeout(ms('10 minutes'));
    const reportIndex = await this._uploadWorker?.reportIndex(repoId.projectPublicId);
    const error = await repo.fetch(provider, reportIndex?.allCommitIds() ?? [], signal).catch(error => error);
    const isPreemptive = signal.aborted;
    const isRateLimited = error && (error instanceof Git.RateLimitError);

    let httpCode = 200;
    if (error && (error instanceof Git.HTTPError))
      httpCode = error.status;

    await this._db.projects.update(project.project_id, {
      source_last_fetch_http_status: httpCode,
      source_last_fetch_timestamp_seconds: Math.floor(Date.now() / 1000),
    });
    if (repo.etag() !== etag) {
      const serialized = repo.serialize();
      const commitsDelta = serialized.commits.length - commitsBefore;
      const branchesDelta = repo.branches().length - branchesBefore;
      const prsDelta = repo.pullRequests().length - prsBefore;
      await this._s3objects.repositories.set(repoId, repo.etag(), serialized);
      await this._channel.notify(repoId);
      log(`saved new ${org.org_slug}/${project.project_slug}: ${commitsDelta >= 0 ? '+' : ''}${commitsDelta} commits, ${branchesDelta >= 0 ? '+' : ''}${branchesDelta} branches, ${prsDelta >= 0 ? '+' : ''}${prsDelta} pull requests`);

      // Only if there were changes, and we finished pre-emptively, shall we schedule yet another build.
      // Otherwise, if we were fetching for the given amount of time, and still didn't manage to fetch anything,
      // then we probably don't have enough time to traverse references.
      if (isPreemptive && !isRateLimited) {
        await this.scheduleBuild(repoId);
        log(`${org.org_slug}/${project.project_slug} finished preemptively - scheduling another pass`);
      }
    } else {
      log(`${org.org_slug}/${project.project_slug} no changes`);
    }
    if (error && (error instanceof Git.RateLimitError)) {
      await this.scheduleBuild(repoId, error.rateLimitUntil);
      log(`${org.org_slug}/${project.project_slug} finished due to rate limit - scheduling another pass after ${error.rateLimitUntil}`);
    }
  }
}
