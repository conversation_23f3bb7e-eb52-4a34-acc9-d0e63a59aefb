import { ProjectPublicId, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, QueueWorkerCallbackOptions } from "@flakiness/database";

import { CachedDatabase } from "./cachedDatabase.js";

import { xxHashObject } from "@flakiness/shared/common/utils.js";
import { brotliDecompressAsync } from "@flakiness/shared/node/compression.js";
import assert from "assert";
import debug from 'debug';
import ms from "ms";
import { SharedCacheStore } from "../common/caches/cache.js";
import { SyncComputationCache } from "../common/computationCache.js";
import { Limiter } from "../common/limiter.js";
import { SingleflightCache } from "../common/singleflightcache.js";
import { Stats as S, Stats } from '../common/stats/stats.js';
import { StatsAnalyzer } from "../common/stats/statsAnalyzer.js";
import { StatsBuilder } from "../common/stats/statsBuilder.js";
import { HDDCache } from "./hddCache.js";
import { ReportIndex } from "./reportIndex.js";
import { S3Report, S3Stats } from "./s3layout.js";
import { S3Objects, StatsMetadata } from "./s3object.js";
import { ServerTiming } from "./serverTiming.js";
import { XNotify, XNotifyChannel } from "./xnotify.js";

const log = debug('fk:stats_worker');


export class StatsWorker {
  private _queue: Queue<S3Stats.Id>;
  private _worker?: QueueWorker<S3Stats.Id>;
  private _channel: XNotifyChannel<S3Stats.Id>;

  // No more than 4 shards in parallel, across all projects.
  private _limiter = new Limiter(4);

  private _projectCache = new SyncComputationCache<ProjectPublicId, SingleflightCache<S3Stats.Id, StatsAnalyzer>>({
    etag: ppid => ppid,
    size: 100, // maximum 100 projects are caches at the same time
    compute: ppid => new SingleflightCache({
      cache: this._memoryCache,
      size: 1000,
      ttl: ms('10min'),
      concurrency: Infinity,
      key: s3id => S3Stats.path(s3id),
      onGetTiming: (s3id, key, method, since, until, cacheHit, cacheSize) => {
        this._serverTiming.recordTraceEvent('getStatsAnalyzer', since, until);
        this._serverTiming.recordProjectCacheTelemetry('getStatsAnalyzer', ppid, until - since, cacheHit, cacheSize);
      },
      fetch: async (s3id, stale, signal) => {
        using slot = await this._limiter.acquireSlot(signal);
        const s3metadata = await this._s3objects.stats.getMetadata(s3id, signal);
        if (!s3metadata)
          return undefined;
        if (stale && s3metadata.etag === stale.etag())
          return stale;
        const hddMetadata = await this._hddCache?.getMetadata(s3id, signal);

        const isGoodToReadFromHDD = hddMetadata?.etag === s3metadata.etag;
        const file = isGoodToReadFromHDD ?
          await this._hddCache?.getBuffer(s3id, signal) :
          await this._s3objects.stats.getBuffer(s3id, signal);
        signal.throwIfAborted();
        if (!file || file.metadata.version !== S.STATS_VERSION)
          return undefined;
        if (!isGoodToReadFromHDD)
          await this._hddCache?.saveBuffer(s3id, file, signal);
        const text = (await brotliDecompressAsync(file.buffer)).toString('utf-8');
        signal.throwIfAborted();
        const jsonData = JSON.parse(text) as Stats.JSONData;
        return new StatsAnalyzer(jsonData, file.metadata.etag);
      },
    })
  });

  static async create(options: {
    memoryCache: SharedCacheStore,
    s3objects: S3Objects,
    db: CachedDatabase,
    xnotify: XNotify,
    serverTiming: ServerTiming,
    hddCache?: {
      dir: string,
      maxSizeBytes: number,
    }
  }) {
    const hddCache = options.hddCache ? await HDDCache.create<S3Stats.Id, StatsMetadata>(options.hddCache.dir, s3id => S3Stats.path(s3id), options.hddCache.maxSizeBytes) : undefined;
    return new StatsWorker(options.memoryCache, options.s3objects, options.db, options.xnotify, options.serverTiming, hddCache);
  }

  constructor(
    private _memoryCache: SharedCacheStore,
    private _s3objects: S3Objects,
    private _db: CachedDatabase,
    xnotify: XNotify,
    private _serverTiming: ServerTiming,
    private _hddCache?: HDDCache<S3Stats.Id, StatsMetadata>,
  ) {
    this._queue = this._db.queues.createQueue<S3Stats.Id>('stats-builder');

    this._channel = xnotify.createChannel<S3Stats.Id>('clean-stats-builder-cache', async s3id => {
      this._projectCache.get(s3id.projectPublicId).refresh(s3id);
    });
  }

  async scheduleBuild(statsIds: S3Stats.Id[]) {
    await this._queue.sendMany(statsIds.map(statsId => ({
      data: statsId,
      options: {
        jobId: S3Stats.path(statsId),
        category: statsId.projectPublicId,
      }
    })));
  }

  async start(workerName: string) {
    assert(!this._worker);
    this._worker = this._queue.createWorker(workerName, async (jobInfo, options) => {
      const statsId = jobInfo.data;
      log('Aggregation started', statsId);
      const result = await this._aggregateStats(statsId, options);
      if (result.aggregated)
        this._channel.notify(statsId);
      log('Aggregation complete', statsId, result);
      // If we didn't finish aggregation in time, then schedule another job.
      if (result.preemptiveTermination)
        this.scheduleBuild([statsId]);
    }, {
      staleTimeoutMs: ms('15 minutes'),
    });
  }

  async stop() {
    assert(this._worker);
    await this._worker.stop();
    this._worker = undefined;
  }

  async statsAnalyzer(shard: S3Stats.Id) {
    return await this._projectCache.get(shard.projectPublicId).get(shard) ?? new StatsAnalyzer(S.createEmptyStats());
  }

  private async _aggregateStats(statsId: S3Stats.Id, { signal }: QueueWorkerCallbackOptions): Promise<{ aggregated: number, preemptiveTermination?: boolean }> {
    const statsCloudMetadata = await this._s3objects.stats.getMetadata(statsId, signal);
    // If there's a saved stats version, and it is "newer" than what we can produce here,
    // than do nothing.
    if (statsCloudMetadata && statsCloudMetadata.version > S.STATS_VERSION)
      return { aggregated: 0 };

    // If report index version if something we don't understand - than bail out right away.
    const indexMetadata = await this._s3objects.reportIndex.getMetadata(statsId, signal);
    if (indexMetadata && indexMetadata.version !== ReportIndex.REPORT_INDEX_VERSION)
      return { aggregated: 0 };

    // Otherwise, compute expected etag for the current reports in store.
    const index = new ReportIndex.ReportIndex(statsId.projectPublicId, await this._s3objects.reportIndex.get({ projectPublicId: statsId.projectPublicId }, signal));
    const reportIds: S3Report.Id[] = index.shardS3Reports(statsId);

    // If expected etag of the aggregation matches currently saved data, then there's nothing else to do.
    if (statsCloudMetadata?.version === S.STATS_VERSION &&
        statsCloudMetadata?.etag === computeStatsETag(reportIds.map(r => r.reportId))) {
      return { aggregated: 0 };
    }

    // If there are no reports, then remove shard.
    if (!reportIds.length) {
      await this._s3objects.stats.remove(statsId, signal);
      return { aggregated: 0 };
    }

    const jsonStats = statsCloudMetadata?.version === S.STATS_VERSION ? await this._s3objects.stats.get(statsId, signal) : undefined;
    const builder = StatsBuilder.create(index.testIndex(), jsonStats?.stats);
    const missingReportIds = reportIds.filter(reportId => !builder.hasRun(reportId.reportId));

    const toBeRemoved = new Set(builder.runIds()).difference(new Set(reportIds.map(r => r.reportId)));
    for (const reportId of toBeRemoved)
      builder.removeRun(reportId);

    let processedReports = 0;
    let preemptiveTermination: boolean = false;
    const deadline = Date.now() + ms('10 minutes');
    // Limit shard computation to 10 minutes - yield to another
    // job afterwards.
    for (const reportId of missingReportIds) {
      if (Date.now() > deadline) {
        preemptiveTermination = true;
        break;
      }
      ++processedReports;
      const flakinessReport = await this._s3objects.reports.get(reportId, signal);
      if (flakinessReport)
        builder.addRun(reportId.reportId, flakinessReport.report);
    }

    const statsJSON = builder.jsonStats();
    await this._s3objects.stats.set({
      statsId,
      etag: computeStatsETag(builder.runIds()),
      jsonStats: statsJSON,
    }, signal);
    log(`added ${processedReports}/${missingReportIds.length} reports to ${S3Stats.path(statsId)}`);
    return { aggregated: processedReports, preemptiveTermination };
  }
}

function computeStatsETag(reportIds: Iterable<S.RunId>): string {
  const sortedReportIds = [...reportIds].sort((a, b) => a < b ? -1 : 1);
  return xxHashObject({
    version: Stats.STATS_VERSION,
    reportIds: sortedReportIds,
  });
}
