import chalk from 'chalk';
import debug from 'debug';
import fs from 'fs';
import path from 'path';

const log = debug('fk:configuration');

type EnvOptionConfig<T> = {
  env: string,
  description?: string,
  required?: boolean,
  default?: T,
}

abstract class EnvOption<T> {
  env: string;
  description?: string;
  required: boolean;
  default?: T;

  value?: T;
  errorMessage?: string;

  constructor(options: EnvOptionConfig<T>) {
    this.env = options.env;
    this.default = options.default;
    this.description = options.description;
    this.required = options.required ?? false;
  };

  abstract doParse(rawValue: string): Promise<T>;

  async parse(rawValue: string | undefined) {
    if (!rawValue && this.required) {
      this.errorMessage ='This env variable must be defined';
      return;
    }
    if (!rawValue)
      return;
    try {
      this.value = await this.doParse(rawValue) ?? this.default;
    } catch (e) {
      if (e instanceof Error)
        this.errorMessage = e.message;
      else 
        this.errorMessage = 'failed to parse';
    }
  }
}

class URLOption extends EnvOption<URL> {
  async doParse(rawValue: string) {
    try {
      return new URL(rawValue);
    } catch (error) {
      throw new Error(`Failed to parse "${rawValue}" as URL`);
    }
  }

  toString() {
    return this.value?.toString() ?? '';
  }
}

class DirectoryOption extends EnvOption<string> {
  async doParse(rawValue: string) {
    const aPath = path.resolve(process.cwd(), rawValue);
    const stat = await fs.promises.stat(aPath).catch(e => null);
    if (!stat)
      throw new Error(`The path "${aPath}" does not exist`);
    if (!stat.isDirectory())
      throw new Error(`The path "${aPath}" is not a directory`);
    return aPath;
  }

  toString() {
    return this.value ?? '';
  }
}

class PathOption extends EnvOption<string> {
  async doParse(rawValue: string) {
    const aPath = path.resolve(process.cwd(), rawValue);
    return aPath;
  }

  toString() {
    return this.value ?? '';
  }
}

class SecretOption extends EnvOption<string> {
  async doParse(rawValue: string) {
    return rawValue;
  }

  toString() {
    return this.value ? '<hidden>' : '';
  }
}

class IntegerOption extends EnvOption<number> {
  async doParse(rawValue: string) {
    const value = Number.parseInt(rawValue, 10);
    if (Object.is(value, NaN))
      throw new Error(`failed to parse number: "${rawValue}"`);
    return value;
  }

  toString() {
    return (this.value ?? '') + '';
  }
}

class TextOption extends EnvOption<string> {
  async doParse(rawValue: string) {
    return rawValue;
  }

  toString() {
    return (this.value ?? '') + '';
  }
}

class BoolOption extends EnvOption<boolean> {
  async doParse(rawValue: string) {
    rawValue = rawValue.trim().toLowerCase();
    if (!rawValue)
      return false;
    return rawValue === '0' || rawValue === 'false' || rawValue === 'no' || rawValue === 'off' ? false : true;
  }

  toString() {
    return this.value ? 'ON' : 'OFF';
  }
}

export class Config {
  static async fromEnvironment<T>(name: string, callback: (env: Env) => Promise<T>): Promise<T | undefined> {
    const env = new Env(process.env);
    const schema = await callback(env);
    let hasErrors = false;
    log(chalk.bold(name));

    for (const option of env._childOptions) {
      if (option.value !== undefined) {
        log(`${chalk.green('[OK]')} ${option.env} = ${chalk.yellow(option.toString())}`);
      } else if (option.errorMessage) {
        log(`${chalk.red('[FAIL]')} ${option.env} ${chalk.red(option.errorMessage)}`);
        hasErrors = true;
      } else {
        log(`${chalk.grey('[MISSING]')} ${option.env} ${chalk.grey(option.description)}`);
      }
    }
    return hasErrors ? undefined : schema;
  }

  static async fromEnvironmentOrDie<K>(name: string, callback: (env: Env) => Promise<K>): Promise<K> {
    const result = await Config.fromEnvironment(name, callback);
    if (!result) {
      log('ERROR: configuration had errors!');
      process.exit(1);
    }
    return result;
  }
}

export class Env {
  _childOptions: EnvOption<any>[] = [];
  constructor(private _source: Record<string, string | undefined>) {}

  async secret(config: EnvOptionConfig<string> & { required: true }): Promise<string>;
  async secret(config: EnvOptionConfig<string> & { required?: false }): Promise<string | undefined>;
  async secret(config: EnvOptionConfig<string>): Promise<string | undefined> {
    const option = new SecretOption(config);
    this._childOptions.push(option);
    await option.parse(this._source[option.env]);
    return option.value;
  }

  async text(config: EnvOptionConfig<string> & { required: true }): Promise<string>;
  async text(config: EnvOptionConfig<string> & { required?: false }): Promise<string | undefined>;
  async text(config: EnvOptionConfig<string>): Promise<string | undefined> {
    const option = new TextOption(config);
    this._childOptions.push(option);
    await option.parse(this._source[option.env]);
    return option.value;
  }

  async bool(config: EnvOptionConfig<boolean> & { required: true }): Promise<boolean>;
  async bool(config: EnvOptionConfig<boolean> & { required?: false }): Promise<boolean | undefined>;
  async bool(config: EnvOptionConfig<boolean>): Promise<boolean | undefined> {
    const option = new BoolOption(config);
    this._childOptions.push(option);
    await option.parse(this._source[option.env]);
    return option.value;
  }

  async path(config: EnvOptionConfig<string> & { required: true }): Promise<string>;
  async path(config: EnvOptionConfig<string> & { required?: false }): Promise<string | undefined>;
  async path(config: EnvOptionConfig<string>): Promise<string | undefined> {
    const option = new PathOption(config);
    this._childOptions.push(option);
    await option.parse(this._source[option.env]);
    return option.value;
  }

  async directory(config: EnvOptionConfig<string> & { required: true }): Promise<string>;
  async directory(config: EnvOptionConfig<string> & { required?: false }): Promise<string | undefined>;
  async directory(config: EnvOptionConfig<string>): Promise<string | undefined> {
    const option = new DirectoryOption(config);
    this._childOptions.push(option);
    await option.parse(this._source[option.env]);
    return option.value;
  }

  async url(config: EnvOptionConfig<URL> & { required: true }): Promise<string>;
  async url(config: EnvOptionConfig<URL> & { required?: false }): Promise<string | undefined>;
  async url(config: EnvOptionConfig<URL>): Promise<string | undefined> {
    const option = new URLOption(config);
    this._childOptions.push(option);
    await option.parse(this._source[option.env]);
    return option.value?.href;
  }

  async integer(config: EnvOptionConfig<number> & { required: true }): Promise<number>;
  async integer(config: EnvOptionConfig<number> & { required?: false }): Promise<number | undefined>;
  async integer(config: EnvOptionConfig<number>): Promise<number | undefined> {
    const option = new IntegerOption(config);
    this._childOptions.push(option);
    await option.parse(this._source[option.env]);
    return option.value;
  }
}



