import { DatabaseConfig } from "@flakiness/database";
import { Multimap } from "@flakiness/shared/common/multimap.js";
import debug from 'debug';
import ms from "ms";
import pg from 'pg';
import pgFormat from 'pg-format';

const logXNotify = debug('fk:xnotify');

type NotificationMessage = {
  channel: string,
  data: any,
}

export type PubSubWorker<T> = (data: T) => Promise<void>;

export class XNotifyChannel<T> {
  constructor(private _xnotify: XNotify, private _name: string, worker: PubSubWorker<T>) {
    this._xnotify.subscribe(this._name, worker);
  }

  async notify(data: T) {
    await this._xnotify.notify(this._name, data);
  }

  close() {
    this._xnotify.unsubscribeAll(this._name);
  }
}

export class XNotify {
  private _client: DurableConnection;
  private _workers = new Multimap<string, PubSubWorker<any>>();

  constructor(dbConfig: DatabaseConfig) {
    this._client = new DurableConnection(dbConfig, async (msg: NotificationMessage) => {
      logXNotify('Received notification %o', msg);
      const workers = this._workers.getAll(msg.channel);
      await Promise.all(workers.map(worker => worker(msg.data).catch(e => logXNotify(e))));
    });
  }

  createChannel<T>(uniqueName: string, worker: PubSubWorker<T>) {
    return new XNotifyChannel(this, uniqueName, worker);
  }

  async close() {
    await this._client.close();
  }

  async notify(channel: string, data: any) {
    const raw: NotificationMessage = { channel, data };
    await this._client.send(raw);
  }

  subscribe<T>(channel: string, worker: PubSubWorker<T>) {
    this._workers.set(channel, worker);
  }

  subscribeAll<T>(channels: string[], worker: PubSubWorker<T>) {
    for (const channel of channels)
      this._workers.set(channel, worker);
  }

  unsubscribeAll(channel: string) {
    this._workers.deleteAll(channel);
  }
}

const MAX_RECONNECT_DELAY = ms('30 seconds');
const BASE_RECONNECT_DELAY = ms('1 second');
const HEARTBEAT_TIMEOUT = ms('30 seconds');
const CONNECTION_TIMEOUT = ms('10 seconds');

class DurableConnection {
  private _pg: pg.Client | null = null;
  private _reconnectTimeout?: NodeJS.Timeout;
  private _reconnectAttempts = 0;
  private _pendingMessages: any[] = [];
  private _heartbeatTimeout?: NodeJS.Timeout;

  constructor(
    private _dbConfig: DatabaseConfig,
    private _onMessage: (data: any) => void
  ) {
    this._connect();
    this._scheduleHeartbeat();
  }

  private _scheduleHeartbeat() {
    this._heartbeatTimeout = setTimeout(async () => {
      try {
        await this._pg?.query("SELECT pg_backend_pid()")
      } catch (e) {
        logXNotify('heartbeat failed: scheduling reconnect.');
        // If the connection failed, then schedule reconnect.
        this._scheduleReconnect();
      }
      this._scheduleHeartbeat();
    }, HEARTBEAT_TIMEOUT);
  }

  private _connect() {
    try {
      this._pg?.removeAllListeners();
      this._pg?.end();
      this._pg = null;

      const client = new pg.Client({
        ...this._dbConfig,
        connectionTimeoutMillis: CONNECTION_TIMEOUT,
      });
      client.connect().then(async () => {
        await client.query('LISTEN "xnotify-notify"');
        this._pg = client;
        this._reconnectAttempts = 0;

        logXNotify('Connected to PG Database');

        // dispatch all pending messages
        const messages = this._pendingMessages.slice();
        this._pendingMessages = [];
        for (const msg of messages)
          await pgNotify(this._pg, msg);
      }).catch(error => {
        logXNotify('Error during connection:', error);
        this._scheduleReconnect();
      });
      client.on('notification', async notification => {
        try {
          const data = JSON.parse(notification.payload!);
          this._onMessage(data);
        } catch (error) {
          logXNotify('Error processing message:', error);
        }
      });
      client.on('end', () => {
        logXNotify('Disconnected from relay server');
        this._pg?.removeAllListeners();
        this._pg?.end();
        this._pg = null;
        this._scheduleReconnect();
      });
      client.on('error', (error) => {
        this._pg?.removeAllListeners();
        this._pg?.end();
        this._pg = null;

        logXNotify('Connection error:', error);
        this._scheduleReconnect();
      });
    } catch (error) {
      logXNotify('Connection failed:', error);
      this._scheduleReconnect();
    }
  }

  private _scheduleReconnect() {
    clearTimeout(this._reconnectTimeout);

    const delay = Math.min(
      MAX_RECONNECT_DELAY,
      BASE_RECONNECT_DELAY * Math.pow(2, this._reconnectAttempts)
    ) * (0.8 + Math.random() * 0.4);

    logXNotify(`Reconnecting in ${Math.round(delay)}ms...`);

    this._reconnectTimeout = setTimeout(() => {
      ++this._reconnectAttempts;
      this._connect();
    }, delay);
  }

  public async send(message: any) {
    if (this._pg) {
      await pgNotify(this._pg, message);
    } else {
      this._pendingMessages.push(message);
    }
  }

  public close() {
    clearTimeout(this._reconnectTimeout);
    clearTimeout(this._heartbeatTimeout);
    this._pg?.removeAllListeners();
    this._pg?.end();
    this._pg = null;
  }
}

async function pgNotify(pg: pg.Client, message: any) {
  await pg.query(`NOTIFY "xnotify-notify", ${pgFormat.literal(JSON.stringify(message))}`);
}
