import { Multimap } from "@flakiness/shared/common/multimap.js";
import { AsyncLocalStorage } from "async_hooks";
import { createCounter, createGauge, createHistogram } from "./telemetry.js";

type Span = {
  name: string,
  since: number,
  until: number,
}

// Project-level caches: these exist per-project.
const projectCacheRequestsCounter = createCounter({
  name: 'fk_project_cache_requests_total',
  help: 'Total number of cache requests',
  labelNames: ['cache_name', 'project_id', 'result'], // hit/miss
});

const projectCacheRequestDuration = createHistogram({
  name: 'fk_project_cache_request_duration_seconds',
  help: 'Cache request duration in seconds',
  labelNames: ['cache_name', 'result', 'project_id'],
  buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0],
});

const projectCacheSizeGauge = createGauge({
  name: 'fk_project_cache_size_total',
  help: 'Total number of items in cache',
  labelNames: ['cache_name', 'project_id']
});

// Server-level caches: these are single per server
const serverCacheRequestsCounter = createCounter({
  name: 'fk_server_cache_requests_total',
  help: 'Total number of cache requests',
  labelNames: ['cache_name', 'result'], // hit/miss
});

const serverCacheRequestDuration = createHistogram({
  name: 'fk_server_cache_request_duration_seconds',
  help: 'Cache request duration in seconds',
  labelNames: ['cache_name', 'result'],
  buckets: [0.001, 0.005, 0.01, 0.05, 0.1, 0.5, 1.0, 5.0],
});

const serverCacheSizeGauge = createGauge({
  name: 'fk_server_cache_size_total',
  help: 'Total number of items in cache',
  labelNames: ['cache_name']
});

function coveredArea(spans: Span[]): number {
  if (!spans.length)
    return 0;
  spans = spans.toSorted((s1, s2) => s1.since - s2.since);
  let lastSpan = spans[0];
  const filteredSpans: Span[] = [lastSpan];
  for (const span of spans) {
    if (lastSpan.until < span.since) {
      filteredSpans.push(span);
      lastSpan = span;
    } else if (lastSpan.until < span.until) {
      lastSpan = {
        name: lastSpan.name,
        since: lastSpan.since,
        until: span.until,
      };
      filteredSpans.pop();
      filteredSpans.push(lastSpan);
    }
  }
  return filteredSpans.reduce((acc, span) => acc + (span.until - span.since), 0);
}

export class ServerTiming {
  private _asyncLocalStorage = new AsyncLocalStorage<Span[]>();

  recordTraceEvent(name: string, since: number, until: number) {
    const spans = this._asyncLocalStorage.getStore();
    if (!spans)
      return;
    spans.push({ name, since, until });
  }

  recordProjectCacheTelemetry(cacheName: string, projectId: string, durationMs: number, cacheHit: boolean, totalCacheSize: number) {
    const result = cacheHit ? 'hit' : 'miss';
    const durationSeconds = durationMs / 1000;

    // Increment counter
    projectCacheRequestsCounter.inc({ cache_name: cacheName, project_id: projectId, result });
    
    // Record duration
    projectCacheRequestDuration.observe({ cache_name: cacheName, project_id: projectId, result }, durationSeconds);

    // Update cache size (this will overwrite previous value for this cache)
    projectCacheSizeGauge.set({ cache_name: cacheName, project_id: projectId }, totalCacheSize);
  }

  recordServerCacheTelemetry(cacheName: string, durationMs: number, cacheHit: boolean, totalCacheSize: number) {
    const result = cacheHit ? 'hit' : 'miss';
    const durationSeconds = durationMs / 1000;

    // Increment counter
    serverCacheRequestsCounter.inc({ cache_name: cacheName, result });
    
    // Record duration
    serverCacheRequestDuration.observe({ cache_name: cacheName, result }, durationSeconds);

    // Update cache size (this will overwrite previous value for this cache)
    serverCacheSizeGauge.set({ cache_name: cacheName, }, totalCacheSize);
  }

  start(name: string): { [Symbol.dispose](): void, done(): void } {
    const since = performance.now();
    return {
      [Symbol.dispose]: () => {
        this.recordTraceEvent(name, since, performance.now());
      },

      done: () => {
        this.recordTraceEvent(name, since, performance.now());
      }
    }
  }

  run<T>(callback: () => T) {
    return this._asyncLocalStorage.run([], callback);
  }

  httpServerTimingHeader() {
    const spans = this._asyncLocalStorage.getStore();
    if (!spans)
      return undefined;
    const allSpans = new Multimap<string, Span>();
    for (const span of spans)
      allSpans.set(span.name, span);
    spans.sort((a, b) => a.since - b.since);
    const results = allSpans.map((name, spans) => [name, coveredArea(Array.from(spans))] as const).sort((a, b) => b[1] - a[1]);
    return results.map(([name, duration]) => `${name};dur=${duration}`).join(',');
  }
}

export function scopeTiming(callback: (since: number, until: number) => void) {
  const since = performance.now();
  return {
    [Symbol.dispose]: () => {
      callback(since, performance.now());
    }
  }
}
