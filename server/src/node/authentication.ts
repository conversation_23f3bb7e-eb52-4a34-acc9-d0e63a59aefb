import { Database, DeviceAuthRequest, OrgAccessRole, Organization, Project, ProjectAccessRole, User, UserPublicId, UserSession, UserSessionId } from '@flakiness/database';
import { randomUUIDBase62 } from '@flakiness/shared/node/nodeutils.js';
import { createHash, randomUUID } from 'crypto';
import debug from 'debug';
import type { Request, Response } from 'express';
import ms from 'ms';
import { setTimeout } from 'timers/promises';
import { KNOWN_CLIENT_IDS } from '../common/knownClientIds.js';
import { Singleflight } from '../common/singleflight.js';
import { type CachedDatabase } from './cachedDatabase.js';
import { parseAuthorizationToken } from './nodeutils.js';

function sha256(text: string) {
  return createHash('sha256').update(text).digest('hex');
}

const log = debug('fk:authentication');

export class Authentication {
  private _sessionAccessInfo = new Map<UserSessionId, {
    accessTimestampMs: number,
  }>();

  // Use singleflight to issue DB writes every minute per token
  private _throttleTokenAccess = new Singleflight<UserSession, void, UserSessionId>({
    key: session => session.session_id,
    fetch: async (session, key, signal) => {
      const accessInfo = this._sessionAccessInfo.get(session.session_id);
      if (!accessInfo)
        return;
      this._sessionAccessInfo.delete(session.session_id);
      await this._db.userSessions.recordSessionAccess(session.session_id, accessInfo);
      // Pause before returning to delay future updates.
      await setTimeout(ms('1 min'), undefined, { ref: false, signal });
    },
  });

  constructor(
    private _db: CachedDatabase,
    private _superuserGithubIds: Set<string>,
    private _cookieDomain: string,
  ) { }

  isSuperUser(user?: User): boolean {
    return user ? this._superuserGithubIds.has(String(user.github_id)) : false;
  }

  async loginOrSignUpWithGithub(response: Response, { githubId, name, login, avatarUrl }: { githubId: number, name: string, login: string, avatarUrl: string }): Promise<void> {
    let user = await this._db.users.getByGithubId(githubId);
    if (!user) {
      user = await this._db.users.create({
        user_public_id: randomUUID() as UserPublicId,
        user_login: login,
        user_name: name,
        github_id: githubId,
        user_avatar_url: avatarUrl,
      });
    }

    const sessionToken = `flakiness-web-${randomUUIDBase62()}`;
    await this._db.userSessions.createSession({
      userId: user.user_id,
      tokenHash: sha256(sessionToken),
      clientId: KNOWN_CLIENT_IDS.OFFICIAL_WEB,
    });

    response.cookie('session_token', sessionToken, {
      httpOnly: true, // Makes the cookie HTTP-only
      secure: true,
      domain: this._cookieDomain, 
    });
  }

  async logout(request: Request, response: Response) {
    response.clearCookie('session_token');
    const session = await this.getUserSession(request);
    if (!session)
      return undefined;
    await this._db.userSessions.dropSession(session.session_id);
  }

  async getUserProjectRole(user: User|undefined, project: Project): Promise<ProjectAccessRole | undefined> {
    if (this.isSuperUser(user))
      return Database.projectAccessRole.editor;

    let impliedRole: ProjectAccessRole|undefined;
    let explicitRole: ProjectAccessRole|undefined;

    if (user) {
      const owned = await this._db.users.getOrganizations(user.user_id);
      const shared = await this._db.orgSharing.getOrganizations(user.user_id);
      if (owned.some(orgId => orgId === project.org_id)) {
        impliedRole = Database.projectAccessRole.editor;
      } else {
        const sharedOrg = shared.find(entry => entry.orgId === project.org_id);
        if (sharedOrg?.accessRole === Database.orgAccessRole.admin)
          impliedRole = Database.projectAccessRole.editor;
        else if (sharedOrg?.accessRole === Database.orgAccessRole.member)
          impliedRole = Database.projectAccessRole.viewer;
      }

      const projectSharing = (await this._db.projectSharing.getUsers(project.project_id)).find(entry => entry.userId === user.user_id);
      explicitRole = projectSharing?.accessRole;  
    }

    if (impliedRole === undefined && project.visibility === Database.projectVisibility.public)
      impliedRole = Database.projectAccessRole.viewer;

    if (explicitRole === Database.projectAccessRole.editor || impliedRole === Database.projectAccessRole.editor)
      return Database.projectAccessRole.editor;
    if (explicitRole === Database.projectAccessRole.viewer || impliedRole === Database.projectAccessRole.viewer)
      return Database.projectAccessRole.viewer;
    return undefined;
  }

  async getUserOrgRole(user: User, organization: Organization): Promise<OrgAccessRole | undefined> {
    const owned = await this._db.users.getOrganizations(user.user_id);
    if (this.isSuperUser(user))
      return Database.orgAccessRole.admin;
    if (owned.some(orgId => orgId === organization.org_id))
      return Database.orgAccessRole.admin;
    const shared = await this._db.orgSharing.getOrganizations(user.user_id);
    for (const { orgId, accessRole } of shared) {
      if (organization.org_id === orgId)
        return accessRole;
    }
    return undefined;
  }

  private _recordSessionAccess(session: UserSession) {
    this._sessionAccessInfo.set(session.session_id, {
      accessTimestampMs: Date.now(),
    });
    this._throttleTokenAccess.fetch(session);
  }

  async createCLISession(request: DeviceAuthRequest): Promise<string|undefined> {
    if (!request.approver_id)
      return undefined;
    const sessionToken = `flakiness-cli-${randomUUIDBase62()}`;
    await this._db.userSessions.createSession({
      userId: request.approver_id,
      tokenHash: sha256(sessionToken),
      name: request.device_name,
      clientId: request.client_id,
    });
    await this._db.uncachedDatabase().deviceAuthRequests.delete(request.request_id);
    return sessionToken;
  }

  async getUserSession(req: Request): Promise<UserSession|undefined> {
    // User session can be stored either in a cookie or in an authorization header.
    const sessionToken = req.cookies?.session_token || parseAuthorizationToken(req);
    if (!sessionToken)
      return undefined;

    return await this._db.userSessions.getByTokenHash(sha256(sessionToken));
  }

  async getLoggedInUser(session: UserSession): Promise<User | undefined> {
    // Record token access
    const user = await this._db.users.get(session.user_id);
    if (user)
      this._recordSessionAccess(session);
    return user;
  }
}
