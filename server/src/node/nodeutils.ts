import { ProjectPublicId } from '@flakiness/database';
import { randomUUIDBase62 } from '@flakiness/shared/node/nodeutils.js';
import debug from 'debug';
import type express from 'express';
import fs from 'fs';
import path from 'path';
import { Worker } from 'worker_threads';

const packageJSONText = fs.readFileSync(path.join(import.meta.dirname, '../../../package.json'), 'utf-8');
export const packageJSON = JSON.parse(packageJSONText);


export function createFlakinessAccessToken(): string {
  return `flakiness-io-` + randomUUIDBase62();
}

export function createProjectPublicId(): ProjectPublicId {
  return `fkproj-` + randomUUIDBase62() as ProjectPublicId;
}

export function createSharedArrayBuffer(buffer: Buffer): Buffer {
  const sharedArrayBuffer = new SharedArrayBuffer(buffer.length);
  const sharedBuffer = Buffer.from(sharedArrayBuffer);
  sharedBuffer.set(buffer);
  return sharedBuffer;
}

const BEARER_PREFIX = 'bearer ';

export function parseAuthorizationToken(req: express.Request): string|undefined {
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.toLowerCase().startsWith(BEARER_PREFIX))
    return authHeader.substring(BEARER_PREFIX.length).trim();
  return undefined;
}

const log = debug('fk:worker_service');

export function startWorkerService(path: string, workerData?: any): { worker: Worker } {
  const service = {
    worker: new Worker(path, { workerData }),
  };

  // Handle worker errors
  service.worker.on('error', (error) => {
      log('Worker error:', error);
  });

  // Restart the worker if it stops unexpectedly
  service.worker.on('exit', (code) => {
    log('worker exit');
    if (code !== 0) {
      log(`Worker stopped with exit code ${code}. Restarting...`);
      service.worker = startWorkerService(path, workerData).worker; // Restart the worker
    } else {
      service.worker.terminate();
    }
  });
  return service;
}
