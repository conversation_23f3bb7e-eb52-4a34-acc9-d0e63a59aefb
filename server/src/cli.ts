#!/usr/bin/env node

import { Database } from "@flakiness/database";
import { Option, program } from "commander";
import crypto from 'crypto';
import ms from "ms";
import { Octokit } from "octokit";
import { CachedDatabase } from "./node/cachedDatabase.js";
import { License, RegistryTokenCapability } from "./node/license.js";

program
  .command('generate-license-encryption-keys')
  .description('Generate private-public keys to supply to container registry')
  .action(async () => {
    const { privateKey, publicKey } = await License.generateKeys();
    console.log(`\n---- BEGIN PUBLIC KEY ----\n`);
    console.log(publicKey);
    console.log(`\n---- END PUBLIC KEY ----\n`);

    console.log(`\n---- BEGIN PRIVATE KEY ----\n`);
    console.log(privateKey);
    console.log(`\n---- END PRIVATE KEY ----\n`);
  });

program
  .command('migrate-postgres-to-latest')
  .description('Read postgres configuration from ENV and migrate it to latest')
  .action(async () => {
    const dbConfig = await CachedDatabase.configFromEnvOrDie();
    const result = await Database.migrate(dbConfig, async (migrator) => {
      return await migrator.migrateToLatest();
    });
    if (result.error) {
      console.error('Failed to migrate database to latest revision!');
      console.error(result.error);
      process.exit(1);
    }
    if (result.results?.length) {
      console.log(`Successfully applied ${result.results.length} migrations!`)
    } else {
      console.log(`Database is latest version.`)
    }
  });

program
  .command('verify-license <license>')
  .description('Verify license')
  .action(async (licenseKey) => {
    const license = await License.initialize(licenseKey);
    if (!license) {
      console.log(`License is not valid.`)
      process.exit(1);
    }
    console.log(``)
    console.log(license.description());
  });

program
  .command('new-license')
  .description('Generate License Key to access container registry & run on-premise installation')
  .addOption(new Option(`-n, --name <type>`, `Account name, i.g. "Degu Labs, Inc"`)
    .makeOptionMandatory())
  .addOption(new Option(`-k, --private-key <type>`, `Base64 of a private key`).env('LICENSE_PRIVATE_KEY')
    .makeOptionMandatory())
  .addOption(new Option(`-p, --permissions <type>`, `Container permissions; all clients must be "pull".`)
    .makeOptionMandatory()
    .choices(['pull', 'pushpull'])
    .default('pull'))
  .addOption(new Option(`--exp-years <number>`, `Token expiration in years`).argParser(e => parseInt(e, 10)).default(0))
  .addOption(new Option(`--exp-months <number>`, `Token expiration in months`).argParser(e => parseInt(e, 10)).default(0))
  .addOption(new Option(`--exp-days <number>`, `Token expiration in days`).argParser(e => parseInt(e, 10)).default(0))
  .addOption(new Option(`--exp-hours <number>`, `Token expiration in hours`).argParser(e => parseInt(e, 10)).default(0))
  .addOption(new Option(`--exp-minutes <number>`, `Token expiration in minutes`).argParser(e => parseInt(e, 10)).default(0))
  .action(async (options: {
    expYears: number,
    expMonths: number,
    expDays: number,
    expHours: number,
    expMinutes: number,
    name: string,
    privateKey: string,
    permissions: 'pull'|'pushpull'
  }) => {
    if (options.expDays + options.expHours + options.expMinutes + options.expMonths + options.expYears === 0) {
      console.error(`Please specify at least one --exp-* argument! Use --help for details`);
      process.exit(1);
    }

    const expMs = Math.floor(
      options.expYears * ms('1 year') +
      options.expMonths * ms('1 year') / 12 +
      options.expDays * ms('1 day') +
      options.expHours * ms('1 hour') +
      options.expMinutes * ms('1 minute')
    );
    if (expMs < ms('1 minute')) {
      throw new Error('the token must be valid for at least 1 minute');
    }

    const caps: RegistryTokenCapability[] = options.permissions === 'pushpull' ? ['push', 'pull'] : ['pull'];
    const untilMs = Date.now() + expMs;

    const license = await License.createLicense({
      privateKey: options.privateKey,
      accountId: options.name,
      caps,
      untilMs,
    });

    console.log(`\n${license.description()}`);

    console.log(`\n---- BEGIN LICENSE ----\n`);
    console.log(license.licenseKey());
    console.log(`\n---- END LICENSE ----`);
  });


program
  .command('create-database-encryption-key')
  .description(`Generate encryption key for the database.`)
  .action(async () => {
    console.log(Database.createEncryptionKey());
  });

program
  .command('create-jwt-token')
  .description(`Generate a secure JWT token.`)
  .action(async () => {
    console.log(crypto.randomBytes(32).toString('hex'));
  });

program
  .command('get-github-id <username>')
  .description(`Get a github id by user name.`)
  .action(async (username) => {
    const octokit = new Octokit();
    try {
      const { data } = await octokit.rest.users.getByUsername({ username });
      console.log(`GitHub user ID for ${username}: ${data.id}`);
    } catch (error: any) {
      if (error.response && error.response.status === 404)
        console.log(`Cannot find github user with username "${username}"`)
      else if (error.response && error.response.status >= 500)
        console.log(`Oops! It looks like github is down - HTTP response is ${error.response.status}`);
      else
        console.error(error);
    }
  });

program.parse();