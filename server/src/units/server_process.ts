import debug from 'debug';
import { HTTPServer } from "../node/httpServer.js";
import { Services } from "../node/services.js";

const log = debug('fk:server_process');

const serverConfig = await HTTPServer.configFromEnvOrDie();
const servicesConfig = await Services.configFromEnvOrDie();

const services = await Services.initializeOrDie(servicesConfig);
const server = await HTTPServer.create(services, serverConfig);

log(`Server started with pid = ${process.pid}`);

const teardown = (signal: string) => {
  log(`Server Received ${signal} - terminating.`);
  services.dispose();
  server.dispose();
}

services.license?.expiredPromise().then(() => teardown('license_expired'));
for (const signal of ['SIGTERM', 'SIGINT'])
  process.on(signal, () => teardown(signal));
