import debug from 'debug';
import { isMainThread, parentPort, workerData } from 'node:worker_threads';
import { Services } from '../node/services.js';
import { TelemetryServer } from '../node/telemetry.js';

const log = debug('fk:builder_worker');

if (isMainThread) {
  log('This script only runs in worker');
} else {
  const services = await Services.initializeOrDie(workerData.servicesConfig);
  const telemetry = workerData.telemetryConfig ? await TelemetryServer.create(workerData.telemetryConfig) : undefined;
  await services.startBackgroundProcessing(workerData.workerName ?? 'node.js worker');
  log(`Builder worker started`);
  parentPort?.once('message', async message => {
    if (message === 'STOP') {
      log(`Builder worker received STOP - terminating...`);
      await services.stopBackgroundProcessing();
      await services.dispose();
      await telemetry?.dispose();
    }
  });
}
