import debug from 'debug';
import os from 'os';
import { packageJSON } from '../node/nodeutils.js';
import { Services } from "../node/services.js";
import { TelemetryServer } from '../node/telemetry.js';

const log = debug('fk:builder_process');

const config = await Services.configFromEnvOrDie();
const telemetryConfig = await TelemetryServer.configFromEnv();
const telemetry = telemetryConfig ? await TelemetryServer.create(telemetryConfig) : undefined;
const services = await Services.initializeOrDie(config);

const workerName = `b${process.env.FK_BUILDER_INDEX ?? ''}.${os.hostname()} v${packageJSON.version}`;
process.title = workerName;

await services.startBackgroundProcessing(workerName);

const teardown = async (signal: string) => {
  log(`Builder Received ${signal} - terminating.`);
  await services.stopBackgroundProcessing();
  await services.dispose();
  await telemetry?.dispose();
}

for (const signal of ['SIGTERM', 'SIGINT'])
  process.on(signal, async () => teardown(signal));
