import type { AppClientId } from "@flakiness/database";

export const KNOWN_CLIENT_IDS = {
  OFFICIAL_WEB: 'flakiness-io-official-cli' as AppClientId,
  OFFICIAL_CLI: 'flakiness-io-official-website' as AppClientId,
}

export function clientName(clientId: AppClientId) {
  if (clientId === KNOWN_CLIENT_IDS.OFFICIAL_CLI)
    return 'Flakiness.io CLI';
  if (clientId === KNOWN_CLIENT_IDS.OFFICIAL_WEB)
    return 'Flakiness.io Website';
  return 'Unknown Website';
}