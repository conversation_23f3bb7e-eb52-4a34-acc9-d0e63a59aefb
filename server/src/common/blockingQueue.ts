import { ManualPromise } from "@flakiness/shared/common/manualPromise.js";
import { setTimeout } from "timers/promises";

export class Queue<T> {
  private _pushStack: T[] = [];
  private _popStack: T[] = [];

  push(element: T) {
    this._pushStack.push(element);
  }

  pop(): T|undefined {
    if (!this._popStack.length) {
      this._popStack = this._pushStack.reverse();
      this._pushStack= [];
    }
    return this._popStack.pop();
  }

  peek(): T|undefined {
    if (!this._popStack.length) {
      this._popStack = this._pushStack.reverse();
      this._pushStack= [];
    }
    return this._popStack.at(-1);
  }

  get size() {
    return this._popStack.length + this._pushStack.length;
  }
}

export class BlockingQueue<T> {
  private _queue = new Queue<T>();
  private _wait?: ManualPromise<void>;

  constructor(private _signal?: AbortSignal) {
    this._signal?.addEventListener('abort', () => {
      this._wait?.resolve();
      this._wait = undefined;
    });
  }

  push(element: T) {
    this._queue.push(element);
    this._wait?.resolve();
    this._wait = undefined;
  }

  async pop(): Promise<T> {
    while (!this._queue.size && !this._signal?.aborted) {
      this._wait ??= new ManualPromise<void>();
      await this._wait.promise;
    }
    this._signal?.throwIfAborted();
    return this._queue.pop()!;
  }

  async *[Symbol.asyncIterator]() {
    while (true)
      yield await this.pop();
  }
}

export class RateLimitedQueue<T> {
  private _queue = new BlockingQueue<T>();
  private _current = 0;
  private _wait?: ManualPromise<void>;

  constructor(private _qps: number) { }

  push(e: T) {
    this._queue.push(e);
  }

  async pop() {
    const result = await this._queue.pop();
    while (this._current >= this._qps) {
      this._wait ??= new ManualPromise<void>();
      await this._wait.promise;
    }
    ++this._current;
    setTimeout(1000, undefined, {
      ref: false,
    }).then(() => {
      --this._current;
      this._wait?.resolve();
      this._wait = undefined;
    });
    return result;
  }

  async *[Symbol.asyncIterator]() {
    while (true)
      yield await this.pop();
  }
}

// const q = new RateLimitedQueue<number>(2);

// async function executor(name: string) {
//   for await (const el of q)
//     console.log(`[${name}] element: ${el}`);
// }
// executor('1');
// executor('2');


// for (let i = 0; i < 10; ++i)
//   q.push(i);
