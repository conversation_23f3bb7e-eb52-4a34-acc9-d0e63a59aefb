import { FlakinessReport } from "@flakiness/report";
import { Brand, xxHash, xxHashObject } from "@flakiness/shared/common/utils.js";
import { Stats } from "../stats/stats.js";
import { WireTypes } from "../wireTypes.js";

type TimelineValue = string;

type NormalizedValue = Brand<string, 'NormalizedValue'>;

function normalize(value: string): NormalizedValue {
  return value.toLowerCase().trim() as NormalizedValue;
}

function maybeNormalize(value: string|undefined): NormalizedValue|undefined {
  return value === undefined ? undefined : normalize(value);
}

export class TimelineKey {
  static systemKeys = {
    NAME: new TimelineKey('name', env => env.name),
    CONFIG_PATH: new TimelineKey('path', env => env.configPath),
    CATEGORY: new TimelineKey('category', env => env.category),
    OS_NAME: new TimelineKey('osname', env => env.systemData.osName + ' ' + env.systemData.osVersion),
    OS_ARCH: new TimelineKey('osarch', env => env.systemData.osArch),
  } as const;

  static createUserKey = (name: string) => new TimelineKey(name, env => {
    const value = env.userSuppliedData?.[name];
    return value !== undefined && value !== null ? String(value) : undefined;
  }, true);

  constructor(
    public readonly name: string,
    public extract: (env: WireTypes.RunEnvironment) => TimelineValue|undefined,
    public readonly isUserData?: boolean) {
  }

  hash() {
    return xxHashObject({ name: this.name, isUserData: !!this.isUserData });
  }

  isEqual(other: TimelineKey) {
    return this.name === other.name && this.isUserData === other.isUserData;
  }
}

type SystemType = 'linux'|'windows'|'macos';
type TimelineDescription = {
  category: string,
  name?: string,
  configPath?: FlakinessReport.GitFilePath,
  metadata?: string,
  system?: string,
  systemType?: SystemType;
}

export class Timeline {
  static etagAll(timelines: Timeline[]) {
    return xxHash(timelines.map(t => t.etag()).sort());
  }

  /**
   * The maximum number of distinct timelines we expect to have per query.
   * This constant is used throughout the engine to establish per-timeline caches.
   */
  static MAX_TIMELINES = 500;

  private _memoizedResults = new Map<Stats.EnvId, boolean>();
  private _filters: [TimelineKey, TimelineValue][] = [];


  static deserialize(json: WireTypes.JSONTimeline) {
    const filters: [TimelineKey, TimelineValue][] = [];
    const systemKeys = Object.values(TimelineKey.systemKeys);
    for (const { name, value } of json.system) {
      const key = systemKeys.find(key => key.name === name);
      if (key)
        filters.push([key, value]);
    }
    for (const { name, value } of json.user)
      filters.push([TimelineKey.createUserKey(name), value]);
    return new Timeline(filters);
  }

  static fromEnv(env: WireTypes.RunEnvironment) {
    const keys: TimelineKey[] = [];
    if (env.name)
      keys.push(TimelineKey.systemKeys.NAME);
    if (env.category)
      keys.push(TimelineKey.systemKeys.CATEGORY);
    if (env.configPath)
      keys.push(TimelineKey.systemKeys.CONFIG_PATH);
    if (env.systemData.osName || env.systemData.osVersion)
      keys.push(TimelineKey.systemKeys.OS_NAME);
    if (env.systemData.osArch)
      keys.push(TimelineKey.systemKeys.OS_ARCH);
    if (env.userSuppliedData) {
      for (const name of Object.keys(env.userSuppliedData))
        keys.push(TimelineKey.createUserKey(name));
    }
    const filters: [TimelineKey, TimelineValue][] = [];
    for (const key of keys) {
      const value = key.extract(env);
      if (value)
        filters.push([key, value]);
    }
    return new Timeline(filters);
  }

  constructor(filters: [TimelineKey, TimelineValue][]) {
    for (const [key, value] of filters)
      this._filters.push([key, value]);
  }

  filters() {
    return this._filters;
  }

  compare(other: Timeline) {
    const a = this.description();
    const b = other.description();
    if ((a.system ?? '') !== (b.system ?? ''))
      return (a.system ?? '') < (b.system ?? '') ? -1 : 1;
    if ((a.name ?? '') !== (b.name ?? ''))
      return (a.name ?? '') < (b.name ?? '') ? -1 : 1;
    if ((a.metadata ?? '') !== (b.metadata ?? ''))
      return (a.metadata ?? '') < (b.metadata ?? '') ? -1 : 1;
    if ((a.configPath ?? '') !== (b.configPath ?? ''))
      return (a.configPath ?? '') < (b.configPath ?? '') ? -1 : 1;
    if (a.category !== b.category)
      return a.category < b.category ? -1 : 1;
    return 0;
  }

  timelineKeys(): TimelineKey[] {
    return this._filters.map(filter => filter[0]);
  }

  acceptsEnvironment(env: WireTypes.RunEnvironment) {
    let result = this._memoizedResults.get(env.envId);
    if (result === undefined) {
      result = this._filters.every(([key, value]) => maybeNormalize(key.extract(env)) === maybeNormalize(value));
      this._memoizedResults.set(env.envId, result);
    }
    return result;
  }

  private _memoizedSerialize?: WireTypes.JSONTimeline;
 
  serialize(): WireTypes.JSONTimeline {
    if (!this._memoizedSerialize) {
      const system = this._filters.filter(([env]) => !env.isUserData);
      const user = this._filters.filter(([env]) => !!env.isUserData);
      const cmp = (a: { name: string, value: TimelineValue }, b: { name: string, value: TimelineValue }) => {
        if (a.name !== b.name)
          return a.name < b.name ? -1 : 1;
        return a.value < b.value ? -1 : 1;
      }
      this._memoizedSerialize = {
        system: system.map(([key, value]) => ({ name: key.name, value })).sort(cmp),
        user: user.map(([key, value]) => ({ name: key.name, value })).sort(cmp),
      };
    }
    return this._memoizedSerialize;
  }

  private _memoizedEtag?: string;
  etag() {
    if (!this._memoizedEtag)
      this._memoizedEtag = xxHashObject(this.serialize());
    return this._memoizedEtag;
  }

  private _memoizedDescription?: TimelineDescription;
  description() {
    if (!this._memoizedDescription)
      this._memoizedDescription = this._descriptionInterval();
    return this._memoizedDescription;
  }

  private _descriptionInterval(): TimelineDescription {
    const env = {
      name: undefined as string|undefined,
      category: '',
      configPath: undefined as FlakinessReport.GitFilePath|undefined,
      systemData: {
        osArch: '',
        osName: '',
      },
    };
    for (const [key, value] of this._filters) {
      if (key === TimelineKey.systemKeys.CATEGORY)
        env.category = value as string;
      else if (key === TimelineKey.systemKeys.NAME)
        env.name = value as string;
      else if (key === TimelineKey.systemKeys.CONFIG_PATH)
        env.configPath = value as FlakinessReport.GitFilePath;
      else if (key === TimelineKey.systemKeys.OS_ARCH)
        env.systemData.osArch = value as string;
      else if (key === TimelineKey.systemKeys.OS_NAME)
        env.systemData.osName = value as string;
    }

    const tokens: string[] = [];
    for (let [key, value] of this._filters.filter(([key]) => key.isUserData)) {
      if (value.toLowerCase() === 'false' || value.toLowerCase() === 'default')
        continue;
      if (value.toLowerCase() === 'true')
        tokens.push(key.name);
      else if (Number.isFinite(Number(value)))
        tokens.push(`${key.name}=${value}`);
      else
        tokens.push(value);
    }
    let arch = env.systemData.osArch;
    if (arch === 'x86_64')
      arch = 'x86-64';
    else if (arch === 'aarch64' || arch === 'arm64')
      arch = 'arm64';
    const system = [env.systemData.osName, arch].join(' ').trim();
    let systemType: SystemType|undefined;
    const impliedContext: string[] = [...tokens, system];
    if (env.name)
      impliedContext.push(env.name);
    const osName = env.systemData.osName.toLowerCase();
    if (osName === 'darwin') {
      impliedContext.push('mac', 'macos', 'mac os')
      systemType = 'macos';
    } else if (osName.startsWith('mingw')) {
      systemType = 'windows';
      impliedContext.push('win32', 'win', 'windows', 'mingw');
    } else if (osName.startsWith('ubuntu') || osName.startsWith('linux')) {
      systemType = 'linux';
      impliedContext.push('linux');
    }

    if (tokens.some(token => token.startsWith('chrome')))
      impliedContext.push('chromium');

    // 1. If some tokens are substrings of some other tokens, including NAME and OS, than we can drop them.
    let filtered = tokens.filter((theToken, theIndex) => impliedContext.every((aToken, aIndex) => aIndex === theIndex || aToken.toLowerCase().indexOf(theToken.toLowerCase()) === -1));
    return {
      name: env.name,
      category: env.category,
      configPath: env.configPath,
      metadata: filtered.length ? filtered.join(', ') : undefined,
      system,
      systemType,
    };
  }
}
