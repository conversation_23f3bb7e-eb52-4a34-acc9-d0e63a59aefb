import { FlakinessReport } from "@flakiness/report";
import { Multimap } from "@flakiness/shared/common/multimap.js";
import { Brand, xxHashObject } from "@flakiness/shared/common/utils.js";
import { Stats } from "../stats/stats.js";
import { WireTypes } from "../wireTypes.js";
import { Timeline, TimelineKey } from "./timeline.js";

type TimelineValue = string;

type NormalizedValue = Brand<string, 'NormalizedValue'>;

function normalize(value: string): NormalizedValue {
  return value.toLowerCase().trim() as NormalizedValue;
}

export class TimelineSplit {
  static DEFAULT = new TimelineSplit();
  static PW_PROJECTS = TimelineSplit.categorySplit(FlakinessReport.CATEGORY_PLAYWRIGHT)
  static JUNIT_ENVS = TimelineSplit.categorySplit(FlakinessReport.CATEGORY_JUNIT);
  static PERF_BENCHMARKS = TimelineSplit.categorySplit(FlakinessReport.CATEGORY_PERF);

  static categorySplit(category: string) {
    return new TimelineSplit().toggleValue(TimelineKey.systemKeys.CATEGORY, category);
  }

  static deserialize(json: WireTypes.JSONTimelineSplit) {
    const split = new TimelineSplit();
    split._isSplitByDefault = !!json.splitByDefault;
    const systemKeys = Object.values(TimelineKey.systemKeys);
    for (const { name, values } of json.filters.system ?? []) {
      const key = systemKeys.find(key => key.name === name);
      if (!key)
        continue;
      split._filterValues.setAll(key.hash(), new Set(Array.from(values, normalize)));
      split._filterKeys.set(key.hash(), key);
    }
    for (const { name, values } of json.filters.user ?? []) {
      const key = TimelineKey.createUserKey(name);
      split._filterValues.setAll(key.hash(), Array.from(values, normalize));
      split._filterKeys.set(key.hash(), key);
    }
    for (const name of json.inverse.system ?? []) {
      const key = systemKeys.find(key => key.name === name);
      if (!key)
        continue;
      split._inversedKeys.set(key.hash(), key);
    }
    for (const name of json.inverse.user ?? []) {
      const key = TimelineKey.createUserKey(name);
      split._inversedKeys.set(key.hash(), key);
    }
    return split;
  }

  static fromTimeline(timeline: Timeline) {
    const split = new TimelineSplit();
    split._isSplitByDefault = false;
    for (const [key, value] of timeline.filters()) {
      split._filterValues.set(key.hash(), normalize(value));
      split._filterKeys.set(key.hash(), key);
    }
    return split;
  }

  private _filterKeys = new Map<string, TimelineKey>();
  private _filterValues = new Multimap<string, NormalizedValue>();
  private _isSplitByDefault: boolean = true;
  private _inversedKeys = new Map<string, TimelineKey>();

  clone() {
    const other = new TimelineSplit();
    other._isSplitByDefault = this._isSplitByDefault;
    other._inversedKeys = new Map(this._inversedKeys);
    for (const [key, values] of this._filterValues)
      other._filterValues.setAll(key, values);
    other._filterKeys = new Map(this._filterKeys);
    return other;
  }

  maxGranularity(envs: WireTypes.RunEnvironment[]) {
    const other = this.clone();
    other._isSplitByDefault = true;
    other._inversedKeys.clear();
    return other;
  }

  isSplitBy(key: TimelineKey) {
    if (this._filterKeys.has(key.hash()))
      return true;
    return this._isSplitByDefault ? !this._inversedKeys.has(key.hash()) : this._inversedKeys.has(key.hash());
  }

  splitBy(key: TimelineKey) {
    if (this.isSplitBy(key))
      return this;
    return this.splitByMany([key]);
  }

  splitByMany(keys: TimelineKey[]) {
    const other = this.clone();
    if (this._isSplitByDefault) {
      for (const key of keys)
        other._inversedKeys.delete(key.hash());
    } else {
      for (const key of keys)
        other._inversedKeys.set(key.hash(), key);
    }    
    return other;
  }

  serialize(): WireTypes.JSONTimelineSplit {
    const systemFilters = Array.from(this._filterKeys.values()).filter(key => !key.isUserData);
    const userFilters = Array.from(this._filterKeys.values()).filter(key => !!key.isUserData);

    const systemCollapse = Array.from(this._inversedKeys.values()).filter(key => !key.isUserData);
    const userCollapse = Array.from(this._inversedKeys.values()).filter(key => !!key.isUserData);

    function serializeFilter(key: TimelineKey, values: Iterable<TimelineValue>) {
      return {
        name: key.name,
        values: [...values].sort((a, b) => a < b ? -1 : 1),
      };
    }
    function cmp(a: { name: string }, b: { name: string }) {
      return a.name < b.name ? -1 : 1;
    }

    return {
      splitByDefault: this._isSplitByDefault ? true : undefined,
      filters: {
        system: systemFilters.length ? systemFilters.map(key => serializeFilter(key, this._filterValues.getAll(key.hash()))).sort(cmp) : undefined,
        user: userFilters.length ? userFilters.map(key => serializeFilter(key, this._filterValues.getAll(key.hash()))).sort(cmp) : undefined,
      },
      inverse: {
        system: systemCollapse.length ? systemCollapse.map(x => x.name).sort((a, b) => a < b ? -1 : 1) : undefined,
        user: userCollapse.length ? userCollapse.map(x => x.name).sort((a, b) => a < b ? -1 : 1) : undefined,
      },
    };
  }

  private _memoizedEtag?: string;
  etag() {
    if (!this._memoizedEtag)
      this._memoizedEtag = xxHashObject(this.serialize());
    return this._memoizedEtag;
  }

  isEqual(other: TimelineSplit) {
    return this.etag() === other.etag();
  }

  private _memoizedAcceptsEnv = new Map<Stats.EnvId, boolean>();

  acceptsEnvironment(env: WireTypes.RunEnvironment): boolean {
    let result = this._memoizedAcceptsEnv.get(env.envId);
    if (result === undefined) {
      result = Array.from(this._filterKeys.values()).every(key => {
        const value = key.extract(env);
        return value && this._filterValues.has(key.hash(), normalize(value));
      });
      this._memoizedAcceptsEnv.set(env.envId, result);
    }
    return result;
  }

  clearAndCollapse(key: TimelineKey) {
    const other = this.clone();
    other._filterValues.deleteAll(key.hash());
    other._filterKeys.delete(key.hash());
    if (this._isSplitByDefault)
      other._inversedKeys.set(key.hash(), key);
    else
      other._inversedKeys.delete(key.hash());
    return other;
  }

  toggleValue(key: TimelineKey, value: TimelineValue) {
    return this.toggleValues(key, [value]);
  }

  toggleValues(key: TimelineKey, rawValues: Iterable<TimelineValue>) {
    const other = this.clone();
    for (const value of new Set(Array.from(rawValues, normalize))) {
      if (other._filterValues.has(key.hash(), value))
        other._filterValues.delete(key.hash(), value);
      else
        other._filterValues.set(key.hash(), value);
    }
    if (other._filterValues.hasAny(key.hash()))
      other._filterKeys.set(key.hash(), key);
    else
      other._filterKeys.delete(key.hash());
    return other;
  }

  hasValue(key: TimelineKey, rawValue: TimelineValue): boolean {
    return this._filterValues.has(key.hash(), normalize(rawValue));
  }

  categories(): string[] {
    return this._filterValues.getAll(TimelineKey.systemKeys.CATEGORY.hash());
  }

  timelines(envs: WireTypes.RunEnvironment[]): Timeline[] {
    // Filter envs to leave only those that we care about.
    envs = envs.filter(env => this.acceptsEnvironment(env));

    // Aggregate all values across environments.
    const values = new Multimap<string, NormalizedValue>();
    for (const e of envs) {
      for (const [key, value] of Timeline.fromEnv(e).filters())
        values.set(key.hash(), normalize(value));
    }
    const timelines = envs.map(e => Timeline.fromEnv(e)).map(timeline => {
      const newFilters = timeline.filters().filter(([key]) => {
        if (!this.isSplitBy(key))
          return false;
        if (this._isSplitByDefault && !this._filterKeys.has(key.hash()))
          return key.isEqual(TimelineKey.systemKeys.CONFIG_PATH) || key.isEqual(TimelineKey.systemKeys.NAME) || values.getAll(key.hash()).length > 1;
        return true;
      });
      return new Timeline(newFilters);
    });
    const dedupe = new Map<string, Timeline>(timelines.map(t => [t.etag(), t]));
    return Array.from(dedupe.values());
  }

  toggleEnsureNonEmptySelection(key: TimelineKey, value: TimelineValue, envs: WireTypes.RunEnvironment[]) {
    //TODO: this is NOT what we want?
    const other = this.toggleValue(key, value);
    return other;
  }
}
