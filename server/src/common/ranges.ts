import { Brand } from '@flakiness/shared/common/utils.js';
import * as vlq from 'vlq';
import { Sequence } from './sequence.js';

export namespace Ranges {
  export type Ranges<T extends number> = Brand<T[], 'ranges'>;
  export type CompressedRanges<T extends number> = Brand<string, 'compressed-ranges'>;
  export type Domain<T extends number> = {
    min: T,
    max: T,
  }

  export const EMPTY = ([] as number[]) as Ranges<number>;
  export const FULL = ([-Infinity, Infinity]) as Ranges<number>;

  export function isFull(ranges: Ranges.Ranges<any>): boolean {
    return ranges.length === 2 && Object.is(ranges[0], -Infinity) && Object.is(ranges[1], Infinity);
  }

  /**
   * ranges have the following properties:
   * - sorted number pairs
   * - in each pair [a, b], b >= a
   * - for each consecutive pairs [a, b], [c, d]: c > b
   * - lots of pairs are of type [a, a]
   * 
   * This lets us compress these pairs like this:
   * 1. First number is the very first number of the ranges - 1.
   * 2. Then, transform the array of numbers into array of deltas between numbers.
   *    NOTICE: deltas between pairs are always > 0, deltas inside pairs could be 0
   * 3. Now, if the delta inside pair is 0, then record delta for this whole pair as negative.
   *    This allows us to encode ranges more efficiently than just array of numbers.
   * 4. Finally, VLQ64 the resulting array.
   * 
   * Example:
   *       ranges: [-5, -5, 2, 4]
   *   compressed: []
   * 
   * 1. first number: -5 - 1 = -6
   * 2. deltas: [1, 0, 7, 2] (the first 1 is since we compute running delta from -6)
   * 3. encoding: [-6, -1, 7, 2]
   */
  export function compress<T extends number>(ranges: Ranges<T>): CompressedRanges<T> {
    if (!ranges.length)
      return '' as CompressedRanges<T>;
    if (isInfinite(ranges))
      throw new Error('Compression of infinite ranges is not supported');
    const prepared: number[] = [];
    let last = ranges[0] - 1;
    prepared.push(last);
    for (let i = 0; i < ranges.length; i += 2) {
      if (ranges[i] === ranges[i + 1]) {
        prepared.push(-(ranges[i] - last));
      } else {
        prepared.push(ranges[i] - last);
        prepared.push(ranges[i + 1] - ranges[i]);
      }
      last = ranges[i + 1];
    }
    return vlq.encode(prepared) as CompressedRanges<T>;
  }

  export function decompress<T extends number>(compressed: CompressedRanges<T>): Ranges<T> {
    if (!compressed.length)
      return [] as number[] as Ranges<T>;
    const prepared = vlq.decode(compressed) as number[];
    const result = [] as number[] as Ranges<T>;
    let last = prepared[0];
    for (let i = 1; i < prepared.length; ++i) {
      if (prepared[i] < 0) {
        result.push((-prepared[i] + last) as T);
        result.push((-prepared[i] + last) as T);
        last -= prepared[i];
      } else {
        result.push((prepared[i] + last) as T);
        last += prepared[i];
      }
    }
    return result;
  }

  export function toString<T extends number>(ranges: Ranges<T>) {
    const tokens = [];
    for (let i = 0; i < ranges.length - 1; i += 2) {
      if (ranges[i] === ranges[i + 1])
        tokens.push(ranges[i])
      else
        tokens.push(`${ranges[i]}-${ranges[i + 1]}`);
    }
    if (!tokens.length)
      return `[]`
    return `[ ` + tokens.join(', ') + ` ]`;
  }

  export function popInplace<T extends number>(ranges: Ranges<T>): T|undefined {
    if (isInfinite(ranges))
      throw new Error('cannot pop from infinite ranges!');
    const last = ranges.at(-1);
    const prelast = ranges.at(-2);

    if (last === undefined || prelast === undefined)
      return undefined;
    if (last === prelast) {
      ranges.pop();
      ranges.pop();
    } else {
      ranges[ranges.length - 1] = (last - 1) as T;
    }
    return last;
  }

  export function *iterate<T extends number>(ranges: Ranges<T>) {
    if (isInfinite(ranges))
      throw new Error('cannot iterate infinite ranges!');
    for (let i = 0; i < ranges.length - 1; i += 2) {
      for (let j = ranges[i]; j <= ranges[i + 1]; ++j)
        yield j;
    }
  }

  export function toSortedList<T extends number>(ranges: Ranges<T>): T[] {
    if (isInfinite(ranges))
      throw new Error('cannot convert infinite ranges!');
    const list = [];
    for (let i = 0; i < ranges.length - 1; i += 2) {
      for (let j = ranges[i]; j <= ranges[i + 1]; ++j)
        list.push(j);
    }
    return list;
  }

  export function toInt32Array<T extends number>(ranges: Ranges<T>): Int32Array {
    if (isInfinite(ranges))
      throw new Error('cannot convert infinite ranges!');
    const result = new Int32Array(cardinality(ranges));
    let idx = 0;
    for (let i = 0; i < ranges.length - 1; i += 2) {
      for (let j = ranges[i]; j <= ranges[i + 1]; ++j)
        result[idx++] = j;
    }
    return result;
  }

  export function fromList<T extends number>(x: T[]): Ranges<T> {
    for (let i = 0; i < x.length - 1; ++i) {
      if (x[i] > x[i + 1]) {
        x = x.toSorted((a: number, b: number) => a - b);
        break;
      }
    }
    return fromSortedList(x);
  }

  export function from<T extends number>(x: T): Ranges<T> {
    return [x, x] as Ranges<T>;
  }

  export function fromSortedList<T extends number>(sorted: T[]): Ranges<T> {
    const ranges: Ranges<T> = ([] as number[]) as Ranges<T>;
    let rangeStart = 0;
    for (let i = 1; i <= sorted.length; ++i) {
      if (i < sorted.length && sorted[i] - sorted[i - 1] <= 1)
        continue;
      ranges.push(sorted[rangeStart], sorted[i - 1]);
      rangeStart = i;
    }
    return ranges;
  }

  export function isInfinite<T extends number>(ranges: Ranges<T>): boolean {
    return ranges.length > 0 && (Object.is(ranges[0], Infinity) || Object.is(ranges[ranges.length - 1], Infinity));
  }

  export function includes<T extends number>(ranges: Ranges<T>, e: T): boolean {
    if (!ranges.length)
      return false;
    if (e < ranges[0] || ranges[ranges.length - 1] < e)
      return false;

    // For small ranges, do linear scan.
    if (ranges.length < 17) {
      for (let i = 0; i < ranges.length - 1; i += 2) {
        if (ranges[i] <= e && e <= ranges[i + 1])
          return true;
      }
      return false;
    }
    // For large ranges, do binsearch.
    let lo = 0, hi = ranges.length;
    while (lo < hi) {
      const mid = (lo + hi) >>> 1;
      if (ranges[mid] === e)
        return true;
      if (ranges[mid] < e)
        lo = mid + 1;
      else
        hi = mid;
    }
    return (lo & 1) !== 0;
  }

  export function cardinality(ranges: Ranges<any>): number {
    if (isInfinite(ranges))
      return Infinity;

    let sum = 0;
    for (let i = 0; i < ranges.length - 1; i += 2)
      sum += ranges[i + 1] - ranges[i] + 1;
    return sum;
  }

  export function offset(ranges: Ranges<any>, offset: number): Ranges<any> {
    return ranges.map(x => x + offset) as Ranges<any>;
  }
  
  export function intersect<T extends number>(ranges1: Ranges<T>, ranges2: Ranges<T>): Ranges<T> {
    const ranges: Ranges<T> = [] as number[] as Ranges<T>;
    if (!ranges1.length || !ranges2.length)
      return ranges;
    if (ranges1[ranges1.length - 1] < ranges2[0] || ranges2[ranges2.length - 1] < ranges1[0])
      return ranges;

    let p1 = 0;
    let p2 = 0;
    while (p1 < ranges1.length - 1 && p2 < ranges2.length - 1) {
      if (ranges1[p1 + 1] < ranges2[p2]) {
        p1 += 2;
        // fast advance
        let offset = 1;
        while (p1 + offset * 2 + 1 < ranges1.length && ranges1[p1 + offset * 2 + 1] < ranges2[p2])
          offset <<= 1;
        p1 += (offset >> 1) << 1;
      } else if (ranges2[p2 + 1] < ranges1[p1]) {
        p2 += 2;
        // fast advance
        let offset = 1;
        while (p2 + offset * 2 + 1 < ranges2.length && ranges2[p2 + offset * 2 + 1] < ranges1[p1])
          offset <<= 1;
        p2 += (offset >> 1) << 1;
      } else {
        const a1 = ranges1[p1], a2 = ranges1[p1 + 1];
        const b1 = ranges2[p2], b2 = ranges2[p2 + 1];

        // Ranges intersect; push intersection
        ranges.push(Math.max(a1, b1) as T, Math.min(a2, b2) as T);
        if (a2 < b2) {
          p1 += 2;
        } else if (a2 > b2) {
          p2 += 2;
        } else {
          p1 += 2;
          p2 += 2;
        }
      }
    }
    return ranges;
  }

  export function capAt<T extends number>(ranges: Ranges<T>, cap: T): Ranges<T> {
    const result: Ranges<T> = [] as number[] as Ranges<T>;
    for (let i = 0; i < ranges.length; i += 2) {
      const start = ranges[i];
      const end = ranges[i + 1];
      if (start > cap)
        break;
      if (end <= cap) {
        result.push(start, end);
      } else {
        result.push(start, cap);
        break;
      }
    }
    return result;
  }

  export function isIntersecting<T extends number>(ranges1: Ranges<T>, ranges2: Ranges<T>): boolean {
    if (!ranges1.length || !ranges2.length)
      return false;
    if (ranges1[ranges1.length - 1] < ranges2[0] || ranges2[ranges2.length - 1] < ranges1[0])
      return false;
    let p1 = 0;
    let p2 = 0;
    while (p1 < ranges1.length - 1 && p2 < ranges2.length - 1) {
      const a1 = ranges1[p1], a2 = ranges1[p1 + 1];
      const b1 = ranges2[p2], b2 = ranges2[p2 + 1];
      if (a2 < b1) {
        p1 += 2;
        // fast advance
        let offset = 1;
        while (p1 + offset * 2 + 1 < ranges1.length && ranges1[p1 + offset * 2 + 1] < ranges2[p2])
          offset <<= 1;
        p1 += (offset >> 1) << 1;
      } else if (b2 < a1) {
        p2 += 2;
        // fast advance
        let offset = 1;
        while (p2 + offset * 2 + 1 < ranges2.length && ranges2[p2 + offset * 2 + 1] < ranges1[p1])
          offset <<= 1;
        p2 += (offset >> 1) << 1;
      } else {
        return true;
      }
    }
    return false;
  }
  
  export function complement<T extends number>(r: Ranges<T>): Ranges<T> {
    if (r.length === 0)
      return [-Infinity, Infinity] as Ranges<T>;
    const result = ([] as number[]) as Ranges<T>;
    // [1, 5], [8, 8]
    // [-inf, 0], [6, 7], [9, inf] 
    // 
    // [-inf, 1], [8, 9]
    // [2, 7], [10, inf]
    // 
    if (!Object.is(r[0], -Infinity))
      result.push(-Infinity as T, r[0] - 1 as T);
    for (let i = 1; i < r.length - 2; i += 2)
      result.push(r[i] + 1 as T, r[i + 1] - 1 as T);
    if (!Object.is(r[r.length - 1], Infinity))
      result.push(r[r.length - 1] + 1 as T, Infinity as T);
    return result;
  }

  export function subtract<T extends number>(ranges1: Ranges<T>, ranges2: Ranges<T>): Ranges<T> {
    return intersect(ranges1, complement(ranges2));
  }

  export function singleRange<T extends number>(from: T, to: T) {
    return [from, to] as Ranges.Ranges<T>;
  }

  /**
   * If input has N ranges, each range has M intervals, and result has R intervals,
   * then this method will result in O(R * M * N) complexity.
   * 
   * We assume that massive unions will almost always have little resulting
   * ranges. If this is not the case, then we can consider a solution that performs an
   * interval merge across all ranges, using Sequence class.
   * 
   * This solution works in O(N * M * log N): iterate over all intervals across ranges - N * M, and
   * use heap to pick the smallest from all of the range pointers - log N.
   * 
   * Heap also adds significant runtime overhead, so in practice, the O(R * N * M) solution
   * works faster, and thus it is implemented here.
   */
  export function unionAll<T extends number>(ranges: Iterable<Ranges<T>>): Ranges<T> {
    let result = Ranges.EMPTY as Ranges<T>;
    for (const r of ranges)
      result = union(result, r);
    return result;
  }

  /**
   * If input has N ranges, each range has M intervals, and result has R intervals,
   * then this method will result in O(M * N * log N) complexity.
   * 
   * Since this method also relies on soft iterators a lot, it has additional ~3x penalty.
   * In practice, it is only practical to use this method if the result of union has a LOT of
   * intervals.
   */
  export function unionAll_2<T extends number>(rangesIterable: Iterable<Ranges<T>>): Ranges<T> {
    const ranges = Array.isArray(rangesIterable) ? rangesIterable : Array.from(rangesIterable);
    if (ranges.length === 0)
      return [] as unknown as Ranges<T>;
    if (ranges.length === 1)
      return ranges[0];
    if (ranges.length === 2)
      return union(ranges[0], ranges[1]);
    const seq = Sequence.merge(ranges.map(r => intervalSequence(r)), (a, b) => a[0] - b[0]);
    const result: [number, number][] = [];
    let last: [number, number]|undefined;
    for (const interval of seq.seek(0)) {
      if (!last || last[1] + 1 < interval[0]) {
        result.push(interval);
        last = interval;
        continue;
      }
      if (last[1] < interval[1])
        last[1] = interval[1];
    }
    return result.flat() as Ranges<T>;
  }

  export function intersectAll<T extends number>(ranges: Ranges<T>[]) {
    if (!ranges.length)
      return Ranges.EMPTY as Ranges<T>;
    let result = Ranges.FULL as Ranges<T>;
    for (const range of ranges)
      result = Ranges.intersect(result, range);
    return result;
  }

  export function domain<T extends number>(ranges: Ranges<T>): Domain<T>|undefined {
    if (!ranges.length)
      return undefined;
    return { min: ranges[0], max: ranges[ranges.length - 1] };
  }

  export function union<T extends number>(ranges1: Ranges<T>, ranges2: Ranges<T>): Ranges<T> {
    if (!ranges1.length)
      return ranges2;
    if (!ranges2.length)
      return ranges1;
    if (ranges2[0] < ranges1[0])
      [ranges1, ranges2] = [ranges2, ranges1];
    const r = [ranges1[0], ranges1[1]] as Ranges.Ranges<T>;
    let p1 = 2, p2 = 0;
    while (p1 < ranges1.length - 1 && p2 < ranges2.length - 1) {
      if (ranges1[p1] <= ranges2[p2]) {
        if (r[r.length - 1] + 1 < ranges1[p1]) {
          r.push(ranges1[p1], ranges1[p1 + 1]);
          p1 += 2;
        } else if (r[r.length - 1] < ranges1[p1 + 1]) {
          r[r.length - 1] = ranges1[p1 + 1];
          p1 += 2;
        } else {
          // Fast advance.
          p1 += 2;
          let offset = 1;
          while (p1 + offset * 2 + 1 < ranges1.length && r[r.length - 1] >= ranges1[p1 + offset * 2 + 1])
            offset <<= 1;
          p1 += (offset >> 1) << 1;
        }
      } else {
        if (r[r.length - 1] + 1 < ranges2[p2]) {
          r.push(ranges2[p2], ranges2[p2 + 1]);
          p2 += 2;
        } else if (r[r.length - 1] < ranges2[p2 + 1]) {
          r[r.length - 1] = ranges2[p2 + 1];
          p2 += 2;
        } else {
          // Fast advance.
          p2 += 2;
          let offset = 1;
          while (p2 + offset * 2 + 1 < ranges2.length && r[r.length - 1] >= ranges2[p2 + offset * 2 + 1])
            offset <<= 1;
          p2 += (offset >> 1) << 1;
        }
      }
    }
    while (p1 < ranges1.length - 1) {
      if (r[r.length - 1] + 1 < ranges1[p1]) {
        r.push(ranges1[p1], ranges1[p1 + 1]);
        p1 += 2;
      } else if (r[r.length - 1] < ranges1[p1 + 1]) {
        r[r.length - 1] = ranges1[p1 + 1];
        p1 += 2;
      } else {
        // Fast advance.
        p1 += 2;
        let offset = 1;
        while (p1 + offset * 2 + 1 < ranges1.length && r[r.length - 1] >= ranges1[p1 + offset * 2 + 1])
          offset <<= 1;
        p1 += (offset >> 1) << 1;
      }
    }
    while (p2 < ranges2.length - 1) {
      if (r[r.length - 1] + 1 < ranges2[p2]) {
        r.push(ranges2[p2], ranges2[p2 + 1]);
        p2 += 2;
      } else if (r[r.length - 1] < ranges2[p2 + 1]) {
        r[r.length - 1] = ranges2[p2 + 1];
        p2 += 2;
      } else {
        // Fast advance.
        p2 += 2;
        let offset = 1;
        while (p2 + offset * 2 + 1 < ranges2.length && r[r.length - 1] >= ranges2[p2 + offset * 2 + 1])
          offset <<= 1;
        p2 += (offset >> 1) << 1;
      }
    }
    return r;
  }

  export function intervalSequence<T extends number>(ranges: Ranges.Ranges<T>): Sequence<[T, T]> {
    return new Sequence<[T, T]>(function(idx): Iterator<[T, T]> {
      return {
        next() {
          if (idx * 2 >= ranges.length)
            return { done: true, value: undefined };
          const value = [ranges[idx * 2], ranges[ idx * 2 + 1]] as [T, T];
          ++idx;
          return { done: false, value };
        },
      }
    }, ranges.length >>> 1);
  }

  export function sequence<T extends number>(ranges: Ranges.Ranges<T>): Sequence<T> {
    let length = 0;
    const leftsums: number[] = [];
    for (let i = 0; i < ranges.length - 1; i += 2) {
      length += ranges[i + 1] - ranges[i] + 1;
      leftsums.push(length);
    }

    return new Sequence<T>(
      function(fromIdx) {
        fromIdx = Math.max(0, Math.min(length, fromIdx));

        const idx = Sequence.fromList(leftsums).partitionPoint(x => x <= fromIdx);
        const intervals = Ranges.intervalSequence(ranges);
        const it = intervals.seek(idx);
        const firstInterval = it.next();
        if (firstInterval.done)
          return { next: () => firstInterval };

        let from = firstInterval.value[0] + fromIdx - (idx > 0 ? leftsums[idx - 1] : 0);
        let to = firstInterval.value[1];

        return {
          next() {
            if (from > to) {
              const interval = it.next();
              if (interval.done)
                return { done: true, value: undefined };
              from = interval.value[0];
              to = interval.value[1];
            }
            return { done: false, value: from++ as T };
          }
        }
      },
      length,
    );
  }
}