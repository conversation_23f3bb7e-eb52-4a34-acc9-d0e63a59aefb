export class Heap<ELEMENT, SCORE> {
  static createMin<ELEMENT>(elements: [ELEMENT, number][] = []) {
    return new Heap<ELEMENT, number>((a, b) => a - b, elements);
  }

  static createMax<ELEMENT>(elements: [ELEMENT, number][] = []) {
    return new Heap<ELEMENT, number>((a, b) => b - a, elements);
  }

  private _heap: {
    score: SCORE,
    element: ELEMENT,
  }[] = [];

  constructor(
    private _cmp: (a: SCORE, b: SCORE) => number,
    elements: [ELEMENT, SCORE][] = []
  ) {
    this._heap = elements.map(([element, score]) => ({ element, score }));
    for (let idx = this._heap.length - 1; idx >= 0; --idx)
      this._down(idx);
  }

  private _up(idx: number) {
    const e = this._heap[idx];
    while (idx > 0) {
      const parentIdx = (idx - 1) >>> 1;
      if (this._cmp(this._heap[parentIdx].score, e.score) <= 0)
        break;
      this._heap[idx] = this._heap[parentIdx];
      idx = parentIdx;
    }
    this._heap[idx] = e;
  }

  private _down(idx: number) {
    const N = this._heap.length;
    const e = this._heap[idx];
    while (true) {
      const leftIdx = idx * 2 + 1;
      const rightIdx = idx * 2 + 2;

      let smallestIdx;
      if (leftIdx < N && rightIdx < N) {
        smallestIdx = this._cmp(this._heap[leftIdx].score, this._heap[rightIdx].score) < 0 ? leftIdx : rightIdx;
      } else if (leftIdx < N) {
        smallestIdx = leftIdx;
      } else if (rightIdx < N) {
        smallestIdx = rightIdx;
      } else {
        break;
      }
      if (this._cmp(e.score, this._heap[smallestIdx].score) < 0)
        break;

      this._heap[idx] = this._heap[smallestIdx];
      idx = smallestIdx;
    }
    this._heap[idx] = e;
  }

  push(element: ELEMENT, score: SCORE) {
    this._heap.push({ element, score });
    this._up(this._heap.length - 1);
  }

  get size() {
    return this._heap.length;
  }

  peekEntry(): [ELEMENT, SCORE]|undefined {
    return this._heap.length ? [this._heap[0].element, this._heap[0].score] : undefined;
  }

  popEntry(): [ELEMENT, SCORE]|undefined {
    if (!this._heap.length)
      return undefined;
    const entry = this._heap[0];
    const last = this._heap.pop()!;

    // If heap is empty, then nothing to do.
    if (!this._heap.length)
      return [entry.element, entry.score];

    this._heap[0] = last;
    this._down(0);
    return [entry.element, entry.score];
  }
}

// const iterators = [
//   [1,2,5,7],
//   [-1,2,4],
//   [5],
// ].map(a => a.values());

// const heap = new HeapSet<ArrayIterator<number>, number>((a, b) => a - b);

// for (const iterator of iterators) {
//   const next = iterator.next();
//   if (next.done)
//     continue;
//   heap.add(iterator, next.value);
// }

// for (let i = 0; i < 5 && heap.size; ++i) {
//   const [iterator, key] = heap.pop()!;
//   console.log(key);
//   const next = iterator.next();
//   if (!next.done)
//     heap.add(iterator, next.value);
// }
