import { Limiter } from "./limiter.js";

type FetchCallback<INPUT, KEYTYPE, OUTPUT> = (input: INPUT, key: KEYTYPE, signal: AbortSignal) => Promise<OUTPUT>;

const MAX_REFETCH = 33;

class InflightRequest<OUTPUT> {
  private _controller?: AbortController;
  private _epoch: number = 0;
  public readonly promise: Promise<{ result: OUTPUT|undefined } | { error: any }>;

  constructor(private _callback: (signal: AbortSignal) => Promise<OUTPUT>) {
    this.promise = this._fetchLoop();
  }

  async _fetchLoop(): Promise<{ result: OUTPUT|undefined } | { error: any }> {
    for (let i = 0; i < MAX_REFETCH; ++i) {
      const epoch = this._epoch;
      let signal: AbortSignal|undefined;
      try {
        const controller = new AbortController();
        this._controller = controller;
        signal = controller.signal;
        const result = await Promise.resolve().then(() => this._callback(controller.signal));
        controller.signal.throwIfAborted();
        if (epoch === this._epoch)
          return { result };
      } catch (e) {
        // Retry if we were aborted and epoch doesn't match.
        if (signal?.aborted) {
          // if the epoch was changed, then we have to re-fetch.
          if (epoch !== this._epoch)
            continue;
          // Otherwise, the request was aborted - so we return `undefined`.
          return { result: undefined };
        }
        // Otherwise, either error in fetcher, or we were aborted without invalidation.
        // Throw the error.
        return { error: e };
      }
    }
    return { error: new Error(`Failed to fetch value after ${MAX_REFETCH} attempts!`) };
  }

  abort() {
    this._controller?.abort();
  }

  invalidate() {
    ++this._epoch;
    this._controller?.abort();
  }
}

export class Singleflight<INPUT, OUTPUT, KEYTYPE = string> {
  private _inflights = new Map<KEYTYPE, InflightRequest<OUTPUT>>();
  private _key: (input: INPUT) => KEYTYPE;
  private _fetch: FetchCallback<INPUT, KEYTYPE, OUTPUT>;

  constructor(options: {
    key: (input: INPUT) => KEYTYPE,
    fetch: FetchCallback<INPUT, KEYTYPE, OUTPUT>,
    concurrency?: number,
  }) {
    this._key = options.key;
    this._fetch = options.fetch;
    if (options.concurrency && options.concurrency !== Infinity) {
      const limiter = new Limiter(options.concurrency);
      this._fetch = async (input: INPUT, key: KEYTYPE, signal: AbortSignal) => {
        using slot = await limiter.acquireSlot(signal);
        return await options.fetch(input, key, signal);
      }
    }
  }

  /**
   * The method will return `undefined` if request was aborted.
   * @param input 
   * @param key 
   * @returns 
   */
  async fetch(input: INPUT, key: KEYTYPE = this._key(input)): Promise<OUTPUT|undefined> {
    let request = this._inflights.get(key);
    if (!request) {
      request = new InflightRequest(async signal => await this._fetch(input, key, signal));
      request.promise.finally(() => {
        // By the time this happens, a new fetch for the same key might've been
        // scheduled already, i.e. if `sf.abort(key);` is immediately followed by `sf.fetch(key)`.
        // So we should cleanup only if there's exactly the same request.
        if (this._inflights.get(key) === request)
          this._inflights.delete(key);
      });
      this._inflights.set(key, request);
    }
    return request.promise.then(r => {
      if ('error' in r)
        throw r.error;
      return r.result;
    });
  }

  has(input: INPUT, key = this._key(input)) {
    return this._inflights.has(key);
  }

  invalidateAll() {
    for (const request of this._inflights.values())
      request.invalidate();
  }

  /**
   * If inflight request is invalidated, it will be aborted, and a new one will
   * be initiated in its place.
   */
  invalidate(input: INPUT, key = this._key(input)) {
    const request = this._inflights.get(key);
    request?.invalidate();
  }

  abort(input: INPUT, key = this._key(input)) {
    const request = this._inflights.get(key);
    if (!request)
      return;
    request.abort();
    this._inflights.delete(key);
  }

  abortAll() {
    for (const request of this._inflights.values())
      request.abort();
    this._inflights.clear();
  }
}
