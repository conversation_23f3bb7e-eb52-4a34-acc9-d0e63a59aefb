export class IntervalTree<ELEMENT> {
  private _length: number;
  private _tree: ELEMENT[] = [];

  constructor(elements: ELEMENT[],
    private _sum: (a: ELEMENT, b: ELEMENT) => ELEMENT,
    private _zero: ELEMENT,
  ) {
    this._tree = new Array(elements.length * 4);
    this._length = elements.length;
    this._build(elements, 0, 0, this._length - 1);
  }

  private _build(elements: ELEMENT[], v: number, tl: number, tr: number): ELEMENT {
    if (tl === tr) {
      this._tree[v] = elements[tl];
    } else {
      const tm = (tl + tr) >> 1;
      this._tree[v] = this._sum(
        this._build(elements, v * 2 + 1, tl, tm),
        this._build(elements, v * 2 + 2, tm + 1, tr),
      );
    }
    return this._tree[v];
  }

  private _rangeQuery(v: number, tl: number, tr: number, l: number, r: number): ELEMENT {
    if (l > r)
      return this._zero;
    if (l === tl && r === tr)
      return this._tree[v];
    const tm = (tl + tr) >> 1;
    return this._sum(
      this._rangeQuery(v * 2 + 1, tl, tm, l, Math.min(r, tm)),
      this._rangeQuery(v * 2 + 2, tm + 1, tr, Math.max(l, tm + 1), r)
    );
  }

  query(fromIdx: number, toIdx: number): ELEMENT {
    const result = this._rangeQuery(0, 0, this._length - 1, clamp(fromIdx, 0, this._length - 1), clamp(toIdx, 0, this._length - 1));
    // Return a sum with zero to make sure that a new object is returned.
    return this._sum(result, this._zero);
  }
}

function clamp(x: number, from: number, to: number) {
  return Math.min(Math.max(x, from), to);
}
