# W-TinyLFU

In the W-TinyLFU, we can sketch element frequencies, and have another "window"
in front of the SLRU cache.

```ts
window = new LRU(maxSize * 0.01);
slru = new SLRU(maxSize - window.maxSize);
tinylfu = new TinyLFU();

function hit(key) {
  tinylfu.hit(key);
  if (window.has(key))
    return window.hit(key);
  if (slru.has(key))
    return slru.hit(key);

  let wVictim = window.hit(key);
  if (!wVictim)
    return undefined;

  const slruVictim = slru.victim(wVictim);

  if (slruVictim && tinylfu.get(slruVictim) >= tinylfu.get(wVictim))
    return wVictim;
  return slru.hit(wVictim);
}

function victim(newKey) {
  if (!newKey) {
    const wVictim = window.victim(newKey);    
    const slruVictim = slru.victim(wVictim);
    if (!wVictim || !slruVictim)
      return wVictim ?? slruVictim;
    return tinylfu.get(slruVictim) < tinylfu.get(wVictim) ? slruVictim : wVictim;
  }

  const wVictim = window.victim(newKey);
  if (!wVictim)
    return undefined;

  const slruVictim = slru.victim(wVictim);

  if (!slruVictim)
    return undefined;

  const extraSlru = newKey === slruVictim ? 1 : 0;
  const extraW = newKey === wVictim ? 1 : 0;
  return tinylfu.get(slruVictim) + extraSlru >= tinylfu.get(wVictim) + extraW ? wVictim : slruVictim;
}

function has(key) {
  return window.has(key) || slru.has(key);
}

function delete(key) {
  window.delete(key);
  slru.delete(key);
}
```