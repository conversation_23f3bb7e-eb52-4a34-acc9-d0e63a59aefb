import { CachePolicy } from "./cache.js";
import { LRUPolicy } from "./lruPolicy.js";

/**
 * Segmented LRU cache consists of 2 LRU segments: probation and protected.
 * - all new elements go to "probation"
 * - if an element from "probation" segment is accessed, then it is moved to "protected"
 * - if an element from "protected" segment is evicted, then it is moved to "probation"
 * - if an element from "probation" segment is evicted, it is just evicted.
 */
export class SegmentedLRUPolicy<K> implements CachePolicy<K> {
  static createDefault<K>(size: number): CachePolicy<K> {
    if (size < 10)
      return new LRUPolicy<K>(size);
    const probation = Math.round(size * 0.2);
    return new SegmentedLRUPolicy<K>({
      probation,
      protected: size - probation,
    });
  }

  private _probation: LRUPolicy<K>;
  private _protected: LRUPolicy<K>;

  constructor(options: { probation: number, protected: number }) {
    this._probation = new LRUPolicy(options.probation);
    this._protected = new LRUPolicy(options.protected);
  }

  hit(key: K) {
    // if probation has the key, then we drop it from the probation segment,
    // and add it to the protected.
    if (this._probation.has(key)) {
      this._probation.delete(key);
      const victim = this._protected.hit(key);
      // If we have a victim, then return the victim into the probation.
      if (victim)
        this._probation.hit(victim);
      return undefined;
    }
    // if protected has the key, then we just hit it. No evictions should happen.
    if (this._protected.has(key)) {
      this._protected.hit(key);
      return undefined;
    }
    // If neither probation nor protected has the key, then add to probation first.
    return this._probation.hit(key);
  }

  victim(newKey?: K) {
    if (!newKey)
      return this._probation.victim() ?? this._protected.victim();

    if (this.has(newKey))
      return undefined;
    return this._probation.victim(newKey);
  }

  delete(key: K) {
    this._probation.delete(key);
    this._protected.delete(key);
  }

  has(key: K) {
    return this._probation.has(key) || this._protected.has(key);
  }

  clear() {
    this._probation.clear();
    this._protected.clear();
  }

  get size() {
    return this._probation.size + this._protected.size;
  }
}