import { HeapSet } from "../heapSet.js";
import { CachePolicy } from "./cache.js";

/**
 * A simple imlpementation of the LRU cache policy with O(log) time.
 * In theory, we can use the Doubly-Linked list to have a O(1) time,
 * but we're a little lazy.
 */
export class LRUPolicy<K> implements CachePolicy<K> {
  private _access = HeapSet.createMin<K>();

  constructor(private _maxSize: number) { }

  hit(key: K) {
    this._access.add(key, performance.now());
    if (this._access.size > this._maxSize)
      return this._access.pop();
    return undefined;
  }

  get size() {
    return this._access.size;
  }

  victim(newKey?: K) {
    if (!newKey)
      return this._access.peek();

    if (this._access.size < this._maxSize || this._access.has(newKey))
      return undefined;
    return this._access.peek();
  }

  has(key: K) {
    return this._access.has(key);
  }

  delete(key: K) {
    this._access.delete(key);
  }

  clear() {
    this._access.clear();
  }
}
