
/**
 * CachePolicy abstracts away the cache key tracking. This interface is enough to implement all types of caches:
 * LRU, LFU, Segmented LRU, Window-TinyLFU and so on.
 */
export interface CachePolicy<K> {
  /**
   * The method will "record" a usage of `key`.
   * Returns:
   * - `undefined` if the key was silently accepted;
   * - `<PERSON>` if the element was accepted, and another one was removed.
   * - returns `key` is the element WAS NOT accepted.
   */
  hit(key: K): K|undefined;

  /**
   * - If the `newKey` is given, then does a dry-run for the `hit` method and returns
   *   a result, that `hit` would've returned, if called with the `newKey`.
   * - If the `newKey` is not given, returns some element to be evicted; the element is not evicted
   *   per se. This is handy to handle memory pressure situations.
   */
  victim(newKey?: K): K|undefined;

  /**
   * Removes an element from policy.
   */
  delete(key: K): void;

  /**
   * Check if the element is tracked by the policy.
   */
  has(key: K): boolean;

  /**
   * Returns number of elements in the policy. This should always match number of elements in the cache.
   */
  get size(): number;

  /**
   * Clears the policy.
   */
  clear(): void;
}

/**
 * Each cache uses a simple map interface to actually "cache" elements.
 */
export interface CacheStore<K, V> {
  get(key: K): V|undefined;
  set(key: K, value: V): void;
  delete(key: K): void;
  has(key: K): boolean;
  clear(): void;
  get size(): number;
  [Symbol.iterator](): IterableIterator<[K, V]>;
  entries(): IterableIterator<[K, V]>;
  keys(): IterableIterator<K>;
  values(): IterableIterator<V>;
}

type GlobalStore = {
  map: Map<number, { onEvict: (key: any, value: any) => void, key: unknown, value: unknown }>,
  admission: boolean,
  policy: CachePolicy<number>,
  idx: number,
  shouldAdmit: () => boolean,
}

/**
 * Multiple caches can share a single backing store, that supports its own "evict" method.
 * In this case, entries will be evicted across the caches, responding to the memory pressure.
 */
export class SharedCacheStore {
  private _store: GlobalStore;

  constructor(options: {
    policy: CachePolicy<number>,
    shouldAdmit?: () => boolean,
  }) {
    this._store = {
      map: new Map(),
      admission: true,
      policy: options.policy,
      idx: 0,
      shouldAdmit: options.shouldAdmit ?? (() => true),
    };
  }

  get size(): number {
    return this._store.map.size;
  }

  clear() {
    const values = [...this._store.map.values()];
    this._store.map.clear();
    this._store.policy.clear();
    for (const value of values)
      value.onEvict(value.key, value.value);
  }

  evict(): void {
    const victim = this._store.policy.victim();
    if (!victim)
      return;
    this._store.policy.delete(victim);
    const value = this._store.map.get(victim)!;
    this._store.map.delete(victim);
    value.onEvict(value.key, value.value);
  }

  createPartition<K, V>(onEvict: (key: K, value: V) => void): StorePartition<K, V> {
    return new StorePartition(this._store, onEvict);
  }
}

/**
 * Store partition implements a unique partition in the shared store.
 * If shared store drops any elements, then 
 */
class StorePartition<K, V> implements CacheStore<K, V> {
  private _keyToGlobalKey = new Map<K, number>();
  private _onEvict: (key: K, value: V) => void;

  constructor(
    private _store: GlobalStore,
    onEvict: (key: K, value: V) => void,
  ) {
    this._onEvict = (key: K, value: V) => {
      this._keyToGlobalKey.delete(key);
      onEvict(key, value);
    }
  }

  clear() {
    for (const gKey of this._keyToGlobalKey.values()) {
      this._store.map.delete(gKey);
      this._store.policy.delete(gKey);
    }
    this._keyToGlobalKey.clear();
  }

  get(key: K): V|undefined {
    const gKey = this._keyToGlobalKey.get(key);
    if (!gKey)
      return undefined;
    this._store.policy.hit(gKey);
    return this._store.map.get(gKey)?.value as V;
  }

  set(key: K, value: V): void {
    if (!this._store.shouldAdmit()) {
      // If the admission is turned off, then setting a key results in its
      // immediate eviction.
      this._onEvict(key, value);
      return;
    }

    let gKey = this._keyToGlobalKey.get(key);
    if (!gKey) {
      gKey = ++this._store.idx;
      this._keyToGlobalKey.set(key, gKey);
    }
    this._store.map.set(gKey, { onEvict: this._onEvict, key, value });
    const victim = this._store.policy.hit(gKey);
    if (victim) {
      const toBeRemoved = this._store.map.get(victim);
      this._store.map.delete(victim);
      toBeRemoved?.onEvict(toBeRemoved.key, toBeRemoved.value);
    }
  }

  has(key: K): boolean {
    return this._keyToGlobalKey.has(key);
  }

  delete(key: K): void {
    const gKey = this._keyToGlobalKey.get(key);
    if (gKey) {
      this._keyToGlobalKey.delete(key);
      this._store.map.delete(gKey);
      this._store.policy.delete(gKey);
    }
  }

  get size(): number {
    return this._keyToGlobalKey.size;
  }

  [Symbol.iterator](): IterableIterator<[K, V]> {
    return this.entries(); 
  }

  *entries(): IterableIterator<[K, V]> {
    for (const gKey of this._keyToGlobalKey.values()) {
      const value = this._store.map.get(gKey)!;
      yield [value.key as K, value.value as V];
    }
  }

  keys(): IterableIterator<K> {
    return this._keyToGlobalKey.keys();
  }

  *values(): IterableIterator<V> {
    for (const gKey of this._keyToGlobalKey.values()) {
      const value = this._store.map.get(gKey)!;
      yield value.value as V;
    }
  }
}

export class Cache<K, V> {
  private _store: CacheStore<K, V>;

  constructor(
    private _policy: CachePolicy<K>,
    store?: SharedCacheStore,
  ) {
    this._store = store ? store.createPartition<K, V>((key: K) => {
      this._policy.delete(key);
    }) : new Map();
  }

  get(key: K): V|undefined {
    if (!this._policy.has(key))
      return undefined;
    this._policy.hit(key);
    return this._store.get(key);
  }

  set(key: K, value: V): void {
    const victim = this._policy.hit(key);
    if (victim === key)
      return;
    if (victim)
      this._store.delete(victim);
    this._store.set(key, value);
  }

  has(key: K) {
    return this._policy.has(key);
  }

  delete(key: K) {
    this._policy.delete(key);
    this._store.delete(key);
  }

  clear() {
    this._policy.clear();
    this._store.clear();
  }

  get size() {
    return this._store.size;
  }

  [Symbol.iterator](): IterableIterator<[K, V]> {
    return this._store.entries(); 
  }

  entries(): IterableIterator<[K, V]> {
    return this._store.entries();
  }

  keys(): IterableIterator<K> {
    return this._store.keys();
  }

  values(): IterableIterator<V> {
    return this._store.values();
  }
}
