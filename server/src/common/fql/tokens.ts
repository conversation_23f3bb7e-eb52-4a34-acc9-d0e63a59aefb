// Word cannot start with "-" since we will use this as a negation operator.
const WORD_REGEX = /^[\.a-zA-Z0-9_/][/\.a-zA-Z0-9_-]*/;

export class Token {
  static serializeWord(word: string, quote: `'`|`"` = `"`) {
    const wordMatch = word.match(WORD_REGEX);
    if (wordMatch && wordMatch[0] === word)
      return word;
    return quote + word.replaceAll('\\', '\\\\').replaceAll(quote, `\\` + quote) + quote;
  }

  static tokenize(query: string): { tokens: Token[], error?: { position: number, message: string } } {
    let index = 0;
    const eol = () => index >= query.length;

    const tokens: Token[] = [];

    while (!eol()) {
      const next = query[index];
      if (/\s/.test(query[index])) {
        ++index;
        continue;
      }

      const subquery = query.substring(index);
      const wordMatch = subquery.match(WORD_REGEX);

      if (wordMatch) {
        const value = wordMatch[0];
        tokens.push(new Token(query, 'word', value, index, index + value.length));
        index += value.length;
      } else if (next === '"' || next === "'") {
        // Quoted word
        const quote = next;
        const from = index;
        ++index;
        let word = '';
        while (index < query.length && query[index] !== quote) {
          const ch = query[index];
          if (ch !== '\\') {
            word += ch;
            ++index;
            continue;
          }
          const esc = query[++index];
          if (esc === '\\') {
            word += '\\';
            ++index;
            continue;
          } else if (esc === quote) {
            word += quote;
            ++index;
            continue;
          } else {
            return { tokens: [], error: { position: index, message: 'bad escape sequence' } };
          }
        }

        if (query[index] === quote) {
          tokens.push(new Token(query, 'word', word, from, index + 1));
          index += 1;
        } else {
          tokens.push(new Token(query, 'word', word, from, index));
          ++index;
        }
      } else {
        tokens.push(new Token(query, 'punctuation', query[index], index, index + 1));
        ++index;
      }
    }
    return { tokens };
  }

  constructor(
    public source: string,
    public type: 'word' | 'punctuation',
    public value: string,
    public from: number,
    public to: number,
  ) { }

  isEqual(other?: Token) {
    if (!other)
      return false;
    return this.type === other.type && this.value === other.value;
  }

  isPunctuation(value: string) {
    return this.type === 'punctuation' && this.value === value;
  }

  isWord() {
    return this.type === 'word';
  }
}
