import { Multimap } from "@flakiness/shared/common/multimap.js";
import { xxHash } from "@flakiness/shared/common/utils.js";
import { Ranges } from "../ranges.js";
import type { Stats } from "../stats/stats.js";
import { WireTypes } from "../wireTypes.js";
import { Extractor } from "./extractor.js";
import { Matcher, MatchResult } from "./matcher.js";
import { Token } from "./tokens.js";

/**
 * This is the main interface to work with FQL. The Query object is immutable.
 * 
 * To discuss the terminology, consider the following FQL query:
 *    status:passed page-click -$new
 * 
 * This query could be parsed with `Query.parse` method. Internally, the query consists of a bunch of different
 * matchers. Matcher's main job is to match objects: matcher accepts an object, and returns match result for it:
 * MATCH, NO_MATCH or EXCLUDE.
 * 
 * In our sample query, there are 3 matchers:
 * 1. status:passed
 * 2. page-click
 * 3. -$new
 * 
 * Each matcher consists of an Extractor, operator, set of values, and possible flags. In our sample query:
 * 1. `s:passed` matcher consists of "status" extractor, "substring" operator and "passed" as a value
 * 2. `page-click` matcher consists of "test" extractor (implied by default), "substring" operator (implied by default)
 *    and "page-click" as a test
 * 3. `-$new` matcher consists of "error" extractor (implied by "$" char), "substring" operator, "new" text and also
 *    exclusion flag "-".
 */
export type QueryParseOptions = {
}

export class Query {
  static EMPTY = Query.parse('');

  static create(options: {
    testId?: Stats.TestId,
  }): Query {
    let fql = Query.parse('');
    if (options.testId)
      fql = fql.toggleFilter(Matcher.TEST_ID_EQ.create([options.testId]));
    return fql;
  }

  static parse(query: string, options: QueryParseOptions = {}, unparsedTokens: Token[] = []): Query {
    const {
    } = options;
    const testMatchers: Matcher<WireTypes.Test, string>[] = [];
    const statusMatchers: Matcher<WireTypes.Outcome, string>[] = [];
    const errorMatchers: Matcher<WireTypes.TestError, string>[] = [];
    const tagMatchers: Matcher<string, string>[] = [];
    const annotationMatchers: Matcher<WireTypes.Annotation, string>[] = [];
    const lineMatchers: Matcher<number, number>[] = [];
    const durationMatchers: Matcher<number, number>[] = [];
    const { tokens } = Token.tokenize(query);
    let tokenIndex = 0;
    while (tokenIndex < tokens.length) {
      const result = Matcher.parse(tokens, tokenIndex);
      if (result) {
        if (result.matcher.extractor.type === 'error')
          errorMatchers.push(result.matcher);
        else if (result.matcher.extractor.type === 'test')
          testMatchers.push(result.matcher);
        else if (result.matcher.extractor.type === 'status')
          statusMatchers.push(result.matcher);
        else if (result.matcher.extractor.type === 'tag')
          tagMatchers.push(result.matcher);
        else if (result.matcher.extractor.type === 'annotation')
          annotationMatchers.push(result.matcher);
        else if (result.matcher.extractor.type === 'testline')
          lineMatchers.push(result.matcher);
        else if (result.matcher.extractor.type === 'testduration')
          durationMatchers.push(result.matcher);
        tokenIndex = result.newTokenIndex;
        continue;
      }
      // If we failed to parse anything, then mark this token as "unparsed"
      unparsedTokens.push(tokens[tokenIndex++]);
    }
    return new Query(false, testMatchers, statusMatchers, errorMatchers, tagMatchers, lineMatchers, annotationMatchers, durationMatchers);
  }

  static parseMatchers(query: string, options?: QueryParseOptions): { matchers: Matcher<any, any>[], unparsed: Token[] } {
    const unparsedTokens: Token[] = [];
    const q = Query.parse(query, options, unparsedTokens);
    return {
      matchers: q._allMatchers,
      unparsed: unparsedTokens,
    };
  }

  private _memoizedOutcomes = new Map<WireTypes.Outcome, MatchResult>();
  private _memoizedTests = new Map<Stats.TestId, MatchResult>();
  private _memoizedTags = new Map<string, MatchResult>();
  private _memoizedErrors = new Map<Stats.ErrorId, MatchResult>();
  private _memoizedLines = new Map<number, MatchResult>();
  private _memoizedDurations = new Map<number, MatchResult>();
  private _memoizedAnnotations = new Map<Stats.AnnotationId, MatchResult>();

  private _memoizedTestsHighlight = new Map<Stats.TestId, Map<string, Ranges.Ranges<number>>>();
  private _memoizedTagHighlight = new Map<string, Ranges.Ranges<number>>();
  private _memoizedErrorsHighlight = new Map<Stats.ErrorId, Map<string, Ranges.Ranges<number>>>();
  private _memoizedAnnotationHighlight = new Map<Stats.AnnotationId, Map<string, Ranges.Ranges<number>>>();

  private _allMatchers: Matcher<any, any>[];
  private _normalized: Query;
  private _etag: string;

  constructor(
    private _isNormalized: boolean,
    private readonly _testMatchers: Matcher<WireTypes.Test, string>[],
    private readonly _statusMatchers: Matcher<WireTypes.Outcome, string>[],
    private readonly _errorMatchers: Matcher<WireTypes.TestError, string>[],
    private readonly _tagMatchers: Matcher<string, string>[],
    private readonly _lineMatchers: Matcher<number, number>[],
    private readonly _annotationMatchers: Matcher<WireTypes.Annotation, string>[],
    private readonly _durationMatchers: Matcher<number, number>[],
  ) {
    this._allMatchers = [
      this._testMatchers,
      this._statusMatchers,
      this._errorMatchers,
      this._tagMatchers,
      this._lineMatchers,
      this._annotationMatchers,
      this._durationMatchers,
    ].flat().sort((a, b) => Matcher.compare(a as any, b as any));
    // Consider the query "status:passed status:failed"
    // 
    // Even though this is technically an "And" condition, we treat it as an "OR" condition.
    // Query normalization will convert this to "status:(passed,failed)" with an "OR" condition.
    // And this is waht we will use for all evaluations.
    this._normalized = this._isNormalized ? this : this.normalize();
    this._etag = xxHash(this._normalized.serialize());
  }

  etag() {
    return this._etag;
  }

  isEmpty() {
    return this._allMatchers.length === 0;
  }

  hasErrorFilters(): boolean {
    return this._errorMatchers.length > 0;
  }

  hasStatusFilters(): boolean {
    return this._statusMatchers.length > 0;
  }

  hasTestFilters(): boolean {
    return this._testMatchers.length > 0;
  }

  normalize(): Query {
    return new Query(
      true,
      normalizeMatchers(this._testMatchers),
      normalizeMatchers(this._statusMatchers),
      normalizeMatchers(this._errorMatchers),
      normalizeMatchers(this._tagMatchers),
      normalizeMatchers(this._lineMatchers),
      normalizeMatchers(this._annotationMatchers),
      normalizeMatchers(this._durationMatchers),
    );
  }

  mapQuery(mapper: <T, K>(f: Matcher<T, K>) => Matcher<T, K> | undefined): Query {
    const removeUndefines = <S>(value: S | undefined): value is S => value !== undefined;
    return new Query(
      false,
      this._testMatchers.map(mapper).filter(removeUndefines),
      this._statusMatchers.map(mapper).filter(removeUndefines),
      this._errorMatchers.map(mapper).filter(removeUndefines),
      this._tagMatchers.map(mapper).filter(removeUndefines),
      this._lineMatchers.map(mapper).filter(removeUndefines),
      this._annotationMatchers.map(mapper).filter(removeUndefines),
      this._durationMatchers.map(mapper).filter(removeUndefines),
    );
  }

  toggleFilter<KK, VV>(toggle: Matcher<KK, VV>): Query {
    const allValues = new Set(toggle.values);
    const toBeAdded = new Set(allValues);
    function mapper(aa: Matcher<any, any>): Matcher<any, any> | undefined {
      if (aa.extractor !== toggle.extractor)
        return aa;
      if (!aa.op.isEqual(toggle.op))
        return aa;
      // equal matcher and extractor imply the same type.
      const a: Matcher<KK, VV> = aa;
      // Find all indexes that should be removed.
      const indexesToFilterOut = new Set<number>();
      for (let i = 0; i < a.values.length; ++i) {
        toBeAdded.delete(a.values[i]);
        if (allValues.has(a.values[i]))
          indexesToFilterOut.add(i);
      }
      const values = a.values.filter((value, index) => !indexesToFilterOut.has(index));
      if (!values.length)
        return undefined;
      return new Matcher<KK, VV>(a.prefix, a.primitive, a.extractor, a.op, values, a.exclusion, a.sourceInfo ? {
        ...a.sourceInfo,
        valuesTokens: a.sourceInfo.valuesTokens.filter((t, index) => !indexesToFilterOut.has(index)),
      } : undefined);
    }
    let q = this.mapQuery(mapper).serialize();
    if (toBeAdded.size) {
      const leftover = new Matcher(toggle.prefix, toggle.primitive, toggle.extractor, toggle.op, toBeAdded, toggle.exclusion);
      if (q.length)
        q += ' ';
      q += leftover.serialize();
    }
    return Query.parse(q);
  }

  clearMatchersWithExtractor(extractor: Extractor<any, any>): Query {
    return this.mapQuery(e => e.extractor === extractor ? undefined : e);
  }

  matchers() {
    return this._allMatchers.map(matcher => matcher.clone());
  }
  
  clearStatusFilters(): Query {
    return this.mapQuery(e => e.extractor.type === 'status' ? undefined : e);
  }

  clearErrorFilters(): Query {
    return this.mapQuery(e => e.extractor.type === 'error' ? undefined : e);
  }

  clearTestIdFilters(): Query {
    return this.mapQuery(e => (e.extractor as any) === Extractor.TEST_ID ? undefined : e);
  }

  clearTestFilters(): Query {
    return this.mapQuery(e => e.extractor.type === 'test' ? undefined : e);
  }

  toggleQuery(query: Query): Query {
    let result: Query = this;
    for (const f of query._allMatchers)
      result = result.toggleFilter(f);
    return result;
  }

  isMatchingFilter(matcher: Matcher<any, any>): boolean {
    const valuesToFind = new Set(matcher.values.map(v => v.toLowerCase()));
    for (const f of this._allMatchers) {
      if (f.extractor !== matcher.extractor || !f.op.isEqual(matcher.op))
        continue;
      for (const value of f.values)
        valuesToFind.delete(value.toLowerCase());
    }
    return valuesToFind.size === 0;
  }

  private _memoizedSerialize?: string;

  serialize(): string {
    if (!this._memoizedSerialize) {
      this._memoizedSerialize = this._allMatchers
        .map(matcher => matcher.serialize())
        .join(' ');
    }
    return this._memoizedSerialize;
  }

  acceptsOutcome(outcome: WireTypes.Outcome): boolean {
    return this.matchesOutcome(outcome) === MatchResult.MATCH;
  }

  matchesOutcome(outcome: WireTypes.Outcome): MatchResult {
    let result = this._memoizedOutcomes.get(outcome);
    if (result === undefined) {
      result = MatchResult.MATCH;
      for (const matcher of this._normalized._statusMatchers)
        result = MatchResult.and(result, matcher.match(outcome));
      this._memoizedOutcomes.set(outcome, result);
    }
    return result;
  }

  // line numbers

  hasLineFilters(): boolean {
    return this._lineMatchers.length > 0;
  }

  acceptsLine(line: number): boolean {
    return this.matchesLine(line) === MatchResult.MATCH;
  }

  matchesLine(line: number|null): MatchResult {
    if (line === null)
      return this._normalized._lineMatchers.every(f => f.exclusion) ? MatchResult.MATCH : MatchResult.NO_MATCH;

    let result = this._memoizedLines.get(line);
    if (result === undefined) {
      result = MatchResult.MATCH;
      for (const filter of this._normalized._lineMatchers)
        result = MatchResult.and(result, filter.match(line));
      this._memoizedLines.set(line, result);
    }
    return result;
  }

  // durations

  hasDurationFilters(): boolean {
    return this._durationMatchers.length > 0;
  }

  acceptsDuration(durationMs: number): boolean {
    return this.matchesDuration(durationMs) === MatchResult.MATCH;
  }

  matchesDuration(durationMs: number): MatchResult {
    let result = this._memoizedDurations.get(durationMs);
    if (result === undefined) {
      result = MatchResult.MATCH;
      for (const filter of this._normalized._durationMatchers)
        result = MatchResult.and(result, filter.match(durationMs));
      this._memoizedDurations.set(durationMs, result);
    }
    return result;
  }

  // tags

  hasTagFilters(): boolean {
    return this._tagMatchers.length > 0;
  }

  acceptsTag(tag: string): boolean {
    return this.matchesTag(tag) === MatchResult.MATCH;
  }

  matchesTag(tag: string|null): MatchResult {
    if (!tag)
      return this._normalized._tagMatchers.every(f => f.exclusion) ? MatchResult.MATCH : MatchResult.NO_MATCH;

    let result = this._memoizedTags.get(tag);
    if (result === undefined) {
      result = MatchResult.MATCH;
      for (const filter of this._normalized._tagMatchers)
        result = MatchResult.and(result, filter.match(tag));
      this._memoizedTags.set(tag, result);
    }
    return result;
  }

  highlightTag(tag: string): Ranges.Ranges<number> {
    let result = this._memoizedTagHighlight.get(tag);
    if (!result) {
      const hl = new Multimap<string, [number, number]>();
      for (const filter of this._normalized._tagMatchers)
        filter.highlight(tag, hl);
      result = Ranges.unionAll(hl.getAll(tag) as Iterable<Ranges.Ranges<number>>);
      this._memoizedTagHighlight.set(tag, result);
    }
    return result;
  }


  // Annotations
  hasAnnotationFilters(): boolean {
    return this._annotationMatchers.length > 0;
  }

  acceptsAnnotation(annotation: WireTypes.Annotation): boolean {
    return this.matchesAnnotation(annotation) === MatchResult.MATCH;
  }

  matchesAnnotation(annotation: WireTypes.Annotation|null): MatchResult {
    if (!annotation) {
      return this._normalized._annotationMatchers.every(f => f.exclusion) ? MatchResult.MATCH : MatchResult.NO_MATCH;
    }

    let result = this._memoizedAnnotations.get(annotation.annotationId);
    if (result === undefined) {
      result = MatchResult.MATCH;
      for (const filter of this._normalized._annotationMatchers)
        result = MatchResult.and(result, filter.match(annotation));
      this._memoizedAnnotations.set(annotation.annotationId, result);
    }
    return result;
  }

  highlightAnnotation(annotation: WireTypes.Annotation): Map<string, Ranges.Ranges<number>> {
    let result = this._memoizedAnnotationHighlight.get(annotation.annotationId);
    if (!result) {
      const hl = new Multimap<string, [number, number]>();
      for (const filter of this._normalized._annotationMatchers)
        filter.highlight(annotation, hl);
      result = new Map<string, Ranges.Ranges<number>>();
      for (const [key, values] of hl)
        result.set(key, Ranges.unionAll(values as Iterable<Ranges.Ranges<number>>));
      this._memoizedAnnotationHighlight.set(annotation.annotationId, result);
    }
    return result;
  }

  // tests

  acceptsTest(test: WireTypes.Test): boolean {
    return this.matchesTest(test) === MatchResult.MATCH;
  }

  matchesTest(test: WireTypes.Test): MatchResult {
    let result = this._memoizedTests.get(test.testId);
    if (result === undefined) {
      result = MatchResult.MATCH;
      for (const filter of this._normalized._testMatchers)
        result = MatchResult.and(result, filter.match(test));
      this._memoizedTests.set(test.testId, result);
    }
    return result;
  }

  highlightTest(test: WireTypes.Test): Map<string, Ranges.Ranges<number>> {
    let result = this._memoizedTestsHighlight.get(test.testId);
    if (!result) {
      const hl = new Multimap<string, [number, number]>();
      for (const filter of this._normalized._testMatchers)
        filter.highlight(test, hl);
      result = new Map<string, Ranges.Ranges<number>>();
      for (const [key, values] of hl)
        result.set(key, Ranges.unionAll(values as Iterable<Ranges.Ranges<number>>));
      this._memoizedTestsHighlight.set(test.testId, result);
    }
    return result;
  }

  acceptsError(error: WireTypes.TestError): boolean {
    return this.matchesError(error) === MatchResult.MATCH;
  }

  matchesError(error: WireTypes.TestError|null): MatchResult {
    if (!error) {
      return this._normalized._errorMatchers.every(f => f.exclusion) ? MatchResult.MATCH : MatchResult.NO_MATCH;
    }
    let result = this._memoizedErrors.get(error.errorId);
    if (result === undefined) {
      result = MatchResult.MATCH;
      for (const filter of this._normalized._errorMatchers)
        result = MatchResult.and(result, filter.match(error));
      this._memoizedErrors.set(error.errorId, result);
    }
    return result;
  }

  highlightError(error: WireTypes.TestError): Map<string, Ranges.Ranges<number>> {
    let result = this._memoizedErrorsHighlight.get(error.errorId);
    if (!result) {
      const hl = new Multimap<string, [number, number]>();
      for (const filter of this._normalized._errorMatchers)
        filter.highlight(error, hl);
      result = new Map<string, Ranges.Ranges<number>>();
      for (const [key, values] of hl)
        result.set(key, Ranges.unionAll(values as Iterable<Ranges.Ranges<number>>));
      this._memoizedErrorsHighlight.set(error.errorId, result);
    }
    return result;
  }
}

function normalizeMatchers<K, V>(matchers: Matcher<K, V>[]): Matcher<K, V>[] {
  return matchers.map(m => m.normalize()).sort((m1, m2) => Matcher.compare(m1, m2));
}
