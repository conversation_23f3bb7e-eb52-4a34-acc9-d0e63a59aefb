import { WireTypes } from "../wireTypes.js";

export type ExtractorType = 'test' | 'error' | 'status' | 'tag' | 'testline' | 'annotation' | 'testduration';

export class Extractor<FROM, TO> {
  static TAG = new Extractor<string, string>('tag', tag => [tag]);
  static TEST_TEXT_PARTS = new Extractor<WireTypes.Test, string>('test', test => [test.filePath, ...test.titles]);
  static TEST_FILE = new Extractor<WireTypes.Test, string>('test', test => [test.filePath]);
  static TEST_ID = new Extractor<WireTypes.Test, string>('test', test => [test.testId]);
  static TEST_LINE = new Extractor<number, number>('testline', lineNumber => [lineNumber]);
  static TEST_DURATION = new Extractor<number, number>('testduration', durationMs => [durationMs]);
  static STATUS = new Extractor<WireTypes.Outcome, string>('status', e => [outcomeToStatusFilter(e)]);
  static ERROR_TEXT = new Extractor<WireTypes.TestError, string>('error', error => [error.message, error.value].filter(x => x !== undefined));
  static ANNOTATION_PARTS = new Extractor<WireTypes.Annotation, string>('annotation', annotation => [annotation.type, annotation.description].filter(x => x !== undefined));

  constructor(
    readonly type: ExtractorType,
    readonly extract: (element: FROM) => TO[]) {
  }
}

export function outcomeToStatusFilter(outcome: WireTypes.Outcome): string {
  if (outcome === 'regressed')
    return 'fire';
  if (outcome === 'unexpected')
    return 'failed';
  if (outcome === 'flaked')
    return 'flaked';
  if (outcome === 'expected')
    return 'passed';
  if (outcome === 'skipped')
    return 'skipped';
  throw new Error(`Unknown outcome - "${outcome}"`)
}