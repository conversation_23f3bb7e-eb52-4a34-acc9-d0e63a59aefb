import { ManualPromise } from "@flakiness/shared/common/manualPromise.js";
import { createAbortPromise } from "@flakiness/shared/common/utils.js";

export class Limiter {
  private _wait?: ManualPromise<void>;
  private _activeSlots: number = 0;
  private _pendingSlots: number = 0;

  constructor(private _concurrency: number) {
  }

  private _releaseSlot() {
    --this._activeSlots;
    if (this._activeSlots < this._concurrency) {
      this._wait?.resolve();
      this._wait = undefined;
    }
  }

  async acquireSlot(signal?: AbortSignal) {
    using abortPromise = signal ? createAbortPromise(signal) : undefined;
    while (this._activeSlots >= this._concurrency && !signal?.aborted) {
      ++this._pendingSlots;
      this._wait ??= new ManualPromise<void>();
      // Either race against abortPromise or await the waiter.
      // Note: we CANNOT race against `undefined` since it resolves immediately.
      abortPromise ?
        await Promise.race([
          abortPromise.promise,
          this._wait.promise,
        ]) :
        await this._wait.promise
      ;
      --this._pendingSlots;
    }
    signal?.throwIfAborted();
    ++this._activeSlots;
    return {
      [Symbol.dispose]: () => this._releaseSlot(),
    }
  }

  activeSlots() {
    return this._activeSlots;
  }

  pendingSlots() {
    return this._pendingSlots;
  }
}

// Usage example:
// const limiter = new Limiter(3); // Allow max 3 concurrent operations
// 
// async function doWork(id: number, signal?: AbortSignal) {
//   try {
//     using slot = await limiter.acquireSlot(signal);
//     console.log(`Starting work ${id}`);
//     await new Promise(resolve => setTimeout(resolve, 1000));
//     console.log(`Finished work ${id}`);
//     // slot is automatically disposed here
//   } catch (error) {
//     if (error.name === 'AbortError') {
//       console.log(`Work ${id} was aborted`);
//     } else {
//       throw error;
//     }
//   }
// }
//
// // This will run max 3 operations concurrently
// Promise.all([
//   doWork(1), doWork(2), doWork(3), doWork(4), doWork(5)
// ]);
//
// // Example with abort signal:
// const controller = new AbortController();
// setTimeout(() => controller.abort(), 2000); // Abort after 2 seconds
// doWork(6, controller.signal);
