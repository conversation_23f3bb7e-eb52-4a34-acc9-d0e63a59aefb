
function assert(value: unknown, message?: string): asserts value {
  if (!value)
    throw new Error(`Assertion failed ${message ?? ''}`.trim())
}

export function bytes(format: string): number {
  format = format.toLowerCase().trim();
  assert(format);
  const match = format.match(/^(\d+)\s*([a-z]*)$/);
  assert(match);
  const value = parseInt(match[1], 10);
  assert(!isNaN(value));
  const unit = match[2];
  if (!unit)
    return value;

  switch (unit) {
    case 'b':
    case 'byte':
    case 'bytes':
    case '':
      return value;
    case 'kb':
    case 'kilobyte':
    case 'kilobytes':
      return value * 1024;
    case 'mb':
    case 'megabyte':
    case 'megabytes':
      return value * 1024 * 1024;
    case 'gb':
    case 'gigabyte':
    case 'gigabytes':
      return value * 1024 * 1024 * 1024;
    case 'tb':
    case 'terabyte':
    case 'terabytes':
      return value * 1024 * 1024 * 1024 * 1024;
    case 'pb':
    case 'petabyte':
    case 'petabytes':
      return value * 1024 * 1024 * 1024 * 1024 * 1024;
    default:
      throw new Error(`Unknown unit: ${unit}`);
  }
}
