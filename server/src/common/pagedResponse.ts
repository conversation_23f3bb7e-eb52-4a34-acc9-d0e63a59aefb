import { WireTypes } from "./wireTypes.js";

export type PageOptions = {
  size: number,
  number: number,
}

export function pagedResponse<T>(elements: T[], pageOptions: PageOptions): WireTypes.PagedResponse<T> {
  let pageSize = pageOptions.size;
  let pageNumber = pageOptions.number;
  const totalPages = Math.ceil(elements.length / pageSize);
  if (-totalPages < pageNumber && pageNumber < totalPages)
    pageNumber = mod(pageNumber, totalPages);
  const pagePosition = pageNumber * pageSize;
  const pageElements = elements.slice(pagePosition, pagePosition + pageSize);

  return {
    elements: pageElements,
    pageNumber,
    pageSize,
    totalPages,
    totalElements: elements.length,
  };
}

// Support negative numbers.
function mod(n: number, m: number) {
  return ((n % m) + m) % m;
}

export class Sorter<EL, AXIS extends string> {
  private _axisName?: AXIS;
  private _direction?: WireTypes.SortDirection;

  constructor(private _elements: EL[], private _comparators: Record<AXIS, (a: EL, b: EL) => number>) {

  }

  all() {
    return this._elements;
  }

  ensureSorted(axisName?: AXIS, direction?: WireTypes.SortDirection) {
    if (!axisName)
      return;
    if (axisName === this._axisName && direction === this._direction)
      return;
    this._axisName = axisName;
    this._direction = direction;
    const comparator = this._comparators[axisName];
    const sign = direction === 'asc' ? 1 : -1;
    this._elements.sort((a, b) => comparator(a, b) * sign);
  }

  page(pageOptions: PageOptions, axisName?: AXIS, direction?: WireTypes.SortDirection) {
    this.ensureSorted(axisName, direction);
    return pagedResponse(this._elements, pageOptions);
  }
}