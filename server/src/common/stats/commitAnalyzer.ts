import { FlakinessReport as FK } from '@flakiness/report';
import { Multimap } from '@flakiness/shared/common/multimap.js';
import { Brand, xxHashObject } from '@flakiness/shared/common/utils.js';
import { SyncComputationCache } from '../computationCache.js';
import { Ranges } from '../ranges.js';
import { Timeline } from '../timeline/timeline.js';
import { WireTypes } from '../wireTypes.js';
import { Histogram as H } from './histogram.js';
import { Stats as S } from './stats.js';
import { StatsAnnotationProfile } from './statsAnalyzer.js';
import { TestOutcomes as T } from './testOutcomes.js';

export type TimelineCounters = {
  outcomes: T.TestOutcomes,
  errors: {
    error: WireTypes.TestError,
    impactedTimelines: number,
    impactedTests: number,
  }[],
  annotations: {
    annotation: WireTypes.Annotation,
    annotatedTests: number,
    annotatedTimelines: number,
  }[],
  tags: {
    tag: string,
    impactedTimelines: number,
    impactedTests: number,
  }[],
}

export type RunReport = {
  runId: S.RunId,
  duration: FK.DurationMS,
  startTimestamp: FK.UnixTimestampMS,
  url: string,
  outcomes: T.TestOutcomes,
  testsWithImages: T.TestRanges,
  testsWithTraces: T.TestRanges,
  testsWithVideos: T.TestRanges,
}

export type Contribution = {
  outcomes: T.TestOutcomes,
  jsonRun: S.JSONRun,
  jsonAttempt: S.JSONAttempt,
  commitAnalyzer: CommitAnalyzer,
}

export class CommitAnalyzer {
  static runEnvironments(commits: CommitAnalyzer[]): WireTypes.RunEnvironment[] {
    const result = new Map<S.EnvId, WireTypes.RunEnvironment>();
    for (const c of commits)
      c.runEnvironments(result);
    return Array.from(result.values());
  }

  static runEnvironmentsForTest(commits: CommitAnalyzer[], testIdx: S.TestIndex): WireTypes.RunEnvironment[] {
    const result = new Map<S.EnvId, WireTypes.RunEnvironment>();
    for (const c of commits)
      c.runEnvironmentsForTest(result, testIdx);
    return Array.from(result.values());
  }

  static regressify(contribs: ContributionReport, unhealthyTests: T.TestRanges): ContributionReport {
    return contribs.map(contrib => {
      const contribOutcomes = T.regressify(contrib.outcomes, unhealthyTests);
      return {
        commitAnalyzer: contrib.commitAnalyzer,
        jsonAttempt: contrib.jsonAttempt,
        jsonRun: contrib.jsonRun,
        outcomes: contribOutcomes,
      } satisfies Contribution;
    }).filter(c => !T.isEmptyOutcomes(c.outcomes)) as ContributionReport;
  }

  static computeDurationBuckets(timeline: Timeline, contribs: ContributionReport): H.Histogram[] {
    const histograms: H.Histogram<number, S.TestIndex>[] = [];
    for (const contrib of contribs) {
      const contribTests = T.flatten(contrib.outcomes);
      if (!contribTests.length)
        continue;
      const fc = contrib.commitAnalyzer;
      for (const jsonAttempt of contrib.jsonRun.attempts) {
        if (!timeline.acceptsEnvironment(fc._envs[jsonAttempt.environmentIdx]))
          continue;

        const h: H.Histogram<number, S.TestIndex> = jsonAttempt.durationBuckets.map((duration, bucketIdx) => [duration, fc._decompress(jsonAttempt.durationBucketTests[bucketIdx])]);
        histograms.push(H.intersect(h, contribTests));
      }
    }
    return histograms.filter(h => !!h.length);
  }

  static computeErrorTests(contribs: ContributionReport): Map<WireTypes.TestError, T.TestRanges> {
    const errors = new Map<S.ErrorId, WireTypes.TestError>();
    const errorTests = new Multimap<S.ErrorId, T.TestRanges>();
    for (const contrib of contribs) {
      // TODO: we seem to be computing this over and over again.
      const contribTests = T.flatten(contrib.outcomes);
      if (!contribTests.length)
        continue;
      const fc = contrib.commitAnalyzer;
      for (const { errorIdx, tests } of contrib.jsonAttempt.errors ?? []) {
        const error = fc._errs[errorIdx];
        const errTests = Ranges.intersect(contribTests, fc._decompress(tests));
        if (errTests.length) {
          errors.set(error.errorId, error);
          errorTests.set(error.errorId, errTests);
        }
      }
    }
    return new Map(errorTests.map((errorId, manyTests) => [errors.get(errorId)!, Ranges.unionAll(manyTests)]));
  }

  static computeAnnotationTests(contribs: ContributionReport): Map<WireTypes.Annotation, T.TestRanges> {
    const annotations = new Map<S.AnnotationId, WireTypes.Annotation>();
    const annotationTests = new Multimap<S.AnnotationId, T.TestRanges>();
    for (const contrib of contribs) {
      const contribTests = T.flatten(contrib.outcomes);
      if (!contribTests.length)
        continue;
      const fc = contrib.commitAnalyzer;
      for (const { annotationIdx, tests } of fc._annotationProfiles[contrib.jsonAttempt.annotationsProfileIdx] ?? []) {
        const annotation = fc._annotations[annotationIdx];
        const t = Ranges.intersect(contribTests, tests);
        if (t.length) {
          annotations.set(annotation.annotationId, annotation);
          annotationTests.set(annotation.annotationId, t);
        }
      }
    }
    return new Map(annotationTests.map((annotationId, manyTests) => [annotations.get(annotationId)!, Ranges.unionAll(manyTests)]));
  }

  static computeTagTests(contribs: ContributionReport): Map<string, T.TestRanges> {
    const tags = new Multimap<string, T.TestRanges>();
    for (const contrib of contribs) {
      const contribTests = T.flatten(contrib.outcomes);
      if (!contribTests.length)
        continue;

      const fc = contrib.commitAnalyzer;
      for (const { tag, tests } of contrib.commitAnalyzer._tags) {
        const tagTests = Ranges.intersect(contribTests, tests);
        if (tagTests.length)
          tags.set(tag, tagTests);
      }
    }
    return new Map(tags.map((tag, manyTests) => [tag, Ranges.unionAll(manyTests)]));
  }

  static computeLineNumbers(contribs: ContributionReport): Int32Array {
    const maxTestIdx = Math.max(0, ...contribs.map(contrib => contrib.commitAnalyzer.lineNumbers.length));
    const lineNumbers = new Int32Array(maxTestIdx);
    for (const contrib of contribs) {
      const contribTests = T.flatten(contrib.outcomes);
      if (!contribTests.length)
        continue;

      const fc = contrib.commitAnalyzer;
      for (const testIdx of Ranges.iterate(contribTests))
        lineNumbers[testIdx] = fc.lineNumbers[testIdx];
    }
    return lineNumbers;
  }

  private _runIdToJSONRun = new Map<S.RunId, S.JSONRun>();

  private _runRawContributions = new Map<S.RunId, Contribution[]>();

  private _runContributionReport = new SyncComputationCache<{
    runId: S.RunId,
    timeline: Timeline,
  }, ContributionReport>({
    size: Timeline.MAX_TIMELINES,
    etag: ({ runId, timeline }) => xxHashObject({ runId, timeline: timeline.etag() }),
    compute: ({ runId, timeline }) => {
      const allContributions = (this._runRawContributions.get(runId) ?? [])
          .filter(c => timeline.acceptsEnvironment(this._envs[c.jsonAttempt.environmentIdx]));
      return createRunContributionReport(allContributions.toReversed());
    }
  });

  private _commitContributionReport = new SyncComputationCache<Timeline, ContributionReport>({
    size: Timeline.MAX_TIMELINES,
    etag: timeline => timeline.etag(),
    compute: timeline => {
      const contribs = [];
      for (const contributions of this._runRawContributions.values()) {
        for (const c of contributions) {
          if (timeline.acceptsEnvironment(this._envs[c.jsonAttempt.environmentIdx]))
            contribs.push(c);
        }
      }
      return createRunContributionReport(contribs.toReversed());
    }
  });

  private _testOutcomes = new SyncComputationCache<Timeline, T.TestOutcomes>({
    size: Timeline.MAX_TIMELINES,
    etag: timeline => timeline.etag(),
    compute: timeline => {
      return T.unionAll(this._commitContributionReport.get(timeline).map(c => c.outcomes));
    }
  });

  private _durations = new SyncComputationCache<Timeline, H.Histogram[]>({
    size: Timeline.MAX_TIMELINES,
    etag: timeline => timeline.etag(),
    compute: timeline => {
      const contribs = this._commitContributionReport.get(timeline);
      return CommitAnalyzer.computeDurationBuckets(timeline, contribs);
    }
  });

  private _runReports = new SyncComputationCache<Timeline, RunReport[]>({
    size: Timeline.MAX_TIMELINES,
    etag: timeline => timeline.etag(),
    compute: timeline => {
      const runReports: RunReport[] = [];
      for (const [runId, jsonRun] of this._runIdToJSONRun) {
        let testsWithImages: T.TestRanges[] = [];
        let testsWithTraces: T.TestRanges[] = [];
        let testsWithVideos: T.TestRanges[] = [];
        const outcomes = T.newOutcomes();
        const contribs = this._runContributionReport.get({ runId, timeline });
        for (let contrib of contribs) {
          const contribTests = T.flatten(contrib.outcomes);
          if (!contribTests.length)
            continue;
          T.unionInplace(outcomes, contrib.outcomes);
          if (contrib.jsonAttempt.testsWithImage)
            testsWithImages.push(Ranges.intersect(contribTests, this._decompress(contrib.jsonAttempt.testsWithImage)));
          if (contrib.jsonAttempt.testsWithVideo)
            testsWithVideos.push(Ranges.intersect(contribTests, this._decompress(contrib.jsonAttempt.testsWithVideo)));
          if (contrib.jsonAttempt.testsWithTrace)
            testsWithTraces.push(Ranges.intersect(contribTests, this._decompress(contrib.jsonAttempt.testsWithTrace)));
        }
        if (T.isEmptyOutcomes(outcomes))
          continue;
        runReports.push({
          runId: runId,
          duration: jsonRun.duration,
          startTimestamp: jsonRun.startTimestamp,
          url: jsonRun.url,
          outcomes: outcomes,
          testsWithImages: Ranges.unionAll(testsWithImages),
          testsWithTraces: Ranges.unionAll(testsWithTraces),
          testsWithVideos: Ranges.unionAll(testsWithVideos),
        });
      }
      return runReports;
    }
  });

  private _tags: { tag: string, tests: T.TestRanges }[] = [];
  public readonly lineNumbers: Int32Array;

  private _etag: string;

  static createEmpty(commitId: S.CommitId) {
    return new CommitAnalyzer(commitId, [], [], [], [], [], c => Ranges.decompress(c));
  }

  static computeEtag(commitId: S.CommitId, json?: S.JSONCommit) {
    return xxHashObject({
      commitId,
      runs: (json?.runs ?? []).map(run => run.runId),
    });
  }

  constructor(
    public readonly commitId: S.CommitId,
    private _envs: WireTypes.RunEnvironment[],
    private _errs: WireTypes.TestError[],
    private _annotationProfiles: StatsAnnotationProfile[],
    private _annotations: WireTypes.Annotation[],
    lineNumbers: number[],
    private _decompress: (c: Ranges.CompressedRanges<S.TestIndex>) => T.TestRanges,
    private _jsonCommit?: S.JSONCommit,
  ) {
    this._etag = CommitAnalyzer.computeEtag(this.commitId, this._jsonCommit);

    for (const run of this._jsonCommit?.runs ?? []) {
      const contributions: Contribution[] = run.attempts.map(attempt => ({
        commitAnalyzer: this,
        outcomes: {
          regressed: Ranges.EMPTY as T.TestRanges,
          unexpected: attempt.unexpectedTests ? this._decompress(attempt.unexpectedTests) : T.EMPTY_TESTS,
          expected: attempt.expectedTests ? this._decompress(attempt.expectedTests) : T.EMPTY_TESTS,
          skipped: attempt.skippedTests ? this._decompress(attempt.skippedTests) : T.EMPTY_TESTS,
          flaked: attempt.flakedTests ? this._decompress(attempt.flakedTests) : T.EMPTY_TESTS,
        },
        jsonRun: run,
        jsonAttempt: attempt,
      }));
      this._runIdToJSONRun.set(run.runId, run);
      this._runRawContributions.set(run.runId, contributions);
    }

    this._tags = (this._jsonCommit?.tags ?? []).map(({ tag, tests }) => ({
      tag,
      tests: this._decompress(tests),
    }));

    this.lineNumbers = new Int32Array(lineNumbers);

    for (const { lineDelta, affectedTests } of (this._jsonCommit?.changedLineNumbers ?? [])) {
      for (const testIdx of Ranges.iterate(this._decompress(affectedTests)))
        this.lineNumbers[testIdx] += lineDelta;
    }
  }

  isEmpty() {
    return this._runIdToJSONRun.size === 0;
  }

  runProcessingInfo(runId: S.RunId): WireTypes.RunProcessingStatus|undefined {
    const jsonRun = this._runIdToJSONRun.get(runId);
    if (!jsonRun)
      return undefined;
    const envs = new Set(jsonRun.attempts.map(attempt => this._envs[attempt.environmentIdx]));
    let testsCount = 0;
    for (const env of envs) {
      const timeline = Timeline.fromEnv(env);
      const contribs = this._runContributionReport.get({ runId, timeline });
      const allTests = Ranges.unionAll(contribs.map(c => T.flatten(c.outcomes)));
      testsCount += Ranges.cardinality(allTests);
    }

    return {
      runId: runId,
      duration: jsonRun.duration,
      environments: Array.from(envs),
      testsCount,
      url: jsonRun.url,
      startTimestampMs: jsonRun.startTimestamp,
    }
  }

  etag() { 
    return this._etag;
  }

  contributions(timeline: Timeline): ContributionReport {
    return this._commitContributionReport.get(timeline);
  }

  testOutcomes(timeline: Timeline): T.TestOutcomes {
    return this._testOutcomes.get(timeline);
  }

  runReports(timeline: Timeline): RunReport[] {
    return this._runReports.get(timeline);
  }

  durations(timeline: Timeline): H.Histogram[] {
    return this._durations.get(timeline);
  }

  contributionInfo(contrib: Contribution) {
    return {
      env: this._envs[contrib.jsonAttempt.environmentIdx],
      testsWithImage: contrib.jsonAttempt.testsWithImage ? this._decompress(contrib.jsonAttempt.testsWithImage) : T.EMPTY_TESTS,
      testsWithTrace: contrib.jsonAttempt.testsWithTrace ? this._decompress(contrib.jsonAttempt.testsWithTrace) : T.EMPTY_TESTS,
      testsWithVideo: contrib.jsonAttempt.testsWithVideo ? this._decompress(contrib.jsonAttempt.testsWithVideo) : T.EMPTY_TESTS,
    };
  }

  runEnvironments(out = new Map<S.EnvId, WireTypes.RunEnvironment>()): Map<S.EnvId, WireTypes.RunEnvironment> {
    for (const run of this._jsonCommit?.runs ?? []) {
      for (const attempt of run.attempts) {
        if (out.has(this._envs[attempt.environmentIdx].envId))
          continue;
        out.set(this._envs[attempt.environmentIdx].envId, this._envs[attempt.environmentIdx]);
      }
    }
    return out;
  }

  runEnvironmentsForTest(out = new Map<S.EnvId, WireTypes.RunEnvironment>(), testIdx: S.TestIndex): Map<S.EnvId, WireTypes.RunEnvironment> {
    for (const run of this._jsonCommit?.runs ?? []) {
      for (const attempt of run.attempts) {
        if (out.has(this._envs[attempt.environmentIdx].envId))
          continue;
        let includes = false;
        includes ||= !!attempt.unexpectedTests && Ranges.includes(this._decompress(attempt.unexpectedTests), testIdx);
        includes ||= !!attempt.expectedTests && Ranges.includes(this._decompress(attempt.expectedTests), testIdx);
        includes ||= !!attempt.flakedTests && Ranges.includes(this._decompress(attempt.flakedTests), testIdx);
        includes ||= !!attempt.skippedTests && Ranges.includes(this._decompress(attempt.skippedTests), testIdx);
        if (includes)
          out.set(this._envs[attempt.environmentIdx].envId, this._envs[attempt.environmentIdx]);
      }
    }
    return out;
  }
}

export type ContributionReport = Brand<Contribution[], 'ContributionReport'>;

export function createCommitContributionReport(contribs: Contribution[]): ContributionReport {
  let seenTests = T.EMPTY_TESTS;
  const result: Contribution[] = [];
  for (const contrib of contribs) {
    const cTests = T.flatten(contrib.outcomes);
    const unseenTests = Ranges.subtract(cTests, seenTests);
    if (!unseenTests.length)
      continue;
    seenTests = Ranges.union(unseenTests, seenTests);
    const cOutcomes = T.intersectRanges(contrib.outcomes, unseenTests);
    result.push({
      commitAnalyzer: contrib.commitAnalyzer,
      jsonRun: contrib.jsonRun,
      jsonAttempt: contrib.jsonAttempt,
      outcomes: cOutcomes
    });
  }
  return result as ContributionReport;
}

export function createRunContributionReport(contribs: Contribution[]): ContributionReport {
  const finalOutcomes = T.newOutcomes();
  for (const contrib of contribs)
    T.unionInplace(finalOutcomes, contrib.outcomes);
  T.addOverlapAsFlakyInplace(finalOutcomes);
  T.normalizeInplace(finalOutcomes);

  const result: Contribution[] = [];
  let leftoverOutcomes = finalOutcomes;
  for (let idx = 0; !T.isEmptyOutcomes(leftoverOutcomes) && idx < contribs.length; ++idx) {
    const contrib = contribs[idx];
    const outcomes = T.cloneOutcomes(contrib.outcomes);
    // Either flaked or unexpected test outcomes could've contributed to final "flaked" status.
    outcomes.flaked = Ranges.union(outcomes.flaked, outcomes.unexpected);
    const intersection = T.intersectWithOutcomes(leftoverOutcomes, outcomes);
    if (T.isEmptyOutcomes(intersection))
      continue;
    leftoverOutcomes = T.subtractOutcomes(leftoverOutcomes, intersection);
    result.push({ ...contrib, outcomes: intersection });
  }
  return result as ContributionReport;
}
