import { FlakinessReport } from '@flakiness/report';
import { Ranges } from '../ranges.js';
import { Timeline } from '../timeline/timeline.js';
import { WireTypes } from '../wireTypes.js';
import { CommitAnalyzer, RunReport } from './commitAnalyzer.js';
import { Histogram as H } from './histogram.js';
import { Stats } from './stats.js';
import { FilterContext } from './testIndex.js';
import { TestOutcomes as T } from './testOutcomes.js';


export class TimelineCommitReport {
  static wireCommitStats(commit: WireTypes.Commit, reports: TimelineCommitReport[]): WireTypes.CommitStats {
    const stats: WireTypes.CommitStats = {
      commit,
      testStats: T.newOutcomeCounts(),
      durationMs: 0 as FlakinessReport.DurationMS,
      runs: [],
    };
    const runStats = new Map<Stats.RunId, WireTypes.RunStats>();
    for (const report of reports) {
      T.accumulateOutcomeCounts(stats.testStats, T.newOutcomeCounts(report.testOutcomes));
      stats.durationMs = stats.durationMs! + H.totalSum(report.durations) as FlakinessReport.DurationMS;

      for (const runReport of report.runs) {
        let rs = runStats.get(runReport.runId);
        if (!rs) {
          rs = {
            hasImages: false,
            hasTraces: false,
            hasVideos: false,
            testStatsOutcomes: T.newOutcomeCounts(),
            run: {
              commitId: commit.commitId,
              duration: runReport.duration,
              runId: runReport.runId,
              startTimestamp: runReport.startTimestamp,
              url: runReport.url,
            }
          }
          runStats.set(runReport.runId, rs);
        }
        rs.hasImages ||= runReport.testsWithImages.length > 0;
        rs.hasTraces ||= runReport.testsWithTraces.length > 0;
        rs.hasVideos ||= runReport.testsWithVideos.length > 0;
        T.accumulateOutcomeCounts(rs.testStatsOutcomes, T.newOutcomeCounts(runReport.outcomes));
      }
    }
    stats.runs = Array.from(runStats.values());
    if (!stats.runs.length)
      stats.durationMs = undefined;
    return stats;
  }

  static create(timeline: Timeline, analyzer: CommitAnalyzer, unhealthyTests: T.TestRanges) {
    const outcomes = T.regressify(analyzer.testOutcomes(timeline), unhealthyTests);
    const durations = analyzer.durations(timeline);

    const unhealthyRunTests = T.unhealthyTestsForAggregatedRun(outcomes);
    const runs = analyzer.runReports(timeline).map(run => ({
      ...run,
      outcomes: T.regressify(run.outcomes, unhealthyRunTests),
    }));
    return new TimelineCommitReport(timeline, outcomes, durations, runs);
  }

  constructor(
    public readonly timeline: Timeline,
    public readonly testOutcomes: T.TestOutcomes,
    public readonly durations: H.Histogram[],
    public readonly runs: RunReport[],
  ) {
  }

  isEmpty() {
    return this.runs.length === 0;
  }

  applyFilter(filter: FilterContext) {
    if (filter.fql.isEmpty())
      return this;
    return new TimelineCommitReport(
      this.timeline,
      T.intersectRanges(this.testOutcomes, filter.testsMask),
      H.intersectAll(this.durations, filter.testsMask).filter(h => h.length > 0),
      this.runs.map(run => ({
        ...run,
        outcomes: T.intersectRanges(run.outcomes, filter.testsMask),
        testsWithImages: Ranges.intersect(run.testsWithImages, filter.testsMask),
        testsWithTraces: Ranges.intersect(run.testsWithTraces, filter.testsMask),
        testsWithVideos: Ranges.intersect(run.testsWithVideos, filter.testsMask),
      })).filter(run => !T.isEmptyOutcomes(run.outcomes)),
    )
  }
}
