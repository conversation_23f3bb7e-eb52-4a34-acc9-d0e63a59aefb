import { FlakinessReport as FK } from '@flakiness/report';
import { Ranges } from '../ranges.js';
import { WireTypes } from '../wireTypes.js';
import { CommitAnalyzer } from './commitAnalyzer.js';
import { Stats as S } from './stats.js';
import { TestOutcomes as T } from './testOutcomes.js';

export type StatsAnnotationProfile = {
  annotationIdx: S.AnnotationIndex,
  tests: T.TestRanges,
}[];

export class StatsAnalyzer {
  private _testLineNumbers: FK.Number1Based[];

  private _etag: string;
  
  private _envs: WireTypes.RunEnvironment[] = [];
  private _errs: WireTypes.TestError[] = [];
  private _annotationProfiles: StatsAnnotationProfile[] = [];
  private _annotations: WireTypes.Annotation[] = [];

  private _commitIdToJSONCommit = new Map<S.CommitId, S.JSONCommit>();
  private _commitIdToCommit = new Map<S.CommitId, CommitAnalyzer>();

  private _rangesCache = new Map<Ranges.CompressedRanges<S.TestIndex>, T.TestRanges>();

  constructor(jsonStats: S.JSONData, etag?: string) {
    // If the data version is incompatible with this code version, or if the test index
    // has been changed, then we have to fall-back to empty filter result.
    if (jsonStats.version < S.STATS_VERSION) {
      jsonStats = S.createEmptyStats();
      etag = S.computeDataSignature(jsonStats);
    } else {
      etag = etag ?? S.computeDataSignature(jsonStats);
    }
    this._etag = etag;

    this._testLineNumbers = jsonStats.testLineNumbers;
    for (let i = 0; i < this._testLineNumbers.length; ++i) {
      if (!this._testLineNumbers[i])
        this._testLineNumbers[i] = 0 as FK.Number1Based;
    }

    this._envs = jsonStats.environments.map(jsonEnv => S.jsonEnvToWireEnv(jsonEnv));

    for (let i = 0 as S.ErrorIndex; i < jsonStats.errors.length; ++i) {
      const error: WireTypes.TestError = {
        errorId: S.computeErrorId(jsonStats.errors[i]),
        message: jsonStats.errors[i].message,
        value: jsonStats.errors[i].value,
      };
      this._errs.push(error);
    }

    for (let i = 0 as S.AnnotationIndex; i < jsonStats.annotations.length; ++i) {
      const annotation: WireTypes.Annotation = {
        annotationId: S.computeAnnotationId(jsonStats.annotations[i]),
        type: jsonStats.annotations[i].type,
        description: jsonStats.annotations[i].description,
      };
      this._annotations.push(annotation);
    }

    for (let commitIndex = 0 as S.CommitIndex; commitIndex < jsonStats.commits.length; ++commitIndex) {
      const commit = jsonStats.commits[commitIndex];
      this._commitIdToJSONCommit.set(commit.commitId, commit);
    }

    this._annotationProfiles = jsonStats.annotationProfiles.map(profile => profile.map(({ annotationIdx, tests }) => ({
      annotationIdx,
      tests: this._decompress(tests),
    })))
  }

  etag() {
    return this._etag;
  }

  _decompress(compressed: Ranges.CompressedRanges<S.TestIndex>): T.TestRanges {
    let result = this._rangesCache.get(compressed);
    if (!result) {
      result = Ranges.decompress(compressed);
      this._rangesCache.set(compressed, result);
    }
    return result;
  }

  getCommitAnalyzerEtag(commitId: S.CommitId) {
     return CommitAnalyzer.computeEtag(commitId, this._commitIdToJSONCommit.get(commitId));
  }

  getCommitAnalyzer(commitId: S.CommitId) {
    let commit = this._commitIdToCommit.get(commitId);
    if (!commit) {
      commit = new CommitAnalyzer(
        commitId,
        this._envs,
        this._errs,
        this._annotationProfiles,
        this._annotations,
        this._testLineNumbers,
        this._decompress.bind(this),
        this._commitIdToJSONCommit.get(commitId),
      );
      this._commitIdToCommit.set(commitId, commit);
    }
    return commit;
  }
}
