import { FlakinessReport as FK, ReportUtils } from '@flakiness/report';
import { Bijection } from '@flakiness/shared/common/bijection.js';
import { Multimap } from '@flakiness/shared/common/multimap.js';
import { Ranges } from '../ranges.js';
import { Histogram } from './histogram.js';
import { Stats as S } from './stats.js';
import { TestIndex } from './testIndex.js';

/**
 * The compression parametrization is defined like this:
 * - let's say X is the original timing value in milliseconds
 * - let's say X' is the decompress(compress(X))
 *
 * The compression guarantees that |X - X'| <= max(X * MaxRelativeError, MinAbsoluteErrorMs);
 *
 * For `maxRelativeError` = 0.05 and `minAbsoluteErrorMs` = 50, this approach ensures that
 * larger durations can have proportionally larger errors (5% of their value)
 * while very small durations won't have unreasonably small error bounds (minimum 50ms)
 */
export type StatsBuilderOptions = {
  durationCompressionMaxRelativeError: number,
  durationCompressionMinAbsoluteErrorMs: number,
}

const DURATION_HISTOGRAM_5_PCT_COMPRESSION: StatsBuilderOptions = {
  durationCompressionMaxRelativeError: 0.05,
  /**
   * In our tests, the default value of "50" results in 1.5KB per report in stats object,
   * whereas a value of "0" results in 2.82KB per report.
   * This makes it for a much nicer durations for very fast tests.
   */
  durationCompressionMinAbsoluteErrorMs: 0,
}

const DURATION_HISTOGRAM_NO_COMPRESSION: StatsBuilderOptions = {
  durationCompressionMaxRelativeError: 0,
  durationCompressionMinAbsoluteErrorMs: 0,
}


export class StatsBuilder {

  private _runIdToCommitId = new Map<S.RunId, S.CommitId>();
  private _commitIdToIndex = new Bijection<S.CommitId, S.CommitIndex>();
  private _envIdToIndex = new Bijection<S.EnvId, S.EnvIndex>();
  private _errorIdToIndex = new Bijection<S.ErrorId, S.ErrorIndex>();
  private _annotationIdToIndex = new Bijection<S.AnnotationId, S.AnnotationIndex>();
  private _annotationsProfileIdToIndex = new Bijection<S.AnnotationsProfileId, S.AnnotationsProfileIndex>();

  static create(testIndex: TestIndex, jsonStats?: S.JSONData) {
    if (!jsonStats || jsonStats.version < S.STATS_VERSION)
      jsonStats = S.createEmptyStats();

    const builder = new StatsBuilder(testIndex, jsonStats);

    for (let i = 0 as S.ErrorIndex; i < jsonStats.errors.length; ++i) {
      const errorId = S.computeErrorId(jsonStats.errors[i]);
      builder._errorIdToIndex.set(errorId, i);
    }
    for (let i = 0 as S.AnnotationIndex; i < jsonStats.annotations.length; ++i) {
      const annotationId = S.computeAnnotationId(jsonStats.annotations[i]);
      builder._annotationIdToIndex.set(annotationId, i);
    }
    for (let i = 0 as S.AnnotationsProfileIndex; i < jsonStats.annotationProfiles.length; ++i) {
      const annotationProfileId = S.computeAnnotationsProfileId(jsonStats.annotationProfiles[i]);
      builder._annotationsProfileIdToIndex.set(annotationProfileId, i);
    }

    for (let i = 0 as S.CommitIndex; i < jsonStats.commits.length; ++i) {
      const commitId = jsonStats.commits[i].commitId as S.CommitId;
      builder._commitIdToIndex.set(commitId, i);

      for (const run of jsonStats.commits[i].runs)
        builder._runIdToCommitId.set(run.runId as S.RunId, commitId);
    }

    for (let i = 0 as S.EnvIndex; i < jsonStats.environments.length; ++i) {
      const envId = S.computeEnvId(builder._json.environments[i]);
      builder._envIdToIndex.set(envId, i);
    }
    return builder;
  }

  constructor(private _testIndex: TestIndex, private _json: S.JSONData) {
  }

  jsonStats(): S.JSONData {
    return this._json;
  }

  hasRun(runId: S.RunId) {
    return this._runIdToCommitId.has(runId);
  }

  runIds(): Iterable<S.RunId> {
    return this._runIdToCommitId.keys();
  }

  removeRun(runId: S.RunId) {
    const commitId = this._runIdToCommitId.get(runId);
    if (!commitId)
      return;
    this._runIdToCommitId.delete(runId);
    let commitIndex = this._commitIdToIndex.get(commitId)!;
    const jsonCommit = this._json.commits[commitIndex];
    jsonCommit.runs = jsonCommit.runs.filter(report => report.runId !== runId);
  }

  addRun(runId: S.RunId, report: FK.Report, options?: StatsBuilderOptions) {
    if (!options) {
      // Do NOT compress durations for performance tests.
      // Usually there are not TOO many of these.
      if (report.category === FK.CATEGORY_PERF)
        options = DURATION_HISTOGRAM_NO_COMPRESSION;
      else
        options = DURATION_HISTOGRAM_5_PCT_COMPRESSION;
    }
    if (this._runIdToCommitId.has(runId))
      return;
    this._runIdToCommitId.set(runId, report.commitId);

    // Add tests that haven't been seen before to the data.
    const test2index = new Map<FK.Test, S.TestIndex>();
    const testLineDeltas = new Multimap<number, S.TestIndex>();
    const tag2TestIndex = new Multimap<string, S.TestIndex>();
    ReportUtils.visitTests(report, (test, parentSuites) => {
      const wireTest = S.flakinessTestToWireTypesTest(test, parentSuites);
      const testIdx = this._testIndex.testIndex(wireTest.testId)!;
      console.assert(testIdx !== undefined);

      test2index.set(test, testIdx);

      const savedLineNumber = this._json.testLineNumbers[testIdx];
      if (isNaN(savedLineNumber)) {
        this._json.testLineNumbers[testIdx] = test.location?.line ?? 0 as FK.Number1Based;
      } else {
        const delta = (test.location?.line ?? 0) - savedLineNumber;
        if (delta !== 0)
          testLineDeltas.set(delta, testIdx);
      }
      for (const tag of test.tags ?? [])
        tag2TestIndex.set(tag, testIdx);
    });

    // Add missing commit, if any.
    const commitId = report.commitId;
    let commitIndex = this._commitIdToIndex.get(commitId);
    if (commitIndex === undefined) {
      commitIndex = this._json.commits.length as S.CommitIndex;
      this._json.commits.push({
        commitId,
        changedLineNumbers: undefined,
        tags: [],
        runs: [],
      });
      this._commitIdToIndex.set(commitId, commitIndex);
    }
    const commit = this._json.commits[commitIndex]!;

    // 1. Add in changedLineNumbers.
    {
      const changedLineNumbers = new Map<number, Ranges.CompressedRanges<S.TestIndex>>();
      for (const { lineDelta, affectedTests } of commit.changedLineNumbers ?? [])
        changedLineNumbers.set(lineDelta, affectedTests);
      for (const [delta, testIndexes] of testLineDeltas) {
        const compressed = changedLineNumbers.get(delta);
        const ranges = compressed ? Ranges.decompress(compressed) : Ranges.EMPTY as Ranges.Ranges<S.TestIndex>;
        changedLineNumbers.set(delta, Ranges.compress(Ranges.union(ranges, Ranges.fromList([...testIndexes]))));
      }
      commit.changedLineNumbers = [...changedLineNumbers].map(([lineDelta, affectedTests]) => ({
        lineDelta,
        affectedTests,
      }));
    }

    // Add tags in 2 steps.
    // 2. Update all existing tags
    for (const tagInfo of commit.tags) {
      if (!tag2TestIndex.hasAny(tagInfo.tag))
        continue;
      const newRanges = Ranges.fromList([...tag2TestIndex.getAll(tagInfo.tag)]);
      const savedRanges = Ranges.decompress<S.TestIndex>(tagInfo.tests);
      tagInfo.tests = Ranges.compress(Ranges.union(newRanges, savedRanges));
      tag2TestIndex.deleteAll(tagInfo.tag);
    }
    // 3. Add all missing tags
    for (const [tag, testIndexes] of tag2TestIndex) {
      const range = Ranges.fromList([...testIndexes]);
      commit.tags.push({
        tag,
        tests: Ranges.compress(range),
      });
    }

    // Add environments that haven't been seen before to the data.
    const env2index = new Map<FK.Environment, S.EnvIndex>();
    for (const env of report.environments) {
      const jsonEnv: S.JSONEnvironment = S.flakinessEnvToJSONEnv(report, env);
      const envId = S.computeEnvId(jsonEnv);
      let index = this._envIdToIndex.get(envId);
      if (index === undefined) {
        index = this._json.environments.length as S.EnvIndex;
        this._envIdToIndex.set(envId, index);
        this._json.environments.push(jsonEnv);
      }
      env2index.set(env, index);
    }

    // Add runs
    const reportPerAttemptEnvStats: {
      expectedTests: Set<S.TestIndex>,
      unexpectedTests: Set<S.TestIndex>,
      flakedTests: Set<S.TestIndex>,
      skippedTests: Set<S.TestIndex>,

      testsWithVideo: Set<S.TestIndex>,
      testsWithTrace: Set<S.TestIndex>,
      testsWithImage: Set<S.TestIndex>,

      errors: Multimap<S.ErrorIndex, S.TestIndex>,
      timings: Map<S.TestIndex, FK.DurationMS>,
      annotations: Multimap<S.AnnotationIndex, S.TestIndex>,
    }[][] = report.environments.map(env => []);
    for (const [test, testIdx] of test2index) {
      // Split attempts by envIdx
      const envIdxToAttempts = new Multimap<S.EnvIndex, FK.RunAttempt>();
      for (const attempt of test.attempts)
        envIdxToAttempts.set(attempt.environmentIdx as S.EnvIndex, attempt);

      for (const [environmentIdx, envAttemptsSet] of envIdxToAttempts) {
        const perAttemptEnvStats = reportPerAttemptEnvStats[environmentIdx];
        const envAttempts = [...envAttemptsSet];
        for (let attemptIdx = 0; attemptIdx < envAttempts.length; ++attemptIdx) {
          const attempt = envAttempts[attemptIdx];
          let envStats = perAttemptEnvStats[attemptIdx];
          if (!envStats) {
            envStats = {
              expectedTests: new Set(),
              unexpectedTests: new Set(),
              flakedTests: new Set(),
              skippedTests: new Set(),

              testsWithVideo: new Set(),
              testsWithTrace: new Set(),
              testsWithImage: new Set(),
          
              timings: new Map(),
              errors: new Multimap(),
              annotations: new Multimap(),
            }
            perAttemptEnvStats[attemptIdx] = envStats;
          }

          // Classify outcome. Note that attempt can either be expected, unexpected or skipped.
          // Just to be on the safe side, we include 'flaky' here.
          const outcome = computeAttemptOutcome(attempt.expectedStatus, attempt.status);
          if (outcome === 'expected')
            envStats.expectedTests.add(testIdx);
          else if (outcome === 'unexpected')
            envStats.unexpectedTests.add(testIdx);
          else if (outcome === 'flaky')
            envStats.flakedTests.add(testIdx);
          else if (outcome === 'skipped')
            envStats.skippedTests.add(testIdx);
          else
            throw new Error(`Unknown test outcome - ${outcome}`);

          // Classify attachments
          for (const attachment of attempt.attachments ?? []) {
            if (attachment.contentType.startsWith('image/'))
              envStats.testsWithImage.add(testIdx);
            else if (attachment.contentType.startsWith('video/'))
              envStats.testsWithVideo.add(testIdx);
            else if (attachment.name === 'trace')
              envStats.testsWithTrace.add(testIdx);
          }

          // Process time
          envStats.timings.set(testIdx, attempt.duration);

          // Process errors
          for (const error of attempt.errors ?? []) {
            const jsonError: S.JSONError = {
              message: error.message,
              value: error.value,
            };
            const errorId = S.computeErrorId(jsonError);
            let errorIdx = this._errorIdToIndex.get(errorId);
            if (errorIdx === undefined) {
              errorIdx = this._json.errors.push(jsonError) - 1 as S.ErrorIndex;
              this._errorIdToIndex.set(errorId, errorIdx);
            }

            envStats.errors.set(errorIdx, testIdx);
          }

          // Process annotations
          for (const annotation of attempt.annotations ?? []) {
            const jsonAnnotation: S.JSONAnnotation = {
              type: annotation.type,
              description: annotation.description,
            };
            const annotationId = S.computeAnnotationId(jsonAnnotation);
            let annotationIdx = this._annotationIdToIndex.get(annotationId);
            if (annotationIdx === undefined) {
              annotationIdx = this._json.annotations.push(jsonAnnotation) - 1 as S.AnnotationIndex;
              this._annotationIdToIndex.set(annotationId, annotationIdx);
            }
            envStats.annotations.set(annotationIdx, testIdx);
          }
        }
      }
    }

    const runs: S.JSONAttempt[] = [];
    for (let localEnvIndex = 0; localEnvIndex < report.environments.length; ++localEnvIndex) {
      const env = report.environments[localEnvIndex];
      const envIndex = env2index.get(env)!;
      const perAttemptStats = reportPerAttemptEnvStats[localEnvIndex];
      for (let attemptIdx = 0; attemptIdx < perAttemptStats.length; ++attemptIdx) {
        const envStats = perAttemptStats[attemptIdx];
        const durations = Histogram.compute(envStats.timings, {
          maxRelativeError: options.durationCompressionMaxRelativeError,
          minRadius: options.durationCompressionMinAbsoluteErrorMs as FK.DurationMS,
        });
        const annotationsProfile: S.JSONRunAnnotation[] = envStats.annotations.map((annotationIdx, testIndexes) => ({
          annotationIdx,
          tests: compressTestIndexes(testIndexes),
        }));
        const annotationsProfileId = S.computeAnnotationsProfileId(annotationsProfile);
        let annotationsProfileIdx = this._annotationsProfileIdToIndex.get(annotationsProfileId);
        if (annotationsProfileIdx === undefined) {
          annotationsProfileIdx = this._json.annotationProfiles.push(annotationsProfile) - 1 as S.AnnotationsProfileIndex;
          this._annotationsProfileIdToIndex.set(annotationsProfileId, annotationsProfileIdx);
        }
  
        runs.push({
          environmentIdx: envIndex,
          attemptIdx: attemptIdx ? attemptIdx : undefined,
          expectedTests: compressTestIndexes(envStats.expectedTests) || undefined,
          unexpectedTests: compressTestIndexes(envStats.unexpectedTests) || undefined,
          skippedTests: compressTestIndexes(envStats.skippedTests) || undefined,
          flakedTests: compressTestIndexes(envStats.flakedTests) || undefined,
  
          testsWithImage: compressTestIndexes(envStats.testsWithImage) || undefined,
          testsWithVideo: compressTestIndexes(envStats.testsWithVideo) || undefined,
          testsWithTrace: compressTestIndexes(envStats.testsWithTrace) || undefined,
  
          durationBuckets: durations.map(bucket => bucket[0]),
          durationBucketTests: durations.map(bucket => Ranges.compress(bucket[1])),
          annotationsProfileIdx,
          errors: envStats.errors.size ? envStats.errors.map((errorIdx, testIndexes) => ({
            errorIdx,
            tests: compressTestIndexes(testIndexes),
          })) : undefined,
        });
      }
    }

    commit.runs.push({
      url: report.url ?? '',
      runId: runId,
      startTimestamp: report.startTimestamp,
      duration: report.duration,
      attempts: runs,
    });

    commit.runs.sort((r1, r2) => r1.runId - r2.runId);
  }
}

function computeAttemptOutcome(expected: FK.TestStatus, actual: FK.TestStatus): FK.TestOutcome {
  if (actual === 'skipped' || actual === 'interrupted')
    return 'skipped';
  return actual === expected ? 'expected' : 'unexpected';
}

function compressTestIndexes(s: Set<S.TestIndex>) {
  return Ranges.compress(Ranges.fromList(Array.from(s)));
}
