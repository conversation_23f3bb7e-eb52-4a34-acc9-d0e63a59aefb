import { FlakinessReport } from '@flakiness/report';
import { PageOptions, Sorter } from '../pagedResponse.js';
import { Ranges } from '../ranges.js';
import { Sequence } from '../sequence.js';
import { Timeline } from '../timeline/timeline.js';
import { wireOutcomesToOutcome, WireTypes } from '../wireTypes.js';
import { Histogram } from './histogram.js';
import { Stats } from './stats.js';
import { FilterContext, TestIndex } from './testIndex.js';
import { TestOutcomes as T } from './testOutcomes.js';
import { TimelineTestsReport } from './timelineTestsReport.js';

const OUTCOME_WEIGHTS: Record<WireTypes.Outcome, number> = {
  regressed: 5,
  unexpected: 4,
  flaked: 3,
  expected: 2,
  skipped: 1,
}

const DAY_OUTCOME_WEIGHTS: Record<WireTypes.DayOutcome, bigint> = {
  regressed: 6n,
  unexpected: 5n,
  flaked: 4n,
  expected: 3n,
  skipped: 2n,
  untested: 1n,
  idle: 0n,
};
const DAY_BITMASK_SHIFT = BigInt(Math.max(...Array.from(Object.values(DAY_OUTCOME_WEIGHTS), w => w.toString(2).length)));

const gDailyOutcomesScore = new WeakMap<WireTypes.DayOutcome[], bigint>();

function dailyOutcomesScore(outcomes: WireTypes.DayOutcome[]): bigint {
  let score = gDailyOutcomesScore.get(outcomes);
  if (!score) {
    let result = 0n;
    for (const outcome of outcomes) {
      result = result | DAY_OUTCOME_WEIGHTS[outcome];
      result = result << DAY_BITMASK_SHIFT;
    }
    result >>= DAY_BITMASK_SHIFT;
    score = result;
    gDailyOutcomesScore.set(outcomes, score);
  }
  return score;
}

export class TestsReport {
  public readonly tests: FastTestsSorter;
  public readonly errors: Sorter<WireTypes.ErrorStats, WireTypes.ErrorStatsSortAxis>;
  public readonly annotations: Sorter<WireTypes.AnnotationStats, WireTypes.AnnotationStatsSortAxis>;
  public readonly tags: Sorter<WireTypes.TagStats, WireTypes.TagStatsSortAxis>;
  public readonly timelines: Sorter<WireTypes.TimelineStats, WireTypes.TimelineStatsSortAxis>;

  private _outcomes: T.TestOutcomeCounts;

  constructor(
    private _testIndex: TestIndex,
    private _timelineTestsReports: TimelineTestsReport[],
    private _acceptableFlakinessRate: number,
  ) {
    const tests = this._testIndex.tests();
    this.tests = new FastTestsSorter({
      name: () => {
        const testScores = this._testIndex.testScores();
        const merged = Sequence.merge<[number, Stats.TestIndex, number]>(this._timelineTestsReports.map((timelineReport, timelineIndex) => {
          const testIndexes = Ranges.toSortedList(timelineReport.timelineTests);
          const lineNumbers = timelineReport.lineNumbers();
          testIndexes.sort((idx1, idx2) => {
            const r = testScores[idx1] - testScores[idx2];
            return r === 0 ? lineNumbers[idx1] - lineNumbers[idx2] : r;
          });
          return Sequence.fromList(testIndexes).map((testIdx) => [timelineIndex, testIdx, lineNumbers[testIdx]]);
        }), (a, b) => {
          const r = testScores[a[1]] - testScores[b[1]];
          return r === 0 ? a[2] - b[2] : r;
        });
        return merged.map(([timelineIndex, testIdx, lineNumber]) => this._timelineTestsReports[timelineIndex].wireTestStats(tests[testIdx], testIdx)!);
      },
      timeline_name: () => {
        const merged = Sequence.merge<[number, Stats.TestIndex, number]>(this._timelineTestsReports.map((timelineReport, timelineIndex) => {
          const outcomes = (['regressed', 'unexpected', 'flaked', 'expected', 'skipped'] as WireTypes.Outcome[]).sort((a, b) => OUTCOME_WEIGHTS[a] - OUTCOME_WEIGHTS[b]);
          const seqs = outcomes.map(outcome => Ranges.sequence(timelineReport.outcomes[outcome]));
          return Sequence.chain(seqs).map(([testIdx, outcomeIdx]) => [timelineIndex, testIdx, OUTCOME_WEIGHTS[outcomes[outcomeIdx]]]);
        }), ([aTimelineIndex, aTestIdx, aOutcomeWeight], [bTimelineIndex, bTestIdx, bOutcomeWeight]) => {
          const aTimeline = this._timelineTestsReports[aTimelineIndex].timeline;
          const bTimeline = this._timelineTestsReports[bTimelineIndex].timeline;
          const cmp = aTimeline.compare(bTimeline);
          return cmp === 0 ? aOutcomeWeight - bOutcomeWeight : cmp;
        });
        return merged.map(([timelineIndex, testIdx, score]) => this._timelineTestsReports[timelineIndex].wireTestStats(tests[testIdx], testIdx)!);
      },
      history: () => {
        const merged = Sequence.merge<[number, Stats.TestIndex, bigint]>(this._timelineTestsReports.map((timelineReport, timelineIndex) => {
          const chunks = speedsortDayOutcomes(timelineReport).reverse();
          const seqs = chunks.map(chunk => Ranges.sequence(chunk.tests));
          return Sequence.chain(seqs).map(([testIdx, chunkIdx]) => [timelineIndex, testIdx, chunks[chunkIdx].score]);
        }), (a, b) => a[2] < b[2] ? -1 : a[2] === b[2] ? 0 : 1);
        return merged.map(([timelineIndex, testIdx, score]) => this._timelineTestsReports[timelineIndex].wireTestStats(tests[testIdx], testIdx)!);
      },
      duration: () => {
        const merged = Sequence.merge<[number, Stats.TestIndex, number]>(this._timelineTestsReports.map((timelineReport, timelineIndex) => {
          const { buffer, offset } = timelineReport.expandedDurations();
          const indexes = Ranges.toInt32Array(timelineReport.timelineTests);
          indexes.sort((a, b) => buffer[a - offset] - buffer[b - offset]);
          return Sequence.fromList(indexes).map((idx => [timelineIndex, idx as Stats.TestIndex, buffer[idx - offset]]));
        }), (a, b) => a[2] - b[2]);
        return merged.map(([timelineIndex, testIdx, score]) => this._timelineTestsReports[timelineIndex].wireTestStats(tests[testIdx], testIdx)!);
      },
      duration_trend: () => {
        const merged = Sequence.merge<[number, Stats.TestIndex, number]>(this._timelineTestsReports.map((timelineReport, timelineIndex) => {
          if (!timelineReport.timelineTests.length)          
            return Sequence.EMPTY;
          const { offset, buffer: trend } = timelineReport.durationTrend();
          const indexes = Ranges.toInt32Array(timelineReport.timelineTests);
          indexes.sort((a, b) => trend[a - offset] - trend[b - offset]);
          return Sequence.fromList(indexes).map((testIdx => [timelineIndex, testIdx as Stats.TestIndex, trend[testIdx - offset]]));
        }), (a, b) => a[2] - b[2]);
        return merged.map(([timelineIndex, testIdx, score]) => this._timelineTestsReports[timelineIndex].wireTestStats(tests[testIdx], testIdx)!);
      },
      outcome: () => {
        const merged = Sequence.merge<[number, Stats.TestIndex, number]>(this._timelineTestsReports.map((timelineReport, timelineIndex) => {
          const outcomes = (['regressed', 'unexpected', 'flaked', 'expected', 'skipped'] as WireTypes.Outcome[]).sort((a, b) => OUTCOME_WEIGHTS[a] - OUTCOME_WEIGHTS[b]);
          const seqs = outcomes.map(outcome => Ranges.sequence(timelineReport.outcomes[outcome]));
          return Sequence.chain(seqs).map(([testIdx, outcomeIdx]) => [timelineIndex, testIdx, OUTCOME_WEIGHTS[outcomes[outcomeIdx]]]);
        }), (a, b) => a[2] < b[2] ? -1 : a[2] === b[2] ? 0 : 1);
        return merged.map(([timelineIndex, testIdx, score]) => this._timelineTestsReports[timelineIndex].wireTestStats(tests[testIdx], testIdx)!);
      },
    });

    const errors = new Map<Stats.ErrorId, WireTypes.ErrorStats>();
    const annotations = new Map<Stats.AnnotationId, WireTypes.AnnotationStats>();
    const tags = new Map<string, WireTypes.TagStats>();
    const timelineStats: WireTypes.TimelineStats[] = [];
    for (const timelineTestsReport of this._timelineTestsReports) {
      for (const [error, tests] of timelineTestsReport.errorTests) {
        let accStats = errors.get(error.errorId);
        if (!accStats) {
          accStats = {
            error,
            impactedTests: 0,
            impactedTimelines: 0,
          };
          errors.set(error.errorId, accStats);
        }
        accStats.impactedTests += Ranges.cardinality(tests);
        accStats.impactedTimelines += 1;
      }
      for (const [annotation, tests] of timelineTestsReport.annotationTests) {
        let accStats = annotations.get(annotation.annotationId);
        if (!accStats) {
          accStats = {
            annotation,
            annotatedTests: 0,
            annotatedTimelines: 0,
          };
          annotations.set(annotation.annotationId, accStats);
        }
        accStats.annotatedTests += Ranges.cardinality(tests);
        accStats.annotatedTimelines += 1;
      }
      for (const [tag, tests] of timelineTestsReport.tagTests) {
        let tagStats = tags.get(tag);
        if (!tagStats) {
          tagStats = {
            tag,
            impactedTests: 0,
            impactedTimelines: 0,
          };
          tags.set(tag, tagStats);
        }
        tagStats.impactedTests += Ranges.cardinality(tests);
        tagStats.impactedTimelines += 1;
      }
      if (timelineTestsReport.timelineTests.length) {
        const ts: WireTypes.TimelineStats = {
          durationMs: timelineTestsReport.totalDurationMs,
          durationChangeMs: 0 as FlakinessReport.DurationMS,
          testStats: T.newOutcomeCounts(timelineTestsReport.outcomes),
          timeline: timelineTestsReport.timeline.serialize(),
          daily: timelineTestsReport.intermediateOutcomes.map(dayStats => {
            const outcome = wireOutcomesToOutcome(T.newOutcomeCounts(dayStats.testOutcomes), this._acceptableFlakinessRate);
            const duration = dayStats.durations.length ? Histogram.totalSum(dayStats.durations) as FlakinessReport.DurationMS : undefined;
            return {
              outcome: outcome ?? (dayStats.commitsCount > 0 ? 'untested' : 'idle'),
              duration,
            }
          }),
        }
        const lastStatsWithDuration = ts.daily.findLast(x => x.duration !== undefined);
        ts.durationChangeMs = (lastStatsWithDuration ? ts.durationMs - lastStatsWithDuration.duration! : 0) as FlakinessReport.DurationMS;
        timelineStats.push(ts);
      }
    }
    this.timelines = new Sorter<WireTypes.TimelineStats, WireTypes.TimelineStatsSortAxis>(timelineStats, {
      total_time: (a, b) => b.durationMs - a.durationMs,
      outcome: (a, b) => OUTCOME_WEIGHTS[wireOutcomesToOutcome(a.testStats, this._acceptableFlakinessRate) ?? 'expected'] - OUTCOME_WEIGHTS[wireOutcomesToOutcome(b.testStats, this._acceptableFlakinessRate) ?? 'expected'],
      name: (a, b) => Timeline.deserialize(a.timeline).compare(Timeline.deserialize(b.timeline)),
      expected: (a, b) => a.testStats.expected - b.testStats.expected,
      unexpected: (a, b) => a.testStats.unexpected - b.testStats.unexpected,
      skipped: (a, b) => a.testStats.skipped - b.testStats.skipped,
      flaked: (a, b) => a.testStats.flaked - b.testStats.flaked,
      regressed: (a, b) => a.testStats.regressed - b.testStats.regressed,
      history: (a, b) => {
        const result = dailyOutcomesScore(a.daily.map(x => x.outcome)) - dailyOutcomesScore(b.daily.map(x => x.outcome));
        return result === 0n ? 0 : (result < 0 ? -1 : 1);
      },
      duration_trend: (a, b) => a.durationChangeMs - b.durationChangeMs,
    });

    this.errors = new Sorter(Array.from(errors.values()), {
      name: (a, b) => {
        const aName = a.error.message ?? a.error.value ?? '';
        const bName = b.error.message ?? b.error.value ?? '';
        return aName < bName ? -1 : 1;
      },
      tests: (a, b) => a.impactedTests - b.impactedTests,
      timelines: (a, b) => a.impactedTimelines - b.impactedTimelines,
    });

    this.annotations = new Sorter(Array.from(annotations.values()), {
      name: (a, b) => {
        const aName = a.annotation.type;
        const bName = b.annotation.type;
        if (aName === bName)
          return (a.annotation.description ?? '') < (b.annotation.description ?? '') ? -1 : 1;
        return aName < bName ? -1 : 1;
      },
      tests: (a, b) => a.annotatedTests - b.annotatedTests,
      timelines: (a, b) => a.annotatedTimelines - b.annotatedTimelines,
    });

    this.tags = new Sorter(Array.from(tags.values()), {
      name: (a, b) => {
        const aName = a.tag;
        const bName = b.tag;
        return aName < bName ? -1 : 1;
      },
      tests: (a, b) => a.impactedTests - b.impactedTests,
      timelines: (a, b) => a.impactedTimelines - b.impactedTimelines,
    });

    this._outcomes = T.newOutcomeCounts();
    for (const report of this._timelineTestsReports)
      T.accumulateOutcomeCounts(this._outcomes, T.newOutcomeCounts(report.outcomes));
  }

  applyFilter(filter: FilterContext): TestsReport {
    return new TestsReport(this._testIndex, this._timelineTestsReports.map(tr => tr.applyFilter(filter)), this._acceptableFlakinessRate);
  }

  totalOutcomes(): WireTypes.OutcomesCount {
    return this._outcomes;
  }

  findTestStats(testId: Stats.TestId, timeline: Timeline) {
    const testIdx = this._testIndex.testIndex(testId);
    if (testIdx === undefined)
      return undefined;
    const test = this._testIndex.tests()[testIdx];
    const timelineReport = this._timelineTestsReports.find(report => report.timeline.etag() === timeline.etag());
    return timelineReport?.wireTestStats(test, testIdx);
  }

  isEmpty() {
    return this._outcomes.expected + this._outcomes.flaked + this._outcomes.regressed + this._outcomes.skipped + this._outcomes.unexpected === 0;
  }

  counters() {
    return {
      testOutcomes: this._outcomes,
      tests: this._timelineTestsReports.reduce((acc, t) => Ranges.cardinality(t.timelineTests) + acc, 0),
      timelines: this.timelines.all().length,
      errors: this.errors.all().length,
      annotations: this.annotations.all().length,
      tags: this.tags.all().length,
    };
  }
}

function speedsortDayOutcomes(timelineReport: TimelineTestsReport, dayIdx: number = 0, tests: T.TestRanges = timelineReport.timelineTests, score: bigint = 0n, result: { tests: T.TestRanges, score: bigint }[] = []) {
  if (dayIdx >= timelineReport.intermediateOutcomes.length) {
    result.push({ tests, score });
    return result;
  }
  let noOutcomes = tests;
  for (const outcome of ['regressed', 'unexpected', 'flaked', 'expected', 'skipped'] as const) {
    const t = Ranges.intersect(tests, timelineReport.intermediateOutcomes[dayIdx].testOutcomes[outcome]);
    if (!t.length)
      continue;
    noOutcomes = Ranges.subtract(noOutcomes, t);
    const s = (score << DAY_BITMASK_SHIFT) | DAY_OUTCOME_WEIGHTS[outcome];
    speedsortDayOutcomes(timelineReport, dayIdx + 1, t, s, result);
  }
  if (noOutcomes.length) {
    const outcome = timelineReport.intermediateOutcomes[dayIdx].commitsCount ? 'untested' : 'idle';
    const s = (score << DAY_BITMASK_SHIFT) | DAY_OUTCOME_WEIGHTS[outcome];
    speedsortDayOutcomes(timelineReport, dayIdx + 1, noOutcomes, s, result);
  }
  return result;
}

export class FastTestsSorter {
  private _axisName: WireTypes.TestStatsSortAxis = 'history';
  private _seq?: Sequence<WireTypes.TestStats>;

  constructor(
    private _axises: Record<WireTypes.TestStatsSortAxis, () => Sequence<WireTypes.TestStats>>,
  ) {

  }

  private _ensureSequence(axisName: WireTypes.TestStatsSortAxis): Sequence<WireTypes.TestStats> {
    if (axisName === this._axisName && this._seq)
      return this._seq;
    this._axisName = axisName;
    this._seq = this._axises[axisName]();
    return this._seq;
  }

  page(pageOptions: PageOptions, axisName?: WireTypes.TestStatsSortAxis, direction?: WireTypes.SortDirection): WireTypes.PagedResponse<WireTypes.TestStats> {
    const seq = this._ensureSequence(axisName ?? 'history');
    const elements = direction === 'asc'
      ? Iterator.from(seq.seek(pageOptions.number * pageOptions.size)).take(pageOptions.size).toArray()
      : Iterator.from(seq.seek(seq.length - (pageOptions.number + 1) * pageOptions.size)).take(pageOptions.size).toArray().reverse();
    return {
      elements,
      pageNumber: pageOptions.number,
      pageSize: pageOptions.size,
      totalElements: seq.length,
      totalPages: Math.ceil(seq.length / pageOptions.size),
    };
  }
}
