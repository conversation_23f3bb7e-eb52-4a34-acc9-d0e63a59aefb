import { FlakinessReport } from '@flakiness/report';
import { MatchResult } from '../fql/matcher.js';
import { Ranges } from '../ranges.js';
import { Timeline } from '../timeline/timeline.js';
import { WireTypes } from '../wireTypes.js';
import { CommitAnalyzer, ContributionReport, createCommitContributionReport } from './commitAnalyzer.js';
import { ExpandedHistogram, Histogram as H } from './histogram.js';
import { SpanAnalyzer, SpanStats } from './spanAnalyzer.js';
import { Stats as S } from './stats.js';
import { FilterContext } from './testIndex.js';
import { TestOutcomes as T } from './testOutcomes.js';

export class TimelineTestsReport {
  static create(timeline: Timeline, span: SpanAnalyzer, unhealthyTests: T.TestRanges): TimelineTestsReport {
    const commitTimelineContribs = span.commits.map(commit => commit.contributions(timeline));
    const contribs = CommitAnalyzer.regressify(createCommitContributionReport(commitTimelineContribs.flat()), unhealthyTests);

    const timelineOutcome = T.regressify(span.testOutcomes(timeline), unhealthyTests);
    const timelineTests = T.flatten(timelineOutcome);
    return new TimelineTestsReport(
      timeline,
      contribs,
      timelineTests,
      timelineOutcome,
      CommitAnalyzer.computeDurationBuckets(timeline, contribs),
      CommitAnalyzer.computeTagTests(contribs),
      CommitAnalyzer.computeErrorTests(contribs),
      CommitAnalyzer.computeAnnotationTests(contribs),
      [],
      [],
    );
  }

  public readonly totalDurationMs: FlakinessReport.DurationMS;

  constructor(
    public readonly timeline: Timeline,
    public readonly contribs: ContributionReport,
    public readonly timelineTests: T.TestRanges,
    public readonly outcomes: T.TestOutcomes,
    private _durations: H.Histogram[],

    public readonly tagTests: Map<string, T.TestRanges>,
    public readonly errorTests: Map<WireTypes.TestError, T.TestRanges>,
    public readonly annotationTests: Map<WireTypes.Annotation, T.TestRanges>,

    public readonly intermediateOutcomes: SpanStats[],
    public readonly historyOutcomes: SpanStats[],
  ) {
    this.totalDurationMs = H.totalSum(this._durations) as FlakinessReport.DurationMS;
  }

  isEmpty() {
    return this.timelineTests.length === 0;
  }

  setIntermediateStats(intermediate: SpanStats[]) {
    return new TimelineTestsReport(
      this.timeline,
      this.contribs,
      this.timelineTests,
      this.outcomes,
      this._durations,
      this.tagTests,
      this.errorTests,
      this.annotationTests,
      intermediate.map(value => ({
        commitsCount: value.commitsCount,
        durations: H.intersectAll(value.durations, this.timelineTests),
        testOutcomes: T.intersectRanges(value.testOutcomes, this.timelineTests),
      })),
      this.historyOutcomes,
    )
  }

  setHistoryStats(history: SpanStats[]) {
    return new TimelineTestsReport(
      this.timeline,
      this.contribs,
      this.timelineTests,
      this.outcomes,
      this._durations,
      this.tagTests,
      this.errorTests,
      this.annotationTests,
      this.intermediateOutcomes,
      history.map(value => ({
        commitsCount: value.commitsCount,
        durations: H.intersectAll(value.durations, this.timelineTests),
        testOutcomes: T.intersectRanges(value.testOutcomes, this.timelineTests),
      })),
    )
  }

  applyFilter(filter: FilterContext): TimelineTestsReport {
    if (filter.fql.isEmpty())
      return this;
    const newOutcomes = T.intersectRanges(this.outcomes, filter.testsMask);
    if (filter.fql.hasStatusFilters()) {
      for (const outcome of ['expected', 'unexpected', 'skipped', 'flaked', 'regressed'] as const) {
        if (!filter.fql.acceptsOutcome(outcome))
          newOutcomes[outcome] = T.EMPTY_TESTS;
      }
    }

    let newErrorTests = this.errorTests;
    if (filter.fql.hasErrorFilters()) {
      newErrorTests = new Map();
      const includeTests: T.TestRanges[] = [];
      const excludeTests: T.TestRanges[] = [];
      if (filter.fql.matchesError(null) === MatchResult.MATCH)
        includeTests.push(T.flatten(newOutcomes));
      for (const [ error, tests ] of this.errorTests) {
        const result = filter.fql.matchesError(error);
        if (result === MatchResult.MATCH) {
          includeTests.push(tests);
          newErrorTests.set(error, tests);
        } else if (result === MatchResult.EXCLUDE) {
          excludeTests.push(tests);
        }
      }
      T.intersectRangesInplace(newOutcomes, Ranges.unionAll(includeTests));
      T.subtractRangesInplace(newOutcomes, Ranges.unionAll(excludeTests));
    }

    let newTagTests = this.tagTests;
    if (filter.fql.hasTagFilters()) {
      newTagTests = new Map();
      const includeTests: T.TestRanges[] = [];
      const excludeTests: T.TestRanges[] = [];
      if (filter.fql.matchesTag(null) === MatchResult.MATCH)
        includeTests.push(T.flatten(newOutcomes));
      for (const [ tag, tests ] of this.tagTests) {
        const result = filter.fql.matchesTag(tag);
        if (result === MatchResult.MATCH) {
          includeTests.push(tests);
          newTagTests.set(tag, tests);
        } else if (result === MatchResult.EXCLUDE) {
          excludeTests.push(tests);
        }
      }
      T.intersectRangesInplace(newOutcomes, Ranges.unionAll(includeTests));
      T.subtractRangesInplace(newOutcomes, Ranges.unionAll(excludeTests));
    }

    let newAnnotationTests = this.annotationTests;
    if (filter.fql.hasAnnotationFilters()) {
      newAnnotationTests = new Map();
      const includeTests: T.TestRanges[] = [];
      const excludeTests: T.TestRanges[] = [];
      if (filter.fql.matchesAnnotation(null) === MatchResult.MATCH)
        includeTests.push(T.flatten(newOutcomes));
      for (const [ annotation, tests ] of this.annotationTests) {
        const result = filter.fql.matchesAnnotation(annotation);
        if (result === MatchResult.MATCH) {
          includeTests.push(tests);
          newAnnotationTests.set(annotation, tests);
        } else if (result === MatchResult.EXCLUDE) {
          excludeTests.push(tests);
        }
      }
      T.intersectRangesInplace(newOutcomes, Ranges.unionAll(includeTests));
      T.subtractRangesInplace(newOutcomes, Ranges.unionAll(excludeTests));
    }

    if (filter.fql.hasLineFilters()) {
      const ln = this.lineNumbers();
      const testIndexes: S.TestIndex[] = [];
      for (let testIdx = 0 as S.TestIndex; testIdx < ln.length; ++testIdx) {
        if (filter.fql.acceptsLine(ln[testIdx]))
          testIndexes.push(testIdx);
      }
      T.intersectRangesInplace(newOutcomes, Ranges.fromSortedList(testIndexes));
    }

    if (filter.fql.hasDurationFilters()) {
      const { offset, buffer } = this.expandedDurations();
      const testIndexes: S.TestIndex[] = [];
      for (let testIdx = 0 as S.TestIndex; testIdx < buffer.length; ++testIdx) {
        if (filter.fql.acceptsDuration(buffer[testIdx]))
          testIndexes.push(testIdx + offset as S.TestIndex);
      }
      T.intersectRangesInplace(newOutcomes, Ranges.fromSortedList(testIndexes));
    }

    const testsMask = T.flatten(newOutcomes);
    return new TimelineTestsReport(
      this.timeline,
      this.contribs,
      testsMask,
      newOutcomes,
      this._durations.map(d => H.intersect(d, testsMask)),
      filterEntityMap(this.tagTests, testsMask),
      filterEntityMap(newErrorTests, testsMask),
      filterEntityMap(this.annotationTests, testsMask),
      this.intermediateOutcomes.map(value => ({
        commitsCount: value.commitsCount,
        durations: H.intersectAll(value.durations, testsMask),
        testOutcomes: T.intersectRanges(value.testOutcomes, testsMask),
      })),
      this.historyOutcomes.map(value => ({
        commitsCount: value.commitsCount,
        durations: H.intersectAll(value.durations, testsMask),
        testOutcomes: T.intersectRanges(value.testOutcomes, testsMask),
      })),
    );
  }

  testDurationMs(testIdx: S.TestIndex) {
    return H.getSum(this._durations, testIdx) as FlakinessReport.DurationMS;
  }

  private _expandedDurations?: ExpandedHistogram.ExpandedHistogram;
  expandedDurations(): ExpandedHistogram.ExpandedHistogram {
    if (!this._expandedDurations) {
      this._expandedDurations = ExpandedHistogram.sumAll(this._durations);
    }
    return this._expandedDurations;
  }

  private _lineNumbers?: Int32Array;
  lineNumbers(): Int32Array {
    if (!this._lineNumbers)
      this._lineNumbers = CommitAnalyzer.computeLineNumbers(this.contribs);
    return this._lineNumbers;
  }

  private _trend?: ExpandedHistogram.ExpandedHistogram;
  durationTrend() {
    if (!this._trend)
      this._trend = this._computeDurationTrend();
    return this._trend;
  }

  private _computeDurationTrend(): ExpandedHistogram.ExpandedHistogram {
    const domain = Ranges.domain(this.timelineTests);
    if (!domain)
      return ExpandedHistogram.EMPTY;


    // We either compute trend against intermediate outcomes, if it exists,
    // or against history outcomes.
    const baselineOutcomes = this.intermediateOutcomes.length ? this.intermediateOutcomes : this.historyOutcomes.toReversed();

    const N = baselineOutcomes.length;
    const baselineHistograms: H.Histogram<number, number>[][] = [];
    let tests = this.timelineTests;
    for (let i = N - 1; i >= 0 && tests.length; --i) {
      const intermediate = baselineOutcomes[i];
      const f = Ranges.intersect(tests, T.flatten(intermediate.testOutcomes));
      if (!f.length)
        continue;
      tests = Ranges.subtract(tests, f);
      baselineHistograms.push(intermediate.durations.map(h => H.intersect(h, f)));
    }

    const last = this.expandedDurations();
    const baseline = ExpandedHistogram.sumAll(baselineHistograms.flat());

    const offset = domain.min;
    const trend = new Float32Array(domain.max - offset + 1);
    for (const testIdx of Ranges.iterate(this.timelineTests))
      trend[testIdx - offset] = last.buffer[testIdx - baseline.offset] - baseline.buffer[testIdx - last.offset];
    return { buffer: trend, offset };
  }

  wireTestStats(test: WireTypes.Test, testIdx: S.TestIndex): WireTypes.TestStats|undefined {
    const contrib = this.contribs.find(run => T.includes(run.outcomes, testIdx));
    if (!contrib)
      return undefined;
    const fc = contrib.commitAnalyzer;
    const jsonAttempt = contrib.jsonAttempt;
    const outcome: WireTypes.Outcome = (['expected', 'unexpected', 'skipped', 'flaked', 'regressed'] as const)
      .find(outcome => Ranges.includes(this.outcomes[outcome], testIdx))!;
      
      // Fill in annotations
    const annotations: WireTypes.Annotation[] = [];
    for (const [annotation, tests] of this.annotationTests) {
      if (Ranges.includes(tests, testIdx))
        annotations.push(annotation);
    }

    const tags: string[] = [];
    for (const [tag, tests] of this.tagTests) {
      if (Ranges.includes(tests, testIdx))
        tags.push(tag);
    }

    const durationMs = this.testDurationMs(testIdx);

    const baselineRun = this.intermediateOutcomes.length ?
        this.intermediateOutcomes.findLast(x => T.includes(x.testOutcomes, testIdx)) :
        this.historyOutcomes.find(x => T.includes(x.testOutcomes, testIdx));
    const baselineDuration = baselineRun ? H.getSum(baselineRun.durations, testIdx) : 0;

    const contribInfo = fc.contributionInfo(contrib);
    return {
      test,
      category: contribInfo.env.category,
      testIdx,
      envId: contribInfo.env.envId,
      attemptIdx: jsonAttempt.attemptIdx,
      timeline: this.timeline.serialize(),
      lineNumber: fc.lineNumbers[testIdx] as FlakinessReport.Number1Based,
      tags,
      annotations,
      outcome,
      reportBreakdown: this.intermediateOutcomes.map(dayStats => {
        const outcome = (['regressed', 'unexpected', 'expected', 'flaked', 'skipped'] as const).find(outcome => Ranges.includes(dayStats.testOutcomes[outcome], testIdx));
        if (outcome)
          return { outcome, duration: H.getSum(dayStats.durations, testIdx) as FlakinessReport.DurationMS };
        return (dayStats.commitsCount > 0) ? { outcome: 'untested' } : { outcome: 'idle' };
      }),
      dailyHistory: this.historyOutcomes.map(dayStats => {
        const outcome = (['regressed', 'unexpected', 'expected', 'flaked', 'skipped'] as const).find(outcome => Ranges.includes(dayStats.testOutcomes[outcome], testIdx));
        if (outcome)
          return { outcome, duration: H.getSum(dayStats.durations, testIdx) as FlakinessReport.DurationMS };
        return (dayStats.commitsCount > 0) ? { outcome: 'untested' } : { outcome: 'idle' };
      }),
      commitId: fc.commitId,
      runId: contrib.jsonRun.runId,
      durationMs,
      durationChangeMs: durationMs - baselineDuration as FlakinessReport.DurationMS,
      hasImage: Ranges.includes(contribInfo.testsWithImage, testIdx),
      hasTrace: Ranges.includes(contribInfo.testsWithTrace, testIdx),
      hasVideo: Ranges.includes(contribInfo.testsWithVideo, testIdx),
    };
  }
}

function filterEntityMap<Q>(map: Map<Q, T.TestRanges>, mask: T.TestRanges): Map<Q, T.TestRanges> {
  return new Map(
    Array.from(map)
      .map(([e, tests]) => [e, Ranges.intersect(tests, mask)] as const)
      .filter(([e, tests]) => !!tests.length)
  );
}
