import { Multimap } from '@flakiness/shared/common/multimap.js';
import { Ranges } from '../ranges.js';
import { Sequence } from '../sequence.js';

export namespace ExpandedHistogram {
  export type ExpandedHistogram = {
    buffer: Float32Array,
    offset: number,
  }

  export const EMPTY: ExpandedHistogram = {
    buffer: new Float32Array(0),
    offset: 0,
  };

  export function sumAll<D extends number = number, E extends number = number>(histograms: Histogram.Histogram<D, E>[]): ExpandedHistogram {
    histograms = histograms.filter(h => !!h.length);
    if (!histograms.length)
      return EMPTY;
    let min = Infinity;
    let max = -Infinity;
    for (let i = 0; i < histograms.length; ++i) {
      const histogram = histograms[i];
      const d = Histogram.domain(histogram);
      if (!d)
        continue;
      min = Math.min(min, d[0]);
      max = Math.max(max, d[1]);
    }
    const offset = min;
    const N = max - offset + 1;
    const buffer = new Float32Array(N);
    const weightsStart = new Float32Array(N);
    const weightsEnd = new Float32Array(N);
    for (let i = 0; i < histograms.length; ++i) {
      for (let idx = 0; idx < histograms[i].length; ++idx) {
        const weight = histograms[i][idx][0];
        const ranges = histograms[i][idx][1];
        for (let i = 0; i < ranges.length; i += 2) {
          weightsStart[ranges[i] - offset] += weight;
          weightsEnd[ranges[i + 1] - offset] += weight;
        }
      }
    }
    let sum = 0;
    for (let value = 0; value < N; ++value) {
      sum += weightsStart[value];
      buffer[value] += sum;
      sum -= weightsEnd[value];
    }
    return { buffer, offset };
  }
}

export namespace Histogram {
  export type Histogram<D extends number = number, E extends number = number> = [D, Ranges.Ranges<E>][];
  export type CompressedHistogram<D extends number = number, E extends number = number> = [D, Ranges.CompressedRanges<E>][];

  export function compress<D extends number, E extends number>(h: Histogram<D, E>): CompressedHistogram<D, E> {
    return h.map(b => [b[0], Ranges.compress(b[1])] as [D, Ranges.CompressedRanges<E>]);
  }

  export function decompress<D extends number, E extends number>(h: CompressedHistogram<D, E>): Histogram<D, E> {
    return h.map(b => [b[0], Ranges.decompress(b[1])] as [D, Ranges.Ranges<E>]);
  }

  export function elements<E extends number = number>(h: Histogram<any, E>): Ranges.Ranges<E> {
    return Ranges.unionAll(h.map(bucket => bucket[1]));
  }

  export function intersect<D extends number = number, E extends number = number>(h: Histogram<D, E>, r: Ranges.Ranges<E>): Histogram<D, E> {
    return h.map(bucket => [bucket[0], Ranges.intersect(bucket[1], r)] as [D, Ranges.Ranges<E>]).filter(bucket => !!bucket[1].length);
  }

  export function intersectAll<D extends number = number, E extends number = number>(hists: Histogram<D, E>[], r: Ranges.Ranges<E>): Histogram<D, E>[] {
    return hists.map(h => h.map(bucket => [bucket[0], Ranges.intersect(bucket[1], r)] as [D, Ranges.Ranges<E>]).filter(bucket => !!bucket[1].length)).filter(h => !!h.length);
  }

  export function sum<D extends number = number, E extends number = number>(h1: Histogram<D, E>, h2: Histogram<D, E>): Histogram<D, E> {
    if (!h1.length)
      return h2;
    if (!h2.length)
      return h1;

    // If we have N buckets, each bucket has M ranges, then naive implementation is
    // O(N * N * M).
    // Now, we can do much better: use heap to pull ranges one-by-one from buckets.
    // This will yield us O(N * log N + M * log M) in result.
    const s1 = Sequence.merge(h1.map(b => createBucketSequence(b[0], b[1])), ([d1, a1, b1], [d2, a2, b2]) => a1 - a2);
    const s2 = Sequence.merge(h2.map(b => createBucketSequence(b[0], b[1])), ([d1, a1, b1], [d2, a2, b2]) => a1 - a2);
    const i1 = s1.seek(0);
    const i2 = s2.seek(0);
    let v1 = i1.next();
    let v2 = i2.next();
    const buckets = new Map<D, Ranges.Ranges<E>>();
    const getBucketTests = (duration: D) => {
      let r = buckets.get(duration);
      if (!r) {
        r = [] as any as Ranges.Ranges<E>;
        buckets.set(duration, r);
      }
      return r;
    }
    while (!v1.done && !v2.done) {
      while (!v1.done && v1.value[2] < v2.value[1]) {
        const r = getBucketTests(v1.value[0]);
        r.push(v1.value[1], v1.value[2]);
        v1 = i1.next();
      }
      while (!v2.done && !v1.done && v2.value[2] < v1.value[1]) {
        const r = getBucketTests(v2.value[0]);
        r.push(v2.value[1], v2.value[2]);
        v2 = i2.next();
      }
      // Now if we have v1 and v2 that intersect..
      if (!v1.done && !v2.done && !(v1.value[2] < v2.value[1] || v2.value[2] < v1.value[1])) {
        const [d1, a1, b1] = v1.value;
        const [d2, a2, b2] = v2.value;
        if (a1 < a2) {
          const r = getBucketTests(d1);
          r.push(a1, a2 - 1 as E);
          v1.value[1] = a2;
        } else if (a2 < a1) {
          const r = getBucketTests(d2);
          r.push(a2, a1 - 1 as E);
          v2.value[1] = a1;
        }
        const r = getBucketTests(d1 + d2 as D);
        if (b1 < b2) {
          r.push(v1.value[1], b1);
          v2.value[1] = b1 + 1 as E;
          v1 = i1.next();
        } else if (b2 < b1) {
          r.push(v1.value[1], b2);
          v1.value[1] = b2 + 1 as E;
          v2 = i2.next();
        } else {
          r.push(v1.value[1], b2);
          v1 = i1.next();
          v2 = i2.next();
        }
      }
    }
    while (!v1.done) {
      const r = getBucketTests(v1.value[0]);
      r.push(v1.value[1], v1.value[2]);
      v1 = i1.next();
    }
    while (!v2.done) {
      const r = getBucketTests(v2.value[0]);
      r.push(v2.value[1], v2.value[2]);
      v2 = i2.next();
    }
    return [...buckets.entries()];
  }

  /*

  Some Definitions:

  0. We have 2 fundamental constants: MAX_ERR = 0.05, MIN_R = 50.
  1. For the segment [Ai, Bi], we introduce the following concepts:
    - The center Ci = (Ai + Bi) / 2
    - The radius Ri = (Bi - Ai) / 2.
  2. A segment [Ai, Bi] is called "good" if Ri <= Max(MAX_ERR * Ci, MIN_R)
  3. A point X belongs to [Ai, Bi] if Ai <= x <= Bi.
  4. For the set of points Xi that belong to the segment [Ai, Bi], MSE (Mean Squared Error) is defined as MSE = Sum (Xi - Ci)^2.

  Task:

  Given an array of non-negative integers, N ~ 100,000, where all numbers range from 0 to 10,000,000:
  Cover these numbers with "good" segments [ai, bi] such that:
  1. The number of segments is minimized.
  2. The total MSE for all segments is minimized.

  Logical sense: this is a "compression" task - encode all timings with a 5% error rate.
  With this encoding and MAX_ERR=0.05 & MIN_R = 0, we can encode all numbers from 0 to 10,000,000 into 151 segments.

  */
  export function compute<D extends number, E extends number>(scores: Map<E, D>, options: {
    maxRelativeError: number, // 0-1
    minRadius: D,
  }): Histogram.Histogram<D, E> {
    // TODO: this is a naive solution. Would something else work nicer?
    const scoreToElements = new Multimap<D, E>();
    for (const [testIdx, timing] of scores)
      scoreToElements.set(timing, testIdx);

    const clusters: { scores: D[], scoreSum: D }[] = [];

    for (const t of [...scoreToElements.keys()].sort((a, b) => a - b)) {
      const lastCluster = clusters.at(-1);
      if (!lastCluster) {
        clusters.push({
          scores: [t],
          scoreSum: t,
        });
        continue;
      }

      const clusterPos = (lastCluster.scoreSum + t) / (lastCluster.scores.length + 1);
      const clusterR = Math.max(options.minRadius, options.maxRelativeError * clusterPos);

      if ((t - lastCluster.scores[0]) > 2 * clusterR) {
        clusters.push({
          scores: [t],
          scoreSum: t,
        });
      } else {
        lastCluster.scores.push(t);
        lastCluster.scoreSum = (lastCluster.scoreSum + t) as D;
      }
    }

    const result = new Multimap<D, E>();
    for (const cluster of clusters) {
      let clusterPos = (cluster.scoreSum / cluster.scores.length);
      // If duration is less than 1ms, then round to nanoseconds.
      if (clusterPos < 1)
        clusterPos = Math.round(clusterPos * 1000) / 1000;
      else
        clusterPos = Math.round(clusterPos);
      for (const timing of cluster.scores)
        result.setAll(clusterPos as D, scoreToElements.getAll(timing))
    }

    return result.map((a, b) => [a, Ranges.fromList([...b])] as [D, Ranges.Ranges<E>]).sort((a, b) => a[0] - b[0]);
  }

  export function domain(h: Histogram): [number, number] | undefined {
    let min = Infinity;
    let max = -Infinity;
    for (let i = 0; i < h.length; ++i) {
      const r = h[i][1];
      if (!r.length)
        continue;
      min = Math.min(min, r[0]);
      max = Math.max(max, r[r.length - 1]);
    }
    return Object.is(min, Infinity) || Object.is(max, -Infinity) ? undefined : [min, max];
  }

  export function get<D extends number, E extends number>(h: Histogram<D, E>, e: E): D|undefined {
    const bucket = h.find(bucket => Ranges.includes(bucket[1], e));
    return bucket ? bucket[0] : undefined;
  }

  export function getSum<D extends number, E extends number>(hs: Histogram<D, E>[], e: E): D {
    let sum = 0;
    for (let hidx = 0; hidx < hs.length; ++hidx) {
      const bucket = hs[hidx].find((bucket) => Ranges.includes(bucket[1], e));
      sum += bucket ? bucket[0] : 0;
    }
    return sum as D;
  }

  export function total<D extends number, E extends number>(h: Histogram<D, E>): D {
    let total = 0;
    for (let bucketIdx = 0; bucketIdx < h.length; ++bucketIdx)
      total += Ranges.cardinality(h[bucketIdx][1]) * h[bucketIdx][0];
    return total as D;
  }

  export function totalSum<D extends number, E extends number>(h: Histogram<D, E>[]): D {
    let total = 0;
    for (let hidx = 0; hidx < h.length; ++hidx) {
      for (let bucketIdx = 0; bucketIdx < h[hidx].length; ++bucketIdx)
        total += Ranges.cardinality(h[hidx][bucketIdx][1]) * h[hidx][bucketIdx][0];
    }
    return total as D;
  }

  /**
   * Creates a function that returns the cumulative weight at any given index.
   * Returns undefined for indices outside the histogram domain, and 0 for indices
   * inside the domain but not covered by any histogram ranges.
   * The function takes O(N) time to initialize, but then responds in O(1).
   */
  export function expand(h: Histogram<number, number>): (idx: number) => number|undefined {
    const d = domain(h);
    if (!d)
      return () => undefined;
    const offset = d[0];
    const N = d[1] - d[0] + 1;
    const weightsStart = new Int32Array(N);
    const weightsEnd = new Int32Array(N);
    for (let idx = 0; idx < h.length; ++idx) {
      const weight = h[idx][0];
      const ranges = h[idx][1];
      for (let i = 0; i < ranges.length; i += 2) {
        weightsStart[ranges[i] - offset] += weight;
        weightsEnd[ranges[i + 1] - offset] += weight;
      }
    }
    const result = new Int32Array(N);
    let sum = 0;
    for (let value = 0; value < N; ++value) {
      sum += weightsStart[value];
      result[value] += sum;
      sum -= weightsEnd[value];
    }
    return (idx: number) => result[idx - offset];
  }
}

function createBucketSequence<D extends number, T extends number>(durationMs: D, tests: Ranges.Ranges<T>) {
  return new Sequence<[D, T, T]>(function seek(idx): Iterator<[D, T, T]> {
    return {
      next() {
        if (idx * 2 >= tests.length)
          return { done: true, value: undefined };
        const value = [durationMs, tests[idx * 2], tests[ idx * 2 + 1]] as [D, T, T];
        ++idx;
        return { done: false, value };
      },
    }
  }, tests.length >> 1);
}

