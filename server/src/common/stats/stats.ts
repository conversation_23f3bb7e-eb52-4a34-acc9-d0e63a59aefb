import { FlakinessReport } from '@flakiness/sdk/browser';
import { Brand, xxHash, xxHashObject } from '@flakiness/shared/common/utils.js';
import { Ranges } from '../ranges.js';
import { WireTypes } from '../wireTypes.js';

export namespace Stats {
  export const STATS_VERSION = 14;

  export type CommitId = FlakinessReport.CommitId;
  export type CommitIndex = Brand<number, 'Stats.CommitIndex'>;

  export type RunId = Brand<number, 'Stats.RunId'>;
  export type ReportIndex = Brand<number, 'Stats.ReportIndex'>;

  export type TestId = Brand<string, 'Stats.TestId'>;
  export type TestIndex = Brand<number, 'Stats.TestIndex'>;

  export type EnvId = Brand<string, 'Stats.EnvId'>;
  export type EnvIndex = Brand<number, 'Stats.EnvIndex'>;

  export type TimelineIndex = Brand<number, 'Stats.TimelineIndex'>;

  export type ErrorId = Brand<string, 'Stats.ErrorId'>;
  export type ErrorIndex = Brand<number, 'Stats.ErrorIndex'>;

  export type AnnotationId = Brand<string, 'Stats.AnnotationId'>;
  export type AnnotationIndex = Brand<number, 'Stats.AnnotationIndex'>;

  export type AnnotationsProfileId = Brand<string, 'Stats.AnnotationsProfileId'>;
  export type AnnotationsProfileIndex = Brand<number, 'Stats.AnnotationsProfileIndex'>;

  export type RunIndex = Brand<number, 'Stats.RunIndex'>;

  export type JSONTests = {
    // Laying out test parts separately improves compression of a 72 reports in a single commit on 13%.
    testTitles: string[][],
    testFilePaths: FlakinessReport.GitFilePath[],
  }

  export type JSONEnvironment = {
    name?: string,
    category: string,
    configPath?: FlakinessReport.GitFilePath;
    systemData: {
      osName: string,
      osVersion: string,
      osArch: string,
    },
    userSuppliedData: Record<string, string|boolean|number>,
  }

  export type JSONError = {
    message?: string,
    value?: string,
  }

  export type JSONAnnotation = {
    type: string,
    description?: string,
  }

  export type JSONRunAnnotation = {
    annotationIdx: AnnotationIndex,
    tests: Ranges.CompressedRanges<TestIndex>,
  };

  // For each report, the set of annotations for different tests has a tendency to repeat itself.
  // So we store a set of annotations-for-tests as "profiles".
  export type JSONAnnotationsProfile = JSONRunAnnotation[];

  export type JSONData = {
    // schema version
    version: number,

    testLineNumbers: FlakinessReport.Number1Based[],

    environments: JSONEnvironment[],

    errors: JSONError[],

    annotations: JSONAnnotation[],
    annotationProfiles: JSONAnnotationsProfile[],

    commits: JSONCommit[],
  }

  export type JSONCommit = {
    commitId: CommitId,
    changedLineNumbers?: {
      lineDelta: number,
      affectedTests: Ranges.CompressedRanges<TestIndex>,
    }[],
    tags: {
      tag: string,
      tests: Ranges.CompressedRanges<TestIndex>,
    }[],
    runs: JSONRun[];
  }

  export type JSONRun = {
    url: string,
    runId: RunId,
    startTimestamp: FlakinessReport.UnixTimestampMS,
    duration: FlakinessReport.DurationMS,

    attempts: JSONAttempt[],
  };

  /**
   * This stores all test attempts for a report. Each attempt has an environment
   * and attemptIdx (deafults to 0 for the first attempt).
   * Usually, the same test is retried in the same environment and has the same
   * annotations, but nevertheless we still store environmentIdx for each attempt to keep the data structure simple.
   */
  export type JSONAttempt = {
    environmentIdx: EnvIndex,
    attemptIdx?: number,

    expectedTests?: Ranges.CompressedRanges<TestIndex>,
    unexpectedTests?: Ranges.CompressedRanges<TestIndex>,
    skippedTests?: Ranges.CompressedRanges<TestIndex>,
    flakedTests?: Ranges.CompressedRanges<TestIndex>,

    // Artifacts
    testsWithVideo?: Ranges.CompressedRanges<TestIndex>,
    testsWithTrace?: Ranges.CompressedRanges<TestIndex>,
    testsWithImage?: Ranges.CompressedRanges<TestIndex>,

    errors?: JSONRunError[],
    annotationsProfileIdx: AnnotationsProfileIndex,

    durationBuckets: FlakinessReport.DurationMS[],
    durationBucketTests: Ranges.CompressedRanges<TestIndex>[],
  }

  export type JSONRunError = {
    errorIdx: ErrorIndex,
    tests: Ranges.CompressedRanges<TestIndex>,
  };

  export function createEmptyStats(): JSONData {
    return {
      version: STATS_VERSION,
      testLineNumbers: [],
      annotations: [],
      annotationProfiles: [],
      environments: [],
      errors: [],
      commits: [],
    };
  }

  export function createEmptyTests(): JSONTests {
    return {
      testTitles: [],
      testFilePaths: [],
    };
  }

  export function flakinessEnvToJSONEnv(report: FlakinessReport.Report, env: FlakinessReport.Environment): JSONEnvironment {
    return {
      name: env.name,
      category: report.category,
      configPath: report.configPath,
      // Explicitly copy nested object fields: if report.environment gets extended,
      // then we have to explicitly extend the JSONEnvironment type to support it.
      systemData: {
        osArch: env.systemData?.osArch ?? 'unknown',
        osName: env.systemData?.osName ?? 'unknown',
        osVersion: env.systemData?.osVersion ?? 'unknown',
      },
      // Currently, we only support a limited set of values for the `userSuppliedData`.
      userSuppliedData: Object.fromEntries(Object.entries(env.userSuppliedData ?? {}).filter(([key, value]) => ['string', 'number', 'boolean'].includes(typeof value))),
    };
  }

  export function jsonEnvToWireEnv(jsonEnv: JSONEnvironment): WireTypes.RunEnvironment {
    return {
      envId: computeEnvId(jsonEnv),
      category: jsonEnv.category,
      name: jsonEnv.name,
      configPath: jsonEnv.configPath,
      systemData: {
        osArch: jsonEnv.systemData.osArch,
        osName: jsonEnv.systemData.osName,
        osVersion: jsonEnv.systemData.osVersion,
      },
      userSuppliedData: jsonEnv.userSuppliedData,
    };
  }

  export function computeTestId(options: { file?: string, titles: string[] }): TestId {
    return xxHash([
      options.file ?? '', 
      ...options.titles
    ]) as TestId;
  }

  export function flakinessTestToWireTypesTest(test: FlakinessReport.Test, parentSuites: FlakinessReport.Suite[]): WireTypes.Test {
    const suiteTitles = parentSuites.filter(suite => suite.type !== 'file').map(suite => suite.title);
    const titles = [...suiteTitles, test.title];
    const testId = computeTestId({
      file: test.location?.file,
      titles: titles,
    });
    return {
      filePath: test.location?.file ?? '' as FlakinessReport.GitFilePath,
      testId,
      titles,
    };
  }

  export function computeEnvId(env: JSONEnvironment): EnvId {
    return xxHashObject(env) as EnvId;
  }

  export function computeErrorId(error: JSONError): ErrorId {
    return xxHashObject(error) as ErrorId;
  }

  export function computeAnnotationId(annotation: JSONAnnotation): AnnotationId {
    return xxHashObject(annotation) as AnnotationId;
  }

  export function computeAnnotationsProfileId(profile: JSONAnnotationsProfile): AnnotationsProfileId {
    profile.sort((a, b) => a.annotationIdx - b.annotationIdx);
    return xxHash(profile.map(xxHashObject)) as AnnotationsProfileId;
  }

  export function computeDataSignature(s: JSONData) {
    const runIds = s.commits.map(commit => commit.runs.map(run => run.runId)).flat();
    runIds.sort();
    return xxHash(runIds.map(report => String(report)));
  }
}
