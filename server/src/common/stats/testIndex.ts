import { FlakinessReport, ReportUtils } from '@flakiness/report';
import { xxHash } from '@flakiness/shared/common/utils.js';
import { Query } from '../fql/query.js';
import { Ranges } from '../ranges.js';
import { WireTypes } from '../wireTypes.js';
import { Stats as S } from './stats.js';
import { TestOutcomes as T } from './testOutcomes.js';

export type FilterContext = {
  tests: WireTypes.Test[],
  testsMask: T.TestRanges,
  fql: Query,
  etag: string,
}

export class TestIndex {
  static etagFilterContext(testIndex: TestIndex, fql: Query) {
    return xxHash([testIndex.etag(), fql.etag()]);
  }

  private _tests: WireTypes.Test[] = [];
  private _testIdToIndex = new Map<S.TestId, S.TestIndex>();
  private _testScores?: Int32Array;
  private _etag: string|undefined;

  constructor(jsonTests?: S.JSONTests) {
    jsonTests ??= S.createEmptyTests();
    for (let i = 0 as S.TestIndex; i < jsonTests.testTitles.length; ++i) {
      const filePath = jsonTests.testFilePaths[i];
      const titles = jsonTests.testTitles[i];
      const testId = S.computeTestId({
        file: filePath,
        titles,
      });
      this._testIdToIndex.set(testId, i);
      this._tests.push({
        filePath,
        titles,
        testId,
      });
    }
  }

  etag() {
    if (!this._etag)
      this._etag = xxHash(this._tests.map(t => t.testId));
    return this._etag;
  }

  testScores(): Int32Array {
    if (!this._testScores) {
      // Compute test scores. These are their sorted order.
      // For certain projects, i.e. Kotlin, which has 300k tests, each with VERY long
      // name, this might take ~200ms.
      const testIndexes = new Int32Array(this._tests.length);
      for (let i = 0; i < testIndexes.length; ++i)
        testIndexes[i] = i;

      const testComparator = (idx1: number, idx2: number) => {
        const a = this._tests[idx1];
        const b = this._tests[idx2];
        for (let i = 0; i < Math.min(a.titles.length, b.titles.length); ++i) {
          if (a.titles[i] !== b.titles[i])
            return a.titles[i] < b.titles[i] ? -1 : 1;
        }
        if (a.titles.length !== b.titles.length)
          return a.titles.length - b.titles.length;

        if (a.filePath !== b.filePath)
          return a.filePath < b.filePath ? -1 : 1;
        return 0;
      }

      testIndexes.sort(testComparator);
      this._testScores = new Int32Array(this._tests.length);
      let lastScore = 0;
      for (let i = 1; i < this._testScores.length; ++i) {
        const r = testComparator(testIndexes[i - 1], testIndexes[i]);
        lastScore = r === 0 ? lastScore : lastScore + 1;
        this._testScores[testIndexes[i]] = lastScore;
      }
    }
    return this._testScores;
  }

  createFilterContext(fql: Query, etag: string = TestIndex.etagFilterContext(this, fql)) {
    let testsMask = [0, this._tests.length - 1] as T.TestRanges;
    if (fql.hasTestFilters()) {
      const indexes: S.TestIndex[] = [];
      for (let tidx = 0 as S.TestIndex; tidx < this._tests.length; ++tidx) {
        if (fql.acceptsTest(this._tests[tidx]))
          indexes.push(tidx);
      }
      testsMask = Ranges.fromSortedList(indexes);
    }

    return {
      tests: this._tests,
      testsMask,
      fql,
      etag,
    };
  }

  testIndex(testId: S.TestId) {
    return this._testIdToIndex.get(testId);
  }

  tests() {
    return this._tests;
  }

  // Since index might be mutated later down while adding this report, and server might add new tests
  // as well, we need to make sure that all methods that return "expanded" test outcomes from server - i.e. those
  // containing actual test ranges - are pre-cut to include tests only until this index.
  // All these methods have required `cutoffTestIndex` parameter for this purpose.
  cutoffIndex() {
    return (this._tests.length - 1) as S.TestIndex;
  }

  countTests() {
    return this._tests.length;
  }

  addReport(report: FlakinessReport.Report) {
    // Add missing tests.
    ReportUtils.visitTests(report, (test, parentSuites) => {
      const wireTest = S.flakinessTestToWireTypesTest(test, parentSuites);
      let testIdx = this._testIdToIndex.get(wireTest.testId);
      if (testIdx === undefined) {
        testIdx = this._tests.push(wireTest) - 1 as S.TestIndex;
        this._testIdToIndex.set(wireTest.testId, testIdx);
        this._etag = undefined;
      }
    });
  }

  serialize(): S.JSONTests {
    const result: S.JSONTests = {
      testFilePaths: [],
      testTitles: [],
    }
    for (const test of this._tests) {
      result.testFilePaths.push(test.filePath);
      result.testTitles.push(test.titles);
    }
    return result;
  }
}
