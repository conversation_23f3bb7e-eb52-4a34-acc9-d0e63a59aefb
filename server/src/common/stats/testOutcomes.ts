import { Ranges } from "../ranges.js";
import { WireTypes } from "../wireTypes.js";
import { Stats } from "./stats.js";

export namespace TestOutcomes {
  export type TestRanges = Ranges.Ranges<Stats.TestIndex>;
  export const EMPTY_TESTS = [] as number[] as TestRanges;
  export type TestOutcomes = {
    regressed: TestRanges,
    unexpected: TestRanges,
    expected: TestRanges,
    skipped: TestRanges,
    flaked: TestRanges,
  };

  export type TestOutcomeCounts = {
    regressed: number,
    expected: number,
    unexpected: number,
    skipped: number,
    flaked: number,
  }

  export function regressifyInplace(outcomes: TestOutcomes, previouslyUnhealthyTests: TestRanges) {
    const newRegressions = Ranges.subtract(outcomes.unexpected, previouslyUnhealthyTests);
    if (newRegressions.length) {
      outcomes.regressed = Ranges.union(outcomes.regressed, newRegressions);
      outcomes.unexpected = Ranges.intersect(outcomes.unexpected, previouslyUnhealthyTests);
    }
  }

  export function regressify(outcomes: TestOutcomes, previouslyUnhealthyTests: TestRanges): TestOutcomes {
    const newRegressions = Ranges.subtract(outcomes.unexpected, previouslyUnhealthyTests);
    return {
      regressed: Ranges.union(outcomes.regressed, newRegressions),
      unexpected: Ranges.intersect(outcomes.unexpected, previouslyUnhealthyTests),
      expected: outcomes.expected,
      flaked: outcomes.flaked,
      skipped: outcomes.skipped,
    };
  }

  export function includes(outcomes: TestOutcomes, testIdx: Stats.TestIndex) {
    return Ranges.includes(outcomes.regressed, testIdx) ||
        Ranges.includes(outcomes.unexpected, testIdx) ||
        Ranges.includes(outcomes.flaked, testIdx) ||
        Ranges.includes(outcomes.expected, testIdx) ||
        Ranges.includes(outcomes.skipped, testIdx);
  }

  export function unhealthyTests(outcomes: TestOutcomes): TestRanges {
    return Ranges.unionAll([outcomes.regressed, outcomes.flaked, outcomes.unexpected]);
  }

  export function unhealthyTestsForAggregatedRun(commitOutcomes: TestOutcomes): TestRanges {
    // A test inside a run should be marked as a regression
    // if and only if:
    // 1. this test is UNEXPECTED inside the run
    // 2. this test is REGRESSED in the commit
    //
    // Since "unhealthyTests" are the tests that SHOULD NOT be promoted to the "regressed",
    // so for the run, we should have a complement of the commit's regressed tests.
    return Ranges.complement(commitOutcomes.regressed);
  }

  export function newOutcomeCounts(outcomes?: TestOutcomes): TestOutcomeCounts {
    return {
      regressed: outcomes ? Ranges.cardinality(outcomes.regressed) : 0, 
      expected: outcomes ? Ranges.cardinality(outcomes.expected) : 0,
      unexpected: outcomes ? Ranges.cardinality(outcomes.unexpected) : 0,
      skipped: outcomes ? Ranges.cardinality(outcomes.skipped) : 0,
      flaked: outcomes ? Ranges.cardinality(outcomes.flaked) : 0,
    };
  }

  export function accumulateOutcomeCounts(acc: TestOutcomeCounts, other: TestOutcomeCounts) {
    acc.regressed += other.regressed;
    acc.expected += other.expected;
    acc.unexpected += other.unexpected;
    acc.skipped += other.skipped;
    acc.flaked += other.flaked;
  }

  export function cloneOutcomes(other: TestOutcomes): TestOutcomes {
    return {
      regressed: other.regressed,
      expected: other.expected,
      unexpected: other.unexpected,
      skipped: other.skipped,
      flaked: other.flaked,
    }
  }

  export function newOutcomes(): TestOutcomes {
    return {
      regressed: Ranges.EMPTY as TestRanges,
      expected: Ranges.EMPTY as TestRanges,
      unexpected: Ranges.EMPTY as TestRanges,
      skipped: Ranges.EMPTY as TestRanges,
      flaked: Ranges.EMPTY as TestRanges,
    }
  }

  export function getTestOutcome(outcomes: TestOutcomes, testIndex: Stats.TestIndex): WireTypes.Outcome|undefined {
    if (Ranges.includes(outcomes.regressed, testIndex))
      return 'regressed';
    if (Ranges.includes(outcomes.unexpected, testIndex))
      return 'unexpected';
    if (Ranges.includes(outcomes.expected, testIndex))
      return 'expected';
    if (Ranges.includes(outcomes.skipped, testIndex))
      return 'skipped';
    if (Ranges.includes(outcomes.flaked, testIndex))
      return 'flaked';
  }

  /**
   * Make sure the outcome sets are disjoin, i.e. every test exists
   * in only one of the outcome sets.
   * 
   * Outcomes cmmonly require normalization after a bunch of unions.
   */
  export function normalizeInplace(outcomes: TestOutcomes) {
    let union = outcomes.regressed;
    outcomes.unexpected = Ranges.subtract(outcomes.unexpected, union);
    union = Ranges.union(union, outcomes.unexpected);
    outcomes.flaked = Ranges.subtract(outcomes.flaked, union);
    union = Ranges.union(union, outcomes.flaked);
    outcomes.expected = Ranges.subtract(outcomes.expected, union);
    union = Ranges.union(union, outcomes.expected);
    outcomes.skipped = Ranges.subtract(outcomes.skipped, union);
  }

  /**
   * The "overlap-as-flaky" normalization brings tests that exist in both "expected"
   * and "unexpected" sets and marks those as "flaky".
   */
  export function addOverlapAsFlakyInplace(outcomes: TestOutcomes) {
    const allExpected = Ranges.unionAll([outcomes.flaked, outcomes.expected, outcomes.skipped]);
    const allUnexpected = Ranges.union(outcomes.unexpected, outcomes.regressed);
    const unexpectedToFlaked = Ranges.intersect(allExpected, allUnexpected);
    outcomes.regressed = Ranges.subtract(outcomes.regressed, unexpectedToFlaked);
    outcomes.unexpected = Ranges.subtract(outcomes.unexpected, unexpectedToFlaked);
    outcomes.expected = Ranges.subtract(outcomes.expected, unexpectedToFlaked);
    outcomes.skipped = Ranges.subtract(outcomes.skipped, unexpectedToFlaked);
    outcomes.flaked = Ranges.union(outcomes.flaked, unexpectedToFlaked);
  }

  export function unionInplace(acc: TestOutcomes, other: TestOutcomes) {
    acc.regressed = Ranges.union(acc.regressed, other.regressed); 
    acc.expected = Ranges.union(acc.expected, other.expected);
    acc.unexpected = Ranges.union(acc.unexpected, other.unexpected);
    acc.flaked = Ranges.union(acc.flaked, other.flaked);
    acc.skipped = Ranges.union(acc.skipped, other.skipped);
    return acc;
  }

  export function union(one: TestOutcomes, another: TestOutcomes) {
    return {
      regressed: Ranges.union(one.regressed, another.regressed),
      expected: Ranges.union(one.expected, another.expected),
      unexpected: Ranges.union(one.unexpected, another.unexpected),
      flaked: Ranges.union(one.flaked, another.flaked),
      skipped: Ranges.union(one.skipped, another.skipped),
    };
  }

  export function unionAll(outcomes: TestOutcomes[]): TestOutcomes {
    const acc = newOutcomes();
    for (const outcome of outcomes)
      unionInplace(acc, outcome);
    return acc;
  }

  export function intersectRanges(outcomes: TestOutcomes, ranges: TestRanges): TestOutcomes {
    return {
      regressed: Ranges.intersect(outcomes.regressed, ranges),
      unexpected: Ranges.intersect(outcomes.unexpected, ranges),
      expected: Ranges.intersect(outcomes.expected, ranges),
      skipped: Ranges.intersect(outcomes.skipped, ranges),
      flaked: Ranges.intersect(outcomes.flaked, ranges),
    };
  }

  export function capAt(outcomes: TestOutcomes, cutoffTestIndex: Stats.TestIndex): TestOutcomes {
    return {
      regressed: Ranges.capAt(outcomes.regressed, cutoffTestIndex),
      unexpected: Ranges.capAt(outcomes.unexpected, cutoffTestIndex),
      expected: Ranges.capAt(outcomes.expected, cutoffTestIndex),
      skipped: Ranges.capAt(outcomes.skipped, cutoffTestIndex),
      flaked: Ranges.capAt(outcomes.flaked, cutoffTestIndex),
    };
  }

  export function intersectRangesInplace(outcomes: TestOutcomes, ranges: TestRanges) {
    outcomes.regressed = Ranges.intersect(outcomes.regressed, ranges);
    outcomes.unexpected = Ranges.intersect(outcomes.unexpected, ranges);
    outcomes.expected = Ranges.intersect(outcomes.expected, ranges);
    outcomes.skipped = Ranges.intersect(outcomes.skipped, ranges);
    outcomes.flaked = Ranges.intersect(outcomes.flaked, ranges);
  }

  export function subtractRangesInplace(outcomes: TestOutcomes, ranges: TestRanges) {
    outcomes.regressed = Ranges.subtract(outcomes.regressed, ranges);
    outcomes.unexpected = Ranges.subtract(outcomes.unexpected, ranges);
    outcomes.expected = Ranges.subtract(outcomes.expected, ranges);
    outcomes.skipped = Ranges.subtract(outcomes.skipped, ranges);
    outcomes.flaked = Ranges.subtract(outcomes.flaked, ranges);
  }

  export function subtractRanges(outcomes: TestOutcomes, ranges: TestRanges): TestOutcomes {
    return {
      regressed: Ranges.subtract(outcomes.regressed, ranges),
      unexpected: Ranges.subtract(outcomes.unexpected, ranges),
      expected: Ranges.subtract(outcomes.expected, ranges),
      skipped: Ranges.subtract(outcomes.skipped, ranges),
      flaked: Ranges.subtract(outcomes.flaked, ranges),
    }
  }

  export function intersectWithOutcomes(a: TestOutcomes, b: TestOutcomes): TestOutcomes {
    return {
      regressed: Ranges.intersect(a.regressed, b.regressed),
      unexpected: Ranges.intersect(a.unexpected, b.unexpected),
      expected: Ranges.intersect(a.expected, b.expected),
      skipped: Ranges.intersect(a.skipped, b.skipped),
      flaked: Ranges.intersect(a.flaked, b.flaked),
    };
  }

  export function subtractOutcomes(a: TestOutcomes, b: TestOutcomes): TestOutcomes {
    return {
      regressed: Ranges.subtract(a.regressed, b.regressed),
      unexpected: Ranges.subtract(a.unexpected, b.unexpected),
      expected: Ranges.subtract(a.expected, b.expected),
      skipped: Ranges.subtract(a.skipped, b.skipped),
      flaked: Ranges.subtract(a.flaked, b.flaked),
    };
  }

  export function flatten(outcomes: TestOutcomes): TestRanges {
    return Ranges.unionAll([outcomes.expected, outcomes.flaked, outcomes.skipped, outcomes.unexpected, outcomes.regressed]);
  }

  export function isEmptyOutcomes(x: TestOutcomes) {
    return x.regressed.length === 0 &&
      x.expected.length === 0 &&
      x.flaked.length === 0 &&
      x.skipped.length === 0 &&
      x.unexpected.length === 0
    ;
  }

  export function isEmptyOutcomeCounts(x: TestOutcomeCounts) {
    return x.regressed === 0 &&
      x.expected === 0 &&
      x.flaked === 0 &&
      x.skipped === 0 &&
      x.unexpected === 0
    ;
  }

  export function sumAllCounts(x: TestOutcomeCounts) {
    return x.regressed + x.expected + x.flaked + x.skipped + x.unexpected;
  }

}