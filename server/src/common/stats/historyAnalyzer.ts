import { xxHashObject } from '@flakiness/shared/common/utils.js';
import { SyncComputationCache } from '../computationCache.js';
import { IntervalTree } from '../intervalTree.js';
import { Ranges } from '../ranges.js';
import { Timeline } from '../timeline/timeline.js';
import { WireTypes } from '../wireTypes.js';
import { CommitAnalyzer } from './commitAnalyzer.js';
import { SpanAnalyzer, SpanStats } from './spanAnalyzer.js';
import { Stats as S, Stats } from './stats.js';
import { TestOutcomes as T } from './testOutcomes.js';

/**
 * History analyzer is responsible for the stats that will be used
 * to regressify outcomes.
 */
export class HistoryAnalyzer {
  static etag(days: SpanAnalyzer[], historyDepthDays: number) {
    return xxHashObject({
      spans: days.map(day => day.etag()),
      historyDepthDays,
    });
  }

  private _intervalTree = new SyncComputationCache<Timeline, IntervalTree<T.TestRanges>>({
    size: Timeline.MAX_TIMELINES,
    etag: timeline => timeline.etag(),
    compute: timeline => {
      const unhealthyTests = this._dailyAnalyzers.map(analyzer => analyzer.unhealthyTests(timeline));
      return new IntervalTree(unhealthyTests, (a, b) => Ranges.union(a, b), T.EMPTY_TESTS);
    }
  });

  private _unhealthyTests = new SyncComputationCache<{ timeline: Timeline, commitId: Stats.CommitId }, T.TestRanges>({
    size: Timeline.MAX_TIMELINES * 200,
    etag: ({ timeline, commitId }) => xxHashObject({
      timeline: timeline.etag(),
      commitId
    }),
    compute: ({ timeline, commitId }) => {
      const commitDay = this._commitIdToDayIndex.get(commitId);
      if (commitDay === undefined)
        return T.EMPTY_TESTS;
      const sinceDay = commitDay + 1;
      const untilDay = Math.min(sinceDay + this._historyDepthDays - 1, this._dailyAnalyzers.length - 1);
      if (sinceDay > untilDay)
        return T.EMPTY_TESTS;
      const tree = this._intervalTree.get(timeline);
      const historyUnhealthy = tree.query(sinceDay, untilDay);
      const span = this._dailyAnalyzers[commitDay];
      const previousCommit = span.parentCommit(commitId);
      if (!previousCommit)
        return historyUnhealthy;
      return Ranges.union(historyUnhealthy, span.unhealthyTests(timeline, previousCommit));
    }
  });

  private _commitIdToDayIndex = new Map<S.CommitId, number>();
  private _commitIdToAnalyzer = new Map<S.CommitId, CommitAnalyzer>();
  private _previousCommit = new Map<S.CommitId, S.CommitId|undefined>();

  constructor(
    private _dailyAnalyzers: SpanAnalyzer[],
    private _historyDepthDays: number,
    private _etag: string = HistoryAnalyzer.etag(_dailyAnalyzers, _historyDepthDays),
  ) {
    for (let dayIdx = 0; dayIdx < this._dailyAnalyzers.length; ++dayIdx) {
      for (const analyzer of this._dailyAnalyzers[dayIdx].commits)
        this._commitIdToDayIndex.set(analyzer.commitId, dayIdx);
    }

    const commitAnalyzers = this._dailyAnalyzers.map(d => d.commits).flat();
    for (let idx = 0; idx < commitAnalyzers.length; ++idx) {
      const analyzer = commitAnalyzers[idx];
      this._commitIdToAnalyzer.set(analyzer.commitId, analyzer);
    }

    // For each commit, compute previous commit.
    let previousCommitId: S.CommitId|undefined;
    for (const analyzer of commitAnalyzers.toReversed()) {
      this._previousCommit.set(analyzer.commitId, previousCommitId);
      previousCommitId = analyzer.commitId;
    }
  }

  etag() {
    return this._etag;
  }

  days() {
    return this._dailyAnalyzers;
  }

  private _memoizedEnvironments?: WireTypes.RunEnvironment[];
  environments() {
    if (!this._memoizedEnvironments)
      this._memoizedEnvironments = CommitAnalyzer.runEnvironments(this._dailyAnalyzers.map(day => day.commits).flat());
    return this._memoizedEnvironments;
  }

  unhealthyTests(timeline: Timeline, commitId: S.CommitId): T.TestRanges {
    return this._unhealthyTests.get({ timeline, commitId });
  }

  commitStats(timeline: Timeline, commitId: S.CommitId, computeDurations: boolean): SpanStats {
    const commit = this._commitIdToAnalyzer.get(commitId);
    if (!commit)
      return { testOutcomes: T.newOutcomes(), commitsCount: 0, durations: [] };
    const unhealthyTests = this._unhealthyTests.get({ timeline, commitId });
    return {
      testOutcomes: T.regressify(commit.testOutcomes(timeline), unhealthyTests),
      commitsCount: 1,
      durations: computeDurations ? commit.durations(timeline) : [],
    };
  }

  dayStats(timeline: Timeline, dayIdx: number, computeDurations: boolean): SpanStats {
    const analyzer = this._dailyAnalyzers[dayIdx];
    if (!analyzer.commits.length)
      return { testOutcomes: T.newOutcomes(), commitsCount: 0, durations: [] };

    const lastCommit = analyzer.commits.at(-1)!;
    const unhealthyTests = this._unhealthyTests.get({ timeline, commitId: lastCommit.commitId });
    return {
      testOutcomes: T.regressify(analyzer.testOutcomes(timeline), unhealthyTests),
      commitsCount: analyzer.commits.length,
      durations: computeDurations ? analyzer.durations(timeline) : []
    };
  }
}
