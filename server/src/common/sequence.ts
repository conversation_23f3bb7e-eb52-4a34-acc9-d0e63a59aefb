import { Heap } from './heap.js';

export class Sequence<T> {
  static fromList<T>(a: ArrayLike<T>): Sequence<T> {
    return new Sequence(
      function(pos: number): Iterator<T> {
        return {
          next() {
            if (pos >= a.length)
              return { done: true, value: undefined };
            return { done: false, value: a[pos++] };
          },
        };
      },
      a.length,
    );
  }

  static chain<T>(seqs: Sequence<T>[]) {
    const leftsums: number[] = [];
    let length = 0;
    for (let i = 0; i < seqs.length; ++i) {
      length += seqs[i].length;
      leftsums.push(length);
    }
    
    return new Sequence<[T, number]>(
      function (fromIdx: number): Iterator<[T, number]> {
        fromIdx = Math.max(0, Math.min(length, fromIdx));
        let idx = Sequence.fromList(leftsums).partitionPoint((x => x <= fromIdx));
        if (idx >= seqs.length) {
          return {
            next: () => ({ done: true, value: undefined }),
          }
        }

        let it = seqs[idx].seek(idx > 0 ? fromIdx - leftsums[idx - 1] : fromIdx);
        return {
          next() {
            let result = it.next();
            while (result.done && ++idx < seqs.length) {
              it = seqs[idx].seek(0);
              result = it.next();
            }
            return result.done ? result : { done: false, value: [result.value, idx] };
          }
        }
      },
      length
    )
  }

  static merge<T>(sequences: Sequence<T>[], cmp: (a: T, b: T) => number) {
    const length = sequences.reduce((acc, seq) => acc + seq.length, 0);
    return new Sequence<T>(
      function(fromIdx) {
        fromIdx = Math.max(0, Math.min(length, fromIdx));

        const offsets = quickAdvance(sequences, cmp, fromIdx);

        const entries: [Iterator<T>, T][] = [];
        
        for (let i = 0; i < sequences.length; ++i) {
          const seq = sequences[i];
          const it = seq.seek(offsets[i]);
          const itval = it.next();
          if (!itval.done)
            entries.push([it, itval.value]);
        }
        const heap = new Heap<Iterator<T>, T>(cmp, entries);

        return {
          next() {
            if (!heap.size)
              return { done: true, value: undefined };
            ++fromIdx;
            const [it, e] = heap.popEntry()!;
            const itval = it.next();
            if (!itval.done)
              heap.push(it, itval.value);
            return { done: false, value: e };
          },
        };
      },
      length
    );
  }

  static EMPTY = new Sequence<any>(function* () {}, 0);

  constructor(
    private _seek: (idx: number) => Iterator<T>,
    public readonly length: number,
  ) {

  }

  seek(idx: number): IterableIterator<T> {
    const it = this._seek(idx);
    (it as any)[Symbol.iterator] = () => it;
    return it as IterableIterator<T>;
  }

  get(idx: number): T|undefined {
    return this.seek(idx).next().value;
  }

  map<K>(mapper: (e: T) => K): Sequence<K> {
    const originalSeek = this._seek;
    return new Sequence<K>(
      function(idx) {
        const it = originalSeek(idx);
        return {
          next() {
            const next = it.next();
            if (next.done)
              return next;
            return { done: false, value: mapper(next.value) };
          }
        }
      },
      this.length,
    );
  }

  /** Number of elements in sequence that are <= comparator. Only works on sorted sequences. */
  partitionPoint(predicate: (a: T) => boolean): number {
    let lo = 0, hi = this.length;
    while (lo < hi) {
      const mid = (lo + hi) >>> 1;
      if (predicate(this.get(mid)!))
        lo = mid + 1;
      else
        hi = mid;
    }
    return lo;
  }
}

// Gleb Evstropov's solution to quickly advance all sequences so that K'th element is one of their first elements.
// The total complexity of this is T * logT * logK.
// Before exlaining the idea, let's do some definitions:
// 1. T is the number of sets
// 2. K is the element at position we're looking at.
// 
// The idea of the algorithm is to advance by some value X one-at-a-time, where X elements are taken as a prefix of some sequence.
// First of all, let's notice, that if for every sequence we have some prefix of length <= K/T, then among these prefixes
// there is **at least one** whose elements all come before index K. So for example, if we take all prefixes of length K/T, and sort them
// by their last element with the comparator `cmp`, then the minimal prefix is guaranteed to be eaten.
//
// Now, let's get a prefix of each sequence of length X = K/(2T). If the sequence's length is less than X, then we can skip it: we consider
// that all sequences have infinite amount of `Infinity` as trailing elements.
//
// We will a prefix from heap, change K, and re-insert a new prefix of the sequence into heap while X*T <= K.
// We can do T operations this way. In T operations, we will half the `K`. After the X * T > K, we have to drop all prefixes and re-initialize
// X.
// This yields the O(T * logT * logK).
// 
function quickAdvance<T>(sequences: Sequence<T>[], cmp: (a: T, b: T) => number, k: number): ArrayLike<number> {
  const offsets = new Map<Sequence<T>, number>(sequences.map(s => [s, 0]));

  while (offsets.size && k > 0) {
    const t = offsets.size;
    const x = Math.max(Math.floor(k / t / 2), 1);

    const entries: [Sequence<T>, T][] = [];
    for (const [seq, offset] of offsets) {
      if (offset + x <= seq.length)
        entries.push([seq, seq.get(offset + x - 1)!]);
    }

    // Build heap in linear time.
    const heap = new Heap<Sequence<T>, T>(cmp, entries);
    while (heap.size && k > 0 && (x === 1 || k >= x * t)) {
      k -= x;
      const [seq] = heap.popEntry()!;
      const offset = offsets.get(seq)! + x;
      if (offset === seq.length)
        offsets.delete(seq);
      else
        offsets.set(seq, offset);

      // add a new block of size X
      if (offset + x <= seq.length)
        heap.push(seq, seq.get(offset + x - 1)!);
    }
  }
  return sequences.map(seq => offsets.get(seq) ?? seq.length);
}
