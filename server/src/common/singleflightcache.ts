import { Cache, SharedCacheStore } from "./caches/cache.js";
import { LRUPolicy } from "./caches/lruPolicy.js";
import { Singleflight } from "./singleflight.js";

export class SingleflightCache<INPUT, OUTPUT> {
  private _singleflight: Singleflight<INPUT, OUTPUT|undefined>;
  private _cache: Cache<string, { value: OUTPUT, deadline: number }>;
  private _currentTime: () => number;

  constructor(private _options: {
    key: (input: INPUT) => string,
    onGetTiming?: (input: INPUT, key: string, method: 'get'|'getFresh', since: number, until: number, cacheHit: boolean, size: number) => void,
    fetch: (input: INPUT, stale: OUTPUT|undefined, signal: AbortSignal) => Promise<OUTPUT|undefined>,
    cache?: SharedCacheStore,
    size: number,
    currentTime?: () => number,
    ttl: number,
    concurrency?: number,
  }) {
    this._currentTime = this._options.currentTime ?? (() => Date.now());
    this._cache = new Cache(new LRUPolicy(this._options.size), this._options.cache);
    this._singleflight = new Singleflight({
      key: this._options.key,
      fetch: async (input: INPUT, key: string, signal: AbortSignal) => {
        const stale = this._cache.get(key);
        const value = await this._options.fetch(input, stale ? stale.value : undefined, signal);
        if (value === undefined || signal.aborted)
          return undefined;
        this._cache.set(key, { value, deadline: this._currentTime() + this._options.ttl });
        return value;
      },
      concurrency: this._options.concurrency ?? 4,
    });
  }

  cacheSize() {
    return this._cache.size;
  }

  async get(input: INPUT): Promise<OUTPUT|undefined> {
    const since = performance.now();
    const key = this._options.key(input);
    const cached = this._cache.get(key);
    if (cached && cached.deadline > this._currentTime()) {
      this._options.onGetTiming?.(input, key, 'get', since, performance.now(), true, this._cache.size);
      return cached.value;
    }
    const fetchPromise = this._singleflight.fetch(input, key);
    const result = cached ? cached.value : await fetchPromise;
    this._options.onGetTiming?.(input, key, 'get', since, performance.now(), false, this._cache.size);
    return result;
  }

  async getFresh(input: INPUT): Promise<OUTPUT|undefined>  {
    const since = performance.now();
    const key = this._options.key(input);
    const cached = this._cache.get(key);
    if (cached && cached.deadline > this._currentTime()) {
      this._options.onGetTiming?.(input, key, 'get', since, performance.now(), true, this._cache.size);
      return cached.value;
    }
    const result = await this._singleflight.fetch(input, key);
    this._options.onGetTiming?.(input, key, 'get', since, performance.now(), false, this._cache.size);
    return result;
  }

  // Drop the cache, and also abort all in-flight requests.
  clear() {
    this._singleflight.abortAll();
    this._cache.clear();
  }

  delete(input: INPUT) {
    const key = this._options.key(input);
    this._singleflight.abort(input, key)
    this._cache.delete(key);
  }

  refresh(input: INPUT, options?: { dropCached: boolean }) {
    const key = this._options.key(input);
    
    // If the in-flight request is running, then just invalidate the request.
    if (this._singleflight.has(input, key))
      this._singleflight.invalidate(input, key);
    // Ohterwise, if cache has entry, then we should initiate a new request.
    else if (this._cache.has(key))
      this._singleflight.fetch(input, key);

    if (options?.dropCached)
      this._cache.delete(key);
  }
}
