import type { AppClientId, ProductPlanPublicId, ProjectPublicId, UserPublicId, UserSessionPublicId } from '@flakiness/database';
import type { FlakinessReport as FK } from '@flakiness/report';
import type { Git } from '../node/git.js';
import type { Stats } from './stats/stats.js';
import { TestOutcomes as T } from './stats/testOutcomes.js';

export namespace WireTypes {
  export type Commit = {
    commitId: FK.CommitId,
    timestamp: FK.UnixTimestampMS, // ms
    message: string,
    avatar_url?: string,
    author?: string,
    walkIndex: number,
  }

  export type Ref = Git.Ref;

  export type PullRequestState = 'open' | 'closed' | 'merged';
  export type PullRequest = {
    url: string,
    headSha: string;
    baseSha: string;
    description: string;
    avatar_url?: string;
    author?: string;
    state: PullRequestState,
    number: number;
    title: string;
    createdTimestamp: FK.UnixTimestampMS;
    updatedTimestamp: FK.UnixTimestampMS;
    commitStats?: CommitStats;
  }

  export type ProjectVisibility = 'public'|'private';
  export type ProjectRole = 'editor' | 'viewer';
  export type Project = {
    projectName: string;
    projectPublicId: ProjectPublicId;
    projectSlug: string;
    runsCount: number;
    readWriteAccessToken?: string;
    sourceType: string;
    sourceLastFetchTime?: FK.UnixTimestampMS;
    sourceLastFetchHTTPCode?: number;
    sourceOwner: string,
    sourceRepo: string,
    lastUploadTimestamp?: FK.UnixTimestampMS;
    visibility: ProjectVisibility;
    org: WireTypes.Organization;
    access: ProjectRole;
    reportTimeIsUploadTime: boolean;
    preferredDataRetentionDays?: number;
    acceptableFlakinessRatio: number;
    defaultTimezone?: string;
    regressionWindowDays: number,
  }

  export type OrgRole = 'admin' | 'member' | 'owner';
  export type Organization = {
    orgName: string;
    orgSlug: string;
    // Organizations are public, and users accessing them might have no explicit role.
    access?: OrgRole;
    // Organization members get to see the limitations by billing.
    restrictedProjectAccess: boolean,
    restrictedCIUploads: boolean,
  }

  export type BillingLimitations = 'restrict-project-access'|'restrict-project-access-except-ci-uploads';
  export type BillingStatus = {
    restrictedProjectAccess?: boolean,
    restrictedCIUploads?: boolean,
    subscription?: Subscription,
  }

  // These are only used when creating new plans.
  export type ProductPlanPrice = {
    priceId: string,
    billing: BillingPeriod,
    description: string,
  }

  export type DeviceAuthRequest = {
    clientId: AppClientId,
    deviceName: string,
    deviceCode: string,
  }

  export type BillingPeriod = 'month'|'day'|'week'|'year';
  export type ProductPlan = {
    /**
     * A unique ID of the plan.
     */
    id: ProductPlanPublicId,
    /**
     * Billing period of the plan
     */
    billing: BillingPeriod,
    /**
     * User-visible plan name
     */
    name: string,
    /**
     * Included license seats. Unlimited if undefined.
     */
    seats?: number,
    /**
     * Included storage in GB, where GB is 1000^3 bytes. Unlimited if undefined.
     */
    storage?: number,
    /**
     * Included test runs. Unlimited if undefined.
     */
    testRuns?: number,
    /**
     * Monthly price of the plan, in USD
     */
    price: number,
    /**
     * Monthly price for each GB outside of included usage, GB-month.
     */
    priceExtraStorage: number,
    /**
     * Monthly price for each test run.
     */
    priceExtraTestRuns: number,
    /**
     * Included trial, if any. Undefined value means no trial was included.
     */
    trialDays?: number,
    /**
     * Maximum data retention in days.
     */
    maxDataRetentionDays: number,
    /**
     * If this plan is a "special offer" to a certain org,
     * then this is the slug of the org. Such plans can *only*
     * be installed on the matching orgs.
     */
    orgSlug?: string,
  }

  export type Subscription = {
    plan: ProductPlan,
    hasBillingIssues: boolean,
    trialEnd?: FK.UnixTimestampMS,
    periodStart: FK.UnixTimestampMS,
    periodEnd: FK.UnixTimestampMS,

    maxDataRetentionDays: number,
    missingPaymentMethod: boolean,

    usedTestRuns: number,
    usedStorage: number,
    usedSeats: number,
  }

  export type DailyMetrics = {
    totalBytes: number;
    runsCount: number,
    testsCount: number,
    dayTimestampMs: FK.UnixTimestampMS,
  }

  export type User = {
    userId: UserPublicId;
    userName: string;
    userLogin: string;
    isSuperUser: boolean;
    avatarUrl?: string;
  }

  export type ProjectCollaborator = {
    user: User,
    userRole: ProjectRole,
  }

  export type OrgMember = {
    user: User,
    accessRole: OrgRole,
  }

  export type AggregatedMetadata = {
    name: string[],
    category: string[],
    configPath: FK.GitFilePath[];
    systemData: {
      osName: string[],
      osArch: string[],
    },
    userSuppliedData: Record<string, (string|boolean|number)[]>,
  }

  export type RunEnvironment = {
    envId: Stats.EnvId,
    category: string,
    name?: string,
    configPath?: FK.GitFilePath;
    systemData: {
      osName: string,
      osVersion: string,
      osArch: string,
    },
    userSuppliedData?: Record<string, string|boolean|number>,
  }

  export type ListCommitOptions = {
    // commit filtering options
    head: string,
    headOffset?: number,
    maxCount?: number,
    sinceTimestamp?: FK.UnixTimestampMS,
    untilTimestamp?: FK.UnixTimestampMS,
  }
  
  export type Annotation = {
    annotationId: Stats.AnnotationId,
    type: string,
    description?: string,
  }

  export type TestError = {
    errorId: Stats.ErrorId,
    message?: string,
    value?: string,
  }

  // The average is computed by dividing one to another.
  export type AverageValue = {
    sum: number,
    count: number,
  }

  /**
   * the `idle` status is for a day that doesn't have any commits
   * the `untested` status is for a day that has commits that weren't tested.
   */
  export type DayOutcome = 'regressed'|'expected'|'unexpected'|'skipped'|'flaked'|'untested'|'idle';

  export type Outcome = 'regressed'|'expected'|'unexpected'|'skipped'|'flaked';

  export type OutcomesCount = T.TestOutcomeCounts;

  export type Test = {
    testId: Stats.TestId,
    filePath: FK.GitFilePath,
    titles: string[],
  };

  export type DayStats = {
    outcome: WireTypes.DayOutcome,
    duration?: FK.DurationMS,
  }

  export type TestStats = {
    /**
     * TestIndex is the index of the test in the global test repository.
     * Unlike testId, test index might change in future, so it should never be
     * cached. For this reason, we also report it as part of testStats rather then
     * Test.
     */
    testIdx: Stats.TestIndex,
    /**
     * This is the environment id inside the run.
     * Could be used to match to the FlakinessReport.TestRun
     */
    envId: Stats.EnvId,
    attemptIdx?: number,
    outcome: Outcome,
    // Reverse-chronological
    reportBreakdown: DayStats[],
    dailyHistory: DayStats[],
    timeline: JSONTimeline,
    test: Test,
    commitId: FK.CommitId,
    runId: Stats.RunId,
    category: string,

    lineNumber: FK.Number1Based,
    tags?: string[],

    hasVideo?: boolean,
    hasImage?: boolean,
    hasTrace?: boolean,

    durationMs: FK.DurationMS,
    durationChangeMs: FK.DurationMS,

    annotations?: Annotation[],
  }

  export type UserSession = {
    sessionPublicId: UserSessionPublicId,
    name?: string,
    clientId: AppClientId,
    lastAccessTimestampSeconds: number,
  }

  export type TimelineStats = {
    timeline: JSONTimeline,
    durationMs: number,
    durationChangeMs: FK.DurationMS,
    daily: WireTypes.DayStats[],
    testStats: OutcomesCount,
  }

  export type ErrorStats = {
    error: WireTypes.TestError,
    impactedTests: number,
    impactedTimelines: number,
  }

  export type AnnotationStats = {
    annotation: WireTypes.Annotation,
    annotatedTests: number,
    annotatedTimelines: number,
  }

  export type TagStats = {
    tag: string,
    impactedTests: number,
    impactedTimelines: number,
  }

  export type CommitStats = {
    commit: Commit,
    durationMs?: FK.DurationMS,
    testStats: OutcomesCount,
    runs: RunStats[],
  }

  export type RunStats = {
    run: WireTypes.RunInfo,
    hasImages: boolean,
    hasTraces: boolean,
    hasVideos: boolean,
    testStatsOutcomes: OutcomesCount,
  }

  export type UnparsedReport = {
    runId: Stats.RunId,
    error: string,
  }

  export type RunInfo = {
    commitId: Stats.CommitId,
    runId: Stats.RunId,
    url: string,
    startTimestamp: FK.UnixTimestampMS,
    duration: FK.DurationMS,
  }

  export type RunProcessingStatus = {
    runId: Stats.RunId,
    duration: FK.DurationMS,
    url: string,
    environments: WireTypes.RunEnvironment[],
    testsCount: number,
    startTimestampMs: FK.UnixTimestampMS,
  }

  export type ServerInfo = {
    enabledBilling: boolean,
    githubAppPublicURL: string,
    onlySuperusersCanCreateOrganizations: boolean,
    hostname: string,
    uptimeMs: number,
    memory: {
      usedBytes: number,
      totalBytes: number,
      timestampMs: number,
    },
    deploymentLicense: {
      validUntilMs: number,
    }
  }

  export type Job = {
    queueName: string;
    workerName?: string,
    heartbeatTimestampSeconds?: number,
    id: number;
    jobId: string;
    category?: string;
    concurrencyId?: string;
    data?: any;
    // -- execution args
    executeAfterTimestampSeconds?: number, // delayed job
    executionError?: string;
    executionResult?: string;
    executionDurationSeconds?: number,
    executionTimestampSeconds?: number,
    submittedTimestampSeconds: number;
    retry: number;
  }

  export type SortDirection = 'asc'|'desc';
  export type TestSortAxis = 'name';
  export type TestStatsSortAxis = 'name'|'timeline_name'|'duration'|'history'|'outcome'|'duration_trend';
  export type TimelineStatsSortAxis = 'name'|'outcome'|'total_time'|'unexpected'|'expected'|'skipped'|'flaked'|'regressed'|'history'|'duration_trend';
  export type ErrorStatsSortAxis = 'name'|'timelines'|'tests';
  export type AnnotationStatsSortAxis = 'name'|'timelines'|'tests';
  export type TagStatsSortAxis = 'name'|'timelines'|'tests';
  export type CommitStatsSortAxis = 'chrono'|'outcome'|'unexpected'|'expected'|'skipped'|'flaked'|'regressed';
  export type RunStatsSortAxis = 'id'|'outcome'|'chrono'|'unexpected'|'expected'|'skipped'|'flaked'|'factual_duration'|'regressed';
  export type PullRequestSortAxis = 'created';

  export type PagedResponse<T> = {
    elements: T[],
    pageNumber: number,
    pageSize: number,
    totalPages: number,
    totalElements: number,
  }

  export type Day = {
    sinceTimestamp: FK.UnixTimestampMS,
    untilTimestamp: FK.UnixTimestampMS,
  }

  export type ReportOptions = {
    orgSlug: string,
    projectSlug: string,
    fql: string,
    timelineSplit: JSONTimelineSplit,
    commitOptions: ListCommitOptions,
    timeZoneId: string,
    regressionWindowDays: number,
    customHistoryHead?: string,
    dailyReportBreakdown: boolean,
    historyBuckets: number,
    acceptableFlakinessRate: number,
  }

  export type JSONTimeline = {
    system: { name: string, value: string }[],
    user: { name: string, value: string }[],
  }

  export type JSONTimelineSplit = {
    splitByDefault?: boolean,
    inverse: {
      system?: string[],
      user?: string[],
    },
    filters: {
      system?: { name: string, values: string[] }[],
      user?: { name: string, values: string[] }[],
    },
  }

  export type PageOptions = {
    number: number,
    size: number,
  }

  export type SortOptions<K> = {
    axis: K,
    direction: WireTypes.SortDirection,
  }

  export const EMPTY_OUTCOMES: WireTypes.OutcomesCount = T.newOutcomeCounts();
}

export function wireOutcomesToOutcome(outcomes: WireTypes.OutcomesCount, acceptableFlakinessRatio: number): WireTypes.Outcome|undefined {
  if (outcomes.regressed)
    return 'regressed';
  if (outcomes.unexpected)
    return 'unexpected';
  const total = outcomes.regressed + outcomes.unexpected + outcomes.flaked + outcomes.expected + outcomes.skipped;
  const flakinessRatio = outcomes.flaked / total;
  if (flakinessRatio > acceptableFlakinessRatio)
    return 'flaked';
  if (outcomes.expected || outcomes.flaked)
    return 'expected';
  if (outcomes.skipped)
    return 'skipped';
  return undefined;
}
