import { FlakinessReport } from '@flakiness/report';
import { ReportUtils } from '@flakiness/report';
import { Stats } from './stats/stats.js';
import { WireTypes } from './wireTypes.js';

export class WireReport {
  public readonly envs: WireTypes.RunEnvironment[];
  public readonly report: FlakinessReport.Report;
  public readonly commitId: FlakinessReport.CommitId;
  public readonly url?: string;
  public readonly duration: FlakinessReport.DurationMS;

  private _testToWireTest = new Map<FlakinessReport.Test, WireTypes.Test>();
  private _testIdToTest = new Map<Stats.TestId, FlakinessReport.Test>();

  constructor(report: FlakinessReport.Report) {
    this.report = report;
    this.commitId = report.commitId;
    this.url = report.url;
    this.duration = report.duration;
    this.envs = report.environments.map(env => Stats.jsonEnvToWireEnv(Stats.flakinessEnvToJSONEnv(report, env)));
    ReportUtils.visitTests(report, (test, parents) => {
      const wireTest = Stats.flakinessTestToWireTypesTest(test, parents);
      this._testToWireTest.set(test, wireTest);
      this._testIdToTest.set(wireTest.testId, test);
    });
  }

  wireTest(test: FlakinessReport.Test) {
    return this._testToWireTest.get(test);
  }

  fkTest(testId: Stats.TestId) {
    return this._testIdToTest.get(testId);
  }
}
