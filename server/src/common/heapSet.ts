
//           0
//     1           2
//  3    4      5      6 
// 7 8  9 10  11 12  13 14
function parent(idx: number) {
  return ((idx - 1) / 2)|0;
}

/**
 * Note: these "set" properties do NOT come for free; in fact, this implementation is 3x slower than
 * regular heap.
 */
export class HeapSet<ELEMENT, SCORE> {
  static createMin<ELEMENT>(elements: [ELEMENT, number][] = []) {
    return new HeapSet<ELEMENT, number>((a, b) => a - b, elements);
  }

  static createMax<ELEMENT>(elements: [ELEMENT, number][] = []) {
    return new HeapSet<ELEMENT, number>((a, b) => b - a, elements);
  }

  private _heap: {
    score: SCORE,
    element: ELEMENT,
  }[] = [];

  constructor(
    private _cmp: (a: SCORE, b: SCORE) => number,
    elements: [ELEMENT, SCORE][] = []
  ) {
    this._heap = elements.map(([element, score]) => ({ element, score }));
    for (let idx = this._heap.length - 1; idx >= 0; --idx) {
      this._indexes.set(this._heap[idx].element, idx);
      this._down(idx);
    }
  }

  private _indexes: Map<ELEMENT, number> = new Map();

  keys() {
    return this._indexes.keys();
  }

  has(e: ELEMENT): boolean {
    return this._indexes.has(e);
  }

  *entries() {
    for (const entry of this._heap)
      yield [entry.element, entry.score];
  }

  private _up(idx: number) {
    while (idx > 0) {
      const parentIdx = parent(idx);
      if (this._cmp.call(null, this._heap[parentIdx].score, this._heap[idx].score) <= 0)
        break;
      const tmp = this._heap[idx];
      this._heap[idx] = this._heap[parentIdx];
      this._heap[parentIdx] = tmp;

      this._indexes.set(this._heap[idx].element, idx);
      this._indexes.set(this._heap[parentIdx].element, parentIdx);

      idx = parentIdx;
    }
  }

  private _down(idx: number) {
    const N = this._heap.length;
    const e = this._heap[idx];
    while (true) {
      const leftIdx = idx * 2 + 1;
      const rightIdx = idx * 2 + 2;

      let smallestIdx;
      if (leftIdx < N && rightIdx < N) {
        smallestIdx = this._cmp(this._heap[leftIdx].score, this._heap[rightIdx].score) < 0 ? leftIdx : rightIdx;
      } else if (leftIdx < N) {
        smallestIdx = leftIdx;
      } else if (rightIdx < N) {
        smallestIdx = rightIdx;
      } else {
        break;
      }
      if (this._cmp(e.score, this._heap[smallestIdx].score) < 0)
        break;

      this._heap[idx] = this._heap[smallestIdx];
      this._indexes.set(this._heap[smallestIdx].element, idx);
      idx = smallestIdx;
    }
    this._heap[idx] = e;
    this._indexes.set(e.element, idx);
  }

  add(element: ELEMENT, score: SCORE) {
    const index = this._indexes.get(element);
    if (index === undefined) {
      this._heap.push({ element, score });
      this._indexes.set(element, this._heap.length - 1);
      this._up(this._heap.length - 1);
    } else {
      const oldKey = this._heap[index].score;
      this._heap[index].score = score;
      if (this._cmp.call(null, score, oldKey) < 0)
        this._up(index);
      else
        this._down(index);
    }
  }

  delete(value: ELEMENT) {
    const index = this._indexes.get(value);
    if (index === undefined)
      return;
    this._indexes.delete(value);

    const last = this._heap.pop()!;
    if (last.element === value)
      return;

    const oldKey = this._heap[index].score;

    this._heap[index] = last;
    this._indexes.set(last.element, index);

    if (this._cmp.call(null, last.score, oldKey) < 0)
      this._up(index);
    else
      this._down(index);
  }

  get size() {
    return this._heap.length;
  }

  clear() {
    this._heap = [];
    this._indexes.clear();
  }

  peekEntry(): [ELEMENT, SCORE]|undefined {
    return this._heap.length ? [this._heap[0].element, this._heap[0].score] : undefined;
  }

  peek(): ELEMENT|undefined {
    return this._heap.length ? this._heap[0].element : undefined;
  }

  pop(): ELEMENT|undefined {
    if (!this._heap.length)
      return undefined;
    return this.popEntry()![0];
  }

  popEntry(): [ELEMENT, SCORE]|undefined {
    if (!this._heap.length)
      return undefined;
    const entry = this._heap[0];
    this._indexes.delete(entry.element);
    const last = this._heap.pop()!;

    // If heap is empty, then nothing to do.
    if (!this._heap.length)
      return [entry.element, entry.score];

    this._heap[0] = last;
    this._down(0);
    return [entry.element, entry.score];
  }
}

// const iterators = [
//   [1,2,5,7],
//   [-1,2,4],
//   [5],
// ].map(a => a.values());

// const heap = new HeapSet<ArrayIterator<number>, number>((a, b) => a - b);

// for (const iterator of iterators) {
//   const next = iterator.next();
//   if (next.done)
//     continue;
//   heap.add(iterator, next.value);
// }

// for (let i = 0; i < 5 && heap.size; ++i) {
//   const [iterator, key] = heap.pop()!;
//   console.log(key);
//   const next = iterator.next();
//   if (!next.done)
//     heap.add(iterator, next.value);
// }
