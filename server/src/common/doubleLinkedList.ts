type DLLHead<VALUE> = {
  prev: DLLNode<VALUE>|DLLTail<VALUE>,
  value: undefined;
}

type DLLTail<VALUE> = {
  next: DLLNode<VALUE>|DLLHead<VALUE>,
  value: undefined;
}

type DLLNode<VALUE> = {
  prev: DLLNode<VALUE>|DLLTail<VALUE>,
  next: DLLNode<VALUE>|DLLHead<VALUE>,
  value: VALUE,
}

/**
 * LRU implementation based on double-linked list
 * 
 * TAIL :: [ prev, next ] :: HEAD
 */
export class DoubleLinkedList<VALUE> implements Iterable<VALUE> {
  private _head: DLLHead<VALUE>;
  private _tail: DLLTail<VALUE>;
  private _nodes = new Map<VALUE, DLLNode<VALUE>>();

  constructor() {
    this._head = { prev: undefined as any, value: undefined };
    this._tail = { next: this._head, value: undefined };
    this._head.prev = this._tail;
  }

  // Implement the iterator protocol
  [Symbol.iterator](): Iterator<VALUE> {
    let current: DLLHead<VALUE>|DLLNode<VALUE> = this._tail.next;
    
    return {
      next: (): IteratorResult<VALUE> => {
        // Skip the head node
        if (current === this._head)
          return { done: true, value: undefined };
        
        const value = current.value;
        current = (current as any).next;
        return {
          done: false,
          value: value as VALUE
        };
      }
    };
  }

  private _cut(node: DLLNode<VALUE>) {
    node.prev.next = node.next;
    node.next.prev = node.prev;
  }

  public moveToTail(value: VALUE) {
    let node = this._nodes.get(value);
    if (!node) {
      node = {
        prev: this._tail,
        next: this._tail.next,
        value,
      };
      this._nodes.set(value, node);
    } else {
      this._cut(node);
      node.prev = this._tail;
      node.next = this._tail.next;
    }
    this._tail.next.prev = node;
    this._tail.next = node;
  }

  public moveToHead(value: VALUE) {
    let node = this._nodes.get(value);
    if (!node) {
      node = {
        next: this._head,
        prev: this._head.prev,
        value,
      };
      this._nodes.set(value, node);
    } else {
      this._cut(node);
      node.next = this._head;
      node.prev = this._head.prev;
    }
    this._head.prev.next = node;
    this._head.prev = node;
  }

  size() {
    return this._nodes.size;
  }

  clear() {
    this._nodes.clear();
    this._head.prev = this._tail;
    this._tail.next = this._head;
  }

  delete(value: VALUE) {
    const node = this._nodes.get(value);
    if (node) {
      this._cut(node);
      this._nodes.delete(value);
    }
  }

  head(): VALUE|undefined {
    return this._head.prev.value;
  }

  tail(): VALUE|undefined {
    return this._tail.next.value;
  }
}
